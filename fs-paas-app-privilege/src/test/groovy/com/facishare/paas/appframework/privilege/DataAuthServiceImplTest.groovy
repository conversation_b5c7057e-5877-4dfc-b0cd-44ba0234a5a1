package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.privilege.dto.DataPrivilegeCalcProgress
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试DataAuthServiceImpl类的方法
 */
@Unroll
class DataAuthServiceImplTest extends Specification {
    
    DataAuthServiceImpl dataAuthService
    DataAuthProxy dataAuthProxy
    
    def setup() {
        dataAuthService = new DataAuthServiceImpl()
        dataAuthProxy = Mock(DataAuthProxy)
        dataAuthService.proxy = dataAuthProxy
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法正常场景，成功获取进度信息
     */
    def "calcProgressTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def expectedBody = new DataPrivilegeCalcProgress.Body(
            tenantId: tenantId,
            total: 100,
            current: 50,
            percent: 50,
            done: false
        )
        
        def mockResult = DataPrivilegeCalcProgress.Result.builder()
            .code(0)
            .message("成功")
            .body(expectedBody)
            .build()
        
        when:
        // 执行被测试方法
        def result = dataAuthService.calcProgress(tenantId)
        
        then:
        // 结果验证
        1 * dataAuthProxy.calcProgress(_) >> { args ->
            DataPrivilegeCalcProgress.Arg arg = args[0]
            assert arg.tenantId == tenantId
            return mockResult
        }
        result == expectedBody
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法异常场景，接口返回错误
     */
    def "calcProgressErrorWithNonZeroCode"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def mockResult = DataPrivilegeCalcProgress.Result.builder()
            .code(errorCode)
            .message(errorMessage)
            .build()
        
        when:
        // 执行被测试方法
        dataAuthService.calcProgress(tenantId)
        
        then:
        // 异常验证
        1 * dataAuthProxy.calcProgress(_) >> mockResult
        def exception = thrown(ValidateException)
        exception.message == errorMessage

        where:
        // 测试数据
        errorCode | errorMessage
        1         | "参数错误"
        100       | "权限不足"
        500       | "系统内部错误"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法参数校验，tenantId为空的情况
     */
    def "calcProgressErrorWithNullTenantId"() {
        given:
        // 测试准备
        String tenantId = null
        
        when:
        // 执行被测试方法
        dataAuthService.calcProgress(tenantId)
        
        then:
        // 异常验证 - 在实际代码中可能会检查参数合法性
        // 如果没有参数校验，这里可能需要修改或移除
        thrown(NullPointerException)
    }
} 