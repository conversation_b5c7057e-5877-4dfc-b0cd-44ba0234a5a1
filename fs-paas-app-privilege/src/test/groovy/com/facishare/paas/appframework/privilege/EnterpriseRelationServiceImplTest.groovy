package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.privilege.util.AppIdUtil
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.support.GDSHandler
import com.fxiaoke.enterpriserelation2.arg.BatchGetRelationDownstreamAndOwnerArg
import com.fxiaoke.enterpriserelation2.arg.GetMapperObjectOwnerIdArg
import com.fxiaoke.enterpriserelation2.arg.UpstreamAndDownstreamOuterTenantIdOutArg
import com.fxiaoke.enterpriserelation2.common.HeaderObj
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationObjService
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试EnterpriseRelationServiceImpl类的方法
 */
@Unroll
class EnterpriseRelationServiceImplTest extends Specification {

    EnterpriseRelationServiceImpl enterpriseRelationService
    EnterpriseRelationService mockEnterpriseRelationService
    EnterpriseRelationObjService mockEnterpriseRelationObjService
    GDSHandler mockGdsHandler

    def setup() {
        enterpriseRelationService = new EnterpriseRelationServiceImpl()

        mockEnterpriseRelationService = Mock(EnterpriseRelationService)
        mockEnterpriseRelationObjService = Mock(EnterpriseRelationObjService)
        mockGdsHandler = Mock(GDSHandler)

        enterpriseRelationService.enterpriseRelationService = mockEnterpriseRelationService
        enterpriseRelationService.enterpriseRelationObjService = mockEnterpriseRelationObjService
        enterpriseRelationService.gdsHandler = mockGdsHandler
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，使用默认参数版本
     */
    def "getRelationDownstreamInfoTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def partnerIdList = ["partner1", "partner2"] as Set

        def mockResult = RestResult.newSuccess([
                "partner1": new RelationDownstreamResult(downstreamOuterTenantId: 10001, relationOwnerOuterUid: 20001),
                "partner2": new RelationDownstreamResult(downstreamOuterTenantId: 10002, relationOwnerOuterUid: 20002)
        ])

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getRelationDownstreamInfo(tenantId, partnerIdList)

        then:
        // 结果验证
        1 * mockEnterpriseRelationService.batchGetRelationDownstreamAndOwner(_, _) >> { args ->
            HeaderObj headerObj = args[0]
            BatchGetRelationDownstreamAndOwnerArg arg = args[1]

            assert headerObj.enterpriseId == Integer.parseInt(tenantId)
            assert arg.objectApiName == "Partner"
            assert arg.upstreamTenantId == Integer.parseInt(tenantId)
            assert arg.crmDataIds.containsAll(partnerIdList)

            return mockResult
        }

        result == mockResult.data
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，使用指定对象API名称版本
     */
    def "getRelationDownstreamInfoWithObjectApiNameTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def objectApiName = "CustomObj"
        def objectDataIds = ["data1", "data2"]

        def mockResult = RestResult.newSuccess([
                "data1": new RelationDownstreamResult(downstreamOuterTenantId: 10001, relationOwnerOuterUid: 20001),
                "data2": new RelationDownstreamResult(downstreamOuterTenantId: 10002, relationOwnerOuterUid: 20002)
        ])

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getRelationDownstreamInfo(tenantId, objectApiName, objectDataIds)

        then:
        // 结果验证
        1 * mockEnterpriseRelationService.batchGetRelationDownstreamAndOwner(_, _) >> { args ->
            HeaderObj headerObj = args[0]
            BatchGetRelationDownstreamAndOwnerArg arg = args[1]

            assert headerObj.enterpriseId == Integer.parseInt(tenantId)
            assert arg.objectApiName == objectApiName
            assert arg.upstreamTenantId == Integer.parseInt(tenantId)
            assert arg.crmDataIds.containsAll(objectDataIds)

            return mockResult
        }

        result == mockResult.data
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelationDownstreamInfo方法，接口调用失败的情况
     */
    def "getRelationDownstreamInfoErrorTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def partnerIdList = ["partner1", "partner2"] as Set

        def mockResult = RestResult.newError(204, "接口调用失败")

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getRelationDownstreamInfo(tenantId, partnerIdList)

        then:
        // 结果验证 - 失败应返回空Map
        1 * mockEnterpriseRelationService.batchGetRelationDownstreamAndOwner(_, _) >> mockResult
        result.size() == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillOutEnterpriseInfo方法正常场景
     */
    def "fillOutEnterpriseInfoTest"() {
        given:
        // 测试准备
        def user = new User("123456", "78910")

        // 模拟对象描述
        def objectDescribe = Mock(IObjectDescribe)
        def objectDescribeExt = Mock(ObjectDescribeExt)
        ObjectDescribeExt.metaClass.static.of = { IObjectDescribe desc -> objectDescribeExt }
        objectDescribeExt.isPRMEnabled() >> true

        // 模拟对象数据
        def objectData1 = Mock(IObjectData)
        def objectData2 = Mock(IObjectData)
        def objectDataExt1 = Mock(ObjectDataExt)
        def objectDataExt2 = Mock(ObjectDataExt)

        ObjectDataExt.metaClass.static.of = { IObjectData data ->
            if (data == objectData1) return objectDataExt1
            if (data == objectData2) return objectDataExt2
        }

        objectDataExt1.getPartnerId() >> "partner1"
        objectDataExt2.getPartnerId() >> "partner2"

        def dataList = [objectData1, objectData2]

        // 模拟下游关系查询结果
        def relationResult = [
                "partner1": new RelationDownstreamResult(downstreamOuterTenantId: 10001, relationOwnerOuterUid: 20001),
                "partner2": new RelationDownstreamResult(downstreamOuterTenantId: 10002, relationOwnerOuterUid: 20002)
        ]

        when:
        // 执行被测试方法
        enterpriseRelationService.fillOutEnterpriseInfo(user, dataList, objectDescribe)

        then:
        // 结果验证
        1 * enterpriseRelationService.getRelationDownstreamInfo(user.tenantId, ["partner1", "partner2"] as Set) >> relationResult

        // 验证设置外部租户和外部负责人
        1 * objectDataExt1.setOutTenantId("10001")
        1 * objectDataExt1.setOutOwner(["20001"])
        1 * objectDataExt1.synchronizeOutTeamMemberOwner("10001", "20001")

        1 * objectDataExt2.setOutTenantId("10002")
        1 * objectDataExt2.setOutOwner(["20002"])
        1 * objectDataExt2.synchronizeOutTeamMemberOwner("10002", "20002")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillOutEnterpriseInfo方法边界场景，不满足PRM启用条件
     */
    def "fillOutEnterpriseInfoWithDisabledPRMTest"() {
        given:
        // 测试准备
        def user = new User("123456", "78910")

        // 模拟对象描述
        def objectDescribe = Mock(IObjectDescribe)
        def objectDescribeExt = Mock(ObjectDescribeExt)
        ObjectDescribeExt.metaClass.static.of = { IObjectDescribe desc -> objectDescribeExt }
        objectDescribeExt.isPRMEnabled() >> false

        def dataList = [Mock(IObjectData)]

        when:
        // 执行被测试方法
        enterpriseRelationService.fillOutEnterpriseInfo(user, dataList, objectDescribe)

        then:
        // 结果验证 - PRM未启用，不应进行后续操作
        0 * enterpriseRelationService.getRelationDownstreamInfo(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUpstreamMapperObjectId方法
     */
    def "getUpstreamMapperObjectIdTest"() {
        given:
        // 测试准备
        def outUser = new User("123456", "78910")
        outUser.outTenantId = "10001"
        outUser.outUserId = "20001"
        def apiName = "CustomObj"
        def ea = "test-ea"

        def mockResult = new RestResult<String>()
        mockResult.data = "mapped-object-id"

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getUpstreamMapperObjectId(outUser, apiName)

        then:
        // 结果验证
        1 * mockGdsHandler.getEAByEI(outUser.tenantId) >> ea
        1 * mockEnterpriseRelationService.getMapperObjectId(_, _) >> { args ->
            HeaderObj headerObj = args[0]
            UpstreamAndDownstreamOuterTenantIdOutArg arg = args[1]

            assert headerObj.enterpriseId == Integer.parseInt(outUser.tenantId)
            assert headerObj.outerTenantId == Long.parseLong(outUser.outTenantId)
            assert headerObj.outerUserId == Long.parseLong(outUser.outUserId)
            assert arg.downstreamOuterTenantId == Long.parseLong(outUser.outTenantId)
            assert arg.objectApiName == apiName
            assert arg.upstreamEa == ea

            return mockResult
        }

        result == mockResult.data
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMapperObjectOwnerId方法正常场景
     */
    def "getMapperObjectOwnerIdTest"() {
        given:
        // 测试准备
        def user = new User("123456", "78910")
        user.outUser = true
        user.outTenantId = "10001"
        user.outUserId = "20001"
        def appId = "test-app"

        def mockResult = new RestResult<Integer>()
        mockResult.success = true
        mockResult.data = 30001

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getMapperObjectOwnerId(user, appId)

        then:
        // 结果验证
        1 * mockEnterpriseRelationService.getMapperObjectOwnerId(_, _) >> { args ->
            HeaderObj headerObj = args[0]
            GetMapperObjectOwnerIdArg arg = args[1]

            assert headerObj.enterpriseId == Integer.parseInt(user.tenantId)
            assert headerObj.outerTenantId == Long.parseLong(user.outTenantId)
            assert headerObj.outerUserId == Long.parseLong(user.outUserId)
            assert arg.fsAppId == appId
            assert arg.downstreamOuterTenantId == Long.parseLong(user.outTenantId)
            assert arg.upstreamTenantId == Integer.parseInt(user.tenantId)

            return mockResult
        }

        result == "30001"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMapperObjectOwnerId方法边界场景，用户不是外部用户
     */
    def "getMapperObjectOwnerIdWithNonOutUserTest"() {
        given:
        // 测试准备
        def user = new User("123456", "78910")
        user.outUser = false
        def appId = "test-app"

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getMapperObjectOwnerId(user, appId)

        then:
        // 结果验证 - 非外部用户应返回null
        0 * mockEnterpriseRelationService.getMapperObjectOwnerId(_, _)
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwnerByUser方法，在灰度名单中且是外部用户
     */
    def "getDefaultDataOwnerByUserTest"() {
        given:
        // 测试准备
        def user = new User("123456", "78910")
        user.outUser = true
        def mockOwner = "30001"

        UdobjGrayConfig.metaClass.static.isAllow = { key, tenantId ->
            if (key == UdobjGrayConfigKey.OUT_USER_CREATE_DATA_OWNER_ID_CALC_EI && tenantId == user.tenantId) {
                return true
            }
            return false
        }

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getDefaultDataOwnerByUser(user)

        then:
        // 结果验证
        1 * enterpriseRelationService.getDefaultDataOwner(user, null) >> mockOwner
        result.present
        result.get() == mockOwner
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultDataOwnerByUser方法，非外部用户的情况
     */
    def "getDefaultDataOwnerByUserWithNonOutUserTest"() {
        given:
        // 测试准备
        def user = new User("123456", "78910")
        user.outUser = false

        when:
        // 执行被测试方法
        def result = enterpriseRelationService.getDefaultDataOwnerByUser(user)

        then:
        // 结果验证 - 非外部用户应返回空Optional
        0 * enterpriseRelationService.getDefaultDataOwner(_, _)
        !result.present
    }
} 