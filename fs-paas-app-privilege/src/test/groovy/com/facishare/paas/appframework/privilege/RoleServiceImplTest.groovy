package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.privilege.model.role.Role
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by luxin on 2018/5/28.
 */

//@ContextConfiguration(value = "classpath:user-defined-button-test.xml")
class RoleServiceImplTest extends Specification {

  static {
    System.setProperty("spring.profiles.active", "ceshi113");
  }

  @Autowired
  RoleService roleService


  def "test addPredefinedRole"() {
    given:

    def role = Role.CHANNEL_MANAGER
    def tenantId = "181818"

    when:
    roleService.addPredefinedRole(tenantId, role)
    then:
    1 == 1
  }

  def "test getOuterRoleInfoListByTypes"() {

    given:
    def tenantId = "2";

    when:
    def result = roleService.getOuterRoleInfoList(tenantId, appId)
    then:
    result.size() >= 0

  }


}
