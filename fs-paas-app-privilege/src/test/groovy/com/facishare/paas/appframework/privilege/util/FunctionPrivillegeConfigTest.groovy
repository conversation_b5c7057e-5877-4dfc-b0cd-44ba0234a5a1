package com.facishare.paas.appframework.privilege.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试FunctionPrivillegeConfig类的方法
 */
@Unroll
class FunctionPrivillegeConfigTest extends Specification {

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openGrayOld_master_detail_obj_api_names_enterpriseids方法
     */
    def "openGrayOld_master_detail_obj_api_names_enterpriseidsTest"() {
        given:
        // 测试准备 - 设置测试用的静态字段oldMasterDetailObjApiNamesEnterpriseIdSet
        def originalSet = Whitebox.getField(FunctionPrivillegeConfig.class, "oldMasterDetailObjApiNamesEnterpriseIdSet").get(null)
        def testSet = new HashSet<String>()
        testSet.addAll(setElements)
        Whitebox.setInternalState(FunctionPrivillegeConfig.class, "oldMasterDetailObjApiNamesEnterpriseIdSet", testSet)

        when:
        // 执行被测试方法
        def result = FunctionPrivillegeConfig.openGrayOld_master_detail_obj_api_names_enterpriseids(enterpriseId)

        then:
        // 结果验证
        result == expected

        cleanup:
        // 恢复原始值
        Whitebox.setInternalState(FunctionPrivillegeConfig.class, "oldMasterDetailObjApiNamesEnterpriseIdSet", originalSet)

        where:
        // 测试数据
        setElements    | enterpriseId || expected
        []             | "123"        || false
        ["123", "456"] | "123"        || true
        ["123", "456"] | "789"        || false
        ["*"]          | "any"        || true
        ["*", "123"]   | "456"        || true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openSlaveUseMasterFunction方法
     */
    def "openSlaveUseMasterFunctionTest"() {
        given:
        // 测试准备 - 设置测试用的静态字段slaveUseMasterFunctionEnterpriseIdSet
        def originalSet = Whitebox.getField(FunctionPrivillegeConfig.class, "slaveUseMasterFunctionEnterpriseIdSet").get(null)
        def testSet = new HashSet<String>()
        testSet.addAll(setElements)
        Whitebox.setInternalState(FunctionPrivillegeConfig.class, "slaveUseMasterFunctionEnterpriseIdSet", testSet)

        when:
        // 执行被测试方法
        def result = FunctionPrivillegeConfig.openSlaveUseMasterFunction(enterpriseId)

        then:
        // 结果验证
        result == expected

        cleanup:
        // 恢复原始值
        Whitebox.setInternalState(FunctionPrivillegeConfig.class, "slaveUseMasterFunctionEnterpriseIdSet", originalSet)

        where:
        // 测试数据
        setElements    | enterpriseId || expected
        []             | "123"        || false
        ["123", "456"] | "123"        || true
        ["123", "456"] | "789"        || false
        ["*"]          | "any"        || true
        ["*", "123"]   | "456"        || true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openPersonnelRoleEditableFunctionCodesGray方法
     */
    def "openPersonnelRoleEditableFunctionCodesGrayTest"() {
        given:
        // 测试准备 - 设置测试用的静态字段personnelRoleEditableFunctionCodesGrayEnterpriseIds
        def originalSet = Whitebox.getField(FunctionPrivillegeConfig.class, "personnelRoleEditableFunctionCodesGrayEnterpriseIds").get(null)
        def testSet = new HashSet<String>()
        testSet.addAll(setElements)
        Whitebox.setInternalState(FunctionPrivillegeConfig.class, "personnelRoleEditableFunctionCodesGrayEnterpriseIds", testSet)

        when:
        // 执行被测试方法
        def result = FunctionPrivillegeConfig.openPersonnelRoleEditableFunctionCodesGray(enterpriseId)

        then:
        // 结果验证
        result == expected

        cleanup:
        // 恢复原始值
        Whitebox.setInternalState(FunctionPrivillegeConfig.class, "personnelRoleEditableFunctionCodesGrayEnterpriseIds", originalSet)

        where:
        // 测试数据
        setElements    | enterpriseId || expected
        []             | "123"        || false
        ["123", "456"] | "123"        || true
        ["123", "456"] | "789"        || false
        ["*"]          | "any"        || true
        ["*", "123"]   | "456"        || true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getter方法，确保它们正常工作
     */
    def "getterMethodsTest"() {
        when:
        // 执行被测试方法
        def slaveNoUseFunctionCodes = FunctionPrivillegeConfig.getSlaveNoUseFunctionCodes()
        def personnelRoleEditableFunctionCodes = FunctionPrivillegeConfig.getPersonnelRoleEditableFunctionCodes()
        def slaveMap = FunctionPrivillegeConfig.getSlaveMap()
        def crmUserListGrayEnterpriseIds = FunctionPrivillegeConfig.getCrmUserListGrayEnterpriseIds()

        then:
        // 结果验证
        slaveNoUseFunctionCodes != null
        personnelRoleEditableFunctionCodes != null
        slaveMap != null
        // crmUserListGrayEnterpriseIds可能为null，所以不做断言
    }
} 