package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.core.exception.PermissionError
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.privilege.util.AuthContextExt
import com.facishare.paas.auth.common.exception.AuthException
import com.facishare.paas.auth.model.AuthContext
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.paas.auth.factory.FieldClient
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试FieldPrivilegeServiceImpl类的方法
 */
@Unroll
class FieldPrivilegeServiceImplTest extends Specification {
    
    FieldPrivilegeServiceImpl fieldPrivilegeService
    FieldClient fieldClient
    
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        fieldPrivilegeService = new FieldPrivilegeServiceImpl()
        fieldClient = Mock(FieldClient)
        fieldPrivilegeService.fieldClient = fieldClient
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试listUserEntitiesFiledPrivilege方法正常场景
     */
    def "listUserEntitiesFiledPrivilegeTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        def apiNames = ["Object1", "Object2"]
        
        def expectedResult = [
            "Object1": ["field1": 1, "field2": 2],
            "Object2": ["field3": 1, "field4": 0]
        ]
        
        when:
        // 执行被测试方法
        def result = fieldPrivilegeService.listUserEntitiesFiledPrivilege(tenantId, userId, apiNames)
        
        then:
        // 结果验证
        1 * fieldClient.userEntityIdsFieldPermission({ AuthContext authContext ->
            authContext.tenantId == tenantId && authContext.userId == userId
        }, { Set<String> apiNameSet ->
            apiNameSet.containsAll(apiNames) && apiNames.containsAll(apiNameSet)
        }) >> expectedResult
        
        result == expectedResult
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试listUserEntitiesFiledPrivilege方法异常场景
     */
    def "listUserEntitiesFiledPrivilegeErrorTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        def apiNames = ["Object1", "Object2"]
        
        // 模拟权限异常
        AuthException authException = new AuthException(401, "权限不足")
        
        when:
        // 执行被测试方法
        fieldPrivilegeService.listUserEntitiesFiledPrivilege(tenantId, userId, apiNames)
        
        then:
        // 异常验证
        1 * fieldClient.userEntityIdsFieldPermission(_, _) >> { throw authException }
        
        def exception = thrown(PermissionError)
        exception.getErrorCode() == authException.getCode()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserFieldPrivilege方法正常场景，使用User参数版本
     */
    def "getUserFieldPrivilegeWithUserTest"() {
        given:
        // 测试准备
        def user = User.systemUser("123456")
        def apiName = "Object1"
        
        def expectedResult = ["field1": 1, "field2": 2, "field3": 0]
        
        when:
        // 执行被测试方法
        def result = fieldPrivilegeService.getUserFieldPrivilege(user, apiName)
        
        then:
        // 结果验证
        1 * fieldClient.userFieldPermission({ AuthContext authContext ->
            authContext.tenantId == user.tenantId && authContext.userId == user.userId
        }, apiName) >> expectedResult
        
        result == expectedResult
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserFieldPrivilege方法正常场景，使用tenantId和userId参数版本
     */
    def "getUserFieldPrivilegeWithParamsTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        def apiName = "Object1"
        
        def expectedResult = ["field1": 1, "field2": 2, "field3": 0]
        
        when:
        // 执行被测试方法
        def result = fieldPrivilegeService.getUserFieldPrivilege(tenantId, userId, apiName)
        
        then:
        // 结果验证
        1 * fieldClient.userFieldPermission({ AuthContext authContext ->
            authContext.tenantId == tenantId && authContext.userId == userId
        }, apiName) >> expectedResult
        
        result == expectedResult
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserFieldPrivilege方法异常场景
     */
    def "getUserFieldPrivilegeErrorTest"() {
        given:
        // 测试准备
        def user = User.systemUser("123456")
        def apiName = "Object1"
        
        // 模拟权限异常
        AuthException authException = new AuthException(401, "权限不足")
        
        when:
        // 执行被测试方法
        fieldPrivilegeService.getUserFieldPrivilege(user, apiName)
        
        then:
        // 异常验证
        1 * fieldClient.userFieldPermission(_, _) >> { throw authException }
        
        def exception = thrown(PermissionError)
        exception.getErrorCode() == authException.getCode()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserNoExportFieldPrivilege方法正常场景
     */
    def "getUserNoExportFieldPrivilegeTest"() {
        given:
        // 测试准备
        def user = User.systemUser("123456")
        def objectApiNames = ["Object1", "Object2"] as Set
        
        def expectedResult = [
            "Object1": ["field1", "field2"] as Set,
            "Object2": ["field3"] as Set
        ]
        
        when:
        // 执行被测试方法
        def result = fieldPrivilegeService.getUserNoExportFieldPrivilege(user, objectApiNames)
        
        then:
        // 结果验证
        1 * fieldClient.notSupportExportFields({ AuthContext authContext ->
            authContext.tenantId == user.tenantId && authContext.userId == user.userId
        }, { List<String> apiNames ->
            apiNames.toSet() == objectApiNames
        }) >> expectedResult
        
        result == expectedResult
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserNoExportFieldPrivilege方法异常场景
     */
    def "getUserNoExportFieldPrivilegeErrorTest"() {
        given:
        // 测试准备
        def user = User.systemUser("123456")
        def objectApiNames = ["Object1", "Object2"] as Set
        
        // 模拟权限异常
        AuthException authException = new AuthException(401, "权限不足")
        
        when:
        // 执行被测试方法
        fieldPrivilegeService.getUserNoExportFieldPrivilege(user, objectApiNames)
        
        then:
        // 异常验证
        1 * fieldClient.notSupportExportFields(_, _) >> { throw authException }
        
        def exception = thrown(PermissionError)
        exception.getErrorCode() == authException.getCode()
    }
}