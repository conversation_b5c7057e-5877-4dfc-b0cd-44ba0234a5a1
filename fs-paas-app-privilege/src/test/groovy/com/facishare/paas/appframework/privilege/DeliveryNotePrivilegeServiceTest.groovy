package com.facishare.paas.appframework.privilege


import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试DeliveryNotePrivilegeService类的方法
 */
@Unroll
class DeliveryNotePrivilegeServiceTest extends Specification {
    
    DeliveryNotePrivilegeService deliveryNotePrivilegeService
    FunctionPrivilegeService functionPrivilegeService
    FunctionPrivilegeProxy functionPrivilegeProxy
    
    def setup() {
        deliveryNotePrivilegeService = new DeliveryNotePrivilegeService()
        functionPrivilegeService = Mock(FunctionPrivilegeService)
        functionPrivilegeProxy = Mock(FunctionPrivilegeProxy)
        
        deliveryNotePrivilegeService.functionPrivilegeService = functionPrivilegeService
        deliveryNotePrivilegeService.privilegeProxy = functionPrivilegeProxy
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法正常场景，成功获取权限并为发货单对象增加权限
     */
    def "deliveryNoteAddFuncAccessTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        
        // 模拟权限结果 - 包含两种权限的角色列表
        def resultMap = [
            (PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE): ["role1", "role2"],
            (PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE): ["role3", "role4"]
        ]
        def result = new FuncCodePrivilege.Result(
            success: true,
            result: resultMap
        )
        
        when:
        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(tenantId, userId)
        
        then:
        // 结果验证
        1 * functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(_, _) >> { args ->
            FuncCodePrivilege.Arg arg = args[0]
            Map<String, String> headers = args[1]
            
            // 验证参数
            assert arg.authContext.tenantId == tenantId
            assert arg.authContext.userId == userId
            assert arg.authContext.appId == PrivilegeConstants.APP_ID
            assert arg.funcCodes.size() == 2
            assert arg.funcCodes.containsAll([PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE, 
                                            PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE])
            
            return result
        }
        
        // 验证第一种权限的调用
        1 * functionPrivilegeService.rolesAddFuncAccess(
            { User user -> user.tenantId == tenantId && user.userId == userId }, 
            "DeliveryNoteObj", 
            "Add", 
            ["role1", "role2"]
        )
        
        // 验证第二种权限的调用
        1 * functionPrivilegeService.rolesAddFuncAccess(
            { User user -> user.tenantId == tenantId && user.userId == userId }, 
            "DeliveryNoteObj", 
            "ConfirmReceipt", 
            ["role3", "role4"]
        )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法异常场景，权限查询失败
     */
    def "deliveryNoteAddFuncAccessErrorTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        
        // 模拟权限查询失败结果
        def result = new FuncCodePrivilege.Result(
            success: false,
            errCode: 100,
            errMessage: "权限查询失败"
        )
        
        when:
        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(tenantId, userId)
        
        then:
        // 结果验证 - 权限调用失败应不执行后续操作
        1 * functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(_, _) >> result
        0 * functionPrivilegeService.rolesAddFuncAccess(_, _, _, _)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法边界场景，角色列表为空
     */
    def "deliveryNoteAddFuncAccessWithEmptyRolesTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        
        // 模拟权限结果 - 包含空角色列表
        def resultMap = [
            (PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE): [],
            (PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE): null
        ]
        def result = new FuncCodePrivilege.Result(
            success: true,
            result: resultMap
        )
        
        when:
        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(tenantId, userId)
        
        then:
        // 结果验证 - 空角色列表应不执行添加权限操作
        1 * functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(_, _) >> result
        0 * functionPrivilegeService.rolesAddFuncAccess(_, _, _, _)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试deliveryNoteAddFuncAccess方法异常场景，权限分配过程中发生异常
     */
    def "deliveryNoteAddFuncAccessWithExceptionTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        def userId = "78910"
        
        // 模拟权限结果
        def resultMap = [
            (PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE): ["role1", "role2"]
        ]
        def result = new FuncCodePrivilege.Result(
            success: true,
            result: resultMap
        )
        
        when:
        // 执行被测试方法
        deliveryNotePrivilegeService.deliveryNoteAddFuncAccess(tenantId, userId)
        
        then:
        // 结果验证 - 模拟分配权限过程中发生异常
        1 * functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(_, _) >> result
        1 * functionPrivilegeService.rolesAddFuncAccess(_, _, _, _) >> { throw new RuntimeException("模拟异常") }
        
        // 方法不应抛出异常
        noExceptionThrown()
    }
}
