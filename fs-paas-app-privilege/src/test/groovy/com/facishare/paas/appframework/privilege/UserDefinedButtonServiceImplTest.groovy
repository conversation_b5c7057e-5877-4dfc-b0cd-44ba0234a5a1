package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.core.model.User
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by luxin on 2018/1/16.
 */
//@ContextConfiguration(value = "classpath:user-defined-button-test.xml")
class UserDefinedButtonServiceImplTest extends Specification {

 // @Autowired
  UserDefinedButtonService userDefinedButtonService

  //@Autowired
      FunctionPrivilegeProxy functionPrivilegeProxy;

  /**
   * 测试自定义按钮权限：查找拥有权限的所有角色
   * @return
   */
  def "GetHavePrivilegeRolesByUserDefinedButton"() {


    given:
    def user = new User("2", "1000");
    UserDefinedButtonService userDefinedButtonService1 = new UserDefinedButtonServiceImpl()

    FunctionPrivilegeProxy proxy = functionPrivilegeProxy

    FunctionPrivilegeService functionPrivilegeService = Mock()
    functionPrivilegeService.createFuncCode(_) >> null
    functionPrivilegeService.rolesAddFuncAccess() >> null

    userDefinedButtonService1.functionPrivilegeService = functionPrivilegeService

    userDefinedButtonService1.functionPrivilegeProxy=proxy

    when:
    //def result = userDefinedButtonService.getHavePrivilegeRolesByUserDefinedButton(user, describeApiName, buttonApiName)

    userDefinedButtonService1.createUserDefinedButton(user,null,null,null,null)

    then:
    1 == 1
    where:
    describeApiName | buttonApiName
    "PriceBookObj"  | "Add"
    "PriceBookObj"  | "Add"
  }

  /**
   * 测试自定义按钮权限：创建权限
   * @return
   */
  def "createUserDefinedButton"() {
    given:
    def user = new User("2", "1000")
    def roles = Lists.asList("00000000000000000000000000000002", "00000000000000000000000000000009")

    when:
    userDefinedButtonService.createUserDefinedButton(user, describeApiName, buttonApiName, buttonName, roles)

    then:
    1 == 1
    where:
    describeApiName | buttonApiName | buttonName
    "hello"         | "world"       | "123"

  }

  /**
   * 测试自定义按钮权限：更新权限
   * @return
   */
  def "updateUserDefinedButtonPrivilegeRoles"() {
    given:
    def user = new User("55732", "1000")
    def addRoles = Lists.asList("00000000000000000000000000000015")
    def delRoles = Lists.asList("00000000000000000000000000000002", "00000000000000000000000000000009")

    when:
    def result = userDefinedButtonService.updateFuncButtonName(user, describeApiName, buttonApiName,buttonName)

    then:
    true == true
    where:
    describeApiName | buttonApiName |buttonName
    "CustomerAccountObj"         | "button_OJIOh__c"|"nihenhao"

  }

  def "deleteUserDefinedButton"() {
    given:
    def user = new User("2", "1000")
    when:
    def result = userDefinedButtonService.deleteUserDefinedButton(user, describeApiName, buttonApiName)

    then:
    result == true
    where:
    describeApiName | buttonApiName
    "hello"         | "world"
  }

}
