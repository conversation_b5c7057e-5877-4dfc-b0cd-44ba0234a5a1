package com.facishare.paas.appframework.privilege

import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.ManageGroupService
import com.facishare.paas.appframework.common.service.model.ManageGroup
import com.facishare.paas.appframework.common.service.model.ManageGroupType
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.facishare.paas.appframework.privilege.dto.*
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试DepartmentDataRightsLogicServiceImpl类的方法
 */
@Unroll
class DepartmentDataRightsLogicServiceImplTest extends Specification {

    DepartmentDataRightsLogicServiceImpl departmentDataRightsLogicService
    DepartmentDataRightsProxy rightsProxy
    FunctionPrivilegeService functionPrivilegeService
    DataPrivilegeCommonService dataPrivilegeCommonService
    ManageGroupService manageGroupService

    def setup() {
        departmentDataRightsLogicService = new DepartmentDataRightsLogicServiceImpl()

        rightsProxy = Mock(DepartmentDataRightsProxy)
        functionPrivilegeService = Mock(FunctionPrivilegeService)
        dataPrivilegeCommonService = Mock(DataPrivilegeCommonService)
        manageGroupService = Mock(ManageGroupService)

        departmentDataRightsLogicService.rightsProxy = rightsProxy
        departmentDataRightsLogicService.functionPrivilegeService = functionPrivilegeService
        departmentDataRightsLogicService.dataPrivilegeCommonService = dataPrivilegeCommonService
        departmentDataRightsLogicService.manageGroupService = manageGroupService
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试upsert方法正常场景
     */
    def "upsertTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def objectAPINames = ["Object1", "Object2"]
        def departmentIds = ["Dept1", "Dept2"]
        def scene = 1
        def type = 0

        // 预期的笛卡尔积结果
        def expectedRightsList = [
                DepartmentDataRightsUpsert.DepartmentRights.builder()
                        .status(0) // ENABLE_STATUS
                        .type(type)
                        .entityId("Object1")
                        .deptId("Dept1")
                        .scene(scene)
                        .build(),
                DepartmentDataRightsUpsert.DepartmentRights.builder()
                        .status(0) // ENABLE_STATUS
                        .type(type)
                        .entityId("Object1")
                        .deptId("Dept2")
                        .scene(scene)
                        .build(),
                DepartmentDataRightsUpsert.DepartmentRights.builder()
                        .status(0) // ENABLE_STATUS
                        .type(type)
                        .entityId("Object2")
                        .deptId("Dept1")
                        .scene(scene)
                        .build(),
                DepartmentDataRightsUpsert.DepartmentRights.builder()
                        .status(0) // ENABLE_STATUS
                        .type(type)
                        .entityId("Object2")
                        .deptId("Dept2")
                        .scene(scene)
                        .build()
        ]

        when:
        // 执行被测试方法
        departmentDataRightsLogicService.upsert(user, objectAPINames, departmentIds, scene, type)

        then:
        // 结果验证
        1 * rightsProxy.upsert(_, _) >> { args ->
            // 验证参数
            Map<String, String> headers = args[0]
            DepartmentDataRightsUpsert.Arg arg = args[1]

            assert headers != null
            assert arg.context.tenantId == user.tenantId
            assert arg.context.userId == user.userId

            // 验证笛卡尔积结果 (由于对象比较，简化验证逻辑)
            assert arg.deptRights.size() == 4
            assert arg.deptRights.every { rights ->
                rights.status == 0 &&
                        objectAPINames.contains(rights.entityId) &&
                        departmentIds.contains(rights.deptId) &&
                        rights.scene == scene &&
                        rights.type == type
            }

            return null
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试upsert方法边界场景，参数为空的情况
     */
    def "upsertTestWithEmptyParams"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def scene = 1
        def type = 0

        when:
        // 执行被测试方法
        departmentDataRightsLogicService.upsert(user, objectAPINames, departmentIds, scene, type)

        then:
        // 结果验证 - 不应该进行任何RPC调用
        0 * rightsProxy.upsert(_, _)

        where:
        objectAPINames | departmentIds
        []             | ["Dept1", "Dept2"]
        null           | ["Dept1", "Dept2"]
        ["Object1"]    | []
        ["Object1"]    | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试query方法，测试分页查询功能
     */
    def "queryTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def objectAPINames = ["Object1", "Object2"]
        def departmentIds = ["Dept1", "Dept2"]
        def scene = 1
        def page = 1
        def size = 20

        def mockRightsList = [
                new DepartmentDataRights(id: "1", entityId: "Object1", deptId: "Dept1"),
                new DepartmentDataRights(id: "2", entityId: "Object1", deptId: "Dept2")
        ]

        def mockPageInfo = new PageInfo(
                currentPage: 1,
                pageSize: 20,
                totalPage: 1,
                total: 2
        )

        def mockQueryResult = new DepartmentDataRightsQueryAll.Result(
                pageInfo: mockPageInfo,
                deptRights: mockRightsList
        )

        // mock权限检查
        dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.tenantId) >> false

        when:
        // 执行被测试方法
        def result = departmentDataRightsLogicService.query(
                user, objectAPINames, departmentIds, scene, page, size)

        then:
        // 结果验证
        1 * rightsProxy.queryAll(_, _) >> { args ->
            // 验证参数
            Map<String, String> headers = args[0]
            DepartmentDataRightsQueryAll.Arg arg = args[1]

            assert headers != null
            assert arg.context.tenantId == user.tenantId
            assert arg.context.userId == user.userId
            assert arg.entityIds == objectAPINames
            assert arg.deptIds == departmentIds
            assert arg.scene == scene
            assert arg.currentPage == page
            assert arg.size == size

            return mockQueryResult
        }

    }

    /**
     * GenerateByAI
     * 测试内容描述：测试query方法，当启用了数据查询验证，且用户有管理组权限
     */
    def "queryTestWithDataQueryValidation"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def objectAPINames = ["Object1", "Object2"]
        def departmentIds = ["Dept1", "Dept2"]
        def scene = 1
        def page = 1
        def size = 20

        def mockRightsList = [
                new DepartmentDataRights(id: "1", entityId: "Object1", deptId: "Dept1"),
                new DepartmentDataRights(id: "2", entityId: "Object1", deptId: "Dept2")
        ]

        def mockPageInfo = new PageInfo(
                currentPage: 1,
                pageSize: 20,
                totalPage: 1,
                total: 2
        )

        def mockQueryResult = new DepartmentDataRightsQueryAll.Result(
                pageInfo: mockPageInfo,
                deptRights: mockRightsList
        )

        // 模拟权限验证
        dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.tenantId) >> true

        def manageGroup = new ManageGroup(
                false,
                ManageGroupType.OBJECT,
                "AccountObj",
                Sets.newHashSet(["AccountObj"])
        )
        manageGroupService.queryManageGroup(user, null, ManageGroupType.OBJECT, true) >> manageGroup

        dataPrivilegeCommonService.checkEntityIdsByManageGroup(objectAPINames, manageGroup) >> ["Object1"]

        def deptAndOrgIds = Sets.newHashSet(["Dept1"])
        functionPrivilegeService.queryDeptAndOrgIdsByScope(user) >> deptAndOrgIds

        dataPrivilegeCommonService.checkDeptAndOrgIdsByManageScope(departmentIds, deptAndOrgIds) >> Sets.newHashSet(["Dept1"])

        when:
        // 执行被测试方法
        Tuple<PageInfo, List<DepartmentDataRights>> result = departmentDataRightsLogicService.query(
                user, objectAPINames, departmentIds, scene, page, size)

        then:
        // 结果验证
        1 * rightsProxy.queryAll(_, _) >> { args ->
            // 验证过滤后的参数
            DepartmentDataRightsQueryAll.Arg arg = args[1]

            assert arg.entityIds == ["Object1"]
            assert arg.deptIds == ["Dept1"]

            return mockQueryResult
        }

        result.getKey() == mockPageInfo
        result.getValue() == mockRightsList
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByIds方法
     */
    def "findByIdsTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def scene = 1
        def ids = ["id1", "id2"]

        def mockRightsList = [
                new DepartmentDataRights(id: "id1", entityId: "Object1", deptId: "Dept1"),
                new DepartmentDataRights(id: "id2", entityId: "Object1", deptId: "Dept2")
        ]

        when:
        // 执行被测试方法
        def result = departmentDataRightsLogicService.findByIds(user, scene, ids)

        then:
        // 结果验证
        1 * rightsProxy.queryByIds(_, _) >> { args ->
            // 验证参数
            Map<String, String> headers = args[0]
            DepartmentDataRightsQueryByIds.Arg arg = args[1]

            assert headers != null
            assert arg.context.tenantId == user.tenantId
            assert arg.context.userId == user.userId
            assert arg.ids == ids
            assert arg.scene == scene

            return mockRightsList
        }

        result == mockRightsList
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disableByIds方法
     */
    def "disableByIdsTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def scene = 1
        def ids = ["id1", "id2"]

        def mockRightsList = [
                new DepartmentDataRights(id: "id1", entityId: "Object1", deptId: "Dept1", type: 0),
                new DepartmentDataRights(id: "id2", entityId: "Object1", deptId: "Dept2", type: 1)
        ]

        when:
        // 执行被测试方法
        departmentDataRightsLogicService.disableByIds(user, scene, ids)

        then:
        // 结果验证
        1 * rightsProxy.queryByIds(_, _) >> mockRightsList

        1 * rightsProxy.upsert(_, _) >> { args ->
            // 验证参数
            DepartmentDataRightsUpsert.Arg arg = args[1]

            assert arg.deptRights.size() == 2
            assert arg.deptRights.every { it.status == 1 } // DISABLE_STATUS
            assert arg.deptRights[0].entityId == "Object1"
            assert arg.deptRights[0].deptId == "Dept1"
            assert arg.deptRights[0].type == 0
            assert arg.deptRights[1].entityId == "Object1"
            assert arg.deptRights[1].deptId == "Dept2"
            assert arg.deptRights[1].type == 1

            return null
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteByIds方法正常场景
     */
    def "deleteByIdsTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def scene = 1
        def ids = ["id1", "id2"]

        def mockRightsList = [
                new DepartmentDataRights(id: "id1", entityId: "Object1", deptId: "Dept1", status: 1), // DISABLE_STATUS
                new DepartmentDataRights(id: "id2", entityId: "Object2", deptId: "Dept2", status: 1)  // DISABLE_STATUS
        ]

        when:
        // 执行被测试方法
        departmentDataRightsLogicService.deleteByIds(user, scene, ids)

        then:
        // 结果验证
        1 * rightsProxy.queryByIds(_, _) >> mockRightsList

        1 * rightsProxy.delete(_, _) >> { args ->
            // 验证参数
            DepartmentDataRightsDelete.Arg arg = args[1]

            assert arg.scene == scene
            assert arg.datas.size() == 2
            assert arg.datas["Object1"] == ["id1"]
            assert arg.datas["Object2"] == ["id2"]

            return null
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteByIds方法异常场景，包含启用状态的权限
     */
    def "deleteByIdsErrorWithEnabledRights"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def scene = 1
        def ids = ["id1", "id2"]

        def mockRightsList = [
                new DepartmentDataRights(id: "id1", entityId: "Object1", deptId: "Dept1", status: 1), // DISABLE_STATUS
                new DepartmentDataRights(id: "id2", entityId: "Object2", deptId: "Dept2", status: 0)  // ENABLE_STATUS
        ]

        when:
        // 执行被测试方法
        departmentDataRightsLogicService.deleteByIds(user, scene, ids)

        then:
        // 异常验证
        1 * rightsProxy.queryByIds(_, _) >> mockRightsList

        def exception = thrown(ValidateException)
        0 * rightsProxy.delete(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enable方法
     */
    def "enableTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        def scene = 1
        def ids = ["id1", "id2"]

        def mockRightsList = [
                new DepartmentDataRights(id: "id1", entityId: "Object1", deptId: "Dept1", type: 0),
                new DepartmentDataRights(id: "id2", entityId: "Object2", deptId: "Dept2", type: 1)
        ]

        when:
        // 执行被测试方法
        departmentDataRightsLogicService.enable(user, scene, ids)

        then:
        // 结果验证
        1 * rightsProxy.queryByIds(_, _) >> mockRightsList

        1 * rightsProxy.upsert(_, _) >> { args ->
            // 验证参数
            DepartmentDataRightsUpsert.Arg arg = args[1]

            assert arg.deptRights.size() == 2
            assert arg.deptRights.every { it.status == 0 } // ENABLE_STATUS
            assert arg.deptRights[0].entityId == "Object1"
            assert arg.deptRights[0].deptId == "Dept1"
            assert arg.deptRights[0].type == 0
            assert arg.deptRights[1].entityId == "Object2"
            assert arg.deptRights[1].deptId == "Dept2"
            assert arg.deptRights[1].type == 1

            return null
        }
    }
} 