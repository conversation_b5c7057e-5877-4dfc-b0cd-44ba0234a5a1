package com.facishare.paas.appframework.privilege

import com.facishare.crm.valueobject.SessionContext
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments
import com.facishare.organization.adapter.api.permission.service.PermissionService
import com.facishare.organization.api.model.RunStatus
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult
import com.facishare.organization.api.service.EmployeeProviderService
import com.facishare.paas.appframework.privilege.dto.JudgeManageRangeInfo
import com.facishare.paas.appframework.privilege.dto.JudgeMangePrivilegeResult
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

import static com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum.PERMISSION_BUSINESS_ROLE_QUERY

/**
 * GenerateByAI
 * 测试内容描述：测试JudgeMangePrivilegeServiceImpl类的方法
 */
@Unroll
class JudgeMangePrivilegeServiceImplTest extends Specification {
    
    JudgeMangePrivilegeServiceImpl judgeMangePrivilegeService
    EmployeeProviderService employeeProviderService
    PermissionService permissionService
    
    def setup() {
        judgeMangePrivilegeService = new JudgeMangePrivilegeServiceImpl()
        
        employeeProviderService = Mock(EmployeeProviderService)
        permissionService = Mock(PermissionService)
        
        judgeMangePrivilegeService.employeeProviderService = employeeProviderService
        judgeMangePrivilegeService.permissionService = permissionService
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilege方法正常场景，有管理权限
     */
    def "judgeMangePrivilegeTestWithPermission"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        // 模拟有管理权限，管理全公司
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = true
        checkResult.isManageWholeCompany = manageWholeCompany
        checkResult.departmentIds = departmentIds
        
        // 模拟员工列表查询
        def employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result()
        employeeResult.employeeIds = employeeIds
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.judgeMangePrivilege(sessionContext)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> { args ->
            CheckFunctionCodeAndGetManageDepartments.Argument arg = args[0]
            
            assert arg.functionCode == PERMISSION_BUSINESS_ROLE_QUERY.functionCode
            assert arg.appId == "facishare-system"
            assert arg.enterpriseId == sessionContext.EId.intValue()
            assert arg.currentEmployeeId == sessionContext.userId
            assert arg.employeeId == sessionContext.userId
            
            return checkResult
        }
        
        if (departmentIds) {
            1 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_) >> { args ->
                BatchGetEmployeeIdsByDepartmentId.Arg arg = args[0]
                
                assert arg.enterpriseId == sessionContext.EId.intValue()
                assert arg.departmentIds == departmentIds
                assert arg.runStatus == RunStatus.ACTIVE
                assert arg.includeLowDepartment
                
                return employeeResult
            }
        } else {
            0 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_)
        }
        
        // 验证结果
        result.havePrivilege
        result.manageWhole == manageWholeCompany
        result.deptIds == departmentIds
        result.employees == expectedEmployees
        
        where:
        // 测试数据
        manageWholeCompany | departmentIds     | employeeIds     | expectedEmployees
        true               | [99999]           | []              | []
        false              | [1001, 1002]      | [2001, 2002]    | ["2001", "2002"]
        false              | []                | []              | []
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilege方法异常场景，没有管理权限
     */
    def "judgeMangePrivilegeTestWithoutPermission"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        // 模拟没有管理权限
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = false
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.judgeMangePrivilege(sessionContext)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> checkResult
        0 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_)
        
        // 验证结果
        !result.havePrivilege
        result.deptIds == null
        result.employees == null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilegeGray方法，管理全公司的情况
     */
    def "judgeMangePrivilegeGrayTestWithManageWhole"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        // 模拟有管理权限，管理全公司
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = true
        checkResult.isManageWholeCompany = true
        checkResult.departmentIds = [99999]
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.judgeMangePrivilegeGray(sessionContext)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> checkResult
        0 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_)
        
        // 验证结果
        result.havePrivilege
        result.manageWhole
        result.deptIds == [99999]
        result.employees == null  // 管理全公司时不需设置员工列表
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试judgeMangePrivilegeGray方法，管理部分部门的情况
     */
    def "judgeMangePrivilegeGrayTestWithDepartments"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        // 模拟有管理权限，管理部分部门
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = true
        checkResult.isManageWholeCompany = false
        checkResult.departmentIds = [1001, 1002]
        
        // 模拟员工列表查询
        def employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result()
        employeeResult.employeeIds = [2001, 2002]
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.judgeMangePrivilegeGray(sessionContext)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> checkResult
        1 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_) >> employeeResult
        
        // 验证结果
        result.havePrivilege
        !result.manageWhole
        result.deptIds == [1001, 1002]
        result.employees == ["2001", "2002"]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getEmployeeByDepartment方法正常场景
     */
    def "getEmployeeByDepartmentTest"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        
        def departmentIds = [1001, 1002]
        
        // 模拟员工列表查询
        def employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result()
        employeeResult.employeeIds = [2001, 2002]
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.getEmployeeByDepartment(departmentIds, sessionContext)
        
        then:
        // 结果验证
        1 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_) >> { args ->
            BatchGetEmployeeIdsByDepartmentId.Arg arg = args[0]
            
            assert arg.enterpriseId == sessionContext.EId.intValue()
            assert arg.departmentIds == departmentIds
            assert arg.runStatus == RunStatus.ACTIVE
            assert arg.includeLowDepartment
            
            return employeeResult
        }
        
        result == ["2001", "2002"]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getEmployeeByDepartment方法边界场景，部门列表为空
     */
    def "getEmployeeByDepartmentTestWithEmptyDepartments"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        def departmentIds = []
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.getEmployeeByDepartment(departmentIds, sessionContext)
        
        then:
        // 结果验证 - 部门为空不应调用员工查询接口
        0 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_)
        result.size() == 0
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试checkTargetFunction方法
     */
    def "checkTargetFunctionTest"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        def targetFunction = "test.function.code"
        
        // 模拟权限检查结果
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = true
        checkResult.isManageWholeCompany = false
        checkResult.departmentIds = [1001, 1002]
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.checkTargetFunction(sessionContext, targetFunction)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> { args ->
            CheckFunctionCodeAndGetManageDepartments.Argument arg = args[0]
            
            assert arg.functionCode == targetFunction
            assert arg.appId == "facishare-system"
            assert arg.enterpriseId == sessionContext.EId.intValue()
            assert arg.currentEmployeeId == sessionContext.userId
            assert arg.employeeId == sessionContext.userId
            
            return checkResult
        }
        
        result == checkResult
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserMangeRangeInfo方法，管理全公司的情况
     */
    def "getUserMangeRangeInfoTestWithManageWhole"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        def targetFunction = "test.function.code"
        
        // 模拟权限检查结果 - 管理全公司
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = true
        checkResult.isManageWholeCompany = true
        
        // 模拟全部员工查询
        def allEmployeeResult = new GetAllEmployeeIdsResult()
        allEmployeeResult.employeeIds = [1001, 1002, 1003]
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.getUserMangeRangeInfo(sessionContext, targetFunction)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> checkResult
        1 * employeeProviderService.getAllEmployeeIds(_) >> { args ->
            GetAllEmployeeIdsArg arg = args[0]
            
            assert arg.enterpriseId == sessionContext.EId.intValue()
            assert arg.runStatus == RunStatus.ACTIVE
            
            return allEmployeeResult
        }
        
        result.hasAbility
        result.manageWholeCompany
        result.manageRangeUserIds == ["1001", "1002", "1003"]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserMangeRangeInfo方法，管理部分部门的情况
     */
    def "getUserMangeRangeInfoTestWithDepartments"() {
        given:
        // 测试准备
        def sessionContext = new SessionContext()
        sessionContext.setEId(12345L)
        sessionContext.setUserId(6789)
        
        def targetFunction = "test.function.code"
        
        // 模拟权限检查结果 - 管理部分部门
        def checkResult = new CheckFunctionCodeAndGetManageDepartments.Result()
        checkResult.hasAbility = true
        checkResult.isManageWholeCompany = false
        checkResult.departmentIds = [1001, 1002]
        
        // 模拟部门员工查询
        def employeeResult = new BatchGetEmployeeIdsByDepartmentId.Result()
        employeeResult.employeeIds = [2001, 2002]
        
        when:
        // 执行被测试方法
        def result = judgeMangePrivilegeService.getUserMangeRangeInfo(sessionContext, targetFunction)
        
        then:
        // 结果验证
        1 * permissionService.checkFunctionCodeAndGetManageDepartments(_) >> checkResult
        1 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_) >> employeeResult
        0 * employeeProviderService.getAllEmployeeIds(_)
        
        result.hasAbility
        !result.manageWholeCompany
        result.manageRangeUserIds == ["2001", "2002"]
    }
} 