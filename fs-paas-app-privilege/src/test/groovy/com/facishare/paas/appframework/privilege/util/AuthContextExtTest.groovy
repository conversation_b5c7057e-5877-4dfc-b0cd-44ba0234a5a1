package com.facishare.paas.appframework.privilege.util

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.auth.model.AuthContext
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试AuthContextExt工具类的方法
 */
@Unroll
class AuthContextExtTest extends Specification {
    
    def originalGetAppIdUserMethod
    def originalGetAppIdStringBooleanMethod
    
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }


    /**
     * GenerateByAI
     * 测试内容描述：测试of方法，使用User参数的版本
     */
    def "ofWithUserTest"() {
        given:
        // 测试准备
        def user = new User(tenantId, userId, outUserId, outTenantId)
        
        // 模拟 RequestUtil.getOutIdentityType()
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .outIdentityType(outIdentityType)
                .appId(appId)
                .build()
        RequestContextManager.setContext(requestContext)
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        
        when:
        // 执行被测试方法
        def result = AuthContextExt.of(user)
        
        then:
        // 结果验证
        result != null
        result.getAuthContext() != null
        result.getAuthContext().getTenantId() == tenantId
        result.getAuthContext().getUserId() == (outUserId ? outUserId : userId)
        result.getAuthContext().getIdentityType() == outIdentityType
        
        where:
        // 测试数据
        tenantId | userId | outUserId | outTenantId | appId        | tenantIdConf | outIdentityType
        "123"    | "1000" | null      | null        | "custom-app" | "74255"                     | null
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | "ALL"                      | "app"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试of方法，使用User和allAppScope参数的版本
     */
    def "ofWithUserAndAllAppScopeTest"() {
        given:
        // 测试准备
        def user = new User(tenantId, userId, outUserId, outTenantId)
        
        // 模拟 RequestUtil.getOutIdentityType()
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .outIdentityType(outIdentityType)
                .appId(appId)
                .build()
        RequestContextManager.setContext(requestContext)
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        

        when:
        // 执行被测试方法
        def result = AuthContextExt.of(user, allAppScope)
        
        then:
        // 结果验证
        result != null
        result.getAuthContext() != null
        result.getAuthContext().getTenantId() == tenantId
        result.getAuthContext().getUserId() == (outUserId ? outUserId : userId)
        result.getAuthContext().getFilterAppId() == !allAppScope
        result.getAuthContext().getIdentityType() == outIdentityType
        
        where:
        // 测试数据
        tenantId | userId | outUserId | outTenantId | appId        | allAppScope | tenantIdConf | outIdentityType
        "123"    | "1000" | null      | null        | "custom-app" | false       | "74255"      | null
        "123"    | "1000" | null      | null        | "custom-app" | true        | "74255"      | null
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | false       | "ALL"        | "app"
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | true        | "ALL"        | "app"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试of方法，使用User、allAppScope和appId参数的版本
     */
    def "ofWithUserAndAllAppScopeAndAppIdTest"() {
        given:
        // 测试准备
        def user = new User(tenantId, userId, outUserId, outTenantId)
        
        // 模拟 RequestUtil.getOutIdentityType()
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .outIdentityType(outIdentityType)
                .appId(userAppId)
                .build()
        RequestContextManager.setContext(requestContext)
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        
        when:
        // 执行被测试方法
        def result = AuthContextExt.of(user, allAppScope, inputAppId)
        
        then:
        // 结果验证
        result != null
        result.getAuthContext() != null
        result.getAuthContext().getTenantId() == tenantId
        result.getAuthContext().getUserId() == (outUserId ? outUserId : userId)
        result.getAuthContext().getFilterAppId() == !allAppScope
        result.getAuthContext().getIdentityType() == outIdentityType
        
        where:
        // 测试数据
        tenantId | userId | outUserId | outTenantId | userAppId    | inputAppId    | allAppScope | tenantIdConf | outIdentityType
        "123"    | "1000" | null      | null        | "custom-app" | "custom-app2" | false       | "74255"      | null
        "123"    | "1000" | null      | null        | "custom-app" | "custom-app2" | true        | "74255"      | null
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | "custom-app2" | false       | "ALL"        | "app"
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | "custom-app2" | true        | "ALL"        | "app"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试buildProperties方法，使用User参数的版本
     */
    def "buildPropertiesWithUserTest"() {
        given:
        // 测试准备
        def user = new User(tenantId, userId, outUserId, outTenantId)
        
        // 模拟 RequestUtil.getAppId()
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .appId(appId)
                .build()
        RequestContextManager.setContext(requestContext)
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        
        when:
        // 执行被测试方法
        def result = AuthContextExt.buildProperties(user)
        
        then:
        // 结果验证
        if (tenantIdConf == "ALL" && outUserId != null) {
            assert result != null
            assert result["bizAppId"] == appId
        } else {
            assert result == null
        }
        
        where:
        // 测试数据
        tenantId | userId | outUserId | outTenantId | appId        | tenantIdConf
        "123"    | "1000" | null      | null        | "custom-app" | "74255"
        "123"    | "1000" | null      | null        | "custom-app" | "ALL"
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | "74255"
        "123"    | "1000" | "out-1"   | "out-123"   | "custom-app" | "ALL"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试buildProperties方法，使用tenantId、appId和isOutUser参数的版本
     */
    def "buildPropertiesWithTenantIdAndAppIdAndIsOutUserTest"() {
        given:
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        
        when:
        // 执行被测试方法
        def result = AuthContextExt.buildProperties(tenantId, appId, isOutUser)
        
        then:
        // 结果验证
        if (tenantIdConf == "ALL" && isOutUser) {
            assert result != null
            assert result["bizAppId"] == appId
        } else {
            assert result == null
        }
        
        where:
        // 测试数据
        tenantId | appId        | isOutUser | tenantIdConf
        "123"    | "custom-app" | false     | "74255"
        "123"    | "custom-app" | false     | "ALL"
        "123"    | "custom-app" | true      | "74255"
        "123"    | "custom-app" | true      | "ALL"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用User参数的版本
     */
    def "getAppIdWithUserTest"() {
        given:
        // 测试准备
        def user = new User(tenantId, userId, outUserId, outTenantId)
        
        // 模拟 RequestUtil.getAppId()
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .appId(requestUtilAppId)
                .build()
        RequestContextManager.setContext(requestContext)
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        
        when:
        // 执行被测试方法
        def result = AuthContextExt.getAppId(user)
        
        then:
        // 结果验证
        if (outUserId != null) {
            if (tenantIdConf == "ALL") {
                assert result == "CRM"  // 使用 PrivilegeConstants.APP_ID 的值
            } else {
                assert result == requestUtilAppId
            }
        } else {
            assert result == appIdUtilAppId
        }
        
        where:
        // 测试数据
        tenantId | userId | outUserId | outTenantId | tenantIdConf | requestUtilAppId | appIdUtilAppId 
        "123"    | "1000" | null      | null        | "74255"      | "request-app"    | "request-app"
        "123"    | "1000" | "out-1"   | "out-123"   | "74255"      | "CRM"            | "request-app"
        "123"    | "1000" | "out-1"   | "out-123"   | "ALL"        | "request-app"    | "appIdUtil-app"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用tenantId、appId和isOutUser参数的版本
     */
    def "getAppIdWithTenantIdAndAppIdAndIsOutUserTest"() {
        given:
        Whitebox.setInternalState(AppFrameworkConfig, "functionPermissionAppIdGrayEi", [tenantIdConf] as Set<String>)
        
        when:
        // 执行被测试方法
        def result = AuthContextExt.getAppId(tenantId, inputAppId, isOutUser)
        
        then:
        // 结果验证
        if (isOutUser) {
            if (tenantIdConf == "ALL") {
                assert result == "CRM"  // 使用 PrivilegeConstants.APP_ID 的值
            } else {
                assert result == inputAppId
            }
        } else {
            assert result == appIdUtilResult
        }
        
        where:
        // 测试数据
        tenantId | inputAppId    | isOutUser | tenantIdConf | appIdUtilResult 
        "123"    | "custom-app"  | false     | "74255"      | "custom-app"
        "123"    | "custom-app"  | true      | "74255"      | "appIdUtil-app"
        "123"    | "custom-app"  | true      | "ALL"        | "appIdUtil-app"
    }
} 