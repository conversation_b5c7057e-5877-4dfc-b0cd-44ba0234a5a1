package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.core.model.User
import com.fxiaoke.paas.auth.factory.FieldClient
import spock.lang.Specification

class FunctionPrivilegeServiceImplTest extends Specification {
    FunctionPrivilegeService functionPrivilegeService
    FieldClient fieldClient = Mock()

    def setup() {
        functionPrivilegeService = new FunctionPrivilegeServiceImpl("fieldClient": fieldClient)
    }

    def "test getUnauthorizedFields"() {
        given:
        def user = User.systemUser("74255")
        when:
        fieldClient.userEntityIdsFieldPermission(*_) >> ["object_a__c": ["field_1__c": 1, "field_2__c": 0], "AccountObj": ["field_1__c": 0, "field_2__c": 1]]
        def fields = functionPrivilegeService.getUnauthorizedFields(user, ["object_a__c", "AccountObj"])
        then:
        fields["object_a__c"].containsAll(["field_2__c"])
        fields["AccountObj"].containsAll(["field_1__c"])
    }

    def "test batchFunPrivilegeCheck"() {
        given:
        def user = User.systemUser("74255")
        when:
        def funcPrivilegeConfig = functionPrivilegeService.batchFunPrivilegeCheck(user, ["object_a__c", "AccountObj"], ["Add"])
        then:
        funcPrivilegeConfig["object_a__c"]["Add"] == true
        funcPrivilegeConfig["AccountObj"]["Add"] == true
    }
}
