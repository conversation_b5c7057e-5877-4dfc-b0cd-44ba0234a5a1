package com.facishare.paas.appframework.privilege.util

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试AppIdUtil工具类的方法
 */
@Unroll
class AppIdUtilTest extends Specification {

    def cleanup() {
        // 清理测试后的RequestContextManager
        RequestContextManager.removeContext()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用User参数的版本
     */
    def "getAppIdTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        
        // 模拟RequestContextManager的上下文
        if (contextAppId != null) {
            def requestContext = RequestContext.builder()
                    .tenantId("123")
                    .user(user)
                    .appId(contextAppId)
                    .requestSource(RequestContext.RequestSource.CEP)
                    .build()
            RequestContextManager.setContext(requestContext)
        }
        
        when:
        // 执行被测试方法
        def result = AppIdUtil.getAppId(user)
        
        then:
        // 结果验证
        result == expected
        
        where:
        // 测试数据
        contextAppId     | expected
        "custom-app"     | "custom-app"
        ""               | PrivilegeConstants.APP_ID
        null             | PrivilegeConstants.APP_ID
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用User和appId两个参数的版本
     */
    def "getAppIdWithAppIdParamTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        
        when:
        // 执行被测试方法
        def result = AppIdUtil.getAppId(user, inputAppId)
        
        then:
        // 结果验证
        result == expected
        
        where:
        // 测试数据
        inputAppId       | expected
        "custom-app"     | "custom-app"
        ""               | PrivilegeConstants.APP_ID
        null             | PrivilegeConstants.APP_ID
        " "              | PrivilegeConstants.APP_ID
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getAppIdWithNoDefault方法
     */
    def "getAppIdWithNoDefaultTest"() {
        given:
        // 测试准备
        def user = new User("123", "456")
        
        // 模拟RequestContextManager的上下文
        if (contextAppId != null) {
            def requestContext = RequestContext.builder()
                    .tenantId("123")
                    .user(user)
                    .appId(contextAppId)
                    .requestSource(RequestContext.RequestSource.CEP)
                    .build()
            RequestContextManager.setContext(requestContext)
        }
        
        when:
        // 执行被测试方法
        def result = AppIdUtil.getAppIdWithNoDefault()
        
        then:
        // 结果验证
        result == expected
        
        where:
        // 测试数据
        contextAppId     | expected
        "custom-app"     | "custom-app"
        ""               | ""
        null             | null
    }
} 