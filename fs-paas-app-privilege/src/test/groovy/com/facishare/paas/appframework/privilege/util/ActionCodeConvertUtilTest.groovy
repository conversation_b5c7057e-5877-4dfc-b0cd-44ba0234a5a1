package com.facishare.paas.appframework.privilege.util

import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试ActionCodeConvertUtil工具类的convert2FuncCode方法
 */
@Unroll
class ActionCodeConvertUtilTest extends Specification {
    
    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法正常场景，包括List特殊处理和普通actionCode处理
     */
    def "convert2FuncCodeTest"() {
        given:
        // 测试准备
        
        when:
        // 执行被测试方法
        def result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode)
        
        then:
        // 结果验证
        result == expected
        
        where:
        // 测试数据
        objectApiName      | actionCode  | expected
        "CustomerObj"      | "List"      | "CustomerObj"
        "CustomerObj"      | "Add"       | "CustomerObj||Add"
        "EmployeeObj"      | "Delete"    | "EmployeeObj||Delete"
        "OrderObj"         | "Edit"      | "OrderObj||Edit"
        ""                 | "List"      | ""
        ""                 | "Add"       | "||Add"
        "LeadObj"          | ""          | "LeadObj||"
    }
}