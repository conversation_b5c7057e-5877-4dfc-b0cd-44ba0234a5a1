package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.CacheContext
import com.facishare.paas.appframework.privilege.util.FunctionPrivillegeConfig
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants
import com.facishare.paas.auth.common.exception.AuthException
import com.facishare.paas.auth.model.AuthContext
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.paas.auth.factory.FuncClient
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试MasterSlaveFunctionPrivilegeService类的方法
 */
@Unroll
class MasterSlaveFunctionPrivilegeServiceTest extends Specification {
    
    MasterSlaveFunctionPrivilegeService masterSlaveFunctionPrivilegeService
    IObjectDescribeService objectDescribeService
    FuncClient funcClient
    
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        masterSlaveFunctionPrivilegeService = new MasterSlaveFunctionPrivilegeService()
        
        objectDescribeService = Mock(IObjectDescribeService)
        funcClient = Mock(FuncClient)
        
        masterSlaveFunctionPrivilegeService.objectDescribeService = objectDescribeService
        masterSlaveFunctionPrivilegeService.funcClient = funcClient
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法正常场景，从缓存中获取部分数据
     */
    def "checkFunctionPrivilegeTest"() {
        given:
        // 测试准备
        def authContext = AuthContext.builder()
            .appId("test-app")
            .tenantId("123456")
            .userId("78910")
            .build()
        
        def funcCodeList = ["Object1||Add", "Object1||Edit", "Object2||Delete"]
        
        // 模拟从缓存中获取部分数据
        def cacheResult = [
            "Object1||Add": true,
            "Object2||Delete": false
        ]

        def threadLocal = Mock(ThreadLocal)
        Whitebox.setInternalState(CacheContext, "THREAD_LOCAL", threadLocal)
        def cacheContext = Mock(CacheContext)
        threadLocal.get() >> cacheContext
        cacheContext.getCache() >> cacheResult
        
        // 模拟从client中获取剩余数据
        def clientResult = [
            "Object1||Edit": true
        ]
        
        when:
        // 执行被测试方法
        def result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(authContext, funcCodeList)
        
        then:
        // 结果验证
        1 * funcClient.userFuncPermissionCheck({ AuthContext ctx ->
            ctx.tenantId == authContext.tenantId && 
            ctx.userId == authContext.userId && 
            ctx.appId == authContext.appId
        }, { Set<String> codes ->
            codes.size() == 3 &&
            codes.containsAll(funcCodeList) &&
            funcCodeList.containsAll(codes)
        }) >> clientResult
        
        result.success
        result.result == [ "Object1||Edit": true]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法异常场景，接口调用失败
     */
    def "checkFunctionPrivilegeErrorTest"() {
        given:
        // 测试准备
        def authContext = AuthContext.builder()
            .appId("test-app")
            .tenantId("123456")
            .userId("78910")
            .build()
        
        def funcCodeList = ["Object1||Add", "Object1||Edit"]
        
        // 模拟接口调用异常
        def authException = new AuthException(401, "权限验证失败")
        
        when:
        // 执行被测试方法
        def result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(authContext, funcCodeList)
        
        then:
        // 结果验证
        1 * funcClient.userFuncPermissionCheck({ AuthContext ctx ->
            ctx.tenantId == authContext.tenantId && 
            ctx.userId == authContext.userId && 
            ctx.appId == authContext.appId
        }, { Set<String> codes ->
            codes.size() == funcCodeList.size() &&
            codes.containsAll(funcCodeList) &&
            funcCodeList.containsAll(codes)
        }) >> { throw authException }
        
        !result.success
        result.errCode == authException.code
        result.errMessage == authException.message
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionDataPrivilege方法正常场景
     */
    def "checkFunctionDataPrivilegeTest"() {
        given:
        // 测试准备
        def authContext = AuthContext.builder()
            .appId("test-app")
            .tenantId("123456")
            .userId("78910")
            .build()
        
        def funcCodeList = ["Object1||Add", "Object1||Edit"]
        
        def expectedResult = [
            "Object1||Add": true,
            "Object1||Edit": false
        ]
        
        when:
        // 执行被测试方法
        def result = masterSlaveFunctionPrivilegeService.checkFunctionDataPrivilege(authContext, funcCodeList)
        
        then:
        // 结果验证
        1 * funcClient.userFuncPermissionCheckWithoutAppId({ AuthContext ctx ->
            ctx.tenantId == authContext.tenantId && 
            ctx.userId == authContext.userId && 
            ctx.appId == authContext.appId
        }, { Set<String> codes ->
            codes.size() == funcCodeList.size() &&
            codes.containsAll(funcCodeList) &&
            funcCodeList.containsAll(codes)
        }) >> expectedResult
        
        result.success
        result.result == expectedResult
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionDataPrivilege方法异常场景
     */
    def "checkFunctionDataPrivilegeErrorTest"() {
        given:
        // 测试准备
        def authContext = AuthContext.builder()
            .appId("test-app")
            .tenantId("123456")
            .userId("78910")
            .build()
        
        def funcCodeList = ["Object1||Add", "Object1||Edit"]
        
        // 模拟接口调用异常
        def authException = new AuthException(401, "权限验证失败")
        
        when:
        // 执行被测试方法
        def result = masterSlaveFunctionPrivilegeService.checkFunctionDataPrivilege(authContext, funcCodeList)
        
        then:
        // 结果验证
        1 * funcClient.userFuncPermissionCheckWithoutAppId({ AuthContext ctx ->
            ctx.tenantId == authContext.tenantId && 
            ctx.userId == authContext.userId && 
            ctx.appId == authContext.appId
        }, { Set<String> codes ->
            codes.size() == funcCodeList.size() &&
            codes.containsAll(funcCodeList) &&
            funcCodeList.containsAll(codes)
        }) >> { throw authException }
        
        !result.success
        result.errCode == authException.code
        result.errMessage == authException.message
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getSlaveHasNoFunctionCodes方法，在灰度列表中
     */
    def "getSlaveHasNoFunctionCodesTest"() {
        given:
        // 测试准备
        def tenantId = "123456"
        

        // 模拟详细描述API名称查询结果
        def detailApiNames = ["DetailObj1", "DetailObj2"]
        Whitebox.setInternalState(AppFrameworkConfig, "masterDetailApprovalGrayList", ["ALL"] as Set)

        when:
        // 执行被测试方法
        def result = masterSlaveFunctionPrivilegeService.getSlaveHasNoFunctionCodes(tenantId)
        
        then:
        // 结果验证
        1 * objectDescribeService.findDetailDescribeApiNamesWithMasterCreated({ String tid ->
            tid == tenantId
        }, { Map<String, Object> context ->
            context != null
        }) >> detailApiNames
        
        // 验证返回的结果包含了所有详细API名称和限制功能码的组合
        result.size() > 0
        FunctionPrivillegeConfig.getSlaveNoUseFunctionCodes().each { functionCode ->
            detailApiNames.each { apiName ->
                assert result.contains(apiName + PrivilegeConstants.FUNC_CODE_SPLIT + functionCode)
            }
        }
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getSlaveHasNoFunctionCodes方法，不在灰度列表中
     */
    def "getSlaveHasNoFunctionCodesNotInGrayListTest"() {
        given:
        // 测试准备
        def tenantId = "123456"

        Whitebox.setInternalState(AppFrameworkConfig, "masterDetailApprovalGrayList", ["74255"] as Set)

        when:
        // 执行被测试方法
        def result = masterSlaveFunctionPrivilegeService.getSlaveHasNoFunctionCodes(tenantId)
        
        then:
        // 结果验证 - 不在灰度列表中应返回空集合
        0 * objectDescribeService.findDetailDescribeApiNamesWithMasterCreated(_, _)
        result.size() == 0
    }
}