package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.privilege.dto.AuthContext
import com.facishare.paas.appframework.privilege.dto.CheckFunctionPrivilege
import com.facishare.paas.appframework.privilege.dto.FuncPermiss
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by liyiguang on 2017/8/21.
 */
//@ContextConfiguration(value = "classpath:application-test.xml")
class FunctionPrivilegeServiceProxyTest extends Specification {

  @Autowired
  FunctionPrivilegeProxy proxy

  def "test checkFunctionPrivilege"() {
    given:
    AuthContext context = AuthContext.builder()
        .appId("CRM")  //暂时写死
        .tenantId(tenantId)
        .userId(userId).build()

        CheckFunctionPrivilege.Arg arg = CheckFunctionPrivilege.Arg.builder()
                .authContext(context)
                .funcCodeList("AccountObj||Add").build()
        when:
        CheckFunctionPrivilege.Result ret = proxy.checkFunctionPrivilege(arg)
        then:
        ret.success == true
        where:
        tenantId | userId
        "2" | "1000"
    }
}
