package com.facishare.paas.appframework.privilege

import com.facishare.paas.appframework.common.service.model.ManageGroup
import com.facishare.paas.appframework.common.service.model.ManageGroupType
import com.fxiaoke.release.FsGrayReleaseBiz
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：测试DataPrivilegeCommonService类的方法
 */
@Unroll
class DataPrivilegeCommonServiceTest extends Specification {

    DataPrivilegeCommonService dataPrivilegeCommonService
    FsGrayReleaseBiz dataAuthGray = Mock(FsGrayReleaseBiz)

    def setup() {
        dataPrivilegeCommonService = new DataPrivilegeCommonService("dataAuthGray": dataAuthGray)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enableDataQueryByDescribeAndScopeValidation方法
     */
    def "enableDataQueryByDescribeAndScopeValidationTest"() {
        given:
        // 测试准备
        def tenantId = "123456"

        when:
        // 执行被测试方法
        dataAuthGray.isAllow(*_) >> expected
        def result = dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(tenantId)

        then:
        // 结果验证
        result == expected

        where:
        // 测试数据
        expected | _
        true     | _
        false    | _
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultPageInfo方法，提供不同的page和size参数
     */
    def "getDefaultPageInfoTest"() {
        given:
        // 测试准备

        when:
        // 执行被测试方法
        def result = dataPrivilegeCommonService.getDefaultPageInfo(page, size)

        then:
        // 结果验证
        result.currentPage == expectedPage
        result.pageSize == expectedSize
        result.totalPage == 0
        result.total == 0

        where:
        // 测试数据
        page | size | expectedPage | expectedSize
        1    | 20   | 1            | 20
        2    | 30   | 2            | 30
        null | 10   | 1            | 10
        5    | null | 5            | 20
        null | null | 1            | 20
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkEntityIdsByManageGroup方法，检查指定范围内实体的权限
     */
    def "checkEntityIdsByManageGroupTest"() {
        given:
        // 测试准备
        def entityIds = inputEntityIds ? Lists.newArrayList(inputEntityIds) : null
        def manageGroup = inputManageGroup

        when:
        // 执行被测试方法
        def result = dataPrivilegeCommonService.checkEntityIdsByManageGroup(entityIds, manageGroup)

        then:
        // 结果验证
        result == expectedResult

        where:
        // 测试数据
        inputEntityIds         | inputManageGroup                                 | expectedResult
        ["entity1", "entity2"] | createManageGroup(false, ["entity1", "entity3"]) | ["entity1"]
        ["entity1", "entity2"] | createManageGroup(true, ["entity1", "entity2"])  | ["entity1", "entity2"]
        ["entity1", "entity2"] | null                                             | []
        ["entity1", "entity2"] | createManageGroup(false, [])                     | []
        []                     | createManageGroup(false, ["entity1", "entity2"]) | ["entity1", "entity2"]
        null                   | createManageGroup(false, ["entity1", "entity2"]) | ["entity1", "entity2"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkDeptAndOrgIdsByManageScope方法，检查指定范围内部门和组织的权限
     */
    def "checkDeptAndOrgIdsByManageScopeTest"() {
        given:
        // 测试准备
        def queryIds = inputQueryIds ? Lists.newArrayList(inputQueryIds) : null
        def deptAndOrgIds = inputDeptAndOrgIds ? Sets.newHashSet(inputDeptAndOrgIds) : null

        when:
        // 执行被测试方法
        def result = dataPrivilegeCommonService.checkDeptAndOrgIdsByManageScope(queryIds, deptAndOrgIds)

        then:
        // 结果验证
        result == Sets.newHashSet(expectedResult)

        where:
        // 测试数据
        inputQueryIds      | inputDeptAndOrgIds | expectedResult
        ["dept1", "dept2"] | ["dept1", "dept3"] | ["dept1"]
        ["dept1", "dept2"] | ["dept1", "dept2"] | ["dept1", "dept2"]
        ["dept1", "dept2"] | []                 | []
        ["dept1", "dept2"] | null               | []
        []                 | ["dept1", "dept2"] | ["dept1", "dept2"]
        null               | ["dept1", "dept2"] | ["dept1", "dept2"]
    }

    // 创建ManageGroup辅助方法
    private ManageGroup createManageGroup(boolean isAllSupport, List<String> supportApiNames) {
        return new ManageGroup(
                isAllSupport, 
                ManageGroupType.OBJECT, 
                "ParentObj", 
                Sets.newHashSet(supportApiNames)
        )
    }
} 