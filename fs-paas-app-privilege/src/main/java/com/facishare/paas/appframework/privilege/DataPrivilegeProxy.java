package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by zhouwr on 2017/10/16
 */
@RestResource(
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC",
        value = "PAAS-DATA-PRIVILEGE",
        desc = "基础数据权限服务", // ignoreI18n
        contentType = "application/json"
)
public interface DataPrivilegeProxy {

    @POST(value = "/datarights/objectspermission", desc = "用户的记录权限查询")
    ObjectsPermission.Result objectsPermission(@HeaderMap Map<String, String> header, @Body ObjectsPermission.Arg arg);

    @POST(value = "/datarights/queryDimensionIntersectionStatus", desc = "查询维度交集状态")
    QueryDimensionIntersectionStatusModel.Result queryDimensionIntersectionStatus(@HeaderMap Map<String, String> header, @Body Map<String, String> arg);

    @POST(value = "/openness/createEntityOpenness", desc = "添加对象级权限规则")
    CreateBaseDataPrivilegeRulesModel.Result createBaseDataPrivilegeRules(@HeaderMap Map<String, String> header, @Body CreateBaseDataPrivilegeRulesModel.Arg arg);

    @POST(value = "/openness/delEntityOpenness", desc = "删除对象级权限规则")
    DelBaseDataPrivilegeRulesModel.Result delBaseDataPrivilegeRules(@HeaderMap Map<String, String> header, @Body DelBaseDataPrivilegeRulesModel.Arg arg);

    @POST(value = "/openness/queryEntityOpenness", desc = "查询对象级权限规则")
    GetBaseDataPrivilegeRulesModel.Result getBaseDataPrivilegeRules(@HeaderMap Map<String, String> header, @Body GetBaseDataPrivilegeRulesModel.Arg arg);

    @POST(value = "/openness/updateEntityOpenness", desc = "更新对象级权限规则")
    UpdateBaseDataPrivilegeRulesModel.Result updateBaseDataPrivilegeRules(@HeaderMap Map<String, String> header, @Body UpdateBaseDataPrivilegeRulesModel.Arg arg);

    @POST(value = "/share/createEntityShare", desc = "添加数据共享规则")
    CreateEntityShareModel.Result createEntityShare(@HeaderMap Map<String, String> header, @Body CreateEntityShareModel.Arg arg);

    @POST(value = "/share/createEntityShareGroup", desc = "添加数据共享规则组")
    EntityShareGroupModel.Result createEntityShareGroup(@HeaderMap Map<String, String> header, @Body EntityShareGroupModel.Arg arg);

    @POST(value = "/share/updateEntityShareGroup", desc = "更新数据共享规则组")
    EntityShareGroupModel.Result updateEntityShareGroup(@HeaderMap Map<String, String> header, @Body EntityShareGroupModel.Arg arg);

    @POST(value = "/share/delEntityShareGroup", desc = "删除数据共享规则组")
    DelEntityShareGroupModel.Result delEntityShareGroup(@HeaderMap Map<String, String> header, @Body DelEntityShareGroupModel.Arg arg);

    @POST(value = "/share/delEntityShare", desc = "删除数据共享规则")
    DelEntityShareModel.Result delEntityShare(@HeaderMap Map<String, String> header, @Body DelEntityShareModel.Arg arg);

    @POST(value = "/share/queryEntityShare", desc = "查询数据共享规则")
    QueryEntityShareModel.Result queryEntityShare(@HeaderMap Map<String, String> header, @Body QueryEntityShareModel.Arg arg);

    @POST(value = "/share/queryAllEntityShareList", desc = "查询全部对象数据共享规则")
    QueryAllEntityShareModel.Result queryAllEntityShareList(@HeaderMap Map<String, String> header, @Body QueryAllEntityShareModel.Arg arg);

    @POST(value = "/share/queryEntityShareByGroupIds", desc = "通过规则组ID查询数据共享规则")
    QueryEntityShareModel.Result queryEntityShareByGroupIds(@HeaderMap Map<String, String> header, @Body QueryEntityShareModel.Arg arg);

    @POST(value = "/share/queryEntityShareGroup", desc = "查询数据共享规则组")
    QueryEntityShareGroupModel.Result queryEntityShareGroup(@HeaderMap Map<String, String> header, @Body QueryEntityShareGroupModel.Arg arg);

    @POST(value = "/share/updateEntityShareStatus", desc = "批量更新数据共享规则状态")
    UpdateEntityShareStatusModel.Result updateEntityShareStatus(@HeaderMap Map<String, String> header, @Body UpdateEntityShareStatusModel.Arg arg);

    @POST(value = "/share/updateEntityShareGroupStatus", desc = "批量更新数据共享规则组状态")
    UpdateEntityShareGroupStatusModel.Result updateEntityShareGroupStatus(@HeaderMap Map<String, String> header, @Body UpdateEntityShareGroupStatusModel.Arg arg);

    @POST(value = "/share/batchUpdateEntityShare", desc = "批量更新数据共享规则")
    BatchUpdateEntityShareModel.Result batchUpdateEntityShare(@HeaderMap Map<String, String> header, @Body BatchUpdateEntityShareModel.Arg arg);

    @POST(value = "/datarights/delDataRights", desc = "删除对象的数据权限")
    DelDataRights.Result delDataRights(@HeaderMap Map<String, String> header, @Body DelDataRights.Arg arg);

    @POST(value = "/share/createEntityFieldShare", desc = "添加条件共享规则")
    CreateEntityFieldShareModel.Result createEntityFieldShare(@HeaderMap Map<String, String> header, @Body CreateEntityFieldShareModel.Arg arg);

    @POST(value = "/share/updateEntityFieldShare", desc = "更新条件共享规则")
    UpdateEntityFieldShareModel.Result updateEntityFieldShare(@HeaderMap Map<String, String> header, @Body UpdateEntityFieldShareModel.Arg arg);

    @POST(value = "/share/updateEntityFieldShareStatus", desc = "更新条件共享规则状态")
    UpdateEntityFieldShareStatusModel.Result updateEntityFieldShareStatus(@HeaderMap Map<String, String> header, @Body UpdateEntityFieldShareStatusModel.Arg arg);

    @POST(value = "/share/deleteEntityFieldShare", desc = "删除条件共享规则")
    DelEntityFieldShareModel.Result deleteEntityFieldShare(@HeaderMap Map<String, String> header, @Body DelEntityFieldShareModel.Arg arg);

    @POST(value = "/share/queryEntityFieldShare", desc = "查询条件共享规则")
    QueryEntityFieldShareModel.Result queryEntityFieldShare(@HeaderMap Map<String, String> header, @Body QueryEntityFieldShareModel.Arg arg);

    @POST(value = "/share/queryAllEntityFieldShareList", desc = "查询全部对象条件共享规则")
    QueryAllEntityFieldShareModel.Result queryAllEntityFieldShareList(@HeaderMap Map<String, String> header, @Body QueryAllEntityFieldShareModel.Arg arg);

    @POST(value = "/dimensionGroup/createDimensionRuleGroup", desc = "创建组合维度规则")
    CreateDimensionRuleGroupModel.Result createDimensionRuleGroup(@HeaderMap Map<String, String> header, @Body CreateDimensionRuleGroupModel.Arg arg);

    @POST(value = "/dimensionGroup/updateDimensionRuleGroup", desc = "更新组合维度规则")
    UpdateDimensionRuleGroupModel.Result updateDimensionRuleGroup(@HeaderMap Map<String, String> header, @Body UpdateDimensionRuleGroupModel.Arg arg);

    @POST(value = "/dimensionGroup/queryDimensionRuleGroupList", desc = "查询组合维度规则")
    QueryDimensionRuleGroupModel.Result queryDimensionRuleGroupList(@HeaderMap Map<String, String> header, @Body QueryDimensionRuleGroupModel.Arg arg);

    @POST(value = "/dimensionGroup/queryDimensionRuleCodeList", desc = "查询组合维度code集合")
    QueryDimensionRuleCodeListModel.Result queryDimensionRuleCodeList(@HeaderMap Map<String, String> header, @Body QueryDimensionRuleCodeListModel.Arg arg);

    @POST(value = "/dimensionGroup/batchDeleteDimensionGroup", desc = "删除组合维度规则")
    DeleteDimensionRuleGroupModel.Result batchDeleteDimensionGroup(@HeaderMap Map<String, String> header, @Body DeleteDimensionRuleGroupModel.Arg arg);

    @POST(value = "/temporary/rights/query", desc = "查询临时权限列表")
    QueryTemporaryPrivilegeList.Result queryTemporaryPrivilegeList(@HeaderMap Map<String, String> header, @Body QueryTemporaryPrivilegeList.Arg arg);

    @POST(value = "/temporary/rights/owner/update", desc = "更新dataId下拥有临时数据权限的人员")
    UpdateTemporaryRights.Result updateTemporaryRights(@HeaderMap Map<String, String> header, @Body UpdateTemporaryRights.Arg arg);

    @POST(value = "/temporary/rights/delete", desc = "删除企业临时数据权限")
    DeleteTemporaryRights.Result deleteTemporaryRights(@HeaderMap Map<String, String> header, @Body DeleteTemporaryRights.Arg arg);

    @POST(value = "/temporary/rights/query/All", desc = "新查询接口")
    QueryTemporaryRightsList.Result queryTemporaryRights(@HeaderMap Map<String, String> header, @Body QueryTemporaryRightsList.Arg arg);

    @POST(value = "/temporary/rights/delete/data", desc = "删除全部数据接口(异步接口)")
    DeleteTemporaryRights.Result deleteTemporaryRightsData(@HeaderMap Map<String, String> header, @Body DeleteTemporaryRights.Arg arg);

    @POST(value = "/temporary/rights/batch/delete/data", desc = "批量删除临时权限数据接口")
    DeleteTemporaryRights.Result batchDeleteTemporaryRightsData(@HeaderMap Map<String, String> header, @Body BatchDeleteTemporaryRights.Arg arg);

    @POST(value = "/temporary/rights/create/batchDeleteTemporaryRightsTask", desc = "创建批量解除临时权限任务")
    BatchDeleteTemporaryPrivilegeModel.Result createBatchDeleteTemporaryRightsTask(@HeaderMap Map<String, String> header, @Body BatchDeleteTemporaryPrivilegeModel.Arg arg);

    @POST(value = "/share/queryRuleCount", desc = "查询条件共享规则数量(老对象+自定义对象)")
    QueryEntityFieldShareCount.Result queryEntityFieldShareCount(@HeaderMap Map<String, String> header, @Body QueryEntityFieldShareCount.Arg arg);

    @POST(value = "/share/queryEntityShareCount", desc = "查询基于来源共享规则数量")
    QueryShareRuleCount.Result queryEntityShareCount(@HeaderMap Map<String, String> header, @Body QueryShareRuleCount.Arg arg);

    @POST(value = "/share/queryEntityShareGroupCount", desc = "查询基于来源共享规则组关联的规则组数量")
    QueryEntityFieldShareCount.Result queryEntityShareGroupCount(@HeaderMap Map<String, String> header, @Body EntityShareGroupModel.Arg arg);

    @POST(value = "/dataAuthQuery/obtainDataAuth", desc = "数据权限查询")
    QueryDataAuthList.Result obtainDataAuth(@HeaderMap Map<String, String> header, @Body QueryDataAuth.Arg arg);

    @POST(value = "/teamRights/queryTeamRoleDescribeList", desc = "查询自定义相关团队角色适用对象列表")
    QueryTeamRoleDescribeListModel.Result queryTeamRoleDescribeList(@HeaderMap Map<String, String> header, @Body QueryTeamRoleDescribeListModel.Arg arg);

    @POST(value = "/teamRights/queryTeamRoleMaxNumber", desc = "查询自定义相关团队角色数量")
    QueryTeamRoleMaxNumberModel.Result queryTeamRoleMaxNumber(@HeaderMap Map<String, String> header, @Body QueryTeamRoleMaxNumberModel.Arg arg);

    @POST(value = "/teamRights/createTeamRole", desc = "创建自定义相关团队角色")
    CreateTeamRoleModel.Result createTeamRole(@HeaderMap Map<String, String> header, @Body CreateTeamRoleModel.Arg arg);

    @POST(value = "/teamRights/queryTeamRole", desc = "查询自定义相关团队角色")
    QueryTeamRoleModel.Result queryTeamRole(@HeaderMap Map<String, String> header, @Body QueryTeamRoleModel.Arg arg);

    @POST(value = "/teamRights/updateTeamRole", desc = "更新自定义相关团队角色")
    UpdateTeamRoleModel.Result updateTeamRole(@HeaderMap Map<String, String> header, @Body UpdateTeamRoleModel.Arg arg);

    @POST(value = "/teamRights/updateTeamRoleStatus", desc = "更新自定义相关团队角色状态")
    UpdateTeamRoleStatusModel.Result updateTeamRoleStatus(@HeaderMap Map<String, String> header, @Body UpdateTeamRoleStatusModel.Arg arg);

    @POST(value = "/teamRights/deleteTeamRole", desc = "删除自定义相关团队角色")
    UpdateTeamRoleStatusModel.Result deleteTeamRole(@HeaderMap Map<String, String> header, @Body UpdateTeamRoleStatusModel.Arg arg);
}
