package com.facishare.paas.appframework.privilege.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GetAllShareRules {
  @Data
  class Arg {
    @JsonProperty("Keyword")
    @SerializedName("Keyword")
    @JSONField(name = "M1")
    String keyword;

    @JsonProperty("KeywordScope")
    @SerializedName("KeywordScope")
    @JSONField(name = "M2")
    Integer keywordScope;

    @JsonProperty("ObjectDescribeApiName")
    @SerializedName("ObjectDescribeApiName")
    @JSONField(name = "M3")
    String objectDescribeApiName;

    @JsonProperty("PermissionType")
    @SerializedName("PermissionType")
    @JSONField(name = "M4")
    Integer permissionType;

    @JsonProperty("Status")
    @SerializedName("Status")
    @JSONField(name = "M5")
    Integer status;

    @JSONField(name = "M6")
    Integer pageNumber;

    @JSONField(name = "M7")
    Integer pageSize;

    @JsonProperty("KeywordType")
    @SerializedName("KeywordType")
    @JSONField(name = "M8")
    Integer keywordType;

    @JsonProperty("Sources")
    @SerializedName("Sources")
    @JSONField(name = "M9")
    private Map<Integer, Set<String>> sources;

    @JsonProperty("Receives")
    @SerializedName("Receives")
    @JSONField(name = "M10")
    private Map<Integer, Set<String>> receives;

    private Set<String> createIds;

    private Set<String> modifyIds;

    private Map<String, Long> createTimeRange;

    private Map<String, Long> modifyTimeRange;

    @JsonProperty("Entices")
    @SerializedName("Entices")
    @JSONField(name = "M11")
    private List<String> entices;

    @JsonProperty("ShareType")
    @SerializedName("ShareType")
    @JSONField(name = "M12")
    private String shareType;

    @JsonProperty("TargetName")
    @SerializedName("TargetName")
    @JSONField(name = "M13")
    private String targetName;

    //null 查询所有的
    //true 只查询共享給外部的
    //false 只查询共享給内部的
    private Boolean outReceive;

    private Integer basedType;

    /**
     * 0来源方上游 1来源方下游
     *
     * NULL 查询全部
     */
    private Integer entityShareType;
  }

  @Builder
  @Data
  class Result {
    @JsonProperty("SharedObjectInfos")
    @SerializedName("SharedObjectInfos")
    @JSONField(name = "M1")
    private List<SharedRuleInfoPojo> sharedObjectInfos;

    @JsonProperty("Page")
    @SerializedName("Page")
    @JSONField(name = "M2")
    private PageInfo page;
  }

  @Data
  @Builder
  class SharedRuleInfoPojo {
    @JsonProperty("Circle")
    @SerializedName("Circle")
    private String circle;
    @JsonProperty("Employee")
    @SerializedName("Employee")
    private String employee;
    @JsonProperty("DataSourceID")
    @SerializedName("DataSourceID")
    private String dataSourceID;
    @JsonProperty("DataSourceName")
    @SerializedName("DataSourceName")
    private String dataSourceName;
    @JsonProperty("DataSourceType")
    @SerializedName("DataSourceType")
    private Integer dataSourceType;
    @JsonProperty("DataSourceStatus")
    @SerializedName("DataSourceStatus")
    private Integer dataSourceStatus;
    @JsonProperty("ObjectDescribeApiName")
    @SerializedName("ObjectDescribeApiName")
    private String objectDescribeApiName;
    @JsonProperty("PermissionType")
    @SerializedName("PermissionType")
    private Integer permissionType;
    @JsonProperty("SharedEntity")
    @SerializedName("SharedEntity")
    private String sharedEntity;
    @JsonProperty("Status")
    @SerializedName("Status")
    private Integer status;
    @JsonProperty("TargetID")
    @SerializedName("TargetID")
    private String targetID;
    @JsonProperty("TargetName")
    @SerializedName("TargetName")
    private String targetName;
    @JsonProperty("TargetType")
    @SerializedName("TargetType")
    private Integer targetType;
    @JsonProperty("TargetStatus")
    @SerializedName("TargetStatus")
    private Integer targetStatus;
    @JsonProperty("ObjectDescribeDisplayName")
    @SerializedName("ObjectDescribeDisplayName")
    private String objectDescribeDisplayName;

    @JsonProperty("TargetTenantID")
    @SerializedName("ObjectDescribeDisplayName")
    private String targetTenantId;

    private String creator;
    private String modifier;
    private String creatorName;
    private String modifierName;
    private Integer creatorStatus;
    private Integer modifierStatus;
    private Long createTime;
    private Long modifyTime;

    private Integer receiveDeptCascade;

    private int basedType;
  }

  @Data
  class PageInfo {
    @JsonProperty("PageCount")
    @SerializedName("PageCount")
    private Integer pageCount;
    @JsonProperty("PageNumber")
    @SerializedName("PageNumber")
    private Integer pageNumber;
    @JsonProperty("PageSize")
    @SerializedName("PageSize")
    private Integer pageSize;
    @JsonProperty("TotalCount")
    @SerializedName("TotalCount")
    private Integer totalCount;
  }
}
