package com.facishare.crm.userdefobj

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class DefObjConstantsTest extends Specification {
    Map grayFieldsConfig = ["AllFieldType": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group"], "DepartmentObj": ["text", "long_text", "select_one", "select_many", "number", "date", "time", "date_time", "true_or_false", "percentile", "url", "auto_number", "formula", "employee", "employee_many"], "PersonnelObj": ["employee_many", "object_reference", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "auto_number", "formula", "department", "employee", "location", "count", "signature", "payment", "sign_in", "area", "group"], "ActiveRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "object_reference_many", "auto_number", "formula", "department", "employee", "employee_many", "location", "signature", "quote", "rich_text", "group"], "JournalObj": ["count", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group", "employee_many"], "TelesalesRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "ServiceLogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "BlogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "ScheduleObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "NewAdvertisementObj": []]
    Map fieldsConfig = ["AllFieldType": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group"], "DepartmentObj": ["text", "long_text", "select_one", "select_many", "number", "date", "time", "date_time", "true_or_false", "percentile", "url", "auto_number", "formula", "employee", "employee_many"], "PersonnelObj": ["object_reference", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "auto_number", "formula", "department", "employee", "location", "count", "signature", "payment", "sign_in", "area", "group"], "ActiveRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "employee_many", "location", "signature", "quote", "rich_text", "group"], "JournalObj": ["count", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group", "employee_many"], "TelesalesRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "ServiceLogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "BlogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"], "ScheduleObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "location", "signature", "quote", "rich_text", "group"]]
    List grayAllFieldTypeList = ["text", "long_text", "html_rich_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "department_many", "employee", "employee_many", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group"]

    Map grayFieldsConfig1 = ["AllFieldType": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group", "date_time_range", "rich_text"], "DepartmentObj": ["text", "long_text", "select_one", "select_many", "number", "date", "time", "date_time", "true_or_false", "percentile", "url", "auto_number", "formula", "employee", "out_employee", "employee_many", "date_time_range", "rich_text"], "PersonnelObj": ["employee_many", "object_reference", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "auto_number", "formula", "department", "employee", "out_employee", "location", "count", "signature", "payment", "sign_in", "area", "group", "date_time_range", "rich_text"], "ActiveRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "object_reference_many", "auto_number", "formula", "department", "employee", "out_employee", "employee_many", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "JournalObj": ["count", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "employee_many", "date_time_range"], "TelesalesRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "ServiceLogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "BlogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "ScheduleObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "NewAdvertisementObj": []]
    Map fieldsConfig1 = ["AllFieldType": ["text", "long_text", "html_rich_text", "rich_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "object_reference_many", "auto_number", "formula", "department", "employee", "out_employee", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group", "date_time_range"], "DepartmentObj": ["text", "long_text", "select_one", "select_many", "number", "date", "time", "date_time", "true_or_false", "percentile", "url", "auto_number", "formula", "employee", "out_employee", "employee_many", "date_time_range", "rich_text"], "PersonnelObj": ["object_reference", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "auto_number", "formula", "department", "employee", "out_employee", "location", "count", "signature", "payment", "sign_in", "area", "group", "date_time_range", "rich_text"], "ActiveRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "object_reference_many", "auto_number", "formula", "department", "employee", "out_employee", "employee_many", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "JournalObj": ["count", "object_reference_many", "text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "employee_many", "date_time_range"], "TelesalesRecordObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "ServiceLogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "BlogObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"], "ScheduleObj": ["text", "long_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "auto_number", "formula", "department", "employee", "out_employee", "location", "signature", "quote", "rich_text", "group", "date_time_range"]]
    List grayAllFieldTypeList1 = ["text", "long_text", "html_rich_text", "rich_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "object_reference_many", "auto_number", "formula", "department", "department_many", "employee", "out_employee", "employee_many", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group", "date_time_range"]
    String fieldTypeGrayConfig = [["ei": "white:74255|78057", "fieldApiName": "big_file_attachment", "describeApiName": "AllFieldType", "bizValue": "file_attachment"],["ei": "white:74255|78057", "fieldApiName": "big_text", "describeApiName": "AllFieldType", "bizValue": "long_text"]]

 /*   def "test getFieldTypeList"() {
        given:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.WEB_DETAIL_LAYOUT_FIELD_GRAY, tenantId) >> gray
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_OUT_EMPLOYEE_FIELD_TYPE_IN_LAYOUT_RESOURCE_EI, tenantId) >> true
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.UN_SUPPORT_PAYMENT_GROUP_GRAY_EI, tenantId) >> false
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.DATE_TIME_RANGE_GRAY_EI, tenantId) >> true
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.RICH_TEXT_GRAY_EI, tenantId) >> true
        Whitebox.setInternalState(DefObjConstants, "WEB_DETAIL_LAYOUT_FIELDS_GRAY", grayFieldsConfig)
        Whitebox.setInternalState(DefObjConstants, "WEB_DETAIL_LAYOUT_FIELDS", fieldsConfig)
        Whitebox.setInternalState(AppFrameworkConfig, "grayAllFieldTypeList", grayAllFieldTypeList)
        Whitebox.setInternalState(AppFrameworkConfig, "grayObjectReferenceManyFieldListTenantid", ["ALL"])
        Whitebox.setInternalState(AppFrameworkConfig, "grayBigFileAttachmentFieldListEi", ["74255"])
        when:
        def fields = DefObjConstants.getFieldTypeList(tenantId, describeApiName, false)
        containOutEmployee == fields.contains("out_employee")
        containDataTimeRange == fields.contains("date_time_range")
        containHtmlRichText == fields.contains("html_rich_text")
        containRichText == fields.contains("rich_text")
        containBigFileAttachment == fields.contains("big_file_attachment")
        then:
        fieldsRet == fields
        where:
        tenantId | gray | describeApiName | containOutEmployee | containDataTimeRange | containHtmlRichText | containRichText | containBigFileAttachment || fieldsRet
        '74255'  | true | 'object_a__c'   | true               | true                 | true                | true            | true                     || ["text", "long_text", "html_rich_text", "rich_text", "select_one", "select_many", "number", "currency", "date", "time", "date_time", "phone_number", "image", "file_attachment", "big_file_attachment", "email", "true_or_false", "percentile", "url", "object_reference", "object_reference_many", "auto_number", "formula", "department", "department_many", "employee", "out_employee", "employee_many", "location", "master_detail", "count", "signature", "quote", "payment", "sign_in", "area", "group", "date_time_range"]
    }

    def "test getFieldTypeList reconfiguration"() {
        given:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.WEB_DETAIL_LAYOUT_FIELD_GRAY, tenantId) >> gray
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_OUT_EMPLOYEE_FIELD_TYPE_IN_LAYOUT_RESOURCE_EI, tenantId) >> true
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.UN_SUPPORT_PAYMENT_GROUP_GRAY_EI, tenantId) >> false
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.DATE_TIME_RANGE_GRAY_EI, tenantId) >> true
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.RICH_TEXT_GRAY_EI, tenantId) >> true
        Whitebox.setInternalState(DefObjConstants, "WEB_DETAIL_LAYOUT_FIELDS_GRAY", grayFieldsConfig1)
        Whitebox.setInternalState(DefObjConstants, "WEB_DETAIL_LAYOUT_FIELDS", fieldsConfig1)
        Whitebox.setInternalState(AppFrameworkConfig, "grayAllFieldTypeList", grayAllFieldTypeList1)
        Whitebox.setInternalState(AppFrameworkConfig, "grayObjectReferenceManyFieldListTenantid", ["ALL"])
        Whitebox.setInternalState(AppFrameworkConfig, "grayBigFileAttachmentFieldListEi", ["74255"])
        when:
        def fields = DefObjConstants.getFieldTypeList(tenantId, describeApiName, false)
        then:
        println("describeApiName:" + describeApiName)
        containOutEmployee == fields.contains("out_employee")
        containReferenceMany == fields.contains("object_reference_many")
        containDataTimeRange == fields.contains("date_time_range")
        containHtmlRichText == fields.contains("html_rich_text")
        containRichText == fields.contains("rich_text")
        containBigFileAttachment == fields.contains("big_file_attachment")
        containBigText == fields.contains("big_text")
        where:
        tenantId | gray | describeApiName      | containOutEmployee | containReferenceMany | containDataTimeRange | containHtmlRichText | containRichText | containBigFileAttachment | containBigText
        '74255'  | true | 'object_a__c'        | true               | true                 | true                 | true                | true            | true                     | true
        '74255'  | true | 'DepartmentObj'      | true               | false                | true                 | false               | true            | false                    | false
        '74255'  | true | 'PersonnelObj'       | true               | true                 | true                 | false               | true            | false                    | false
        '74255'  | true | 'ActiveRecordObj'    | true               | true                 | true                 | false               | true            | false                    | false
        '74255'  | true | 'JournalObj'         | true               | true                 | true                 | false               | true            | false                    | false
        '74255'  | true | 'TelesalesRecordObj' | true               | false                | true                 | false               | true            | false                    | false
        '74255'  | true | 'ServiceLogObj'      | true               | false                | true                 | false               | true            | false                    | false
        '74255'  | true | 'BlogObj'            | true               | false                | true                 | false               | true            | false                    | false
        '74255'  | true | 'ScheduleObj'        | true               | false                | true                 | false               | true            | false                    | false
        '78057'  | true | 'object_a__c'        | true               | true                 | true                 | true                | true            | false                    | true
    }*/

    def "test getFieldTypeList reconfiguration new"() {
        given:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.WEB_DETAIL_LAYOUT_FIELD_GRAY, tenantId) >> gray
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_OUT_EMPLOYEE_FIELD_TYPE_IN_LAYOUT_RESOURCE_EI, tenantId) >> true
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.UN_SUPPORT_PAYMENT_GROUP_GRAY_EI, tenantId) >> false
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.DATE_TIME_RANGE_GRAY_EI, tenantId) >> true
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.RICH_TEXT_GRAY_EI, tenantId) >> true
        Whitebox.setInternalState(DefObjConstants, "WEB_DETAIL_LAYOUT_FIELDS_GRAY", grayFieldsConfig1)
        Whitebox.setInternalState(DefObjConstants, "WEB_DETAIL_LAYOUT_FIELDS", fieldsConfig1)
        Whitebox.setInternalState(AppFrameworkConfig, "grayAllFieldTypeList", grayAllFieldTypeList1)
        Whitebox.setInternalState(AppFrameworkConfig, "grayObjectReferenceManyFieldListTenantid", ["ALL"])
        Whitebox.setInternalState(AppFrameworkConfig, "grayBigFileAttachmentFieldListEi", ["74255"])
        def fieldGrayConfig = ["big_file_attachment": AppFrameworkConfig.GrayRuleHelper.of('white:74255', ['AllFieldType'] as Set, 'file_attachment'),
                               "big_text"           : AppFrameworkConfig.GrayRuleHelper.of('white:74255', ['AllFieldType'] as Set, 'long_text')]
        Whitebox.setInternalState(AppFrameworkConfig, "fieldGrayConfig", fieldGrayConfig)

        when:
        def fields = DefObjConstants.getFieldTypeList(tenantId, describeApiName, false)
        then:
        println("describeApiName:" + describeApiName)
        containOutEmployee == fields.contains("out_employee")
        containReferenceMany == fields.contains("object_reference_many")
        containDataTimeRange == fields.contains("date_time_range")
        containHtmlRichText == fields.contains("html_rich_text")
        containRichText == fields.contains("rich_text")
        containBigFileAttachment == fields.contains("big_file_attachment")
        containBigText == fields.contains("big_text")
        indexBoolean == (fields.get(fields.indexOf("long_text") + 1) == "big_text")
        where:
        tenantId | gray | describeApiName      | containOutEmployee | containReferenceMany | containDataTimeRange | containHtmlRichText | containRichText | containBigFileAttachment | containBigText | indexBoolean
        '74255'  | true | 'object_a__c'        | true               | true                 | true                 | true                | true            | true                     | true           | true
        '74255'  | true | 'DepartmentObj'      | true               | false                | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'PersonnelObj'       | true               | true                 | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'ActiveRecordObj'    | true               | true                 | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'JournalObj'         | true               | true                 | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'TelesalesRecordObj' | true               | false                | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'ServiceLogObj'      | true               | false                | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'BlogObj'            | true               | false                | true                 | false               | true            | false                    | false          | false
        '74255'  | true | 'ScheduleObj'        | true               | false                | true                 | false               | true            | false                    | false          | false
        '78057'  | true | 'object_a__c'        | true               | true                 | true                 | true                | true            | false                    | false          | false
    }

}
