package com.facishare.paas.appframework.common.util

import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

class CollectionUtilsTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对集合类型的判断
     */
    @Unroll
    def "notEmpty collection test with #desc"() {
        expect:
        CollectionUtils.notEmpty(collection) == result

        where:
        desc                | collection        || result
        "非空集合"          | [1, 2, 3]         || true
        "空集合"            | []                || false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对集合类型的null值判断
     */
    def "notEmpty collection test with null"() {
        expect:
        // 使用下面的静态方法调用而不是实例方法调用
        boolean result = com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty((Collection) null)
        result == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对集合类型的判断
     */
    @Unroll
    def "empty collection test with #desc"() {
        expect:
        CollectionUtils.empty(collection) == result

        where:
        desc                | collection        || result
        "非空集合"          | [1, 2, 3]         || false
        "空集合"            | []                || true
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对集合类型的null值判断
     */
    def "empty collection test with null"() {
        expect:
        // 使用下面的静态方法调用而不是实例方法调用
        boolean result = com.facishare.paas.appframework.common.util.CollectionUtils.empty((Collection) null)
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对Map类型的判断
     */
    @Unroll
    def "empty map test with #desc"() {
        expect:
        CollectionUtils.empty(map) == result

        where:
        desc                | map               || result
        "非空Map"           | [a: 1, b: 2]      || false
        "空Map"             | [:]               || true
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对Map类型的null值判断
     */
    def "empty map test with null"() {
        expect:
        // 使用下面的静态方法调用而不是实例方法调用
        boolean result = com.facishare.paas.appframework.common.util.CollectionUtils.empty((Map) null)
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对Map类型的判断
     */
    @Unroll
    def "notEmpty map test with #desc"() {
        expect:
        CollectionUtils.notEmpty(map) == result

        where:
        desc                | map               || result
        "非空Map"           | [a: 1, b: 2]      || true
        "空Map"             | [:]               || false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对Map类型的null值判断
     */
    def "notEmpty map test with null"() {
        expect:
        // 使用下面的静态方法调用而不是实例方法调用
        boolean result = com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty((Map) null)
        result == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对List类型的转换
     */
    def "nullToEmpty list test"() {
        given:
        List<Integer> emptyList = []
        List<Integer> normalList = [1, 2, 3]

        expect:
        // 非null值测试
        CollectionUtils.nullToEmpty(emptyList).isEmpty()
        CollectionUtils.nullToEmpty(normalList) == normalList
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对List类型的null值转换
     */
    def "nullToEmpty list test with null"() {
        expect:
        // 明确指定类型并用完全限定名称调用方法
        def result = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty((List) null)
        result != null
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Set类型的转换
     */
    def "nullToEmpty set test"() {
        given:
        Set<Integer> emptySet = [] as Set
        Set<Integer> normalSet = [1, 2, 3] as Set

        expect:
        // 非null值测试
        CollectionUtils.nullToEmpty(emptySet).isEmpty()
        CollectionUtils.nullToEmpty(normalSet) == normalSet
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Set类型的null值转换
     */
    def "nullToEmpty set test with null"() {
        expect:
        // 明确指定类型并用完全限定名称调用方法
        def result = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty((Set) null)
        result != null
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Map类型的转换
     */
    def "nullToEmpty map test"() {
        given:
        Map<String, Integer> emptyMap = [:]
        Map<String, Integer> normalMap = [a: 1, b: 2]

        expect:
        // 非null值测试
        CollectionUtils.nullToEmpty(emptyMap).isEmpty()
        CollectionUtils.nullToEmpty(normalMap) == normalMap
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Map类型的null值转换
     */
    def "nullToEmpty map test with null"() {
        expect:
        // 明确指定类型并用完全限定名称调用方法
        def result = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty((Map) null)
        result != null
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试size方法获取集合大小
     */
    @Unroll
    def "size collection test with #desc"() {
        expect:
        CollectionUtils.size(collection) == result

        where:
        desc            | collection    || result
        "空集合"         | []            || 0
        "有3个元素的集合" | [1, 2, 3]     || 3
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试size方法对Collection的null值处理
     */
    def "size collection test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("size", Collection.class)
        
        expect:
        method.invoke(null, [null] as Object[]) == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试size方法获取Map大小
     */
    @Unroll
    def "size map test with #desc"() {
        expect:
        CollectionUtils.size(map) == result

        where:
        desc          | map           || result
        "空Map"       | [:]           || 0
        "有2个元素的Map" | [a: 1, b: 2] || 2
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试size方法对Map的null值处理
     */
    def "size map test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("size", Map.class)
        
        expect:
        method.invoke(null, [null] as Object[]) == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSingleton方法判断集合是否只有一个元素
     */
    @Unroll
    def "isSingleton test with #desc"() {
        expect:
        CollectionUtils.isSingleton(collection) == result

        where:
        desc            | collection    || result
        "空集合"         | []            || false
        "1个元素"        | [1]           || true
        "多个元素"        | [1, 2, 3]     || false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试isSingleton方法处理null值
     */
    def "isSingleton test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("isSingleton", Collection.class)
        
        expect:
        method.invoke(null, [null] as Object[]) == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试firstNotEmpty方法获取第一个非空集合
     */
    def "firstNotEmpty test 1"() {
        given:
        def emptyList = []
        def nullList = null
        def list1 = [1, 2, 3]
        def list2 = [4, 5]
        
        Collection<?> emptyCollection = emptyList
        Collection<?> nullCollection = nullList
        Collection<?> collection1 = list1
        Collection<?> collection2 = list2

        expect:
        CollectionUtils.firstNotEmpty([nullCollection, emptyCollection] as Collection[]) == null
        CollectionUtils.firstNotEmpty([nullCollection, collection1, collection2] as Collection[]) == collection1
        CollectionUtils.firstNotEmpty([emptyCollection, collection2] as Collection[]) == collection2
    }


    /**
     * GenerateByAI
     * 测试内容描述：测试concatAndNotContainEmpty方法合并多个列表并去除空值
     */
    def "concatAndNotContainEmpty test"() {
        given:
        def list1 = [1, null, 3]
        def list2 = [null, 3, ""]
        def list3 = ["", 4, 5]
        def emptyList = []
        def nullList = null

        expect:
        // 合并并去除空值
        CollectionUtils.concatAndNotContainEmpty(list1, list2, list3) == [1, 3, 3, 4, 5]
        // 忽略空列表
        CollectionUtils.concatAndNotContainEmpty(list1, emptyList, list3) == [1, 3, 4, 5]
        // 忽略null
        CollectionUtils.concatAndNotContainEmpty(list1, nullList, list3) == [1, 3, 4, 5]
        // 所有都为空时返回空列表
        CollectionUtils.concatAndNotContainEmpty(emptyList, nullList) == []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试splitMap方法将Map分割成多个小Map
     */
    def "splitMap test"() {
        given:
        def map = [a: 1, b: 2, c: 3, d: 4, e: 5]
        def emptyMap = [:]

        when:
        def result = CollectionUtils.splitMap(map, 2)

        then:
        result.size() == 3
        result[0] == [a: 1, b: 2]
        result[1] == [c: 3, d: 4]
        result[2] == [e: 5]

        when:
        def emptyResult = CollectionUtils.splitMap(emptyMap, 2)

        then:
        emptyResult.size() == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEqual方法比较两个集合是否相等
     */
    @Unroll
    def "isEqual test with #desc"() {
        expect:
        CollectionUtils.isEqual(a, b) == result

        where:
        desc                | a                 | b                 || result
        "a为空, b为空"      | []                | []                || true
        "不同内容"          | [1, 2]            | [3, 4]            || false
        "相同内容"          | [1, 2]            | [1, 2]            || true
        "相同内容不同顺序"   | [1, 2, 3]         | [3, 2, 1]         || true
        "a包含b"            | [1, 2, 3]         | [1, 2]            || false
        "b包含a"            | [1, 2]            | [1, 2, 3]         || false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试isEqual方法处理null值
     */
    def "isEqual test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("isEqual", Collection.class, Collection.class)
        
        expect:
        // 两个都为null
        method.invoke(null, null, null) == true
        // a为null, b为空
        method.invoke(null, null, []) == true
        // a为空, b为null
        method.invoke(null, [], null) == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortByGivenOrder方法根据给定顺序对列表进行排序
     */
    def "sortByGivenOrder test"() {
        given:
        def chaosList = [
            [id: 3, name: "c"],
            [id: 1, name: "a"],
            [id: 5, name: "e"],
            [id: 2, name: "b"]
        ]
        def orderedList = [1, 2, 3, 4]

        when:
        def result = CollectionUtils.sortByGivenOrder(chaosList, orderedList, { it.id })

        then:
        result.collect { it.id } == [1, 2, 3, 5]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortByGivenOrder方法在边界情况下的处理
     */
    def "sortByGivenOrder edge cases test"() {
        given:
        def emptyChaosList = []
        def emptyOrderedList = []
        def normalChaosList = [[id: 1], [id: 2]]
        def normalOrderedList = [1, 2]

        expect:
        // 当chaosList为空时，直接返回
        CollectionUtils.sortByGivenOrder(emptyChaosList, normalOrderedList, { it.id }) == emptyChaosList
        // 当orderedList为空时，直接返回
        CollectionUtils.sortByGivenOrder(normalChaosList, emptyOrderedList, { it.id }) == normalChaosList
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addIfAbsent方法添加不存在的元素
     */
    def "addIfAbsent test"() {
        given:
        def list1 = [[id: 1, name: "a"], [id: 2, name: "b"]]
        def list2 = [[id: 2, name: "b"], [id: 3, name: "c"]]
        def equals = { a, b -> a.id == b.id }

        when:
        def result = CollectionUtils.addIfAbsent(list1, list2, equals)

        then:
        result.size() == 3
        result.collect { it.id } == [1, 2, 3]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addIfAbsent方法在边界情况下的处理
     */
    def "addIfAbsent edge cases test"() {
        given:
        def equals = { a, b -> a == b }
        def list1 = [1, 2, 3]
        def emptyList = []
        def nullList = null

        expect:
        // 当list2为空时，返回list1副本
        CollectionUtils.addIfAbsent(list1, emptyList, equals) == list1
        // 当list2为null时，返回list1副本
        CollectionUtils.addIfAbsent(list1, nullList, equals) == list1
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNotEmpty方法获取第一个非空集合
     */
    def "firstNotEmpty test 2"() {
        given:
        def emptyList = []
        def list1 = [1, 2, 3]
        def list2 = [4, 5]

        expect:
        // 返回第一个非空集合
        CollectionUtils.firstNotEmpty(list1, list2) == list1
        CollectionUtils.firstNotEmpty(emptyList, list2) == list2
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试firstNotEmpty方法处理null值
     */
    def "firstNotEmpty test with null"() {
        given:
        def nullList = null
        def emptyList = []
        def list1 = [1, 2, 3]

        expect:
        // 所有集合都为空时返回null
        CollectionUtils.firstNotEmpty(nullList, emptyList) == null
        // 忽略null值，返回第一个非空集合
        CollectionUtils.firstNotEmpty(nullList, list1) == list1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试concatAndDistinct方法合并多个列表并去重
     */
    def "concatAndDistinct test"() {
        given:
        def list1 = [1, 2, 3]
        def list2 = [2, 3, 4]
        def list3 = [3, 4, 5]
        def emptyList = []
        def nullList = null

        expect:
        // 合并并去重
        CollectionUtils.concatAndDistinct(list1, list2, list3) == [1, 2, 3, 4, 5]
        // 忽略空列表
        CollectionUtils.concatAndDistinct(list1, emptyList, list3) == [1, 2, 3, 4, 5]
        // 忽略null
        CollectionUtils.concatAndDistinct(list1, nullList, list3) == [1, 2, 3, 4, 5]
        // 所有都为空时返回空列表
        CollectionUtils.concatAndDistinct(emptyList, nullList) == []
    }



} 