import com.facishare.paas.appframework.common.service.GeoAddressProxy
import com.facishare.paas.appframework.common.service.GeoAddressService
import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO
import com.facishare.paas.appframework.common.service.impl.GeoAddressServiceImpl
import com.facishare.paas.appframework.core.model.User
import spock.lang.Specification

class GeoAddressServiceImplTest extends Specification {

    GeoAddressService geoAddressService
    GeoAddressProxy geoAddressProxy = Mock(GeoAddressProxy)

    def setup() {
        geoAddressService = new GeoAddressServiceImpl(geoAddressProxy: geoAddressProxy)
    }

    def "测试 getAddressByGeo 方法"() {
        given:
        def request = new GeoAddressDTO.ReverseGeoRequest(longitude: 116.397128, latitude: 39.916527)

        when:
        def restResult = new GeoAddressDTO.RestResult()
        restResult.setCode('200')
        restResult.setData new GeoAddressDTO.GeoResult(address: "北京市海淀区")
        geoAddressProxy.getAddressByGeo(_, _) >> restResult
        def result = geoAddressService.getAddressByGeo(User.systemUser('74255'), request)

        then:
        result != null
        // 这里可以添加更多的断言来验证返回结果
    }
} 