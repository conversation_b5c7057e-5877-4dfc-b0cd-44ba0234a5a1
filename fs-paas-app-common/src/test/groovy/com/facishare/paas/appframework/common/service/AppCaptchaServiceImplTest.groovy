package com.facishare.paas.appframework.common.service

import com.facishare.fsc.api.model.CreateValidateCode
import com.facishare.fsc.api.service.CreateImageService
import com.facishare.paas.appframework.core.model.User
import com.facishare.userlogin.api.model.captcha.BuildCodeDto
import com.facishare.userlogin.api.model.captcha.VerifyCodeDto
import com.facishare.userlogin.api.service.ImageCodeService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class AppCaptchaServiceImplTest extends Specification {
    def imageCodeService = Mock(ImageCodeService)
    def createImageService = Mock(CreateImageService)
    AppCaptchaServiceImpl appCaptchaServiceImpl
    User user = User.systemUser("74255")

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        appCaptchaServiceImpl = new AppCaptchaServiceImpl()
        appCaptchaServiceImpl.setImageCodeService(imageCodeService)
        appCaptchaServiceImpl.setCreateImageService(createImageService)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证码功能，验证传入完整参数时能正确生成验证码和图片
     */
    @Unroll
    def "createCaptchaCodeTest_WithFullParams"() {
        given:
        def captchaCode = "1234"
        def captchaId = "abcd"
        def expireSeconds = 180
        
        def codeRes = new BuildCodeDto.Result()
        codeRes.setId("genId")
        codeRes.setCode("genCode")
        
        def imgRes = new CreateValidateCode.Result()
        imgRes.data = "image_data".bytes

        when:
        def result = appCaptchaServiceImpl.createCaptchaCode(user, captchaCode, captchaId, expireSeconds)

        then:
        1 * imageCodeService.buildCode(_) >> { args ->
            def arg = args[0] as BuildCodeDto.Argument
            assert arg.code == captchaCode
            assert arg.id == captchaId
            assert arg.expireSeconds == expireSeconds
            return codeRes
        }
        
        1 * createImageService.createValidateCode(_) >> { args ->
            def arg = args[0] as CreateValidateCode.Arg
            assert arg.code == "genCode"
            return imgRes
        }
        
        result.epxId == "genId"
        result.data == "image_data".bytes
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证码功能，验证不传参数时使用默认值(180秒)生成验证码
     */
    def "createCaptchaCodeTest_WithDefaultParams"() {
        when:
        appCaptchaServiceImpl.createCaptchaCode(user)
        
        then:
        1 * imageCodeService.buildCode(_) >> { args ->
            def arg = args[0] as BuildCodeDto.Argument
            assert arg.code == null
            assert arg.id == null
            assert arg.expireSeconds == 180
            return new BuildCodeDto.Result()
        }
        
        1 * createImageService.createValidateCode(_) >> new CreateValidateCode.Result()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证码验证功能，验证正确的验证码返回成功
     */
    def "verifyCaptchaCodeTest_Success"() {
        given:
        def captchaCode = "1234"
        def captchaId = "abcd"
        
        def verifyResult = new VerifyCodeDto.Result()
        verifyResult.setMatch(true)
        
        when:
        def result = appCaptchaServiceImpl.verifyCaptchaCode(user, captchaCode, captchaId)
        
        then:
        1 * imageCodeService.verifyCode(_) >> { args ->
            def arg = args[0] as VerifyCodeDto.Argument
            assert arg.code == captchaCode
            assert arg.id == captchaId
            return verifyResult
        }
        
        result.success == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证码验证功能，验证错误的验证码返回失败
     */
    def "verifyCaptchaCodeTest_Failure"() {
        given:
        def captchaCode = "1234"
        def captchaId = "abcd"
        
        def verifyResult = new VerifyCodeDto.Result()
        verifyResult.setMatch(false)
        
        when:
        def result = appCaptchaServiceImpl.verifyCaptchaCode(user, captchaCode, captchaId)
        
        then:
        1 * imageCodeService.verifyCode(_) >> { args ->
            def arg = args[0] as VerifyCodeDto.Argument
            assert arg.code == captchaCode
            assert arg.id == captchaId
            return verifyResult
        }
        
        result.success == false
    }
}