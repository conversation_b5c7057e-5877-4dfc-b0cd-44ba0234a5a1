package com.facishare.paas.appframework.common.service

import com.facishare.function.biz.api.model.FuncExecuteContext
import com.facishare.function.biz.api.model.FuncUser
import com.facishare.function.biz.api.service.FunctionService
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.dto.*
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.userlogin.api.model.validatecode.BuildValidateCode
import com.facishare.userlogin.api.model.validatecode.VerifyValidateCode
import com.facishare.userlogin.api.service.ImageCodeService
import com.facishare.userlogin.api.service.ValidateCodeService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Field

class PhoneNumberServiceImplTest extends Specification {
    def tenantId = "74255"
    def phoneNumberInformationProxy = Mock(PhoneNumberInformationProxy)
    def phoneNumberInfoV2Proxy = Mock(PhoneNumberInfoV2Proxy)
    def smsServiceProxy = Mock(SmsServiceProxy)
    def functionService = Mock(FunctionService)
    def validateCodeService = Mock(ValidateCodeService)
    def imageCodeService = Mock(ImageCodeService)
    def appCaptchaService = Mock(AppCaptchaService)
    def smsCodeService = Mock(SmsCodeService)
    
    PhoneNumberServiceImpl phoneNumberService
    RequestContext requestContext
    User user

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
        
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> false
    }

    def setup() {
        phoneNumberService = new PhoneNumberServiceImpl(
                phoneNumberInformationProxy: phoneNumberInformationProxy,
                phoneNumberInfoV2Proxy: phoneNumberInfoV2Proxy,
                smsServiceProxy: smsServiceProxy,
                functionService: functionService
        )
        phoneNumberService.setCaptchaService(appCaptchaService)
        phoneNumberService.setSmsCodeService(smsCodeService)
        
        requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .build()
        RequestContextManager.setContext(requestContext)
        
        user = User.systemUser(tenantId)
        

    }

    def cleanup() {
        RequestContextManager.removeContext()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询企业短信服务开通状态方法
     */
    @Unroll
    def "checkSmsStatus test with status #smsStatus"() {
        given:
        smsServiceProxy.checkSmsStatus(_ as CheckSmsStatus.Arg) >> new CheckSmsStatus.Result(status: smsStatus)

        expect:
        phoneNumberService.checkSmsStatus(tenantId) == expected

        where:
        smsStatus | expected
        1         | false
        0         | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试刷新图形验证码方法
     */
    def "refreshCaptcha test"() {
        given:
        def captchaResult = Captcha.Result.builder()
                .epxId("captchaId")
                .data("captchaData".bytes)
                .build()
        
        when:
        def result = phoneNumberService.refreshCaptcha()
        
        then:
        1 * appCaptchaService.createCaptchaCode(null) >> captchaResult
        result != null
        result.epxId == "captchaId"
        result.data != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试校验短信验证码方法
     */
    def "checkVerificationCode test"() {
        given:
        def areaCode = "+86"
        def phone = "13800138000"
        def smsCode = "1234"
        
        when:
        def result = phoneNumberService.checkVerificationCode(areaCode, phone, smsCode)
        
        then:
        1 * smsCodeService.verifySmsCode(null, areaCode, phone, smsCode) >> "SUCCESS"
        result == "SUCCESS"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量查询电话号码信息方法
     */
    def "batchQueryPhoneNumberInfo test"() {
        given:
        def mobileSet = ["13800138000", "13900139000"] as Set
        def phoneInfoResult1 = new QueryPhoneNumberInformation.Result(
                mobile: "13800138000", 
                province: "北京", 
                city: "北京", 
                operator: "",
                code: "010"
        )
        def phoneInfoResult2 = new QueryPhoneNumberInformation.Result(
                mobile: "13900139000", 
                province: "上海", 
                city: "上海", 
                operator: "",
                code: "021"
        )
        def phoneInfoResults = [phoneInfoResult1, phoneInfoResult2]
        
        when:
        def result = phoneNumberService.batchQueryPhoneNumberInfo(mobileSet)
        
        then:
        1 * phoneNumberInformationProxy.queryBatchQueryPhoneNumberInformation(_ as QueryPhoneNumberInformation.Mobiles, _) >> 
                new QueryPhoneNumberInformation.Code(errCode: 200, results: phoneInfoResults)
        
        result.size() == 2
        result.any { it.mobile == "13800138000" && it.province == "北京" && it.operator == "" }
        result.any { it.mobile == "13900139000" && it.province == "上海" && it.operator == "" }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空集合的情况下批量查询电话号码信息方法
     */
    def "batchQueryPhoneNumberInfo with empty set test"() {
        given:
        def emptySet = [] as Set
        
        when:
        def result = phoneNumberService.batchQueryPhoneNumberInfo(emptySet)
        
        then:
        0 * phoneNumberInformationProxy.queryBatchQueryPhoneNumberInformation(_, _)
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询单个电话号码信息方法
     */
    def "queryPhoneNumberInfo test"() {
        given:
        def mobile = "13800138000"
        def phoneInfoResult = new QueryPhoneNumberInformation.Result(
                mobile: mobile, 
                province: "北京", 
                city: "北京", 
                operator: "",
                code: "010"
        )
        
        when:
        def result = phoneNumberService.queryPhoneNumberInfo(mobile)
        
        then:
        1 * phoneNumberInformationProxy.queryPhoneNumberInformation(_ as Map, _) >> 
                new QueryPhoneNumberInformation.Code(errCode: 200, results: [phoneInfoResult])
        
        result != null
        result.mobile == mobile
        result.province == "北京"
        result.city == "北京"
        result.operator == ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当电话号码为空时查询电话号码信息方法
     */
    def "queryPhoneNumberInfo with empty mobile test"() {
        given:
        def emptyMobile = ""
        
        when:
        def result = phoneNumberService.queryPhoneNumberInfo(emptyMobile)
        
        then:
        0 * phoneNumberInformationProxy.queryPhoneNumberInformation(_, _)
        result != null
        result.mobile == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成短信验证码方法
     */
    def "generateVerificationCode test"() {
        given:
        def mobile = "13800138000"
        def ip = "127.0.0.1"
        def captchaCode = "1234"
        def captchaId = "abcd"
        def expireTime = 300
        
        def captchaVerifyResult = CaptchaVerify.Result.builder().success(true).build()
        def buildValidateCodeResult = new BuildValidateCode.Result(result: BuildValidateCode.SendValidateCodeEnum.SUCCESS, code: "123456")
        
        when:
        def result = phoneNumberService.generateVerificationCode(user, mobile, ip, captchaCode, captchaId, expireTime)
        
        then:
        // 实际代码没有调用verifyCaptchaCode，所以注释掉这个期望
        // 1 * appCaptchaService.verifyCaptchaCode(user, captchaCode, captchaId) >> captchaVerifyResult
        // 调用createSmsCode方法
        1 * smsCodeService.createSmsCode(user, ip, null, mobile, captchaCode, captchaId, expireTime) >> buildValidateCodeResult
        
        result != null
        result.status == "SUCCESS"
        result.code == "123456"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当图形验证码验证失败时生成短信验证码方法
     */
    def "generateVerificationCode with invalid captcha test"() {
        given:
        def mobile = "13800138000"
        def ip = "127.0.0.1"
        def captchaCode = "1234"
        def captchaId = "abcd"
        def expireTime = 300
        
        // 注意：在此测试中实际并不调用verifyCaptchaCode，但创建了一个空结果模拟验证失败
        def buildValidateCodeResult = new BuildValidateCode.Result(result: null)
        
        when:
        def result = phoneNumberService.generateVerificationCode(user, mobile, ip, captchaCode, captchaId, expireTime)
        
        then:
        // 实际代码没有调用verifyCaptchaCode，所以不需要这个期望
        // 1 * appCaptchaService.verifyCaptchaCode(user, captchaCode, captchaId) >> captchaVerifyResult
        
        // 但会调用createSmsCode，只是返回的result.getResult()为null
        1 * smsCodeService.createSmsCode(user, ip, null, mobile, captchaCode, captchaId, expireTime) >> buildValidateCodeResult
        
        result != null
        result.status == null
        result.code == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试发送短信验证码方法
     */
    def "sendSmsCode test"() {
        given:
        def ea = "testEa"
        def ip = "127.0.0.1"
        def areaCode = "+86"
        def mobile = "13800138000"
        def captchaCode = "1234"
        def captchaId = "abcd"
        
        when:
        def result = phoneNumberService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId)
        
        then:
        1 * smsCodeService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId, 
                                      "CRM", "register.default_sm_validate_code_content") >> "SUCCESS"
        
        result == "SUCCESS"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证短信验证码方法
     */
    def "verifySmsCode test"() {
        given:
        def areaCode = "+86"
        def mobile = "13800138000"
        def smsCode = "1234"
        
        when:
        def result = phoneNumberService.verifySmsCode(user, areaCode, mobile, smsCode)
        
        then:
        1 * smsCodeService.verifySmsCode(user, areaCode, mobile, smsCode) >> "SUCCESS"
        
        result == "SUCCESS"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过APL发送验证码方法
     */
    def "sendCodeByAPL test"() {
        given:
        def functionApiName = "testFunction"
        def methodName = "testMethod"
        def arg = GenerateVerificationCode.SendVerificationCodeArg.builder()
                .objectApiName("Object")
                .fieldApiName("Phone")
                .verificationCode("1234")
                .objectData(["mobile": "13800138000"])
                .build()
        
        when:
        phoneNumberService.sendCodeByAPL(user, functionApiName, methodName, arg)
        
        then:
        1 * functionService.executeFuncMethod(_, functionApiName, methodName, _, 0) >> { args ->
            def context = args[0] as FuncExecuteContext
            assert context.getUser() instanceof FuncUser
            assert context.getUser().getUserId() == user.getUserId()
            
            def requestBody = args[3] as List<String>
            assert requestBody.size() == 1
            def jsonString = requestBody[0]
            def argsMap = JacksonUtils.fromJson(jsonString, Map.class)
            assert argsMap.verificationCode == arg.verificationCode
            assert argsMap.objectApiName == arg.objectApiName
            
            // 返回有效的JSON格式响应，而不是简单的"success"字符串
            return "{\"success\":true,\"errorMessage\":null}"
        }
    }
} 