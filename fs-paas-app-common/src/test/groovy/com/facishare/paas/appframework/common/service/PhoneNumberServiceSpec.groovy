package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.Captcha
import com.facishare.paas.appframework.common.service.dto.CheckSmsStatus
import com.facishare.paas.appframework.common.service.dto.VerifyValidateCode
import com.facishare.paas.appframework.core.model.User
import com.facishare.userlogin.api.model.captcha.BuildCodeDto
import com.facishare.userlogin.api.service.ImageCodeService
import com.facishare.userlogin.api.service.ValidateCodeService
import spock.lang.Specification

class PhoneNumberServiceSpec extends Specification {
	String tenantId = "74255"
	def smsProxy = Mock(SmsServiceProxy)
	def validateCodeService = Mock(ValidateCodeService)
	def imageCodeService = Mock(ImageCodeService)
	def appCaptchaService = Mock(AppCaptchaService)
	PhoneNumberServiceImpl phoneNumberService = new PhoneNumberServiceImpl(
		"smsServiceProxy": smsProxy,
		"validateCodeService": validateCodeService,
	)

	def setup() {
		phoneNumberService.setCaptchaService(appCaptchaService)
	}


	def "test checkSmsStatus"() {
		given:
		smsProxy.checkSmsStatus(_ as CheckSmsStatus.Arg) >> smsStatus

		expect:
		result == phoneNumberService.checkSmsStatus(ei)

		where:
		ei  | smsStatus                              || result
		"1" | new CheckSmsStatus.Result("status": 1) || false
		"2" | new CheckSmsStatus.Result("status": 0) || true
	}

	def "test refreshCaptcha"() {
		when:
		appCaptchaService.createCaptchaCode(_ as User) >> Captcha.Result.builder().build()
		phoneNumberService.refreshCaptcha()
		then:
		1 * appCaptchaService.createCaptchaCode(null)
	}


	def "test"() {
		when:
		phoneNumberService.checkVerificationCode("+86", "123456", "1234")
		then:
		1 * phoneNumberService.validateCodeService.verifyCode(_) >> new VerifyValidateCode.Result()
		noExceptionThrown()
	}
}
