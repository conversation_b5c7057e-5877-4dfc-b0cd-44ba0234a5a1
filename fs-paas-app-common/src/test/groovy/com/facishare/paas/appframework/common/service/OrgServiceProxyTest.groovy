package com.facishare.paas.appframework.common.service

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import com.facishare.paas.appframework.common.service.dto.QueryUserInfoByIds
import spock.lang.Specification

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/8/18.
 */

class OrgServiceProxyTest extends Specification {

//    @Autowired
//    OrgServiceProxy orgServiceProxy
//
//    def "test queryUserInfoByIds"() {
//        given:
//        QueryUserInfoByIds.Arg arg = QueryUserInfoByIds.Arg.builder()
//                .appId("CRM")
//                .tenantId(tenantId)
//                .userId(userId).ids(ids).build()
//        when:
//        def ret = orgServiceProxy.queryUserInfoByIds(arg)
//        then:
//        ret.result.size() == 1
//        where:
//        tenantId | userId | ids
//        "7"      | "1000" | ["1000"]
//    }

}
