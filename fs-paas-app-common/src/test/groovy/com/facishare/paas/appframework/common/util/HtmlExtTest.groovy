package com.facishare.paas.appframework.common.util


import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfigFactory
import org.jsoup.nodes.Element
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([StoneCGIConfig, ConfigFactory])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*"])
class HtmlExtTest extends Specification {
    def setup() {
        PowerMockito.mockStatic(ConfigFactory)
        def configFactory = Mock(IConfigFactory)
        PowerMockito.when(ConfigFactory.getInstance()).thenReturn(configFactory)
    }

    def "test format image path"() {
        given:
        PowerMockito.mockStatic(StoneCGIConfig)
        PowerMockito.when(StoneCGIConfig.getFileShareSkey()).thenReturn('1111111111111111')
        def element = new Element('img')
        element.attr('src', originalUrl)
        def htmlElement = HtmlExt.HtmlElementExt.of(element)
        when:
        htmlElement.formatImagePath()
        then:
        element.attr("src") == formattedUrl
        where:
        originalUrl | formattedUrl
        '/o/123/jpg' | '/i/123/jpg'
        '/image/s/5356E9242EFF24C4E77ABB7451C23BFC1D2E7355D4787F61310E99856DF7A98346E19795573D647CC6EE268FB90A0D7D800E3CB29AD5E01A9156FC7452B4F58067C3EC7C16836787F9C1E70646F751942B5A058A65C26A92/0*300/jpg' | '/image/i/N_202111_20_5e8c3e066b20463082fbddaa06c25800/0*300/jpg'
        'https://url.com/some/path.jpg' | 'https://url.com/some/path.jpg'
        'https://attachment.outlook.live.net/owa/MSA%3A471102375%40qq.com/service.svc/s/GetAttachmentThumbnail?id=AQMkADAwATM0MDAAMS0wNzI1LWI4Y2YtMDACLTAwCgBGAAADlmJondGRF0WFtkLqBOxGnAcAZ4TBcEsDOU2B6GlQQckNUwAAAgEJAAAAZ4TBcEsDOU2B6GlQQckNUwAFq12WaQAAAAESABAAcjErhuzLD0%2BQjvhsHTPn4g%3D%3D&thumbnailType=2&isc=1&token=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&X-OWA-CANARY=DIv4JGv6FUCiwU_JgQXrsEBW5ag_FtwYM_CIqtHUlvIpnbFWohtiVfaozyj6qJmGXCunsh03qe8.&owa=outlook.live.com&scriptVer=20240105004.04&clientId=F746A0AA28F74B80AC1597FF5EFCDEAF&animation=true' | 'https://attachment.outlook.live.net/owa/MSA%3A471102375%40qq.com/service.svc/s/GetAttachmentThumbnail?id=AQMkADAwATM0MDAAMS0wNzI1LWI4Y2YtMDACLTAwCgBGAAADlmJondGRF0WFtkLqBOxGnAcAZ4TBcEsDOU2B6GlQQckNUwAAAgEJAAAAZ4TBcEsDOU2B6GlQQckNUwAFq12WaQAAAAESABAAcjErhuzLD0%2BQjvhsHTPn4g%3D%3D&thumbnailType=2&isc=1&token=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&X-OWA-CANARY=DIv4JGv6FUCiwU_JgQXrsEBW5ag_FtwYM_CIqtHUlvIpnbFWohtiVfaozyj6qJmGXCunsh03qe8.&owa=outlook.live.com&scriptVer=20240105004.04&clientId=F746A0AA28F74B80AC1597FF5EFCDEAF&animation=true'
        'https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_6bddc8ea554448c093294813e07df1cc&appId=FSAID_11490c84' | 'https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_6bddc8ea554448c093294813e07df1cc&appId=FSAID_11490c84'
    }

    def 'test getAll image path'() {
        given:
        HtmlExt htmlExt = HtmlExt.of(url)
        when:
        def imageSrc = htmlExt.getAllTempImageSrc()
        then:
        imageSrc.size() == size
        imageSrc.containsKey(TNPATH) == contains
        where:
        url                                                                                                                                                                                                                                                                    | TNPATH                                          | size | contains
        '<p><img src="https://yxy.zsjr002511.com/FSC/EM/File/ViewTempImg?TempFileName=TN_6bddc8ea554448c093294813e07df1cc" style="max-width: 100%;"></p>'                                                                                                                      | 'TN_6bddc8ea554448c093294813e07df1cc'           | 1    | true
        '<p><img src="https://img.fxiaoke.com/image/s/85D7CEEA2656ACA35F6EA30D926F1616F533AA89829876D352B0C5239A682CEAE4D20788590476B9F40FF2CA0DDB20115FCFE960A2F40DE8E8B34315A8E6FC24667EE3ECE6C28F3D00443EEC9D8689F61BFD3936F5E4F6F4/0*0/jpg" style="max-width: 100%;"></p>' | _                                               | 0    | false
        '<p><img src="https://img.fxiaoke.com/image/i/N_202411_28_b6a09016bda4413691d73ee8b72fc297/0*0/jpg" style="max-width: 100%;"></p >'                                                                                                                                    | _                                               | 0    | false
        '<p><img src="https://img.fxiaoke.com/image/i/TN_202411_28_b6a09016bda4413691d73ee8b72fc297/0*0/jpg" style="max-width: 100%;"></p >'                                                                                                                                   | 'TN_202411_28_b6a09016bda4413691d73ee8b72fc297' | 1    | true
    }
}