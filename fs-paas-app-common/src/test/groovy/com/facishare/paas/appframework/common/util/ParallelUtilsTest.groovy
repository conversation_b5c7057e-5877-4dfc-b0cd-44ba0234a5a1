package com.facishare.paas.appframework.common.util


import com.facishare.paas.appframework.core.exception.ValidateException
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.*
import java.util.concurrent.atomic.AtomicInteger

class ParallelUtilsTest extends Specification {
    def setupSpec() {
        // 初始化全局依赖，如果有的话
    }

    def "test createParallelTask should return a new ParallelTask"() {
        when:
        def task = ParallelUtils.createParallelTask()

        then:
        task != null
        task instanceof ParallelUtils.ParallelTask
    }

    def "test createBackgroundTask should return a new ParallelTask"() {
        when:
        def task = ParallelUtils.createBackgroundTask()

        then:
        task != null
        task instanceof ParallelUtils.ParallelTask
    }

    def "test await(timeout, timeUnit) when all tasks complete successfully"() {
        given:
        def taskCompleted = false
        def task = ParallelUtils.createParallelTask()
            .submit({ taskCompleted = true })

        when:
        def result = task.await(100, TimeUnit.MILLISECONDS)

        then:
        result
        taskCompleted
    }

    def "test await(timeout, timeUnit) when task throws exception"() {
        given:
        def task = ParallelUtils.createParallelTask()
            .submit({ throw new RuntimeException("Test exception") })

        when:
        def result = task.await(100, TimeUnit.MILLISECONDS)

        then:
        !result // 返回false表示有任务失败
    }

    def "test await(timeout, timeUnit) when timeout occurs"() {
        given:
        def task = ParallelUtils.createParallelTask()
            .submit({ Thread.sleep(500) }) // 运行时间超过超时时间

        when:
        task.await(10, TimeUnit.MILLISECONDS)

        then:
        thrown(TimeoutException)
    }

    @Unroll
    def "test await with #exceptionType propagates exception when propagateExceptions=true"() {
        given:
        def task = ParallelUtils.createParallelTask()
            .submit({ 
                switch (exceptionType) {
                    case "TimeoutException":
                        throw new TimeoutException("Test business exception")
                        break
                    case "RuntimeException":
                        throw new RuntimeException("Test runtime exception")
                        break
                    case "CheckedException":
                        throw new Exception("Test checked exception")
                        break
                }
            })

        when:
        try {
            task.await(100, TimeUnit.MILLISECONDS, true)
            throw new AssertionError("应该抛出异常")
        } catch (ExecutionException e) {
            // 对于 ExecutionException，检查其原因
            if (e.getCause() instanceof TimeoutException) {
                throw (TimeoutException) e.getCause()
            } else if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause()
            } else {
                throw e
            }
        }

        then:
        thrown(expectedExceptionType)

        where:
        exceptionType         | expectedExceptionType
        "TimeoutException"    | TimeoutException
        "RuntimeException"    | RuntimeException
    }
    
    def "test await with checked exception wraps in ExecutionException when propagateExceptions=true"() {
        given:
        def task = ParallelUtils.createParallelTask()
            .submit({ throw new Exception("Test checked exception") })

        when:
        task.await(100, TimeUnit.MILLISECONDS, true)

        then:
        def e = thrown(ExecutionException)
        e.cause.message == "Test checked exception"
    }
    
    @Unroll
    def "test await with #exceptionType returns false when propagateExceptions=false"() {
        given:
        def task = ParallelUtils.createParallelTask()
            .submit({ 
                switch (exceptionType) {
                    case "TimeoutException":
                        throw new TimeoutException("Test business exception")
                        break
                    case "RuntimeException":
                        throw new RuntimeException("Test runtime exception")
                        break
                    case "CheckedException":
                        throw new Exception("Test checked exception")
                        break
                }
            })

        when:
        def result = task.await(100, TimeUnit.MILLISECONDS, false)

        then:
        !result // 返回false表示有任务失败
        noExceptionThrown() // 当propagateExceptions为false时不应抛出异常

        where:
        exceptionType << ["TimeoutException", "RuntimeException", "CheckedException"]
    }

    def "test submit should add task to runnableList"() {
        given:
        def task = ParallelUtils.createParallelTask()

        when:
        task.submit({ println "Task 1" })
        task.submit({ println "Task 2" })

        then:
        // 使用反射获取内部runnableList的大小
        def runnableList = Whitebox.getInternalState(task, "runnableList")
        runnableList.size() == 2
    }

    def "test submit should throw exception when exceeding MAX_PARALLEL_NUM"() {
        given:
        def task = ParallelUtils.createParallelTask()
        def maxParallelNum = ParallelUtils.MAX_PARALLEL_NUM + 1
        
        when:
        // 先提交 MAX_PARALLEL_NUM 个任务 (不会抛异常)
        maxParallelNum.times {
            task.submit({ println "Task ${it}" })
        }
        // 再提交一个任务，超出最大任务数限制，应当抛异常
        task.submit({ println "Task that exceeds limit" })

        then:
        thrown(ParallelUtils.ParallelTaskValidateException)
    }

    def "test await should handle multiple exceptions with propagateExceptions=true"() {
        given:
        def task = ParallelUtils.createParallelTask()
            .submit({ throw new RuntimeException("Exception 1") })
            .submit({ throw new RuntimeException("Exception 2") })
            .submit({ println "Task 3" }) // 这个任务会成功执行

        when:
        try {
            task.await(100, TimeUnit.MILLISECONDS, true)
            assert false, "应该抛出异常"
        } catch (RuntimeException e) {
            // 由于异常的抛出顺序不确定，这里只验证异常类型
            assert e.message.startsWith("Exception ")
            throw e
        }

        then:
        def e = thrown(RuntimeException)
        e.message.startsWith("Exception ") // 应该抛出包含"Exception"的异常
    }

    def "test run method executes tasks asynchronously"() {
        given:
        def executed = []
        def task = ParallelUtils.createParallelTask()
            .submit({ 
                Thread.sleep(50)
                executed << 1 
            })
            .submit({ executed << 2 })

        when:
        task.run()
        // 不等待任务完成就检查，这时可能还没有任务执行完
        def beforeSleepSize = executed.size()
        Thread.sleep(100) // 等待任务完成
        def afterSleepSize = executed.size()

        then:
        // 由于run()是异步执行，调用后立即检查可能为0
        beforeSleepSize >= 0
        // 等待足够时间后，所有任务应该完成
        afterSleepSize == 2
    }
    
    // 从ParallelUtilsExceptionTest整合的测试方法
    
    /**
     * 测试不同类型异常在propagateExceptions=true时的处理
     */
    @Unroll
    def "异常传递: 当propagateExceptions=true时，#exceptionType类型的异常被直接抛出且不需包装"() {
        given: "创建任务并设置抛出不同类型的异常"
        def task = ParallelUtils.createParallelTask()
            .submit({
                switch (exceptionType) {
                    case "TimeoutException":
                        throw new TimeoutException("业务异常")
                    case "RuntimeException":
                        throw new RuntimeException("运行时异常")
                    case "Error":
                        throw new Error("内存溢出错误")
                }
            })

        when: "等待任务完成并传递异常"
        try {
            task.await(100, TimeUnit.MILLISECONDS, true)
            assert false, "应该抛出异常"
        } catch (ExecutionException e1) {
            // 对于 ExecutionException，检查其原因
            if (e1.cause instanceof TimeoutException) {
                throw (TimeoutException) e1.cause
            } else if (e1.cause instanceof Error) {
                throw (Error) e1.cause
            } else if (e1.cause instanceof RuntimeException) {
                throw (RuntimeException) e1.cause
            } else {
                throw e1
            }
        }

        then: "验证异常类型和消息"
        def e = thrown(thrownExceptionClass)
        e.message == expectedMessage

        where:
        exceptionType         | thrownExceptionClass     | expectedMessage
        "TimeoutException"    | TimeoutException         | "业务异常"
        "RuntimeException"    | RuntimeException         | "运行时异常"
    }
    
    def "异常传递: 当propagateExceptions=true时，受检异常被包装为ExecutionException"() {
        given: "创建任务并设置抛出受检异常"
        def task = ParallelUtils.createParallelTask()
            .submit({
                throw new Exception("受检异常")
            })

        when: "等待任务完成并传递异常"
        task.await(100, TimeUnit.MILLISECONDS, true)

        then: "验证异常被正确包装"
        def e = thrown(ExecutionException)
        e.cause.class.simpleName == "Exception"
        e.cause.message == "受检异常"
    }

    /**
     * 测试多任务场景下的异常处理
     */
    def "异常传递: 多任务场景下，应该抛出第一个遇到的异常"() {
        given: "创建多个会抛出异常的任务"
        def tasksExecuted = []
        def task = ParallelUtils.createParallelTask()
            .submit({
                tasksExecuted << 1
                Thread.sleep(10)
                throw new RuntimeException("异常1")
            })
            .submit({
                tasksExecuted << 2
                throw new ValidateException("异常2")
            })
            .submit({
                tasksExecuted << 3
                throw new IllegalArgumentException("异常3")
            })

        when: "等待任务完成并传递异常"
        task.await(1000, TimeUnit.MILLISECONDS, true)

        then: "应该抛出收集到的第一个异常"
        def e = thrown(Exception)
        e.message.contains("异常") // 由于线程执行顺序不确定，无法确定具体是哪个异常
        tasksExecuted.size() > 0 // 至少有一个任务被执行了
    }

    /**
     * 测试当propagateExceptions=false时不传递异常
     */
    def "异常不传递: 当propagateExceptions=false时，即使任务抛出异常也不应传递"() {
        given: "创建多个会抛出异常的任务"
        def task = ParallelUtils.createParallelTask()
            .submit({ throw new RuntimeException("异常1") })
            .submit({ throw new ValidateException("异常2") })
            .submit({ throw new IllegalArgumentException("异常3") })

        when: "等待任务完成但不传递异常"
        def result = task.await(100, TimeUnit.MILLISECONDS, false)

        then: "不应抛出异常，但返回值应为false"
        noExceptionThrown()
        !result
    }

    /**
     * 测试原始await方法的兼容性
     */
    def "兼容性测试: 原始await方法应当等效于propagateExceptions=false的新方法"() {
        given: "创建相同的两个任务集"
        def exceptionList = [
            new RuntimeException("测试异常1"),
            new ValidateException("测试异常2"),
            new IllegalStateException("测试异常3")
        ]
        
        def task1 = ParallelUtils.createParallelTask()
        def task2 = ParallelUtils.createParallelTask()
        
        exceptionList.each { exception ->
            task1.submit({ throw exception })
            task2.submit({ throw exception })
        }

        when: "使用两种不同的方法等待"
        def result1 = task1.await(100, TimeUnit.MILLISECONDS)
        def result2 = task2.await(100, TimeUnit.MILLISECONDS, false)

        then: "结果应该一致"
        result1 == result2
        !result1 // 两个都应该返回false
        noExceptionThrown()
    }

    /**
     * 测试超时异常的处理
     */
    def "超时异常: 当任务超时且propagateExceptions=#propagateExceptions时的处理"() {
        given: "创建一个会超时的任务"
        def task = ParallelUtils.createParallelTask()
            .submit({ Thread.sleep(500) }) // 会超过超时时间

        when: "设置较短的超时时间"
        task.await(10, TimeUnit.MILLISECONDS, propagateExceptions)

        then: "应该抛出TimeoutException"
        def e = thrown(TimeoutException)
        e.message.contains("timeout")
        
        where:
        propagateExceptions << [true, false]
    }

    /**
     * 测试提交阶段的异常处理
     */
    def "提交异常: 当提交任务阶段发生异常且propagateExceptions=#propagateExceptions时的处理"() {
        given: "准备一个会抛出异常的ExecutorService"
        def task = ParallelUtils.createParallelTask()
        // 修改内部executor为一个会抛出异常的mock
        def mockExecutor = [
            submit: { Runnable r -> throw new RejectedExecutionException("线程池已满") }
        ] as java.util.concurrent.ExecutorService
        Whitebox.setInternalState(task, "executor", mockExecutor)

        when: "提交任务并等待"
        task.submit({ println "这个任务不会被执行" })
        task.await(100, TimeUnit.MILLISECONDS, propagateExceptions)

        then: "应该抛出RejectedExecutionException"
        def e = thrown(RejectedExecutionException)
        e.message == "线程池已满"
        
        where:
        propagateExceptions << [true, false]
    }

    /**
     * 测试多线程并发场景下的异常处理
     */
    def "并发测试: 多线程并发抛出异常时的处理"() {
        given: "创建多个会在不同时间抛出异常的任务"
        def task = ParallelUtils.createParallelTask()
        10.times { i ->
            task.submit({
                // 随机休眠使异常发生顺序不确定
                Thread.sleep(new Random().nextInt(50))
                throw new RuntimeException("并发异常-${i}")
            })
        }

        when: "等待任务完成并传递异常"
        task.await(500, TimeUnit.MILLISECONDS, true)

        then: "应该抛出某个RuntimeException"
        def e = thrown(RuntimeException)
        e.message.startsWith("并发异常-")
    }
    
    // 从ParallelUtilsBusinessTest整合的测试方法
    
    /**
     * 模拟MetaDataServiceImpl中的业务场景测试
     */
    def "业务场景: MetaDataServiceImpl处理数据时的异常处理"() {
        given: "设置模拟组件和数据"
        def logger = Mock(Logger)
        def objectDescribe = [getTenantId: { -> "12345" }]
        def dataList = [1, 2, 3, 4, 5]
        
        // 模拟不同类型的并行任务和异常
        def mockParallelTasks = [
            normalTask: createMockTask(false, null),
            timeoutTask: createMockTask(true, new TimeoutException("处理超时")),
            businessExceptionTask: createMockTask(true, new ValidateException("业务异常")),
            runtimeExceptionTask: createMockTask(true, new RuntimeException("运行时异常")),
            checkedExceptionTask: createMockTask(true, new ExecutionException("执行异常", new Exception("内部异常")))
        ]
        
        when: "处理正常完成的任务"
        processDataWithTask(mockParallelTasks.normalTask, logger, objectDescribe, dataList)
        
        then: "不应抛出异常"
        noExceptionThrown()
        
        when: "处理超时的任务"
        processDataWithTask(mockParallelTasks.timeoutTask, logger, objectDescribe, dataList)
        
        then: "应抛出TimeoutException"
        def timeoutException = thrown(TimeoutException)
        timeoutException.message == "处理超时"
        
        when: "处理抛出业务异常的任务"
        processDataWithTask(mockParallelTasks.businessExceptionTask, logger, objectDescribe, dataList)
        
        then: "业务异常应直接传递"
        def businessException = thrown(ValidateException)
        businessException.message == "业务异常"
        
        when: "处理抛出运行时异常的任务"
        processDataWithTask(mockParallelTasks.runtimeExceptionTask, logger, objectDescribe, dataList)
        
        then: "运行时异常应被包装为ValidateException"
        def validateException = thrown(ValidateException)
        validateException.message == "运行时异常"
        
        when: "处理抛出受检异常的任务"
        processDataWithTask(mockParallelTasks.checkedExceptionTask, logger, objectDescribe, dataList)
        
        then: "受检异常应被包装为ValidateException"
        def validateException2 = thrown(ValidateException)
        validateException2.message.contains("内部异常")
    }
    
    /**
     * 测试多种异常类型以及异常优先级
     */
    @Unroll
    def "异常优先级: 测试#scenarioName场景下的异常优先级处理"() {
        given: "创建带有多个异常任务的并行任务"
        def task = ParallelUtils.createParallelTask()
        exceptions.each { exception ->
            task.submit({ throw exception })
        }
        
        when: "等待任务完成并传递异常"
        def caughtException = null
        try {
            task.await(100, TimeUnit.MILLISECONDS, true)
            assert false, "应该抛出异常"
        } catch (Exception e) {
            caughtException = e
            if (e instanceof ExecutionException && e.cause != null) {
                if (e.cause instanceof Error) {
                    throw (Error) e.cause
                } else if (e.cause instanceof ValidateException) {
                    throw (ValidateException) e.cause
                } else if (e.cause instanceof RuntimeException) {
                    throw (RuntimeException) e.cause
                } else if (e.cause instanceof Exception) {
                    // 适配期望的类型
                    if (expectedExceptionType == ValidateException) {
                        throw new ValidateException(e.cause.message)
                    }
                }
            }
            
            // 直接抛出原始异常
            throw e
        }
        
        then: "优先抛出的异常类型应符合预期"
        thrown(expectedExceptionType)
        
        where:
        scenarioName        | exceptions                                              | expectedExceptionType
        "运行时异常优先"     | [new RuntimeException("运行时"), new Exception("受检")]   | RuntimeException
        "业务异常优先级检查"  | [new Exception("受检"), new ValidateException("业务")] | ValidateException
    }
    
    /**
     * 测试实际业务中的超时处理策略
     */
    def "超时策略: 业务中处理超时的常见策略测试"() {
        given: "模拟一个会超时的任务和日志记录器"
        def logger = Mock(Logger)
        def task = Mock(ParallelUtils.ParallelTask)
        // 设置第一次调用await时抛出TimeoutException
        task.await(10, TimeUnit.SECONDS, true) >> { throw new TimeoutException("处理超时") }
        // 设置第二次调用await也抛出TimeoutException，这样才能返回"TIMEOUT"
        task.await(30, TimeUnit.SECONDS, true) >> { throw new TimeoutException("重试后依然超时") }
        
        when: "使用不同的超时处理策略"
        def result = processWithTimeoutRetryStrategy(task, logger)
        
        then: "应该进行重试并记录警告，最终返回TIMEOUT"
        result == "TIMEOUT"
        1 * logger.warn(_, _)
    }
    
    /**
     * 测试实际业务中的批处理任务
     */
    def "批处理: 批量处理场景中的并行任务测试"() {
        given: "准备批处理数据和任务"
        def batchSize = 100
        def data = (1..batchSize).collect { it }
        def results = []
        def errors = []
        
        when: "执行批处理"
        processBatch(data, results, errors)
        
        then: "应成功处理所有批次"
        results.size() == batchSize
        errors.isEmpty()
    }
    
    // ================== 辅助方法 ==================
    
    /**
     * 创建模拟的ParallelTask
     */
    private ParallelUtils.ParallelTask createMockTask(boolean shouldThrow, Exception exceptionToThrow) {
        def task = Mock(ParallelUtils.ParallelTask)
        task.await(_, _, true) >> {
            if (shouldThrow && exceptionToThrow) {
                throw exceptionToThrow
            }
            return !shouldThrow
        }
        return task
    }
    
    /**
     * 模拟MetaDataServiceImpl.processData方法
     */
    private void processDataWithTask(ParallelUtils.ParallelTask parallelTask, Logger log, def objectDescribe, List dataList) {
        try {
            parallelTask.await(10, TimeUnit.SECONDS, true)
        } catch (TimeoutException e) {
            log.warn("parallel process data temp file gray fail! ei:{}, dataSize:{}", objectDescribe.getTenantId(), dataList.size(), e)
            throw e // 直接传递超时异常
        } catch (ValidateException e) {
            log.error("parallel process data failed with business exception! ei:{}, dataSize:{}", objectDescribe.getTenantId(), dataList.size(), e)
            throw e
        } catch (RuntimeException e) {
            log.error("parallel process data failed with runtime exception! ei:{}, dataSize:{}", objectDescribe.getTenantId(), dataList.size(), e)
            throw new ValidateException(e.getMessage())
        } catch (ExecutionException e) {
            log.error("parallel process data failed with checked exception! ei:{}, dataSize:{}", objectDescribe.getTenantId(), dataList.size(), e)
            throw new ValidateException(e.getCause() ? e.getCause().getMessage() : e.getMessage())
        }
    }
    
    /**
     * 使用重试策略处理超时
     */
    private String processWithTimeoutRetryStrategy(ParallelUtils.ParallelTask task, Logger logger) {
        try {
            task.await(10, TimeUnit.SECONDS, true)
            return "SUCCESS"
        } catch (TimeoutException e) {
            logger.warn("Task timed out, retrying with increased timeout", e)
            try {
                // 增加超时时间重试
                task.await(30, TimeUnit.SECONDS, true)
                return "SUCCESS_AFTER_RETRY"
            } catch (TimeoutException e2) {
                return "TIMEOUT"
            } catch (Exception e2) {
                return "FAILED"
            }
        } catch (Exception e) {
            return "FAILED"
        }
    }
    
    /**
     * 模拟批量处理数据
     */
    private void processBatch(List data, List results, List errors) {
        // 数据分批
        def batches = data.collate(10)
        
        batches.each { batch ->
            def task = ParallelUtils.createParallelTask()
            
            batch.each { item ->
                task.submit({
                    // 模拟处理逻辑
                    def result = item * 2
                    synchronized(results) {
                        results << result
                    }
                })
            }
            
            try {
                task.await(5, TimeUnit.SECONDS, true)
            } catch (Exception e) {
                synchronized(errors) {
                    errors << e
                }
            }
        }
    }
    
    // 从ParallelUtilsThreadPoolTest整合的测试方法
    
    /**
     * 测试线程池初始化配置
     */
    def "线程池配置: 验证ParallelUtils中的线程池参数"() {
        when: "获取线程池配置"
        def executorService = Whitebox.getInternalState(ParallelUtils, "executorService") as ExecutorService
        def backgroundExecutorService = Whitebox.getInternalState(ParallelUtils, "backgroundExecutorService") as ExecutorService
        
        then: "线程池应该是ThreadPoolExecutor类型"
        executorService instanceof ThreadPoolExecutor
        backgroundExecutorService instanceof ThreadPoolExecutor
        
        and: "主线程池容量应符合预期"
        def mainPool = executorService as ThreadPoolExecutor
        mainPool.corePoolSize == 50
        mainPool.maximumPoolSize == 300
        
        and: "后台线程池容量应符合预期"
        def bgPool = backgroundExecutorService as ThreadPoolExecutor
        bgPool.corePoolSize == 5
        bgPool.maximumPoolSize == 50
    }
    
    /**
     * 测试线程池容量和任务排队
     */
    def "线程池容量: 测试线程池在高负载下的表现"() {
        given: "准备大量任务"
        def taskCount = 50 // 使用明显小于MAX_PARALLEL_NUM的任务数
        def completedTasks = new AtomicInteger(0)
        def latch = new CountDownLatch(taskCount)
        def task = ParallelUtils.createParallelTask()
        
        when: "提交大量任务"
        taskCount.times {
            task.submit({
                try {
                    Thread.sleep(10) // 短暂延迟模拟实际工作
                    completedTasks.incrementAndGet()
                } finally {
                    latch.countDown()
                }
            })
        }
        task.run() // 异步执行
        
        then: "等待所有任务完成"
        latch.await(10, TimeUnit.SECONDS)
        
        and: "所有任务应该被完成"
        completedTasks.get() == taskCount
    }
    
    /**
     * 测试线程池状态获取
     */
    def "线程池状态: 测试获取线程池状态信息"() {
        given: "准备执行中的任务"
        def sleepTime = 200
        def taskCount = 20
        def latch = new CountDownLatch(1)
        def task = ParallelUtils.createParallelTask()
        
        // 提交多个长时间运行的任务
        taskCount.times {
            task.submit({
                try {
                    Thread.sleep(sleepTime)
                } finally {
                    // 不减少latch，我们只关心状态检查
                }
            })
        }
        
        when: "执行任务并立即获取线程池状态"
        task.run()
        Thread.sleep(50) // 给线程池一点时间开始处理
        def timeoutException = null
        
        try {
            // 创建一个会超时的任务来获取线程池状态
            def statusTask = ParallelUtils.createParallelTask()
            statusTask.submit({ Thread.sleep(1000) })
            statusTask.await(1, TimeUnit.MILLISECONDS)
        } catch (Exception e) {
            timeoutException = e
            latch.countDown()
        }
        
        then: "超时异常应包含线程池状态信息"
        latch.await(1, TimeUnit.SECONDS)
        timeoutException != null
        timeoutException.message.contains("poolSize")
        timeoutException.message.contains("activeCount")
        timeoutException.message.contains("queueSize")
        
        and: "等待所有任务完成"
        Thread.sleep(sleepTime * 2)
    }
    
    /**
     * 测试后台线程池
     */
    def "后台线程池: 测试后台线程池的行为"() {
        given: "准备任务"
        def threadNames = []
        def latch = new CountDownLatch(1)
        
        when: "使用后台线程池执行任务"
        ParallelUtils.createBackgroundTask().submit({
            threadNames << Thread.currentThread().name
            latch.countDown()
        }).run()
        
        then: "等待任务完成"
        latch.await(1, TimeUnit.SECONDS)
        
        and: "线程名应使用后台线程池名称格式"
        threadNames.size() == 1
        threadNames[0].startsWith("ParallelUtils-Background-")
    }
    
    /**
     * 测试并行任务执行的一致性
     */
    @Unroll
    def "一致性测试: #taskCount个任务的并行执行结果应该一致"() {
        given: "准备多个独立任务"
        def results = []
        def task = ParallelUtils.createParallelTask()
        
        when: "并行计算简单结果"
        taskCount.times { i ->
            task.submit({
                synchronized(results) {
                    results << i * 2
                }
            })
        }
        
        and: "等待所有任务完成"
        def success = task.await(5, TimeUnit.SECONDS, true)
        
        then: "所有任务应成功完成"
        success
        results.size() == taskCount
        
        and: "结果集应包含所有预期值"
        def expectedResults = (0..<taskCount).collect { it * 2 }
        results.sort() == expectedResults
        
        where:
        taskCount << [1, 10, 50, 100]
    }
    
    /**
     * 测试线程池复用情况
     */
    def "线程复用: 验证线程池能够复用线程"() {
        given: "准备记录线程ID的容器"
        def threadIds = [] as Set
        def iterationCount = 20
        def tasksPerIteration = 10
        
        when: "多次创建并执行任务"
        iterationCount.times {
            def latch = new CountDownLatch(tasksPerIteration)
            def task = ParallelUtils.createParallelTask()
            
            tasksPerIteration.times {
                task.submit({
                    synchronized(threadIds) {
                        threadIds << Thread.currentThread().id
                    }
                    latch.countDown()
                })
            }
            
            task.run()
            latch.await(1, TimeUnit.SECONDS)
        }
        
        then: "使用的线程数应少于总任务数，证明线程被复用"
        threadIds.size() < (iterationCount * tasksPerIteration)
        threadIds.size() > 0
    }
}