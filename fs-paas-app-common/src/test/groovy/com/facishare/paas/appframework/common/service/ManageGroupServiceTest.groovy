package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.AddToManageGroupByUserId
import com.facishare.paas.appframework.common.service.dto.DeleteManageContent
import com.facishare.paas.appframework.common.service.dto.FindManageGroupObjects
import com.facishare.paas.appframework.common.service.dto.GetObjListWithParentByManageGroup
import com.facishare.paas.appframework.common.service.dto.OrganizationStatus
import com.facishare.paas.appframework.common.service.model.ManageGroup
import com.facishare.paas.appframework.common.service.model.ManageGroupContainer
import com.facishare.paas.appframework.common.service.model.ManageGroupType
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class ManageGroupServiceTest extends Specification {
    def platServiceProxy = Mock(PlatServiceProxy)
    ManageGroupService manageGroupService
    def user = User.systemUser("74255")
    def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
    
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        manageGroupService = new ManageGroupService()
        manageGroupService.setPlatProviderServiceProxy(platServiceProxy)
        
        // Mock UdobjGrayConfig.isAllow
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        
        // Mock RestUtils.buildHeaders
        GroovyMock(RestUtils, global: true)
        RestUtils.buildHeaders(_) >> ["header": "value"]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询管理组，当灰度关闭且非旧灰度时返回全部支持的ManageGroup
     */
    def "queryManageGroup_WhenGrayOff_ReturnAllSupport"() {
        given:
        def parentApiName = "parent"
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        def result = manageGroupService.queryManageGroup(user, parentApiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> false
        0 * platServiceProxy._
        
        result.allSupport
        result.type == manageGroupType
        result.parentApiName == parentApiName
        result.supportApiNames.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询管理组，当灰度开启且用户是超级管理员时返回全部支持的ManageGroup
     */
    def "queryManageGroup_WhenSupperAdmin_ReturnAllSupport"() {
        given:
        def parentApiName = "parent"
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        def result = manageGroupService.queryManageGroup(user, parentApiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> true
        0 * platServiceProxy._
        
        result.allSupport
        result.type == manageGroupType
        result.parentApiName == parentApiName
        result.supportApiNames.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询管理组，当灰度开启且调用服务成功时返回服务结果
     */
    def "queryManageGroup_WhenGrayOn_CallService_Success"() {
        given:
        def parentApiName = "parent"
        def manageGroupType = ManageGroupType.LAYOUT
        def notSystemUser = new User("74255", "1000")
        def apiNames = ["api1", "api2"]
        
        def restResult = new FindManageGroupObjects.RestResult()
        def result = new FindManageGroupObjects.Result()
        result.setAllResult(false)
        result.setApiNames(apiNames)
        result.setParentApiName(parentApiName)
        restResult.setErrCode(0)
        restResult.setResult(result)
        
        when:
        def manageGroup = manageGroupService.queryManageGroup(notSystemUser, parentApiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, notSystemUser.tenantId) >> true
        1 * platServiceProxy.findManageGroupObjects(_, _) >> { args ->
            assert args[0] != null
            def arg = args[1] as FindManageGroupObjects.Arg
            assert arg.tenantId == notSystemUser.tenantId
            assert arg.userId == notSystemUser.userId
            assert arg.appId == "facishare-system"
            assert arg.parentApiName == parentApiName
            assert arg.type == manageGroupType.type
            return restResult
        }
        
        !manageGroup.allSupport
        manageGroup.type == manageGroupType
        manageGroup.parentApiName == parentApiName
        manageGroup.supportApiNames == apiNames as Set
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询管理组，当灰度开启但服务调用失败时返回全部支持的ManageGroup
     */
    def "queryManageGroup_WhenGrayOn_CallService_Failure"() {
        given:
        def parentApiName = "parent"
        def manageGroupType = ManageGroupType.LAYOUT
        def notSystemUser = new User("74255", "1000")
        
        def restResult = new FindManageGroupObjects.RestResult()
        restResult.setErrCode(1)
        
        when:
        def manageGroup = manageGroupService.queryManageGroup(notSystemUser, parentApiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, notSystemUser.tenantId) >> true
        1 * platServiceProxy.findManageGroupObjects(_, _) >> restResult
        
        manageGroup.allSupport
        manageGroup.type == manageGroupType
        manageGroup.parentApiName == parentApiName
        manageGroup.supportApiNames.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询多个管理组，当灰度关闭时返回全部支持的ManageGroupContainer
     */
    def "queryManageGroups_WhenGrayOff_ReturnAllSupport"() {
        given:
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        def result = manageGroupService.queryManageGroups(user, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> false
        0 * platServiceProxy._
        
        result.allSupport
        result.manageGroups.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询多个管理组，当灰度开启且用户是超级管理员时返回全部支持的ManageGroupContainer
     */
    def "queryManageGroups_WhenSupperAdmin_ReturnAllSupport"() {
        given:
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        def result = manageGroupService.queryManageGroups(user, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> true
        0 * platServiceProxy._
        
        result.allSupport
        result.manageGroups.isEmpty()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试查询多个管理组，当灰度开启且调用服务成功时返回服务结果
     */
    def "queryManageGroups_WhenGrayOn_CallService_Success"() {
        given:
        def manageGroupType = ManageGroupType.LAYOUT
        def notSystemUser = new User("74255", "1000")
        
        def mgResult1 = new FindManageGroupObjects.Result()
        mgResult1.setAllResult(false)
        mgResult1.setApiNames(["api1", "api2"])
        mgResult1.setParentApiName("parent1")
        
        def mgResult2 = new FindManageGroupObjects.Result()
        mgResult2.setAllResult(true)
        mgResult2.setApiNames(["api3"])
        mgResult2.setParentApiName("parent2")
        
        def restResult = new GetObjListWithParentByManageGroup.RestResult()
        def result = new GetObjListWithParentByManageGroup.Result()
        result.setAllResult(false)
        result.setManageGroups([mgResult1, mgResult2])
        restResult.setErrCode(0)
        restResult.setResult(result)
        
        when:
        def container = manageGroupService.queryManageGroups(notSystemUser, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, notSystemUser.tenantId) >> true
        1 * platServiceProxy.getObjListWithParentByManageGroup(_, _) >> { args ->
            assert args[0] != null
            def arg = args[1] as GetObjListWithParentByManageGroup.Arg
            assert arg.tenantId == notSystemUser.tenantId
            assert arg.userId == notSystemUser.userId
            assert arg.appId == "facishare-system"
            assert arg.type == manageGroupType.type
            return restResult
        }
        
        !container.allSupport
        container.manageGroups.size() == 2
        container.manageGroups[0].allSupport == false
        container.manageGroups[0].parentApiName == "parent1"
        container.manageGroups[0].supportApiNames == ["api1", "api2"] as Set
        container.manageGroups[1].allSupport == true
        container.manageGroups[1].parentApiName == "parent2"
        container.manageGroups[1].supportApiNames == ["api3"] as Set
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试添加到管理组，当灰度关闭且非旧灰度时直接返回不调用服务
     */
    def "addToManageGroup_WhenGrayOff_DoNothing"() {
        given:
        def parentApiName = "parent"
        def apiName = "api"
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        manageGroupService.addToManageGroup(user, parentApiName, apiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> false
        0 * platServiceProxy._
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试添加到管理组，当灰度开启且用户是超级管理员时直接返回不调用服务
     */
    def "addToManageGroup_WhenSupperAdmin_DoNothing"() {
        given:
        def parentApiName = "parent"
        def apiName = "api"
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        manageGroupService.addToManageGroup(user, parentApiName, apiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> true
        0 * platServiceProxy._
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试添加到管理组，当灰度开启且调用服务成功时不抛异常
     */
    def "addToManageGroup_WhenGrayOn_CallService_Success"() {
        given:
        def parentApiName = "parent"
        def apiName = "api"
        def manageGroupType = ManageGroupType.LAYOUT
        def notSystemUser = new User("74255", "1000")
        
        def restResult = new AddToManageGroupByUserId.RestResult()
        restResult.setErrCode(0)
        
        when:
        manageGroupService.addToManageGroup(notSystemUser, parentApiName, apiName, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, notSystemUser.tenantId) >> true
        1 * platServiceProxy.addToManageGroupByUserId(_, _) >> { args ->
            assert args[0] != null
            def arg = args[1] as AddToManageGroupByUserId.Arg
            assert arg.tenantId == notSystemUser.tenantId
            assert arg.userId == notSystemUser.userId
            assert arg.appId == "facishare-system"
            assert arg.parentApiName == parentApiName
            assert arg.type == manageGroupType.type
            assert arg.apiName == ManageGroup.buildSupportApiName(parentApiName, apiName, manageGroupType)
            return restResult
        }
        
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试删除管理组，当灰度关闭时不调用服务
     */
    def "deleteManageGroup_WhenGrayOff_DoNothing"() {
        given:
        def parentApiName = "parent"
        def apiNames = ["api1", "api2"] as Set
        def manageGroupType = ManageGroupType.OBJECT
        
        when:
        manageGroupService.deleteManageGroup(user, parentApiName, apiNames, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.tenantId) >> false
        0 * platServiceProxy._
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试删除管理组，当灰度开启且调用服务成功时不抛异常
     */
    def "deleteManageGroup_WhenGrayOn_CallService_Success"() {
        given:
        def parentApiName = "parent"
        def apiNames = ["api1", "api2"] as Set
        def manageGroupType = ManageGroupType.LAYOUT
        def notSystemUser = new User("74255", "1000")
        
        def restResult = new DeleteManageContent.RestResult()
        restResult.setErrCode(0)
        
        when:
        manageGroupService.deleteManageGroup(notSystemUser, parentApiName, apiNames, manageGroupType)
        
        then:
        1 * fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, notSystemUser.tenantId) >> true
        1 * platServiceProxy.deleteManageContent(_, _) >> { args ->
            assert args[0] != null
            def arg = args[1] as DeleteManageContent.Arg
            assert arg.tenantId == notSystemUser.tenantId
            assert arg.userId == notSystemUser.userId
            assert arg.appId == "facishare-system"
            assert arg.groupType == manageGroupType.type
            assert arg.apiNames.size() == 2
            assert arg.apiNames.contains(ManageGroup.buildSupportApiName(parentApiName, "api1", manageGroupType))
            assert arg.apiNames.contains(ManageGroup.buildSupportApiName(parentApiName, "api2", manageGroupType))
            return restResult
        }
        
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试检查组织是否开放，调用服务并返回结果
     */
    def "isOpenOrganization_Success"() {
        given:
        def tenantId = "74255"
        def result = new OrganizationStatus.Result()
        result.setOpenOrganization(true)
        
        when:
        def isOpen = manageGroupService.isOpenOrganization(tenantId)
        
        then:
        1 * platServiceProxy.openOrganization(_) >> { args ->
            def arg = args[0] as OrganizationStatus.Arg
            assert arg.tenantId == tenantId
            return result
        }
        
        isOpen == true
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试旧灰度规则，使用AppFrameworkConfig
     */
    @Unroll
    def "oldMangeGroupGray_Test"() {
        given:
        def manageGroupType = mgType
        def notSystemUser = new User("74255", "1000")
        
        // 使用MetaClass来模拟静态方法调用
        def originalMethod = AppFrameworkConfig.metaClass.static.isGrayManageGroup
        try {
            AppFrameworkConfig.metaClass.static.isGrayManageGroup = { String tenantId -> 
                return grayResult 
            }
            
            when:
            def result = Whitebox.invokeMethod(manageGroupService, "oldMangeGroupGray", notSystemUser, manageGroupType)
            
            then:
            result == expectedResult
        } finally {
            // 恢复原始方法
            AppFrameworkConfig.metaClass.static.isGrayManageGroup = originalMethod
        }
        
        where:
        mgType                | grayResult || expectedResult
        ManageGroupType.OBJECT | true      || true
        ManageGroupType.OBJECT | false     || false
        ManageGroupType.LAYOUT | true      || false
        ManageGroupType.LAYOUT | false     || false
    }
}