package com.facishare.paas.appframework.common.service.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/3/6
 * @Description :
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AIFormulaConfigDTO {
	private Integer fieldN;
	private Integer optionN;
	private Double temperature;
	private Integer maxTokens;
	private String model;
	private Map<String, String> fieldTypeExplain;
	@Builder.Default
	private Map<String, Set<String>> noSupportFields = Maps.newHashMap();    // objApi -> list<filedApi>

	@Builder.Default
	private Set<String> subNoSupportTypes = Sets.newHashSet(    // 二级对象下不允许选择的字段
			IFieldType.MASTER_DETAIL,
			IFieldType.OBJECT_REFERENCE,
			IFieldType.EMPLOYEE,
			IFieldType.DEPARTMENT
	);
	@Builder.Default
	private Set<String> noSupportApis = Sets.newHashSet(
			"owner_department",
			"data_own_department",
			"data_own_organization"
	);
	@Builder.Default
	private Set<String> noSupportTypes = Sets.newHashSet(
			"group",
			"image",
			"file_attachment",
			"big_file_attachment",
			"select_many",
			"signature",
			"quote",
			"lock_rule",
			"country",
			"province",
			"city",
			"district",
			"town",
			"village",
			"multi_level_select_one",
			"dimension",
			"out_employee",
			"department_many",
			"employee_many",
			"html_rich_text",
			"rich_text",
			"big_text",
			"object_reference_many",
			"out_department",
			"data_visibility_range"
	);
	@Builder.Default
	private Set<String> noSupportGlobsVars = Sets.newHashSet(
			"currentTime__g",
			"currentDate__g",
			"currentDateTime__g"
	);

	public static final String GENERATE_EXPRESSION = "可选字段:{0}, 全局变量{1}\n" +
			"\n" +
			"纷享销客计算公式的格式要求如下\n" +
			"1. 字段需要用$括起来\n" +
			"2. 查找关联对象可以使用$a.b$获取对应字段\n" +
			"3. 时间类型字段有[时间,日期,时间日期]三种类型,只允许相同类型做运算,不同类型之间需要转换后计算,格式转换请使用对应的时间函数\n" +
			"4. 选项类型字段可以通过$field._label$访问对应实际选择值value而非存储值, 直接通过$field$访问选项值为存储的key\n" +
			"5. 式子使用```fx包裹; 返回值类型使用```returnType包裹\n" +
			"6. 支持 +-*/ ()等运算符\n" +
			"\n" +
			"可以利用的函数:\n" +
			"逻辑函数:IF(logical_test, value_if_true, value_if_false);CASE(expression, value1, result1, value2, result2,...,else_result);NULLVALUE(expression, substitute_expression);AND(boolean1,boolean2...);OR(boolean1,boolean2...);NOT(boolean);ISNULL(expression);ISNUMBER(string);\n" +
			"时间函数:DATE(year,month,day);DATEVALUE(string);DATETIMEVALUE(string);DATETIMETODATE(datetime);DATETIMETOTIME(datetime);YEARS(number);MONTHS(number);DAYS(number);HOURS(number);MINUTES(number);NOW()->datetime;TODAY()->date;\n" +
			"计算函数:MIN(number1,number2);MAX(number1,number2);MOD(number1,number2);ADDS(number1,number2);ROUNDUP(number,num_digits);\n" +
			"文本函数:STARTWITH(string1, string2);ENDWITH(string1, string2);EQUALS(string1, string2);LEN(string);CONTAINS(string, compare_string);VALUE(string);NUMBERSTRING(number);NUMBERSTRINGRMB(number);TRIM(string);\n" +
			"数组函数:ARRAYCONTAINS(list, text);ARRAYCONTAINSALL(list1, list2);\n" +
			"\n" +
			"返回值类型:[数值,金额,百分比,日期时间,日期,时间,文本,布尔] \n" +
			"\n" +
			"当前正在{2}对象下新建一个计算字段,请根据上述信息先进行分析再按照示例回答给出对应用户指令对应的纷享销客计算公式和公式对应的返回值类型\n" +
			"\n" +
			"用户指令\"\"\"\n" +
			"{3}\n" +
			"\"\"\""; // ignoreI18n

	public static final AIFormulaConfigDTO DEFAULT = AIFormulaConfigDTO.builder()
			.fieldN(50)
			.optionN(50)
			.temperature(0.0)
			.maxTokens(2048)
			.model("qwen-plus")
			.fieldTypeExplain(Maps.newHashMap())
			.build();

	public String getFieldTypeExplain(String fieldType) {
		return CollectionUtils.nullToEmpty(fieldTypeExplain).getOrDefault(fieldType, fieldType);
	}

	public boolean supportApi(String api, String describeApi) {
		if (StringUtils.isBlank(api) || StringUtils.isBlank(describeApi)) {
			return false;
		}
		return !CollectionUtils.nullToEmpty(noSupportApis).contains(api)
				&& !CollectionUtils.nullToEmpty(noSupportFields)
				.getOrDefault(describeApi, Sets.newHashSet())
				.contains(api);
	}

	public boolean supportGlobsVar(String api) {
		if (StringUtils.isBlank(api)) {
			return false;
		}
		return !CollectionUtils.nullToEmpty(noSupportGlobsVars).contains(api);
	}

	public boolean supportType(String type, boolean isSub) {
		if (StringUtils.isBlank(type)) {
			return false;
		}
		return !CollectionUtils.nullToEmpty(noSupportTypes).contains(type)
				&& !(isSub && CollectionUtils.nullToEmpty(subNoSupportTypes).contains(type));    // 二级对象下不支持的字段
	}
}
