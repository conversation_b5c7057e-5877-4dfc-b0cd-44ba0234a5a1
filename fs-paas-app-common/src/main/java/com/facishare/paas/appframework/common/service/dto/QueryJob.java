package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Objects;

/**
 * create by z<PERSON><PERSON> on 2019/10/08
 */
public interface QueryJob {
    String ACTION_JOB_TYPE = "BULK_ACTION";
    String FORMULA_JOB_TYPE = "FORMULA";
    long THIRTY_DAYS_MILLIS = 30 * 24 * 60 * 60 * 1000L;

    @Data
    @Builder
    class Arg {
        private String jobType;
        private String jobId;
        private String objectDescribeApiName;
        private List<Integer> status;
        private boolean manual;
        private Long startTime;
        private Long endTime;
        private Integer timeType;
        private Integer limit;

        public static Arg buildButtonJobQuery(String jobId, String objectDescribeApiName) {
            return Arg.builder()
                    .jobId(jobId)
                    .jobType(ACTION_JOB_TYPE)
                    .objectDescribeApiName(objectDescribeApiName)
                    .status(Lists.newArrayList(200))
                    .build();
        }

        public static Arg buildNotEndJobQuery(String objApiName, String jobType) {
            return Arg.builder()
                    .objectDescribeApiName(objApiName)
                    .jobType(jobType)
                    .status(Lists.newArrayList(JobStatus.INIT.getCode(), JobStatus.PROCESSING.getCode(),
                            JobStatus.PROCESSED.getCode(), JobStatus.EXCEPTION.getCode()))
                    .build();
        }

        public static Arg buildNotEndJobQueryForCalculationProgress(String objApiName, Long startTime) {
            long defaultStartTime = System.currentTimeMillis() - THIRTY_DAYS_MILLIS;
            startTime = Objects.isNull(startTime) ? defaultStartTime : Math.max(startTime, defaultStartTime);
            return Arg.builder()
                    .objectDescribeApiName(objApiName)
                    .jobType(FORMULA_JOB_TYPE)
                    .status(Lists.newArrayList(JobStatus.INIT.getCode(), JobStatus.PROCESSING.getCode(),
                            JobStatus.PROCESSED.getCode(), JobStatus.EXCEPTION.getCode(), JobStatus.FORCE_END.getCode()))
                    .timeType(1)
                    .startTime(startTime)
                    .limit(1000)
                    .build();
        }
    }

    @Data
    class Result {
        private int code;
        private String message;
        private ResultInfo data;

        public boolean isSuccess() {
            return code == 0;
        }

    }

    @Data
    class ResultInfo {
        private List<JobInfo> jobInfoList;
        private List<String> jobList;
    }

    @Data
    class JobInfo {
        private long createTime;
        private long completeTime;
        private String createBy;
        private int executeCount;
        private int predictDataNum;
        private int executeDataNum;
        private String executeResult;
        private String id;
        private String jobType;
        private long lastModifiedTime;
        private String objectDescribeApiName;
        private int status;
        private String tenantId;
        private String jobParam;
        private int completeDataNum;
        private Long startTime;
        private Long dispatchTime;

        public boolean completed() {
            return status == JobStatus.END.getCode()
                    || completeTime > 0
                    || completeDataNum >= totalDataNum();
        }

        public String jobStatusText() {
            //任务已完成，直接返回已完成状态
            if (completed()) {
                return JobStatus.i18nText(JobStatus.END.getCode());
            }
            return JobStatus.i18nText(status);
        }

        private int totalDataNum() {
            if (status == JobStatus.PROCESSED.getCode()) {
                return executeDataNum;
            }
            return predictDataNum;
        }

        public Long estimatedStartTime() {
            Long realStartTime = startTime();
            //任务已经开始执行，返回空
            if (Objects.nonNull(realStartTime)) {
                return null;
            }
            long now = System.currentTimeMillis();
            long delayTime = 60 * 1000;
            //指定了开始时间，返回开始时间或当前时间+60秒
            if (Objects.nonNull(startTime)) {
                return startTime > now ? startTime : now + delayTime;
            }
            //默认返回当前时间+60秒
            return now + delayTime;
        }

        public Long startTime() {
            int totalDataNum = totalDataNum();
            //没有数据需要计算，返回创建时间
            if (totalDataNum == 0) {
                return createTime;
            }
            //等待执行，返回空
            if (status == JobStatus.INIT.getCode()) {
                return null;
            }
            //已经开始执行，返回执行时间
            if (Objects.nonNull(dispatchTime)) {
                return dispatchTime;
            }
            //指定了开始时间，返回开始时间或创建时间
            if (Objects.nonNull(startTime)) {
                return Math.max(startTime, createTime);
            }
            //默认返回创建时间
            return createTime;
        }

        public Long estimatedCompleteTime() {
            Long realCompleteTime = completeTime();
            //任务已经完成，返回空
            if (Objects.nonNull(realCompleteTime)) {
                return null;
            }
            int totalDataNum = totalDataNum();
            long delayTime = 60 * 1000;
            long now = System.currentTimeMillis();
            Long realStartTime = startTime();
            Long estimatedStartTime = estimatedStartTime();
            long computeStartTime = ObjectUtils.firstNonNull(realStartTime, estimatedStartTime, now + delayTime);
            if (completeDataNum > 0 && completeDataNum < totalDataNum && now > computeStartTime) {
                // 已开始计算，根据已计算数据量计算预计完成时间，计算公式=当前时间+（当前时间-开始计算时间）*（总数据量-已计算数据量）/已计算数据量 + 60秒（预留时长）
                return now + (now - computeStartTime) * (totalDataNum - completeDataNum) / completeDataNum + delayTime;
            } else {
                // 未开始计算，根据预计开始时间计算预计完成时间，计算公式=预计开始时间+30(预估计算速度为每条/30ms)*总数据量+60秒(预留时长)
                return computeStartTime + 30L * totalDataNum + delayTime;
            }
        }

        public Long completeTime() {
            //任务已完成，返回完成时间
            if (completeTime > 0) {
                return completeTime;
            }
            //任务已完成，但是没有更新完成时间，返回最后修改时间
            if (completed()) {
                return lastModifiedTime;
            }
            //任务未完成，返回空
            return null;
        }
    }

    @Getter
    enum JobStatus {
        INIT(0, I18NKey.JOB_STATUS_INIT),
        PROCESSING(100, I18NKey.JOB_STATUS_PROCESSING),
        PROCESSED(150, I18NKey.JOB_STATUS_PROCESSING),
        END(200, I18NKey.JOB_STATUS_END),
        EXCEPTION(300, I18NKey.JOB_STATUS_FAILED),
        CANCELED(400, I18NKey.JOB_STATUS_CANCELED),
        FORCE_END(500, I18NKey.JOB_STATUS_PROCESSING),
        PENDING(600, I18NKey.JOB_STATUS_INIT),
        NOT_START(700, I18NKey.JOB_STATUS_INIT),
        FAIL(800, I18NKey.JOB_STATUS_FAILED);

        private final int code;
        private final String i18nKey;

        JobStatus(int code, String i18nKey) {
            this.code = code;
            this.i18nKey = i18nKey;
        }

        public static JobStatus codeOf(int code) {
            for (JobStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return null;
        }

        public static String i18nText(int code) {
            JobStatus jobStatus = codeOf(code);
            if (Objects.isNull(jobStatus)) {
                return String.valueOf(code);
            }
            return I18NExt.getOrDefault(jobStatus.getI18nKey(), jobStatus.name());
        }
    }

}
