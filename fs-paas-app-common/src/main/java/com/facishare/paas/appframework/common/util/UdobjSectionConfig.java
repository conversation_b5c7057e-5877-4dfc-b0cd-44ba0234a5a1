package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.common.service.model.AIFormulaConfigDTO;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/3/18
 * @Description : 读取 fs-udobj-config.ini
 */
@Slf4j
public class UdobjSectionConfig {
	private static final String CONFIG_FILE = "fs-udobj-config.ini";
	private static final Splitter SPLITTER = Splitter.on(",").trimResults().omitEmptyStrings();

	@Getter
	private static AIFormulaConfigDTO aiFormulaConfigDTO = AIFormulaConfigDTO.DEFAULT;

	static {
		ConfigFactory.getConfig(CONFIG_FILE, "ai_formula", UdobjSectionConfig::loadAiFormula);
	}

	private static void loadAiFormula(IConfig config) {
		try {
			Integer fieldN = config.getInt("fieldN", 50);
			Integer optionN = config.getInt("optionN", 5);
			Double temperature = config.getDouble("temperature", 0.0);
			Integer maxTokens = config.getInt("maxTokens", 2048);
			String model = config.get("model", "qwen-plus");

			String fieldTypeExplainS = config.get("fieldTypeExplain");
			String noSupportApisS = config.get("noSupportApis");
			String noSupportTypesS = config.get("noSupportTypes");
			String noSupportGlobsVarsS = config.get("noSupportGlobsVars");

			if (StringUtils.isBlank(noSupportApisS) || StringUtils.isBlank(noSupportTypesS)) {
				log.error("getAIFormulaConfig failed, old data:{} because of blank, config:{}", aiFormulaConfigDTO, config);
			}

			Map<String, String> fieldTypeExplain = loadMap(fieldTypeExplainS);
			Set<String> noSupportApis = loadSet(noSupportApisS);
			Set<String> noSupportTypes = loadSet(noSupportTypesS);
			Set<String> noSupportGlobsVars = loadSet(noSupportGlobsVarsS);
			Map<String, Set<String>> noSupportFields = loadMap(config, ".noSupportFields");

			AIFormulaConfigDTO newConfig = AIFormulaConfigDTO.builder()
					.fieldN(fieldN)
					.optionN(optionN)
					.temperature(temperature)
					.maxTokens(maxTokens)
					.model(model)
					.fieldTypeExplain(fieldTypeExplain)
					.noSupportApis(noSupportApis)
					.noSupportTypes(noSupportTypes)
					.noSupportGlobsVars(noSupportGlobsVars)
					.noSupportFields(noSupportFields)
					.build();

			log.info("getAIFormulaConfig success, old data:{} data:{}", aiFormulaConfigDTO, newConfig);
			aiFormulaConfigDTO = newConfig;
		} catch (Exception e) {
			log.error("getAIFormulaConfig failed, old data:{} new config:{}", aiFormulaConfigDTO, config, e);
		}
	}

	private static Map<String, String> loadMap(String valueS) {
		if (StringUtils.isEmpty(valueS)) {
			return Maps.newHashMap();
		}
		return SPLITTER.splitToList(valueS).stream()
				.map(entry -> entry.split(":", 2))    // 防止和配置k-v读取冲突
				.collect(Collectors.toMap(
						entry -> entry[0],
						entry -> entry[1]
				));
	}

	private static Map<String, Set<String>> loadMap(IConfig config, String keyWords) {
		Map<String, Set<String>> mapConfig = Maps.newHashMap();
		Map<String, String> allConfig = config.getAll();
		allConfig.keySet().forEach(key -> {
			if (StringUtils.endsWith(key, keyWords)) {
				mapConfig.put(StringUtils.substringBefore(key, keyWords), loadSet(config.get(key)));
			}
		});
		return mapConfig;
	}

	private static Set<String> loadSet(String valueS) {
		if (StringUtils.isEmpty(valueS)) {
			return Sets.newHashSet();
		}
		return Sets.newHashSet(SPLITTER.split(valueS));
	}

}
