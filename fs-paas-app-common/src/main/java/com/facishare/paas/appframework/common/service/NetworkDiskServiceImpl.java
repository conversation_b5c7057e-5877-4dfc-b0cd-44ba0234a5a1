package com.facishare.paas.appframework.common.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.netdisk.api.model.*;
import com.facishare.netdisk.api.model.base.Operator;
import com.facishare.netdisk.api.model.type.NodeCategory;
import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.appframework.common.util.ImportConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.FileSizeConverter;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.warehouse.api.dubbo.FilePackedService;
import com.facishare.warehouse.api.model.FilePackedArg;
import com.facishare.warehouse.api.model.FilePackedResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/12/11 3:05 PM
 */
@Slf4j
@Service("networkDiskService")
public class NetworkDiskServiceImpl implements NetworkDiskService {

    //打包文件如果是网盘文件，需要传入一个身份标示，用于安全校验，
    //而非网盘文件不需要，但是NPath是否是网盘文件应该透明，所以这个标示都透传
    private static final String IDENTITY_INFORMATION = "XiaoKeNetDisk";

    @Autowired
    private NetworkDiskProxy networkDiskProxy;
    @Autowired
    private FilePackedService filePackedService;
    @Autowired
    private GDSHandler gdsHandler;


    @Override
    public List<V5FileInfo> getFileInfoByNPath(User user, List<String> paths) {
        String ea = gdsHandler.getEAByEI(user.getTenantId());
        return getFileInfoByNPath(paths, user.getTenantId(), ea, user);
    }

    @Override
    public List<V5FileInfo> getFileInfoByNPath(List<String> paths, String tenantId, String ea, User user) {
        //组装请求网盘接口参数
        NSGetFileInfoByNPathArg arg = new NSGetFileInfoByNPathArg();
        arg.setNPathList(paths);
        Operator operator = new Operator();
        operator.setEmployeeId(Integer.parseInt(user.getUserId()));
        operator.setNetdiskManager(false);
        operator.setEnterpriseId(Integer.parseInt(tenantId));
        operator.setEnterpriseAccount(ea);
        arg.setOperator(operator);
        //请求接口
        NSGetFileInfoByNPathResult result = networkDiskProxy.getFieldInfoByNPath(RestUtils.buildHeaders(user), arg);
        return result.getFileInfoList() == null ? Lists.newArrayList() : result.getFileInfoList();
    }

    @Override
    public String exportFilesWithXml(String xml, String tenantId, String ea, User user, String fileName) {
        NSGetDownloadTokenArg arg = new NSGetDownloadTokenArg();
        arg.setEmployeeId(Integer.parseInt(user.getUserId()));
        arg.setEnterpriseAccount(ea);
        arg.setStructure(xml);
        arg.setFileName(fileName);
        arg.setExtension("zip");

        Operator operator = new Operator();
        operator.setEmployeeId(Integer.parseInt(user.getUserId()));
        operator.setNetdiskManager(false);
        operator.setEnterpriseId(Integer.parseInt(tenantId));
        operator.setEnterpriseAccount(ea);
        arg.setOperator(operator);
        NSGetDownloadTokenResult result = networkDiskProxy.getDownloadToken(RestUtils.buildHeaders(user), arg);
        return result.getToken();
    }

    @Override
    public FilePackedResult packedFile(User user, String xml, String jobId) {
        return packedFile(user, xml, jobId, null);
    }

    @Override
    public FilePackedResult packedFile(User user, String xml, String jobId, Integer totalCount) {
        return packedFile(user, xml, jobId, totalCount, false);
    }

    @Override
    public FilePackedResult packedFile(User user, String xml, String jobId, Integer totalCount, boolean skipDuplicatedFile) {
        FilePackedArg arg = new FilePackedArg();
        arg.setEmployId(user.getTenantIdInt());
        arg.setBizType(DefObjConstants.PACKAGE_NAME_CRM);
        arg.setEa(gdsHandler.getEAByEI(user.getTenantId()));
        arg.setDocuments(xml);
        arg.setDownloadUser(user.getUserIdOrOutUserIdIfOutUser());
        arg.setDownloadSecurityGroup(IDENTITY_INFORMATION);
        arg.setWarehouseType("N");
        arg.setJobId(jobId);
        arg.setSkipDuplicatedFile(skipDuplicatedFile);
        if (Objects.nonNull(totalCount)) {
            arg.setDataCount(totalCount);
        }
        int length = JSON.toJSONString(arg).getBytes().length;
        long exportXmlMaxLength = ImportConfig.getExportXmlMaxLength();
        if (length > exportXmlMaxLength) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPORT_XML_MAX_LENGTH_ERROR,
                    "文件请求长度{0},超过最大允许长度{1},请减少数据量后重新操作.", // ignoreI18n
                    FileSizeConverter.convertFileSize(length), FileSizeConverter.convertFileSize(exportXmlMaxLength)));
        }
        return filePackedService.getFilePackedResult(arg);
    }

    @Override
    public NSGetFoldersByParentIDResult getFoldersByParentId(String parentId, String tenantId, String ea, User user) {
        if (Objects.isNull(parentId)) {
            return null;
        }
        NSGetFoldersByParentIDArg arg = new NSGetFoldersByParentIDArg();
        Operator operator = new Operator();
        operator.setEmployeeId(Integer.parseInt(user.getUserId()));
        operator.setNetdiskManager(false);
        operator.setEnterpriseId(Integer.parseInt(tenantId));
        operator.setEnterpriseAccount(ea);
        arg.setOperator(operator);

        arg.setNodeCategory(NodeCategory.Personal);
        arg.setParentId(parentId);

        return networkDiskProxy.getFoldersByParentId(RestUtils.buildHeaders(user), arg);
    }
}
