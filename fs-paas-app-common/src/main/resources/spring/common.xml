<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/app-task-async.xml"/>
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean"
          p:configName="fs-paas-metadata-service-redis"/>
    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>
    <bean id="biServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-bi-rest-proxy" init-method="init"/>

    <bean id="orgServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.OrgServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="sendCrmMessageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.SendCrmMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="SendNewCrmMessageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.SendNewCrmMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="sendEmailProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.SendEmailProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="servicePersonnelSearchSettingProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.service.ServicePersonnelSearchSettingProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="mqMessageProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-crm-mq-action-producer"/>
    <bean id="caTemporaryPrivilegeMQSender"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-crm-temporary-privilege-producer"/>
    <!--通用选项集-->
    <bean id="optionChangeProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-option-set-change-producer"/>
    <bean id="appDescribeChangeProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-describe-change-event-producer"/>

    <bean id="treeViewFieldChangeProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-tree-view-field-change-event-producer"/>

    <bean id="dataHangTagProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-data-hang-tag-producer"/>


    <bean id="domainPluginInstanceMQProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-mq-producer"
          c:sectionName="domain-plugin-instance-event"/>

    <bean id="copyObjectProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-copy-object-mq-producer"
          c:sectionName="paas-copy-object-event"/>

    <bean id="changeOrderOpenProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-mq-producer"
          c:sectionName="paas-change-order-open-event"/>

    <bean id="wipeCustomListSettingsProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-mq-producer"
          c:sectionName="paas-wipe-custom-list-settings-producer"/>

    <bean id="publicObjectMigrateProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-mq-producer"
          c:sectionName="public-object-migrate-producer"/>

    <bean id="recordReferenceProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-mq-producer"
          c:sectionName="paas-record-entity-reference-producer"/>
    <bean id="onlineDocProducer"
          class="com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer"
          init-method="init"
          destroy-method="close"
          c:config="fs-paas-app-mq-producer"
          c:sectionName="paas-online-doc-auth-change-producer"/>

    <bean id="phoneNumberInformationProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.PhoneNumberInformationProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="phoneNumberInfoV2Proxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.PhoneNumberInfoV2Proxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <bean id="objectSearchServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.ObjectSearchServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="messageBoxServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.MessageBoxServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="sendCalculateJobProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.JobScheduleProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="networkDiskProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.NetworkDiskProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="referenceServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.ReferenceServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="messagePlatformProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.MessagePlatformProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="connectionServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.ConnectionServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="smsServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.SmsServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="tagServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.TagRestServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="actionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.ActionProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="controllerProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.ControllerProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="departmentDataRightsProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.privilege.DepartmentDataRightsProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="printTemplateServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.PrintTemplateServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="userRoleInformationProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.UserRoleInformationProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="platServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.PlatServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="geoAddressProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.GeoAddressProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="dataLoaderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.proxy.DataLoaderProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <bean id="paasWebPageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.WebPageProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="qinXinFlowMessageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.proxy.QinXinFlowMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <!-- 文件系统服务 start  -->
    <!-- copy from fs-fsc-rest-client.xml -->
    <bean id="fSCApiRestHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-fsc-api-rest-client"/>
    </bean>
    <!-- 验证码图片 -->
    <bean id="createImageService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
        <property name="objectType" value="com.facishare.fsc.api.service.CreateImageService"/>
        <property name="serverHostProfile" ref="fSCApiRestHostProfile"/>
    </bean>
    <!-- Shared File -->
    <bean id="sharedFileService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
        <property name="objectType" value="com.facishare.fsc.api.service.SharedFileService"/>
        <property name="serverHostProfile" ref="fSCApiRestHostProfile"/>
    </bean>
    <!-- 文件系统服务 end -->

    <import resource="classpath:META-INF/fs-polling-mq.xml"/>
    <import resource="classpath:spring/token-core.xml"/>

    <!--fs-organization-adapter-api -->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>
    <!--fs-organization-api -->
    <import resource="classpath:spring/fs-organization-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-api-dubbo-rest-client-host-config.xml"/>
    <!-- 组织架构开启缓存 -->
    <import resource="classpath:fs-organization-cache-no-dubbo.xml"/>
    <!-- 用户登陆服务 -->
    <import resource="classpath:META-INF/fs-user-login-api-dubbo-rest-client.xml"/>

    <import resource="classpath:enterpriserelation2/enterpriserelation-erdepartment.xml"/>

</beans>
