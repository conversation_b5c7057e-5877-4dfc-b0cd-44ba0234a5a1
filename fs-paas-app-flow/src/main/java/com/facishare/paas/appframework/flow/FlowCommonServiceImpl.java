package com.facishare.paas.appframework.flow;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.dto.ExistAvailableDef;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service("flowCommonServiceImpl")
public class FlowCommonServiceImpl implements FlowCommonService {

    @Autowired
    private FlowCommonProxy flowCommonProxy;

    @Override
    public ExistAvailableDef.ExistAvailable existAvailableDef(User user, String describeApiName, String dataId) {

        Map<String, String> header = buildHeaders(user);
        ExistAvailableDef.Arg arg = ExistAvailableDef.Arg.builder()
                .entityId(describeApiName)
                .objectId(dataId)
                .build();

        ExistAvailableDef.ExistAvailable existAvailable = ExistAvailableDef.ExistAvailable.builder()
                .existAvailableBpmDef(false)
                .existAvailableStageDef(false)
                .build();
        try {
            ExistAvailableDef.Result result = flowCommonProxy.existAvailableDef(arg, header);
            if (result.getCode() == 0) {
                existAvailable = result.getData();
            } else {
                log.warn("existAvailableDef warn, header:{}, arg:{}, result:{}", JSON.toJSONString(header), JSON.toJSONString(arg), JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("existAvailableDef error, header:{}, arg:{}", JSON.toJSONString(header), JSON.toJSONString(arg), e);
        }

        return existAvailable;
    }

    private Map<String, String> buildHeaders(User user) {
        Map<String, String> ret = RestUtils.buildHeaders(user);
        ret.put("x-tenant-id", String.valueOf(user.getTenantId()));
        ret.put("x-user-id", user.getUserIdWithFlowGray());
        return ret;
    }
}
