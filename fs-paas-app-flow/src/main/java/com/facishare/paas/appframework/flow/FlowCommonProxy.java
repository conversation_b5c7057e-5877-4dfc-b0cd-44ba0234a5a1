package com.facishare.paas.appframework.flow;

import com.facishare.paas.appframework.flow.dto.ExistAvailableDef;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PAAS-FlowCommon", contentType = "application/json")
public interface FlowCommonProxy {
    @POST(value = "flow/def/existAvailableDef", desc = "获取是否存在可发起的业务流、阶段推进器流程定义接口")
    ExistAvailableDef.Result existAvailableDef(@Body ExistAvailableDef.Arg arg, @HeaderMap Map<String, String> header);
}