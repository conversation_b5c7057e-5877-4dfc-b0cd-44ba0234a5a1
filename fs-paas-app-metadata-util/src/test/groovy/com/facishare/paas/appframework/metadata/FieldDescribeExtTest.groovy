package com.facishare.paas.appframework.metadata

import com.facishare.paas.metadata.api.GroupField
import com.facishare.paas.metadata.api.describe.Area
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.Text
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import spock.lang.Specification

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/9
 */
class FieldDescribeExtTest extends Specification {

    def "correctAreaDetailAddress when describeExt is null"() {
        given:
        def area = Stub(Area)
        def fieldDescribeExt = FieldDescribeExt.of(area)
        when:
        fieldDescribeExt.correctAreaDetailAddress(null)
        then:
        thrown(NullPointerException)
    }

    def "correctAreaDetailAddress"() {
        given:
        def area = Stub(Area)
        def text = Mock(Text)
        def describeExt = Stub(ObjectDescribeExt)
        area.getAreaDetailAddressFieldApiName() >> fieldName
        area.getType() >> IFieldType.GROUP
        area.getGroupType() >> GroupField.GROUP_TYPE_AREA
        describeExt.getActiveFieldDescribeSilently(fieldName) >> Optional.of(text)

        def fieldDescribeExt = FieldDescribeExt.of(area)
        when:
        fieldDescribeExt.correctAreaDetailAddress(describeExt)
        then:
        1 * text.setMaxLength(FieldDescribeExt.AREA_DETAIL_ADDRESS_MAX_LENGTH)
        where:
        fieldName | _
        "field1"  | _
    }

    def "test needFillEmployeeExtAttribute"() {
        given:
        def fieldDescribe = FieldDescribeFactory.newInstance(["api_name": "field__c", type: fieldType])
        def fieldDescribeExt = FieldDescribeExt.of(fieldDescribe)
        expect:
        fieldDescribeExt.needFillEmployeeExtAttribute() == result
        where:
        fieldType                 | result
        IFieldType.EMPLOYEE       | true
        IFieldType.EMPLOYEE_MANY  | true
        IFieldType.OUT_EMPLOYEE   | true
        IFieldType.OUT_DEPARTMENT | false
    }

    def "test needFillDepartmentExtAttribute"() {
        given:
        def fieldDescribe = FieldDescribeFactory.newInstance(["api_name": "field__c", type: fieldType])
        def fieldDescribeExt = FieldDescribeExt.of(fieldDescribe)
        expect:
        fieldDescribeExt.needFillDepartmentExtAttribute() == result
        where:
        fieldType                  | result
        IFieldType.EMPLOYEE        | false
        IFieldType.DEPARTMENT      | true
        IFieldType.DEPARTMENT_MANY | true
        IFieldType.OUT_DEPARTMENT  | true
    }
}
