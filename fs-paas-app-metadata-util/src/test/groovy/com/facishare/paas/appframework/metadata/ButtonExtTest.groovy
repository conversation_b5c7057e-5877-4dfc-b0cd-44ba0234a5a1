package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.ObjectLockStatus
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.search.Operator
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

import static com.facishare.paas.appframework.metadata.ButtonUsePageType.*

/**
 * create by zhao<PERSON> on 2019/09/20
 */
class ButtonExtTest extends Specification {

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    @Unroll
    def "get udefButton by buttonApiName #buttonApiName"() {
        when:
        IUdefButton udefButton = ButtonExt.getDefaultButtonByApiName(buttonApiName, "AccountObj")
        then:
        udefButton != null
        udefButton.getApiName() == buttonApiName
        udefButton.getDefineType() == defineType
        wheresIsEqual(udefButton.getWheres(), expectedWheres)
        udefButton.getLabel() == label
        udefButton.getUsePages() == usePages
        ButtonExt.of(udefButton).getActionCode() == action
        where:
        buttonApiName                     || defineType | expectedWheres               | label          | action             | usePages
        "Lock_button_default"             || "system"   | getLockButtonWheres()        | "锁定"         | "Lock"             | [Detail.getId(), ListBatch.getId()]
        "Unlock_button_default"           || "system"   | getUnlockButtonWheres()      | "解锁"         | "Unlock"           | [Detail.getId(), ListBatch.getId()]
        "Abolish_button_default"          || "system"   | getBulkInvalidButtonWheres() | "作废"         | "Abolish"          | [Detail.getId(), ListBatch.getId()]
        "Clone_button_default"            || "system"   | getCloneButtonWheres()       | "复制"         | "Clone"            | [Detail.getId()]
        "EditTeamMember_button_default"   || "system"   | getTeamMemberButtonWheres()  | "编辑团队成员" | "EditTeamMember"   | [Detail.getId()]
        "AddTeamMember_button_default"    || "system"   | getTeamMemberButtonWheres()  | "添加团队成员" | "AddTeamMember"    | [Detail.getId(), ListBatch.getId()]
        "DeleteTeamMember_button_default" || "system"   | getTeamMemberButtonWheres()  | "移除团队成员" | "DeleteTeamMember" | [Detail.getId(), ListBatch.getId()]
        "ChangeOwner_button_default"      || "system"   | getChangeOwnerButtonWheres() | "更换负责人"   | "ChangeOwner"      | [Detail.getId(), ListBatch.getId()]
        "Add_button_default"              || "system"   | getCreateButtonWheres()      | "新建"         | "Add"              | [Create.getId()]
        "Edit_button_default"             || "system"   | getEditButtonWheres()        | "编辑"         | "Edit"             | [Edit.getId()]
    }

    def getEditButtonWheres() {
        return Lists.newArrayList()
    }

    def getCreateButtonWheres() {
        return Lists.newArrayList()
    }

    def getChangeOwnerButtonWheres() {
        return getLockButtonWheres()
    }

    def getTeamMemberButtonWheres() {
        return getLockButtonWheres()
    }

    def getCloneButtonWheres() {
        return Lists.newArrayList()
    }

    def getBulkInvalidButtonWheres() {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.IN, ObjectLifeStatus.LIFE_STATUS_API_NAME,
                Lists.newArrayList(ObjectLifeStatus.NORMAL.getCode(), ObjectLifeStatus.UNDER_REVIEW.getCode(),
                        ObjectLifeStatus.IN_CHANGE.getCode(), ObjectLifeStatus.INEFFECTIVE.getCode())).getFilter())

        Wheres wheres = new Wheres()
        wheres.setFilters(filters)
        return Lists.newArrayList(wheres)
    }

    def getUnlockButtonWheres() {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, ObjectLockStatus.LOCK_STATUS_API_NAME,
                ObjectLockStatus.LOCK.getStatus()).getFilter())

        Wheres wheres = new Wheres()
        wheres.setFilters(filters)
        return Lists.newArrayList(wheres)
    }

    private boolean wheresIsEqual(buttonWheres, expectedWheres) {
        def buttonWheresExts = buttonWheres.collect { WheresExt.of(it) }
        def expectedWheresExt = expectedWheres.collect { WheresExt.of(it) }
        return buttonWheresExts == expectedWheresExt
    }

    def getLockButtonWheres() {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, ObjectLockStatus.LOCK_STATUS_API_NAME,
                ObjectLockStatus.UNLOCK.getStatus()).getFilter())

        Wheres wheres = new Wheres()
        wheres.setFilters(filters)
        return Lists.newArrayList(wheres)
    }
}
