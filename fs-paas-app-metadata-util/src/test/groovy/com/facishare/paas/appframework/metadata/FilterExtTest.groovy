package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.junit.Assert
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * create by z<PERSON><PERSON> on 2021/07/19
 */
class FilterExtTest extends Specification {
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def "test"() {
        when:
        IFilter filter = new Filter()
        List fieldValue = [1, 2, "1", "2"]
        filter.setFieldValues(fieldValue)
        def jsonString = filter.toJsonString()
        then:
        println jsonString
        def jsonMap = JacksonUtils.fromJson(jsonString, Map)
        println(JacksonUtils.toJson(jsonMap))
        1 == 1
    }

    def "test validateFilter"() {
        given:
        def describe = new ObjectDescribe(["fields": ["a": ["api_name": "a", "type": "date_time"]]])
        def filterExt = FilterExt.of(Operator.LTE, "a", "20")
        when:
        filterExt.validateFilter(describe)
        then:
        1==1

    }
}
