package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSONArray
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.common.PasswordUtil
import spock.lang.Specification
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey

/**
 * create by z<PERSON><PERSON> on 2020/10/16
 */
class DuplicatedSearchExtTest extends Specification {

    def "test_generate_duplicate_search_data"() {
        given: "准备数据"
        and: "mock 描述"
        IObjectDescribe describe = Mock(IObjectDescribe)
        and: "准备 duplicatedSearch"
        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        and: "准备 objectData"

        List<IObjectData> objectDataList = JSONArray.parseArray(objectDataJson, Map).collect { ObjectDataExt.of(it) }
        when:
        describe.getTenantId() >> "78057"
        describe.getApiName() >> "object_t1XtF__c"
        def result = DuplicatedSearchExt.of(duplicatedSearch).generateDuplicateSearchData(objectDataList, describe)
        then:
        println result
        JacksonUtils.readTree(JacksonUtils.toJson(expect)) == JacksonUtils.readTree(JacksonUtils.toJson(result))
        where:
        duplicatedSearchJson                                                                       | objectDataJson                   || expect
        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""" | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":"自定义字段"}]""" || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_name_主属性"            : ["1"],
                                  "DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_field_98WZ4__c_自定义字段": ["1"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""" | """[{"_id":"2","name":"主属性",
"field_98WZ4__c":null}]"""    || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_name_主属性": ["2"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""" | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]""" || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_name_主属性"            : ["1", "2"],
                                  "DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_field_98WZ4__c_自定义字段": ["2"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"}]},{"connector":"OR",
"conditions":[{"connector":"AND","field_name":"name","field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,
"support_import":true}"""                                                               | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]""" || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_name_主属性"            : ["1", "2"],
                                  "DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_field_98WZ4__c_自定义字段": ["2"]]

    }

    def "test_generate_duplicate_search_data_FUZZY_duplicatedSearch"() {
        given: "准备数据"
        and: "mock 描述"
        IObjectDescribe describe = Mock(IObjectDescribe)
        and: "准备 duplicatedSearch"
        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        and: "准备 objectData"

        List<IObjectData> objectDataList = JSONArray.parseArray(objectDataJson, Map).collect { ObjectDataExt.of(it) }
        when:
        describe.getTenantId() >> "78057"
        describe.getApiName() >> "object_t1XtF__c"
        def result = DuplicatedSearchExt.of(duplicatedSearch).generateDuplicateSearchData(objectDataList, describe)
        then:
        println result
        result.isEmpty()
        where:
        duplicatedSearchJson                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | objectDataJson                   || _
        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW","enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND","field_name":"field_98WZ4__c","field_value":"FUZZY","mapping_field":"kw0"},{"connector":"AND","field_name":"name","field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""" | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":"自定义字段"}]""" || _
    }


    def "test_generate_duplicate_search_data_FUZZY_and PRECISE"() {
        given: "准备数据"
        and: "mock 描述"
        IObjectDescribe describe = Mock(IObjectDescribe)
        and: "准备 duplicatedSearch"
        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        and: "准备 objectData"

        List<IObjectData> objectDataList = JSONArray.parseArray(objectDataJson, Map).collect { ObjectDataExt.of(it) }
        when:
        describe.getTenantId() >> "78057"
        describe.getApiName() >> "object_t1XtF__c"
        def result = DuplicatedSearchExt.of(duplicatedSearch).generateDuplicateSearchData(objectDataList, describe)
        then:
        println result
        JacksonUtils.readTree(JacksonUtils.toJson(expect)) == JacksonUtils.readTree(JacksonUtils.toJson(result))
        where:
        duplicatedSearchJson         | objectDataJson                   || expect
        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"}]},{"connector":"OR",
"conditions":[{"connector":"AND","field_name":"name","field_value":"FUZZY"}]}]},"operator_id":"1000","version":1,
"support_import":true}""" | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]""" || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_field_98WZ4__c_自定义字段": ["2"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"FUZZY","mapping_field":"kw0"}]},{"connector":"OR",
"conditions":[{"connector":"AND","field_name":"name","field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,
"support_import":true}""" | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]""" || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_name_主属性": ["1", "2"]]
    }

    def "Password decode"() {
        expect:
        def str = PasswordUtil.decode("8DA9D5B1E41238DC8BC1ED3502290B5C60E49EC07367ECBACDFD96E99012E87C")
        println str
    }

    def "test RuleCondition"() {
        given:
        def condition = new DuplicatedSearchExt.RuleCondition(fieldName, emptyPolicy, policy, distance)

        expect:
        condition.getFieldName() == fieldName
        condition.getEmptyPolicy() == emptyPolicy.toString()
        condition.getPolicy() == policy?.name() ?: ""
        condition.getDistance() == expectedDistance
        condition.ignoreEmpty() == (emptyPolicy == IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY)

        where:
        fieldName    | emptyPolicy                          | policy                    | distance | expectedDistance
        "field1"     | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE | "300"    | "300"
        "field2"     | IDuplicatedSearch.EmptyPolicy.CHECK_EMPTY  | IDuplicatedSearch.Policy.DISTANCE | "500"    | "500"
        "field3"     | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | null                     | "0"      | "0"
        "field4"     | IDuplicatedSearch.EmptyPolicy.CHECK_EMPTY  | IDuplicatedSearch.Policy.FUZZY   | ""       | "0"
        "field5"     | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE | null     | "0"
    }

    def "test RuleCondition from"() {
        given:
        def condition = Mock(IDuplicatedSearch.Condition)
        condition.getFieldName() >> fieldName
        condition.getEmptyPolicy() >> emptyPolicy
        condition.getFieldValue() >> policy
        condition.getRange() >> distance

        when:
        def ruleCondition = DuplicatedSearchExt.RuleCondition.from(condition)

        then:
        ruleCondition.getFieldName() == fieldName
        ruleCondition.getEmptyPolicy() == emptyPolicy.toString()
        ruleCondition.getPolicy() == policy?.name() ?: ""
        ruleCondition.getDistance() == expectedDistance

        where:
        fieldName    | emptyPolicy                          | policy                    | distance | expectedDistance
        "field1"     | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE | "300"    | "300"
        "field2"     | IDuplicatedSearch.EmptyPolicy.CHECK_EMPTY  | IDuplicatedSearch.Policy.DISTANCE | "500"    | "500"
        "field3"     | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | null                     | "0"      | "0"
        "field4"     | IDuplicatedSearch.EmptyPolicy.CHECK_EMPTY  | IDuplicatedSearch.Policy.FUZZY   | ""       | "0"
        "field5"     | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE | null     | "0"
    }

    def "test isNeedDuplicatedV2"() {
        given:
        def udobjGrayConfigClass = GroovyMock(UdobjGrayConfig.class, global: true)
        UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_SUPPORT_DISTANCE, tenantId) >> isGrayDuplicated
        UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, tenantId) >> isGrayDuplicated
        
        def duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.getTenantId() >> tenantId
        def useableRules = Mock(IDuplicatedSearch.UseableRules)
        duplicatedSearch.getUseableRules() >> useableRules
        
        def rules = []
        if (hasDistancePolicy) {
            def rule = Mock(IDuplicatedSearch.Rule)
            def conditions = []
            def condition = Mock(IDuplicatedSearch.Condition)
            condition.getFieldValue() >> IDuplicatedSearch.Policy.DISTANCE
            conditions.add(condition)
            rule.getConditions() >> conditions
            rules.add(rule)
        } else {
            def rule = Mock(IDuplicatedSearch.Rule)
            def conditions = []
            def condition = Mock(IDuplicatedSearch.Condition)
            condition.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
            conditions.add(condition)
            rule.getConditions() >> conditions
            rules.add(rule)
        }
        useableRules.getRules() >> rules
        
        def duplicatedSearchExt = DuplicatedSearchExt.of(duplicatedSearch)
        
        when:
        def result = duplicatedSearchExt.isNeedDuplicatedV2()
        
        then:
        result == expected
        
        where:
        tenantId | isGrayDuplicated | hasDistancePolicy | expected
        "74255"  | true             | true              | false
        "74255"  | false            | true              | true
        "74255"  | true             | false             | false
        "74255"  | false            | false             | false
    }
}
