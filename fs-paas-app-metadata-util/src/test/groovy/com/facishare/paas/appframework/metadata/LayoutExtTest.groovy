package com.facishare.paas.appframework.metadata

import com.alibaba.dubbo.common.json.JSON
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import com.facishare.paas.metadata.api.describe.IFieldType

class LayoutExtTest extends Specification {
    JsonNode loadJsonNode(String resource) {
        // 定义ObjectMapper对象，用于解析JSON数据
        ObjectMapper objectMapper = new ObjectMapper()
        // 读取JSON文件并解析它
        try {
            def url = this.getClass().getClassLoader().getResource(resource)
            JsonNode jsonNode = objectMapper.readTree(url)
            // 在这里进行单元测试操作
            // 例如检查JSON数据中的字段是否符合预期
            return jsonNode
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化I18n
     */
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def "ParseAllFields"() {
        given:
        def json = '''
            {
                "buttons": [],
                "components": [{
                    "field_section": [{
                        "form_fields": [{
                            "is_readonly": false,
                            "is_required": true,
                            "api_name": "name",
                            "render_type": "text",
                            "field_name": "name",
                            "is_show_label": false
                        }, {
                            "is_readonly": false,
                            "is_required": false,
                            "api_name": "address",
                            "render_type": "text",
                            "field_name": "address",
                            "is_show_label": false
                        }, {
                            "is_readonly": false,
                            "is_required": false,
                            "api_name": "country",
                            "render_type": "country",
                            "field_name": "country",
                            "is_show_label": false
                        }],
                        "api_name": "layout_AccountObj_mobile_left",
                        "column": 0
                    }, {
                        "form_fields": [{
                            "is_readonly": false,
                            "is_required": false,
                            "api_name": "account_level",
                            "label": "客户级别",
                            "render_type": "select_one",
                            "field_name": "account_level",
                            "is_show_label": false
                        }],
                        "api_name": "layout_AccountObj_mobile_right",
                        "column": 1
                    }],
                    "buttons": [],
                    "api_name": "table_component",
                    "ref_object_api_name": "AccountObj",
                    "include_fields": [],
                    "type": "table"
                }],
                "last_modified_time": *************,
                "is_deleted": false,
                "version": 2,
                "create_time": *************,
                "_id": "c2e967a4084a11e98b3f00155dfc152c",
                "agent_type": "agent_type_mobile",
                "is_show_fieldname": false,
                "layout_description": null,
                "api_name": "layout_AccountObj_mobile",
                "what_api_name": null,
                "default_component": null,
                "created_by": "1000",
                "display_name": "移动端默认列表页",
                "is_default": false,
                "last_modified_by": "1001",
                "layout_type": "list",
                "package": "CRM",
                "ref_object_api_name": "AccountObj",
                "tenant_id": "590064",
                "ui_event_ids": [],
                "hidden_buttons": [],
                "hidden_components": []
            }
        '''

        def layout = new Layout(JSON.parse(json, Map.class))
        def layoutExt = LayoutExt.of(layout)
        when:
        def fields = layoutExt.parseAllFields();
        then:
        fields.size() == 4
    }

    def "ParseAllFields2"() {
        given:
        def json = '''
        {
            "buttons": [],
            "components": [{
                "buttons": [],
                "api_name": "table_component",
                "ref_object_api_name": "object_429t4__c",
                "include_fields": [{
                    "label": "主属性",
                    "render_type": "text",
                    "api_name": "name"
                }, {

                    "label": "创建人",
                    "render_type": "text",
                    "field_name": "created_by"
                }, {

                    "label": "最后修改时间",
                    "render_type": "date_time",
                    "field_name": "last_modified_time"
                }],
                "type": "table"
            }],


            "hidden_buttons": null,
            "hidden_components": null
        }

'''

        def layout = new Layout(JSON.parse(json, Map.class))
        def layoutExt = LayoutExt.of(layout)
        when:
        def fields = layoutExt.parseAllFields();
        then:
        fields.size() == 3
    }

    def "test updateField"() {
        given:
        def layout = new Layout()
        layout.fromJsonString(loadJsonNode('layout.json').toString())
        def dateRangeFieldJsonNode = loadJsonNode('dateRangeField.json')
        def dateRangeFieldGroup = dateRangeFieldJsonNode.get('date_range__c').toString()
        def dateRangeField = FieldDescribeFactory.newInstance(dateRangeFieldGroup)
        def startTimeField = FieldDescribeFactory.newInstance(dateRangeFieldJsonNode.get('start_time__c').toString())
        def endTimeField = FieldDescribeFactory.newInstance(dateRangeFieldJsonNode.get('end_time__c').toString())
        def fieldList = []
        fieldList << startTimeField
        fieldList << endTimeField
        def fieldLayoutPojo = new FieldLayoutPojo()
        fieldLayoutPojo.setShow(true)
        fieldLayoutPojo.setRenderType('date_time')
        when:
        def layoutExt = LayoutExt.of(layout)
        layoutExt.updateField(dateRangeField, fieldList, fieldLayoutPojo)
        then:
        // TODO implement assertions
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateRichTextFieldInLayout方法对HTML富文本字段超出数量限制时的异常情况
     */
    def "validateRichTextFieldInLayout_HTMLRichTextExceededLimitError"() {
        given: "准备测试数据和Mock对象"
        // 设置租户和对象级别的HTML富文本限制
        Map<String, Integer> htmlLimits = ["testTenant": 2]
        Map<String, Integer> objHtmlLimits = ["testObject": 2]
        Map<String, Integer> richLimits = ["testTenant": 3]
        Map<String, Integer> objRichLimits = ["testObject": 3]
        
        Whitebox.setInternalState(AppFrameworkConfig, "htmlRichTextLimit", htmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", objHtmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "richTextLimit", richLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", objRichLimits)
        
        // 创建具有超出限制的HTML富文本字段的布局JSON
        def json = '''
        {
            "buttons": [],
            "components": [{
                "field_section": [{
                    "form_fields": [
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "html_rich_text",
                            "field_name": "html_rich_1"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "html_rich_text",
                            "field_name": "html_rich_2"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "html_rich_text",
                            "field_name": "html_rich_3"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "text",
                            "field_name": "normal_field"
                        }
                    ],
                    "api_name": "layout_section",
                    "column": 0
                }],
                "buttons": [],
                "api_name": "form_component",
                "type": "form"
            }],
            "tenant_id": "testTenant",
            "ref_object_api_name": "testObject"
        }
        '''
        
        def layout = new Layout(JSON.parse(json, Map.class))
        layout.setTenantId("testTenant")
        layout.setRefObjectApiName("testObject")
        
        def layoutExt = LayoutExt.of(layout)
        
        when: "执行验证HTML富文本字段数量的方法"
        layoutExt.validateRichTextFieldInLayout()
        
        then: "应该抛出ValidateException异常"
        def exception = thrown(ValidateException)
        exception != null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateRichTextFieldInLayout方法对协同富文本字段超出数量限制时的异常情况
     */
    def "validateRichTextFieldInLayout_CooperativeRichTextExceededLimitError"() {
        given: "准备测试数据和Mock对象"
        // 设置租户和对象级别的富文本限制
        Map<String, Integer> htmlLimits = ["testTenant": 3]
        Map<String, Integer> objHtmlLimits = ["testObject": 3]
        Map<String, Integer> richLimits = ["testTenant": 1]
        Map<String, Integer> objRichLimits = ["testObject": 1]
        
        Whitebox.setInternalState(AppFrameworkConfig, "htmlRichTextLimit", htmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", objHtmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "richTextLimit", richLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", objRichLimits)
        
        // 创建具有超出限制的协同富文本字段的布局JSON
        def json = '''
        {
            "buttons": [],
            "components": [{
                "field_section": [{
                    "form_fields": [
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "rich_text",
                            "field_name": "rich_text_1"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "rich_text",
                            "field_name": "rich_text_2"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "text",
                            "field_name": "normal_field"
                        }
                    ],
                    "api_name": "layout_section",
                    "column": 0
                }],
                "buttons": [],
                "api_name": "form_component",
                "type": "form"
            }],
            "tenant_id": "testTenant",
            "ref_object_api_name": "testObject"
        }
        '''
        
        def layout = new Layout(JSON.parse(json, Map.class))
        layout.setTenantId("testTenant")
        layout.setRefObjectApiName("testObject")
        
        def layoutExt = LayoutExt.of(layout)
        
        when: "执行验证协同富文本字段数量的方法"
        layoutExt.validateRichTextFieldInLayout()
        
        then: "应该抛出ValidateException异常"
        def exception = thrown(ValidateException)
        exception != null
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateRichTextFieldInLayout方法在富文本字段数量正常时的情况
     */
    def "validateRichTextFieldInLayout_NormalCase"() {
        given: "准备测试数据和Mock对象"
        // 设置租户和对象级别的富文本限制
        Map<String, Integer> htmlLimits = ["testTenant": 3]
        Map<String, Integer> objHtmlLimits = ["testObject": 3]
        Map<String, Integer> richLimits = ["testTenant": 3]
        Map<String, Integer> objRichLimits = ["testObject": 3]
        
        Whitebox.setInternalState(AppFrameworkConfig, "htmlRichTextLimit", htmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", objHtmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "richTextLimit", richLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", objRichLimits)
        
        // 创建具有正常数量富文本字段的布局JSON
        def json = '''
        {
            "buttons": [],
            "components": [{
                "field_section": [{
                    "form_fields": [
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "html_rich_text",
                            "field_name": "html_rich_1"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "rich_text",
                            "field_name": "rich_text_1"
                        },
                        {
                            "is_readonly": false,
                            "is_required": false,
                            "render_type": "text",
                            "field_name": "normal_field"
                        }
                    ],
                    "api_name": "layout_section",
                    "column": 0
                }],
                "buttons": [],
                "api_name": "form_component",
                "type": "form"
            }],
            "tenant_id": "testTenant",
            "ref_object_api_name": "testObject"
        }
        '''
        
        def layout = new Layout(JSON.parse(json, Map.class))
        layout.setTenantId("testTenant")
        layout.setRefObjectApiName("testObject")
        
        def layoutExt = LayoutExt.of(layout)
        
        when: "执行验证富文本字段数量的方法"
        layoutExt.validateRichTextFieldInLayout()
        
        then: "不应抛出异常"
        noExceptionThrown()
    }
}
