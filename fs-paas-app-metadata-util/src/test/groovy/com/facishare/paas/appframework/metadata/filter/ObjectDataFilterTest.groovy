package com.facishare.paas.appframework.metadata.filter

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import spock.lang.Shared
import spock.lang.Specification

class ObjectDataFilterTest extends Specification {

    String searchQueryInfo = '''{"limit":20,"offset":0,"filters":[{"operator":"N","field_name":"field_4t0Cb__c","field_values":["option1"]}],"orders":[{"fieldName":"last_modified_time","isAsc":false}],"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"LIKE","field_name":"name","field_values":["产品"]}]}]}'''

    @Shared
    String describeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1711955706846,"create_time":1610014319834,"description":"","last_modified_by":"1000","display_name":"zxf-主","created_by":"1031","version":121,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_3BbVb__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"fCh","_id":"5ff6de6f599b3e0001803c21","fields":{"field_s57r7__c":{"describe_api_name":"object_3BbVb__c","auto_adapt_places":false,"description":"","is_unique":false,"file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"index_name":"a_4","support_file_types":["jpg","gif","jpeg","png"],"is_index":true,"file_amount_limit":7,"is_active":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"create_time":1672218051630,"is_encrypted":false,"label":"图片多个","is_watermark":false,"field_num":2,"file_size_limit":20971520,"is_ocr_recognition":false,"api_name":"field_s57r7__c","is_need_cdn":false,"_id":"63ac05c373ab8900018ebae4","is_index_field":false,"identify_type":"","help_text":"单个图片不得超过20M","status":"new"},"lock_rule":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319187,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定规则","is_unique":false,"rules":[],"default_value":"default_lock_rule","label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5ff6de6f599b3e0001803c19","is_single":false,"label_r":"锁定规则","is_index_field":false,"index_name":"s_2","help_text":"","status":"new"},"field_p3b8C__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":true,"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1639040590476,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":8,"api_name":"field_p3b8C__c","_id":"61b1c64e63a33500016e8c89","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_m4hw1__c":{"describe_api_name":"object_3BbVb__c","auto_adapt_places":false,"description":"","is_unique":false,"file_source":[],"type":"image","is_required":false,"define_type":"custom","is_single":false,"index_name":"a_3","support_file_types":["jpg","gif","jpeg","png"],"is_index":true,"file_amount_limit":1,"is_active":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"create_time":1649831295701,"is_encrypted":false,"label":"图片","is_watermark":false,"field_num":16,"file_size_limit":20971520,"is_ocr_recognition":true,"api_name":"field_m4hw1__c","is_need_cdn":false,"_id":"62566d7f1287fd000163f6f6","is_index_field":false,"identify_type":"VatInvoice","help_text":"单个图片不得超过20M","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"label_r":"汇率","index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1610014319847,"is_encrypted":false,"display_style":"input","step_value":1,"length":10,"default_value":"","label":"汇率","field_num":7,"api_name":"mc_exchange_rate","_id":"5ff6de6f599b3e0001803c1e","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"field_dy79C__c":{"describe_api_name":"object_3BbVb__c","is_index":true,"is_active":false,"create_time":1688632314786,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"default_value":false,"label":"关闭标识","type":"true_or_false","field_num":20,"is_required":false,"enable_clone":true,"api_name":"field_dy79C__c","options":[{"label":"是","value":true},{"label":"否","value":false}],"define_type":"custom","_id":"64a67bfa6087bb00010fe8ff","is_single":false,"is_index_field":false,"index_name":"b_1","help_text":"","status":"new"},"life_status_before_invalid":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"作废前生命状态","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":false,"label_r":"作废前生命状态","index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1610014319188,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":9,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"5ff6de6f599b3e0001803c1c","is_index_field":false,"help_text":"","status":"new"},"owner_department":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":true,"label_r":"负责人主属部门","index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1610014319833,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"5ff6de6f599b3e0001803c0c","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"lock_status":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"锁定状态","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","is_single":false,"label_r":"锁定状态","index_name":"s_4","is_index":true,"is_active":true,"create_time":1610014319187,"is_encrypted":false,"default_value":"0","label":"锁定状态","field_num":3,"is_need_convert":false,"api_name":"lock_status","_id":"5ff6de6f599b3e0001803c1a","is_index_field":false,"config":{},"help_text":"","status":"new"},"package":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319834,"pattern":"","is_unique":false,"description":"package","label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","max_length":200,"status":"released"},"create_time":{"describe_api_name":"object_3BbVb__c","is_index":true,"create_time":1610014319834,"is_unique":false,"description":"create_time","label":"创建时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"field_pD1cc__c":{"describe_api_name":"object_3BbVb__c","auto_location":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"location","is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"t_5","is_index":true,"is_active":true,"create_time":1671534640473,"is_encrypted":false,"label":"定位","is_geo_index":false,"field_num":17,"api_name":"field_pD1cc__c","range_limit":false,"_id":"63a19830fbab970001c33184","radius_range":100,"is_index_field":false,"help_text":"","status":"new"},"field_anp1B__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"d_2","max_length":14,"is_index":true,"is_active":true,"create_time":1639040616524,"is_encrypted":false,"display_style":"input","step_value":1,"length":12,"default_value":"","label":"数字","field_num":14,"api_name":"field_anp1B__c","_id":"61b1c66863a33500016e8ddd","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"version":{"describe_api_name":"object_3BbVb__c","is_index":false,"create_time":1610014319834,"length":8,"is_unique":false,"description":"version","label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"created_by":{"describe_api_name":"object_3BbVb__c","is_index":true,"is_active":true,"create_time":1610014319834,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"relevant_team":{"describe_api_name":"object_3BbVb__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1610014319845,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5ff6de6f599b3e0001803c18","is_single":false,"label_r":"相关团队","is_index_field":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"field_2sXf1__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"type":"long_text","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"t_1","max_length":2000,"is_index":true,"is_active":true,"create_time":1639040594143,"is_encrypted":false,"min_length":0,"default_value":"","label":"多行文本","field_num":12,"api_name":"field_2sXf1__c","_id":"61b1c65263a33500016e8cc8","is_index_field":false,"help_text":"","status":"new"},"field_ZEnbo__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[],"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_8","is_index":true,"is_active":true,"create_time":1689240506592,"is_encrypted":false,"default_value":"","label":"查找关联-zxf","target_api_name":"object_Uh1x4__c","target_related_list_name":"target_related_list_2Jpd0__c","field_num":24,"target_related_list_label":"zxf","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_ZEnbo__c","_id":"64afc3ba2519f70001fdcef4","is_index_field":true,"help_text":"","status":"new"},"field_wnU66__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"type":"html_rich_text","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"s_0","max_length":131072,"is_index":false,"is_active":true,"create_time":1709202939019,"is_encrypted":false,"default_value":"","label":"富文本","field_num":29,"api_name":"field_wnU66__c","_id":"65e05dfbcaca160001f1c5a0","is_index_field":false,"help_text":"","status":"new"},"data_own_department":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"department","is_required":false,"wheres":[],"optional_type":"department","define_type":"package","is_single":true,"label_r":"归属部门","index_name":"data_owner_dept_id","is_index":true,"is_active":true,"create_time":1610014319834,"is_encrypted":false,"default_value":"","label":"归属部门","is_need_convert":false,"api_name":"data_own_department","_id":"62566e311287fd000163f7e7","is_index_field":false,"help_text":"","status":"released"},"field_79Iq9__c":{"expression_type":"js","return_type":"number","describe_api_name":"object_3BbVb__c","auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_4","is_index":true,"is_active":true,"expression":"$field_anp1B__c$","create_time":1689240408498,"is_encrypted":false,"label":"计算字段-数字","field_num":22,"api_name":"field_79Iq9__c","_id":"64afc3592519f70001fdc171","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"name":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","is_single":false,"label_r":"主属性","index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1610014327870,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"5ff6de6f599b3e0001803c0a","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_N4tuo__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_0","max_length":100,"is_index":true,"is_active":false,"create_time":1702885120926,"is_encrypted":false,"default_value":"","label":"我也是新建的就是有些慢","field_num":28,"api_name":"field_N4tuo__c","_id":"657ff7019feacd00019bf052","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"_id":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319834,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"field_4t0Cb__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"enable_clone":true,"options":[{"label":"是","value":"option1"},{"label":"否","value":"R2IhaQ4x2"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_single":false,"index_name":"s_9","is_index":true,"is_active":true,"create_time":1696687020326,"is_encrypted":false,"default_value":"R2IhaQ4x2","label":"已发货完成","field_num":26,"api_name":"field_4t0Cb__c","_id":"652163aca9dbcf0001f6eda5","is_index_field":false,"config":{},"help_text":"","status":"new"},"tenant_id":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319834,"pattern":"","is_unique":false,"description":"tenant_id","label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"data_own_organization":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"department","is_required":false,"wheres":[],"optional_type":"department","define_type":"package","is_single":true,"label_r":"归属组织","index_name":"a_2","is_index":true,"is_active":true,"create_time":1647329963905,"is_encrypted":false,"default_value":"","label":"归属组织","is_need_convert":false,"api_name":"data_own_organization","_id":"623042ac04a720000162e97e","is_index_field":false,"help_text":"","status":"released"},"field_1j8hm__c":{"describe_api_name":"object_3BbVb__c","auto_adapt_places":false,"support_file_suffix":[],"description":"","is_unique":false,"file_source":["local","net"],"type":"file_attachment","is_required":false,"define_type":"custom","is_single":false,"index_name":"a_5","support_file_types":[],"is_index":true,"file_amount_limit":1,"is_active":true,"create_time":1672224772356,"is_encrypted":false,"label":"附件","field_num":18,"file_size_limit":104857600,"api_name":"field_1j8hm__c","_id":"63ac200427d46d0001e0e3e9","is_index_field":false,"help_text":"单个文件不得超过100M","status":"new"},"field_g93Hk__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_many","default_to_zero":false,"is_required":false,"enable_clone":true,"options":[{"not_usable":false,"label":"子","value":"v33SM03ve"},{"not_usable":false,"label":"丑","value":"c245o1dba"},{"not_usable":false,"label":"寅","value":"Rqwssqt7W"},{"not_usable":false,"label":"卯","value":"QQ087R3o2"},{"not_usable":false,"label":"辰","value":"ZS3X48k30"},{"not_usable":false,"label":"巳","value":"tix1KJ6Dy"},{"not_usable":false,"label":"午","value":"d620Mcose"},{"not_usable":false,"label":"未","value":"wElooMEK8"},{"not_usable":false,"label":"申","value":"e5mia8QTi"},{"not_usable":false,"label":"酉","value":"bj9232o03"},{"not_usable":false,"label":"戌","value":"429MSbHMT"},{"not_usable":false,"label":"亥","value":"NU11dXcLH"},{"not_usable":false,"label":"示例选项1","value":"zefLmOj1x"},{"not_usable":false,"label":"示例选项2","value":"14tJ9G2xY"},{"not_usable":false,"label":"示例选项3","value":"07axJgyKi"},{"not_usable":false,"label":"示例选项4","value":"2X3406o4n"},{"not_usable":false,"label":"示例选项5","value":"71l8Bp12N"}],"define_type":"custom","is_single":false,"index_name":"a_0","is_index":true,"is_active":true,"create_time":1699614137417,"is_encrypted":false,"option_type":"general","default_value":[],"label":"多选","field_num":27,"option_api_name":"option_tr6JL__c","api_name":"field_g93Hk__c","_id":"654e0db9c060db0001ec633e","is_index_field":false,"config":{},"help_text":"","status":"new"},"origin_source":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319834,"is_unique":false,"label":"数据来源","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"origin_source","options":[{"label":"数据同步","value":"0"}],"define_type":"system","is_extend":false,"index_name":"s_os","config":{"display":0},"status":"released","description":""},"lock_user":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319187,"is_encrypted":false,"auto_adapt_places":false,"description":"加锁人","is_unique":false,"where_type":"field","label":"加锁人","type":"employee","field_num":5,"is_need_convert":false,"is_required":false,"wheres":[],"api_name":"lock_user","define_type":"package","_id":"5ff6de6f599b3e0001803c1b","is_single":true,"label_r":"加锁人","is_index_field":false,"index_name":"a_1","help_text":"","status":"new"},"field_e2d2P__c":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1689240531388,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"currency","remove_mask_roles":{},"description":"","is_unique":false,"label":"引用字段-金额","type":"quote","quote_field":"field_ZEnbo__c__r.field_kXqA9__c","is_required":false,"api_name":"field_e2d2P__c","define_type":"custom","_id":"64afc3d32519f70001fdd203","is_single":false,"is_index_field":false,"index_name":"d_6","is_show_mask":false,"help_text":"","status":"new"},"is_deleted":{"describe_api_name":"object_3BbVb__c","is_index":false,"create_time":1610014319834,"is_unique":false,"description":"is_deleted","default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","options":[],"define_type":"system","index_name":"is_del","status":"released"},"object_describe_api_name":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319834,"pattern":"","is_unique":false,"description":"object_describe_api_name","label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","max_length":200,"status":"released"},"out_owner":{"describe_api_name":"object_3BbVb__c","is_index":true,"is_active":true,"create_time":1610014319834,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"index_name":"o_owner","config":{"display":1},"status":"released","description":""},"field_xga2D__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"date","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"l_1","is_index":true,"is_active":true,"create_time":1643013488823,"is_encrypted":false,"default_value":"","label":"日期","time_zone":"GMT+8","field_num":15,"api_name":"field_xga2D__c","date_format":"yyyy-MM-dd","_id":"61ee65706698240001c8266c","is_index_field":false,"help_text":"","status":"new"},"field_r10fy__c":{"expression_type":"js","return_type":"currency","describe_api_name":"object_3BbVb__c","auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_5","is_index":true,"is_active":true,"expression":"$field_anp1B__c$","create_time":1689240451792,"is_encrypted":false,"label":"计算字段-金额","field_num":23,"api_name":"field_r10fy__c","_id":"64afc3842519f70001fdcb9b","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"mc_functional_currency":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319848,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"default_value":"","label":"本位币","type":"select_one","field_num":11,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"5ff6de6f599b3e0001803c1f","is_single":false,"label_r":"本位币","is_index_field":false,"index_name":"s_5","config":{},"help_text":"","status":"new"},"owner":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"employee","is_required":true,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"ISN","field_name":"vice_departments","field_values":[]}]}],"define_type":"package","is_single":true,"label_r":"负责人","index_name":"owner","is_index":true,"is_active":true,"create_time":1610014319832,"is_encrypted":false,"default_value":"","label":"负责人","is_need_convert":false,"api_name":"owner","_id":"5ff6de6f599b3e0001803c0b","is_index_field":false,"help_text":"","status":"new"},"field_B0c4B__c":{"return_type":"currency","describe_api_name":"object_3BbVb__c","auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"object_ssu7t__c","is_required":false,"wheres":[],"define_type":"custom","is_single":false,"index_name":"d_7","field_api_name":"field_O1lbo__c","is_index":true,"default_result":"d_null","is_active":true,"create_time":1689689497062,"is_encrypted":false,"count_type":"sum","count_field_api_name":"field_6U0pa__c","label":"汇总收款金额","field_num":25,"count_to_zero":false,"api_name":"field_B0c4B__c","count_field_type":"currency","_id":"64b69d992054fd00017498a2","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"field_Q51E3__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference_many","is_required":false,"wheres":[],"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"a_6","is_index":true,"is_active":true,"create_time":1688033677380,"is_encrypted":false,"default_value":"","label":"查找关联(多选)","target_api_name":"object_Uh1x4__c","target_related_list_name":"target_related_list_AaXda__c","field_num":19,"target_related_list_label":"zxf-主","action_on_target_delete":"set_null","api_name":"field_Q51E3__c","_id":"649d598e96389500017f507b","is_index_field":false,"help_text":"","status":"new"},"last_modified_time":{"describe_api_name":"object_3BbVb__c","is_index":true,"create_time":1610014319834,"is_unique":false,"description":"last_modified_time","label":"最后修改时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"dimension_d2":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"dimension","dimension_type":"d2","is_required":false,"wheres":[],"define_type":"package","is_single":false,"index_name":"a_8","is_index":true,"is_active":true,"create_time":1710240350722,"is_encrypted":false,"default_value":"","label":"归属行业线","is_need_convert":false,"api_name":"dimension_d2","_id":"65f0325efd02e500019b4ff6","is_index_field":false,"help_text":"","status":"new"},"life_status":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","is_single":false,"label_r":"生命状态","index_name":"s_3","is_index":true,"is_active":true,"create_time":1610014319840,"is_encrypted":false,"default_value":"normal","label":"生命状态","field_num":4,"is_need_convert":false,"api_name":"life_status","_id":"5ff6de6f599b3e0001803c13","is_index_field":false,"config":{},"help_text":"","status":"new"},"dimension_d1":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"dimension","dimension_type":"d1","is_required":false,"wheres":[],"define_type":"package","is_single":false,"index_name":"a_7","is_index":true,"is_active":true,"create_time":1710240334246,"is_encrypted":false,"default_value":"","label":"归属区域线","is_need_convert":false,"api_name":"dimension_d1","_id":"65f0324efd02e500019b4fe1","is_index_field":false,"help_text":"","status":"new"},"dimension_d3":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"dimension","dimension_type":"d3","is_required":false,"wheres":[],"define_type":"package","is_single":false,"index_name":"a_9","is_index":true,"is_active":true,"create_time":1710240362773,"is_encrypted":false,"default_value":"","label":"归属产品线","is_need_convert":false,"api_name":"dimension_d3","_id":"65f0326afd02e500019b50a0","is_index_field":false,"help_text":"","status":"new"},"last_modified_by":{"describe_api_name":"object_3BbVb__c","is_index":true,"is_active":true,"create_time":1610014319834,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_3BbVb__c","is_index":false,"is_active":true,"create_time":1610014319834,"pattern":"","is_unique":false,"description":"out_tenant_id","label":"外部企业","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","index_name":"o_ei","config":{"display":0},"max_length":200,"status":"released"},"mc_currency":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"options":[{"label":"BYR - 白俄罗斯卢布","value":"BYR"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"BIF - 布隆迪法郎","value":"BIF"},{"label":"BRL - 巴西币","value":"BRL"},{"label":"BOB - 玻利维亚的玻利维亚诺","value":"BOB"},{"label":"BYN - 白俄罗斯卢布","value":"BYN"},{"label":"BZD - 伯利兹元","value":"BZD"},{"label":"BOV - 玻利维亚姆夫多尔","value":"BOV"},{"label":"BRB - 巴西克鲁塞罗（旧）","value":"BRB"},{"label":"BSD - 巴哈马元","value":"BSD"},{"label":"自定义QWE","value":"qwe"},{"label":"自定义zas","value":"zas"},{"label":"翻译ASD","value":"asd"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"ada名称","value":"ada"},{"label":"BTN - 不丹卢比","value":"BTN"},{"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"BHD - Bahraini Dinar","value":"BHD"},{"label":"BND - Brunei Dollar","value":"BND"},{"label":"BMD - 百慕大元","value":"BMD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"label":"BWP - 博茨瓦纳普拉","value":"BWP"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","is_single":false,"label_r":"币种","index_name":"s_6","is_index":true,"is_active":true,"create_time":1610014319846,"is_encrypted":false,"default_value":"","label":"币种","field_num":6,"api_name":"mc_currency","_id":"5ff6de6f599b3e0001803c1d","is_index_field":false,"config":{},"help_text":"","status":"new"},"record_type":{"describe_api_name":"object_3BbVb__c","is_index":true,"is_active":true,"create_time":1610014319839,"is_encrypted":false,"auto_adapt_places":false,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"api_name":"record_2PqA1__c","description":"","label":"业务类型1"}],"define_type":"package","_id":"5ff6de6f599b3e0001803c12","is_single":false,"label_r":"业务类型","is_index_field":false,"index_name":"r_type","config":{},"help_text":"","status":"released"},"field_Z1phM__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1689240379635,"is_encrypted":false,"length":12,"default_value":"","label":"金额","currency_unit":"￥","field_num":21,"api_name":"field_Z1phM__c","_id":"64afc33b2519f70001fdbcc5","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"field_22eNM__c":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"enable_clone":true,"options":[{"label":"1","value":"KQ7o2uwS7"},{"label":"2","value":"vPNnnj9Qm"},{"label":"3","value":"tvgsB14F5"},{"not_usable":true,"label":"4","value":"pPny8e2eQ"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1639040611376,"is_encrypted":false,"default_value":"","label":"单选","field_num":13,"api_name":"field_22eNM__c","_id":"61b1c66363a33500016e8dda","is_index_field":false,"config":{},"help_text":"","status":"new"},"mc_exchange_rate_version":{"describe_api_name":"object_3BbVb__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":false,"label_r":"汇率版本","index_name":"t_3","max_length":256,"is_index":false,"is_active":true,"create_time":1610014319849,"is_encrypted":false,"default_value":"","label":"汇率版本","field_num":10,"api_name":"mc_exchange_rate_version","_id":"5ff6de6f599b3e0001803c20","is_index_field":false,"help_text":"","status":"new"}},"release_version":"6.4","actions":{}}'''

    def "test doFilter invariable order" () {
        given:
        def templateQuery = SearchTemplateQuery.fromJsonString(searchQueryInfo)
        IObjectDescribe detail = new ObjectDescribe(JSON.parseObject(describeJson))
        ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                .describeExt(ObjectDescribeExt.of(detail))
                .queryExt(SearchTemplateQueryExt.of(templateQuery))
                .build();
        def dataList = []
        def data = new ObjectData(['_id': '1', 'name': '产品1'])
        dataList << data
        dataList << data
        dataList << new ObjectData(['_id':'2','name':'产品2'])
        dataList << new ObjectData(['_id':'3','name':'产品3'])
        dataList << new ObjectData(['_id':'4','name':'产品4'])
        dataList << new ObjectData(['_id':'5','name':'产品5'])
        when:
        def result = dataFilter.doFilter(dataList)

        then:
        result.size() == 5
        def idList = dataList.collect { ObjectData it -> it.getId() }.unique()
        def resultIdList = result.collect({ ObjectData it -> it.getId() })
        idList == resultIdList
    }
}
