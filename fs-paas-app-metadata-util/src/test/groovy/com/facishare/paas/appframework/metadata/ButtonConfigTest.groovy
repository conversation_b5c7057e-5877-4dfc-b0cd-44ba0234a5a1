package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.crm.userdefobj.DefObjConstants
import com.facishare.paas.appframework.metadata.config.ButtonConfig
import com.facishare.paas.appframework.metadata.config.UdefButtonConfig
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.impl.search.Filter
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Table
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * create by zhao<PERSON> on 2019/09/18
 */
class ButtonConfigTest extends Specification {
    /** 初始化数据*/
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    def setup() {
        initButtonConfig()
    }

    private void initButtonConfig() {
        def udefButtenConfigJson = '''{"api_name":"udef_button","descrieb_api_name":"udef","tenant_id":"-100",
"type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,"attr":{"api_name":{"edit":0},
"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},"wheres":{"edit":1},
"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},"stage_actions":{"pre":{"edit":1},
"current":{"edit":0},"post":{"edit":1}}}},"edit":1}'''
        def addButtonConfigJson = '''{"api_name":"Add_button_default","descrieb_api_name":"udef",
"tenant_id":"-100","type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,
"attr":{"api_name":{"edit":0},"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},
"wheres":{"edit":1},"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},
"stage_actions":{"pre":{"edit":1},"current":{"edit":0},"post":{"edit":1}}}},"edit":0}'''
        def allocateButtonConfigJson = '''{"api_name":"AccountObj_Allocate_button_default","descrieb_api_name":"AccountObj",
"tenant_id":"-100","type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,
"attr":{"api_name":{"edit":0},"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},
"wheres":{"edit":1},"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},
"stage_actions":{"pre":{"edit":1},"current":{"edit":0},"post":{"edit":1}}}},"edit":0}'''

        def BUTTON_SYSTEM_FILTERS = '''{"UDObj":{"Lock_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"0\\"]}]","Unlock_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"1\\"]}]","EditTeamMember_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"0\\"]}]","AddTeamMember_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"0\\"]}]","DeleteTeamMember_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"0\\"]}]","ChangeOwner_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"0\\"]}]","Abolish_button_default":"[{\\"operator\\":\\"EQ\\",\\"define_type\\":\\"system\\",\\"connector\\":\\"AND\\",\\"field_name\\":\\"lock_status\\",\\"field_values\\":[\\"0\\"]}]"}}'''

        def udefButtenConfig = UdefButtonConfig.fromJson(udefButtenConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put(DefObjConstants.UDOBJ, ButtonConfig.UDEF_BUTTON, udefButtenConfig)
        def addButtonConfig = UdefButtonConfig.fromJson(addButtonConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put(DefObjConstants.UDOBJ, "Add_button_default", addButtonConfig)
        def allocateButtonConfig = UdefButtonConfig.fromJson(allocateButtonConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put("AccountObj", "AccountObj_Allocate_button_default", allocateButtonConfig)

        Map<String, String, String> buttonSystemFilters = JSON.parseObject(BUTTON_SYSTEM_FILTERS, Map)
        buttonSystemFilters.collect { k, v ->
            v.collect { key, value ->
                ButtonConfig.BUTTON_SYSTEM_FILTERS.put(k, key,
                        JSON.parseArray(value, Filter)
                )
            }
        }
    }

    def "getButtonConfigByDescribeApiNameAndButtonApiName"() {
        when:
        def config = ButtonConfig.getConfig(describeApiName, buttonApiName)
        then:
        config.getDescribeApiName() == describeApiName
        config.getApiName() == buttonApiName
        config.getEdit() == edit
        where:
        tenantId | describeApiName   | buttonApiName                        | edit
        "71698"  | "object_1xq2p__c" | "button_hpP3K__c"                    | 1
        "71698"  | "object_1xq2p__c" | "Add_button_default"                 | 0
        "71698"  | "AccountObj"      | "Add_button_default"                 | 0
        "71698"  | "AccountObj"      | "AccountObj_Allocate_button_default" | 0
    }


    def "parseTableFromConfig"() {
        String str = '''{"UDObj":{"increment":["Export","ExportFile"],"order":["ChangeOwner","Abolish","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"AccountObj":{"increment":["SendMail","Export","ExportFile"],"order":["SendMail","Move","ChangeOwner","Abolish","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"ContactObj":{"increment":["SendMail","Merge","Export","ExportFile"],"order":["SendMail","Merge","ChangeOwner","Abolish","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"LeadsObj":{"increment":["SendMail","Export","ExportFile"],"order":["SendMail","Return","Abolish","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"OpportunityObj":{"increment":["ChangeSaleAction","Export","ExportFile"],"order":["ChangeSaleAction","Abolish","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"ProductObj":{"increment":["StatusOn","StatusOff","Export","ExportFile"],"order":["ChangeOwner","Abolish","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile","StatusOn","StatusOff"]},"PriceBookObj":{"increment":["BulkDelete","Export","ExportFile"],"order":["ChangeOwner","BulkDelete","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"PriceBookProductObj":{"increment":["BulkDelete","Export","ExportFile"],"order":["ChangeOwner","AddTeamMember","DeleteTeamMember","Lock","Unlock","Export","ExportFile"]},"StockObj":{"increment":["Export"],"order":["ChangeOwner","Export"]},"ServiceRecordObj":{"increment":["Export"],"order":["Export"]},"OrderPaymentObj":{"increment":["Export"],"order":["Export"]},"CustomerAccountObj":{"increment":["Export"],"order":["Export"]}}'''
        when:
        Table<String, String, List<String>> table = JSON.parseObject(str, Table.class)
        println "result==>" + table

        Map map = JSON.parseObject(str, Map.class)
        println "map==>" + map
        then:
        noExceptionThrown()
    }

    def "test_generated_button_system_filter"() {
        given:
        IUdefButton button = Mock(IUdefButton)
        button.getDefineType() >> "system"
        button.getDescribeApiName() >> objectApiName
        button.getApiName() >> buttonApiName
        when:
        ButtonConfig.generatedButtonSystemFilter(button)
        then:
        1 * button.setWheres(_)
        where:
        objectApiName       | buttonApiName
        "object_testObj__c" | "Lock_button_default"
    }
}
