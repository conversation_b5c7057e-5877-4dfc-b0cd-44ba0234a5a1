package com.facishare.paas.appframework.metadata.layout.component

import com.facishare.paas.appframework.metadata.FieldDescribeExt
import com.facishare.paas.expression.ExpressionService
import com.facishare.paas.expression.ExpressionServiceImpl
import spock.lang.Specification

class ListComponentExtTest extends Specification {

    ListComponentExt listComponentExt = Mock(ListComponentExt)

    def "getGeneralInfoByPageType"() {
        given:
        def pageType = "enable_selected_layout"
        when:
        listComponentExt.getGeneralInfoByPageType(pageType)

        then:
        noExceptionThrown()
    }

    def "getGeneralInfoByPageTypeNoType"() {
        given:
        def pageType = "errorType"
        when:
        listComponentExt.getGeneralInfoByPageType(pageType)

        then:
        noExceptionThrown()
    }
}
