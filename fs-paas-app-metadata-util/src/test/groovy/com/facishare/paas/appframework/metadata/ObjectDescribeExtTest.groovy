package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * create by <PERSON><PERSON><PERSON> on 2019/11/13
 */
class ObjectDescribeExtTest extends Specification {

    /**
     * 初始化I18n
     */
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def "test email pattern"() {
        given:
        Pattern pattern = Pattern.compile(ObjectDescribeExt.EMAIl_PATTERN)
        
        when:
        Matcher matcher = pattern.matcher(emailStr)
        
        then:
        matcher.find()
        
        where:
        emailStr           | _
        "<EMAIL>"      | _
        "<EMAIL>"  | _
        "<EMAIL>"       | _
        "<EMAIL>" | _
    }

    def "test email pattern not matcher"() {
        given:
        Pattern pattern = Pattern.compile(ObjectDescribeExt.EMAIl_PATTERN)
        
        when:
        Matcher matcher = pattern.matcher(emailStr)
        
        then:
        !matcher.find()
        
        where:
        emailStr   | _
        "s163.com" | _
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateRichTextFieldInDescribe方法对HTML富文本字段的数量限制验证
     */
    @Unroll
    def "validateRichTextFieldInDescribe_HtmlRichTextExceededLimit"() {
        given: "准备测试数据和Mock对象"
        // 设置 AppFrameworkConfig 的相关方法返回
        GroovySpy(AppFrameworkConfig, global: true)
        
        // 设置租户和对象级别的HTML富文本限制
        Map<String, Integer> htmlLimits = ["testTenant": 2]
        Map<String, Integer> objHtmlLimits = ["testObject": 2]
        
        Whitebox.setInternalState(AppFrameworkConfig, "htmlRichTextLimit", htmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", objHtmlLimits)
        
        // 创建测试对象
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("testObject")
        objectDescribe.setTenantId("testTenant")
        
        // 创建HTML富文本字段
        IFieldDescribe htmlRichTextField1 = FieldDescribeFactory.newInstance([
            "api_name": "htmlRichText1",
            "type": IFieldType.HTML_RICH_TEXT
        ])
        
        IFieldDescribe htmlRichTextField2 = FieldDescribeFactory.newInstance([
            "api_name": "htmlRichText2",
            "type": IFieldType.HTML_RICH_TEXT
        ])
        
        IFieldDescribe htmlRichTextField3 = FieldDescribeFactory.newInstance([
            "api_name": "htmlRichText3",
            "type": IFieldType.HTML_RICH_TEXT
        ])
        
        // 设置普通字段
        IFieldDescribe normalField = FieldDescribeFactory.newInstance([
            "api_name": "normal",
            "type": IFieldType.TEXT
        ])
        
        // 设置字段列表
        objectDescribe.setFieldDescribes([htmlRichTextField1, htmlRichTextField2, htmlRichTextField3, normalField])
        
        def objectDescribeExt = ObjectDescribeExt.of(objectDescribe)
        
        when: "执行HTML富文本字段数量验证方法"
        objectDescribeExt.validateRichTextFieldInDescribe()
        
        then: "验证是否抛出异常"
        thrown(ValidateException)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateRichTextFieldInDescribe方法对协同富文本字段的数量限制验证
     */
    @Unroll
    def "validateRichTextFieldInDescribe_CooperativeRichTextExceededLimit"() {
        given: "准备测试数据和Mock对象"
        // 设置 AppFrameworkConfig 的相关方法返回
        GroovySpy(AppFrameworkConfig, global: true)
        
        // 设置租户和对象级别的协同富文本限制
        Map<String, Integer> richLimits = ["testTenant": 2]
        Map<String, Integer> objRichLimits = ["testObject": 2]
        
        Whitebox.setInternalState(AppFrameworkConfig, "richTextLimit", richLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", objRichLimits)
        
        // 创建测试对象
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("testObject")
        objectDescribe.setTenantId("testTenant")
        
        // 创建协同富文本字段
        IFieldDescribe cooperativeRichTextField1 = FieldDescribeFactory.newInstance([
            "api_name": "richText1",
            "type": IFieldType.RICH_TEXT
        ])
        
        IFieldDescribe cooperativeRichTextField2 = FieldDescribeFactory.newInstance([
            "api_name": "richText2",
            "type": IFieldType.RICH_TEXT
        ])
        
        IFieldDescribe cooperativeRichTextField3 = FieldDescribeFactory.newInstance([
            "api_name": "richText3",
            "type": IFieldType.RICH_TEXT
        ])
        
        // 设置普通字段
        IFieldDescribe normalField = FieldDescribeFactory.newInstance([
            "api_name": "normal",
            "type": IFieldType.TEXT
        ])
        
        // 设置字段列表
        objectDescribe.setFieldDescribes([cooperativeRichTextField1, cooperativeRichTextField2, cooperativeRichTextField3, normalField])
        
        def objectDescribeExt = ObjectDescribeExt.of(objectDescribe)
        
        when: "执行协同富文本字段数量验证方法"
        objectDescribeExt.validateRichTextFieldInDescribe()
        
        then: "验证是否抛出异常"
        thrown(ValidateException)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateRichTextFieldInDescribe方法在正常情况下不抛出异常
     */
    @Unroll
    def "validateRichTextFieldInDescribe_NormalCase"() {
        given: "准备测试数据和Mock对象"
        // 设置 AppFrameworkConfig 的相关方法返回
        GroovySpy(AppFrameworkConfig, global: true)
        
        // 设置租户和对象级别的富文本限制
        Map<String, Integer> htmlLimits = ["testTenant": 3]
        Map<String, Integer> objHtmlLimits = ["testObject": 3]
        Map<String, Integer> richLimits = ["testTenant": 3]
        Map<String, Integer> objRichLimits = ["testObject": 3]
        
        Whitebox.setInternalState(AppFrameworkConfig, "htmlRichTextLimit", htmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", objHtmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "richTextLimit", richLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", objRichLimits)
        
        // 创建测试对象
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("testObject")
        objectDescribe.setTenantId("testTenant")
        
        // 创建富文本字段
        IFieldDescribe htmlRichTextField = FieldDescribeFactory.newInstance([
            "api_name": "htmlRichText",
            "type": IFieldType.HTML_RICH_TEXT
        ])
        
        IFieldDescribe cooperativeRichTextField = FieldDescribeFactory.newInstance([
            "api_name": "richText",
            "type": IFieldType.RICH_TEXT
        ])
        
        // 设置普通字段
        IFieldDescribe normalField = FieldDescribeFactory.newInstance([
            "api_name": "normal",
            "type": IFieldType.TEXT
        ])
        
        // 设置字段列表
        objectDescribe.setFieldDescribes([htmlRichTextField, cooperativeRichTextField, normalField])
        
        def objectDescribeExt = ObjectDescribeExt.of(objectDescribe)
        
        when: "执行富文本字段数量验证方法"
        objectDescribeExt.validateRichTextFieldInDescribe()
        
        then: "验证没有抛出异常"
        noExceptionThrown()
    }
}
