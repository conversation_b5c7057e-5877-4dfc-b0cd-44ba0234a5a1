package com.facishare.paas.appframework.metadata;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ButtonActionType {
    Convert("convert", "对象映射"),
    Updates("updates", "字段更新"),
    SendCRMRemind("send_crm_remind", "发送提醒"),
    SendMail("send_email", "发邮件"),
    CustomFunction("custom_function", "自定义函数"),
    CustomBiz("custom_biz", "预制业务规则"),
    UIEvent("ui_event", "UI事件"),
    AIAgent("ai_agent", "AI Agent"),
    ;


    private String id;

    ButtonActionType(String id, String label) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    private static Map<String, ButtonActionType> buttonActionTypeMap;

    static {
        buttonActionTypeMap = Stream.of(values()).collect(Collectors.toMap(ButtonActionType::getId, it -> it));
    }

    public static ButtonActionType of(String actionType) {
        return buttonActionTypeMap.get(actionType);
    }
}
