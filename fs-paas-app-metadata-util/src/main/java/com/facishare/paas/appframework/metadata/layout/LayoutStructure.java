package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liwei on 2019/12/6
 */
@Slf4j
@Builder
@Data
public class LayoutStructure {
    public static final String PLUGINS = "plugins";
    public static final String CLIENT_TYPE = "client_type";
    public static final String CLIENT_TYPE_APP = "app";
    public static String LAYOUT = "layout";
    public static String COMPONENTS = "components";
    public static String COLUMNS = "columns";
    public static String HEAD = "head";
    public static String BODY = "body";
    public static String LEFT = "left";
    public static String RIGHT = "right";
    public static String API_NAME = "api_name";
    public static String CHILDREN = "children";
    public static String CONTAINER_DEFAULT = "container_default";
    public static String FIELD_ALIGN = "field_align";
    public static String DETAIL_FORM_LAYOUT = "detail_form_layout";

    public static String IS_TILE_HELP_TEXT = "is_tile_help_text";

    public static String IS_OPEN_TILE_HELP_TEXT = "is_open_tile_help_text";

    public static String TITLE_NAME = "title_name";
    public static String SHOW_TAG = "show_tag";

    private List<Map> head;
    private Map body;

    public static LayoutStructure of() {
        Map headInfo = convertToMap(ComponentExt.HEAD_INFO_COMPONENT_NAME);
        return LayoutStructure.builder().head(Lists.newArrayList(headInfo)).build();
    }

    public static void removePlugins(LayoutExt layoutExt) {
        Optional.ofNullable(layoutExt.getLayoutStructure()).ifPresent(x -> x.remove(PLUGINS));
    }

    private void setLayoutStructure(LayoutExt layoutExt) {
        Map<String, Object> layoutStructure = Maps.newHashMap();
        layoutStructure.put(HEAD, this.getHead());
        layoutStructure.put(BODY, this.getBody());
        layoutExt.setLayoutStructure(layoutStructure);
    }

    public static void buildLayoutStructure(LayoutExt layoutExt, ObjectDescribeExt describeExt, List<IComponent> components,
                                            List<Map> leftTabsApiNames, List<Map> leftBottomComponentApiNames, boolean isDetail,
                                            boolean isMobileLayout) {
        if (!layoutExt.isNewLayout()) {
            LayoutStructure layoutStructure = of();
            List<Map> leftStructureList = Lists.newArrayList();
            String containerApiName = CONTAINER_DEFAULT + "_" + layoutExt.getName();
            addDefaultComponentsAndStructure(layoutExt, describeExt, components, leftStructureList,
                    containerApiName, leftTabsApiNames, isDetail);

            if (CollectionUtils.notEmpty(leftBottomComponentApiNames)) {
                leftStructureList.addAll(leftBottomComponentApiNames);
            }

            List<Map> rightList = getRightComponentInfo(components);
            Map<String, Object> body = Maps.newHashMap();
            body.put(LEFT, leftStructureList);
            body.put(RIGHT, rightList);

            layoutStructure.setBody(body);
            layoutStructure.setLayoutStructure(layoutExt);

            //适配hidden_components
            List<String> oldHiddenComponents = layoutExt.getHiddenComponents();
            Set<String> hiddenComponents = layoutExt.getComponentsSilently().stream()
                    .filter(x -> x.isHidden()).map(x -> x.getName()).collect(Collectors.toSet());
            if (CollectionUtils.notEmpty(oldHiddenComponents)) {
                hiddenComponents.addAll(oldHiddenComponents);
            }
            layoutExt.setHiddenComponents(Lists.newArrayList(hiddenComponents));
        } else {
            modifyLayoutStructure(layoutExt, components, isMobileLayout);
            //将不在components中的组件从layout_structure里清理掉
            clearLayoutStructureByComponents(layoutExt, components);
            // 处理被禁用的按钮
            handleNotActiveButtons(layoutExt);
        }
        //根据端上需求适配各种版本的layout_structure
        convertLayoutStructure(layoutExt);
        layoutExt.fillComponentId(components);
        //Web端布局的按钮放在head_info里
        if (!isMobileLayout) {
            layoutExt.setButtons(Lists.newArrayList());
        }
        layoutExt.removeTopInfo();
        layoutExt.setComponents(components);
    }

    private static void convertLayoutStructure(LayoutExt layoutExt) {
        //库里是老布局，端上需要V3布局，则V2转V3
        if (layoutExt.isV2Layout() && LayoutContext.isVersionV3()) {
            convertV2LayoutStructure2V3(layoutExt);
            return;
        }

        //库里是V3布局，端上需要V2布局，则V3转V2（移动端布局只支持V3）
        if (layoutExt.isV3Layout() && !LayoutContext.isVersionV3() && !LayoutContext.isMobileLayout()) {
            convertV3LayoutStructure2V2(layoutExt);
        }
    }

    private static void convertV2LayoutStructure2V3(LayoutExt layoutExt) {
        List<String> leftComponents = getLeftStructure(layoutExt).stream().map(x -> (String) x.get(API_NAME)).collect(Collectors.toList());
        List<String> rightComponents = getRightStructure(layoutExt).stream().map(x -> (String) x.get(API_NAME)).collect(Collectors.toList());

        Map<String, Object> row1 = Maps.newLinkedHashMap();
        row1.put(COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
        List<List<String>> components1 = Lists.newArrayList();
        components1.add(Lists.newArrayList(ComponentExt.HEAD_INFO_COMPONENT_NAME));
        row1.put(COMPONENTS, components1);

        Map<String, Object> row2 = Maps.newLinkedHashMap();
        row2.put(COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "auto"), ImmutableMap.of("width", "500px", "retractable", true)));
        List<List<String>> components = Lists.newArrayList();
        components.add(leftComponents);
        components.add(rightComponents);
        row2.put(COMPONENTS, components);

        Map<String, Object> layoutStructure = layoutExt.getLayoutStructure();
        layoutStructure.put(LAYOUT, Lists.newArrayList(row1, row2));
        layoutStructure.remove(HEAD);
        layoutStructure.remove(BODY);
    }

    private static void convertV3LayoutStructure2V2(LayoutExt layoutExt) {
        List<Map> rows = getLayoutStructure(layoutExt);
        List<Map> head = Lists.newArrayList(convertToMap(ComponentExt.HEAD_INFO_COMPONENT_NAME));
        Map body = Maps.newLinkedHashMap();
        List<Map> left = Lists.newArrayList();
        List<Map> right = Lists.newArrayList();

        List<List<String>> columns = (List<List<String>>) rows.get(1).get(COMPONENTS);
        left.addAll(columns.get(0).stream().map(x -> convertToMap(x)).collect(Collectors.toList()));
        if (columns.size() > 1 && CollectionUtils.notEmpty(columns.get(1))) {
            right.addAll(columns.get(1).stream().map(x -> convertToMap(x)).collect(Collectors.toList()));
        }
        body.put(LEFT, left);
        body.put(RIGHT, right);

        Map<String, Object> layoutStructure = layoutExt.getLayoutStructure();
        layoutStructure.put(HEAD, head);
        layoutStructure.put(BODY, body);
        layoutStructure.remove(LAYOUT);

        //为页签容器补充v2版本设计器需要使用的define_type
        layoutExt.getTabsComponents().forEach(x -> x.setDefineType(ComponentDefineType.CONTAINER.getType()));
    }

    public static void clearLayoutStructureByComponents(LayoutExt layoutExt, List<IComponent> components) {
        if (!layoutExt.isNewLayout()) {
            return;
        }
        Set<String> componentApiNames = components.stream().map(IComponent::getName).collect(Collectors.toSet());
        clearLayoutStructureByComponentNames(layoutExt, components, componentApiNames);
    }

    public static void clearLayoutStructureByComponentNames(LayoutExt layoutExt, List<IComponent> components, Set<String> retainedComponentNames) {
        if (!layoutExt.isNewLayout()) {
            return;
        }
        if (layoutExt.isV3Layout()) {
            List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
            rows.forEach(row -> {
                List<List<String>> columns = (List<List<String>>) row.get(COMPONENTS);
                columns.forEach(column -> column.removeIf(x -> !retainedComponentNames.contains(x)));
            });
        } else {
            List<Map> head = (List<Map>) layoutExt.getLayoutStructure().get(HEAD);
            List<Map> left = getLeftStructure(layoutExt);
            List<Map> right = getRightStructure(layoutExt);
            clearContainer(layoutExt, head, retainedComponentNames);
            clearContainer(layoutExt, left, retainedComponentNames);
            clearContainer(layoutExt, right, retainedComponentNames);
        }

        //清理页签里的组件
        components.stream().filter(x -> ComponentExt.of(x).isTabs()).map(x -> (ITabsComponent) x).forEach(x -> {
            List<TabSection> tabSections = x.getTabs();
            Iterator<TabSection> tabSectionIterator = tabSections.iterator();
            Iterator<List<String>> childComponents = CollectionUtils.nullToEmpty(x.getComponents()).iterator();
            while (childComponents.hasNext()) {
                tabSectionIterator.next();
                List<String> tabChild = CollectionUtils.nullToEmpty(childComponents.next());
                tabChild.removeIf(name -> !retainedComponentNames.contains(name));
                if (CollectionUtils.empty(tabChild)) {
                    tabSectionIterator.remove();
                    childComponents.remove();
                }
            }
            x.setTabs(tabSections);
        });

        //清理导航里的组件
        components.stream().filter(x -> ComponentExt.of(x).isNavigation()).map(x -> (INavigationComponent) x).forEach(x -> {
            List<String> children = x.getComponents();
            children.removeIf(componentName -> !retainedComponentNames.contains(componentName));
        });

        //清理栅格容器里的组件
        components.stream().filter(x -> ComponentExt.of(x).isGrid()).map(x -> (IGridComponent) x).forEach(x -> {
            List<List<String>> childrenList = GridComponentExt.of(x).getComponents();
            childrenList.forEach(children -> children.removeIf(componentName -> !retainedComponentNames.contains(componentName)));
        });
    }

    private static void clearContainer(LayoutExt layoutExt, List<Map> container, Set<String> componentApiNames) {
        if (CollectionUtils.empty(container)) {
            return;
        }
        container.removeIf(x -> {
            if (!componentApiNames.contains(x.get(IComponent.NAME))) {
                log.warn("clear layout structure:tenantId:{},layoutApiName:{},componentApiName:{}",
                        layoutExt.getTenantId(), layoutExt.getName(), x.get(IComponent.NAME));
                return true;
            }
            return false;
        });
        container.stream().forEach(x -> {
            List<Map> children = (List<Map>) x.get(CHILDREN);
            clearContainer(layoutExt, children, componentApiNames);
        });
    }

    public static void restoreLayout(LayoutExt layoutExt, PageType pageType) {
        if (!layoutExt.isNewLayout()) {
            return;
        }
        LayoutComponents.restoreComponentOrder(layoutExt);
        LayoutComponents.restoreTopInfo(layoutExt);
        layoutExt.getHeadInfoComponent().ifPresent(x -> layoutExt.setButtons(x.getButtons()));

        List<IComponent> components = layoutExt.getComponentsSilently();
        components = ComponentOrder.order(components);
        //去掉新布局设计器增加的组件
        components.removeIf(x -> ComponentExt.HEAD_INFO_COMPONENT_NAME.equals(x.getName())
                || ComponentExt.TOP_INFO_COMPONENT_NAME.equals(x.getName())
                || ComponentExt.of(x).isTabs()
                || ComponentExt.of(x).isCustomComponent()
                || ComponentExt.of(x).isContactRelation()
                || ComponentExt.of(x).isGrid()
        );
        //终端详情页需要保留摘要卡片、客户层级关系和图表
        if (pageType != PageType.NewDetail) {
            components.removeIf(x -> ComponentExt.SUMMARY_CARD_COMPONENT_NAME.equals(x.getName())
                    || ComponentExt.of(x).isAccountHierarchy()
                    || ComponentExt.of(x).isChartComponent()
                    || ComponentExt.of(x).isIFrameComponent());
        }
        //将详细信息换到第一位
        LayoutComponents.swapDetailComponent(components);
        layoutExt.setComponents(components);

        //去掉layout_structure
        layoutExt.toMap().remove(ILayout.LAYOUT_STRUCTURE);
    }

    public static void handleNotActiveButtons(LayoutExt layoutExt) {
        layoutExt.removeUnActiveButton();
    }

    public static void modifyLayoutStructure(LayoutExt layoutExt, List<IComponent> components, boolean disableOutContainer) {
        components.stream().filter(x -> ComponentExt.of(x).isTabs()).map(x -> (TabsComponent) x).forEach(tabsComponent -> {
            if (!layoutExt.isV2Layout() || CollectionUtils.notEmpty(tabsComponent.getComponents())) {
                return;
            }

            getLeftStructure(layoutExt).stream().filter(y -> tabsComponent.getName().equals(y.get(API_NAME))).forEach(y -> {
                List<Map> relatedList = (List<Map>) y.get(CHILDREN);
                if (CollectionUtils.empty(relatedList)) {
                    return;
                }
                Pair<List<TabSection>, List<String>> pair = buildTabSections(components, relatedList);
                tabsComponent.setTabs(pair.getKey());
                pair.getValue().forEach(componentName -> ComponentExt.of(tabsComponent).addComponent(componentName));
                y.remove(CHILDREN);
            });
        });

        Set<String> oldComponentApiNames = layoutExt.getComponentsSilently().stream()
                .map(x -> x.getName())
                .collect(Collectors.toSet());
        List<String> hiddenComponents = CollectionUtils.nullToEmpty(layoutExt.getHiddenComponents());
        List<IComponent> newRelatedComponents = components.stream()
                .filter(x -> ComponentExt.of(x).isMasterDetailComponent() || ComponentExt.of(x).isRelatedList())
                .filter(x -> !hiddenComponents.contains(x.getName()))
                .filter(x -> !oldComponentApiNames.contains(x.getName()))
                .collect(Collectors.toList());

        List<String> componentNames = replaceComponentsWithChangeOrder(layoutExt, newRelatedComponents);
        if (CollectionUtils.notEmpty(componentNames)) {
            newRelatedComponents.removeIf(component -> componentNames.contains(component.getName()));
        }
        addComponentsToLeftStructure(layoutExt, newRelatedComponents, disableOutContainer);
    }

    /**
     * 变更单对象的从对象组件，需要保持和原单对象从对象组件的相对位置一致
     * 将导航、页签和LayoutStructure中，原单从对象组件name替换为变更单对象的组件name
     *
     * @param layoutExt
     * @param components
     * @return
     */
    private static List<String> replaceComponentsWithChangeOrder(LayoutExt layoutExt, List<IComponent> components) {
        List<ComponentExt> detailComponents = components.stream()
                .map(ComponentExt::of)
                .filter(it -> it.isMasterDetailComponent() && !Strings.isNullOrEmpty(it.getOriginalName()))
                .collect(Collectors.toList());
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.empty(detailComponents) || !layoutExt.isNewLayout()) {
            return Collections.emptyList();
        }
        // 有导航
        result.addAll(replaceComponentsWithNavigation(layoutExt, detailComponents));
        detailComponents.removeIf(it -> result.contains(it.getName()));
        // 有页签
        result.addAll(replaceComponentsWithTabs(layoutExt, detailComponents));
        detailComponents.removeIf(it -> result.contains(it.getName()));
        // 在LayoutStructure
        result.addAll(replaceComponentsWithLayoutStructure(layoutExt, detailComponents));
        return result;
    }

    private static Collection<String> replaceComponentsWithLayoutStructure(LayoutExt layoutExt, List<ComponentExt> detailComponents) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.empty(detailComponents) || !layoutExt.isV3Layout()) {
            return result;
        }
        List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
        rows.forEach(row -> {
            List<List<String>> columns = (List<List<String>>) row.get(COMPONENTS);
            for (List<String> componentNames : columns) {
                result.addAll(replace(detailComponents, componentNames));
            }
        });
        return result;
    }

    private static Collection<String> replaceComponentsWithTabs(LayoutExt layoutExt, List<ComponentExt> detailComponents) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.empty(detailComponents)) {
            return result;
        }
        List<ITabsComponent> tabsComponents = layoutExt.getTabsComponents();
        if (CollectionUtils.empty(tabsComponents)) {
            return result;
        }
        for (ITabsComponent tabsComponent : tabsComponents) {
            List<List<String>> components = tabsComponent.getComponents();
            for (List<String> componentNames : components) {
                result.addAll(replace(detailComponents, componentNames));
            }
        }
        return result;
    }

    private static List<String> replaceComponentsWithNavigation(LayoutExt layoutExt, List<ComponentExt> detailComponents) {
        if (CollectionUtils.empty(detailComponents)) {
            return Collections.emptyList();
        }
        return layoutExt.getNavigationComponent().map(navigation -> {
            List<String> childComponents = navigation.getComponents();
            return replace(detailComponents, childComponents);
        }).orElse(Collections.emptyList());
    }

    private static List<String> replace(List<ComponentExt> detailComponents, List<String> childComponents) {
        List<String> result = Lists.newArrayList();
        for (ComponentExt detailComponent : detailComponents) {
            int index = childComponents.indexOf(detailComponent.getOriginalName());
            if (index != -1) {
                String componentName = detailComponent.getName();
                childComponents.set(index, componentName);
                result.add(componentName);
            }
        }
        return result;
    }

    // 添加新创建相关对象和从对象的component，优先加到页签，如果没有页签则加到left
    public static void addComponentsToLeftStructure(LayoutExt layoutExt, List<IComponent> newComponents) {
        addComponentsToLeftStructure(layoutExt, newComponents, false);
    }

    public static void addComponentsToLeftStructure(LayoutExt layoutExt, List<IComponent> newComponents, boolean disableOutContainer) {
        addComponentsToLeftStructure(layoutExt, newComponents, Integer.MAX_VALUE, disableOutContainer);
    }

    public static void addComponentsToLeftStructure(LayoutExt layoutExt, List<IComponent> newComponents, int index) {
        addComponentsToLeftStructure(layoutExt, newComponents, index, false);
    }

    public static void addComponentsToLeftStructure(LayoutExt layoutExt, List<IComponent> newComponents, int index, boolean disableOutContainer) {
        if (CollectionUtils.empty(newComponents) || !layoutExt.isNewLayout()) {
            return;
        }
        List<String> newComponentApiNames = newComponents.stream().map(x -> x.getName()).collect(Collectors.toList());
        List<Map> newComponentApiNameMaps = newComponents.stream().map(x -> convertToMap(x.getName())).collect(Collectors.toList());
        Set<String> tabsComponents = layoutExt.getTabsComponents().stream().map(x -> x.getName()).collect(Collectors.toSet());
        INavigationComponent navigation = null;
        String lastTabApiName = null;
        if (layoutExt.isV3Layout()) {
            navigation = layoutExt.getNavigationComponent().orElse(null);
            List<String> tabApiNames = Lists.newArrayList();
            getLayoutStructure(layoutExt).forEach(row -> {
                ((List<List<String>>) row.get(COMPONENTS)).forEach(column -> {
                    tabApiNames.addAll(column.stream().filter(x -> tabsComponents.contains(x)).collect(Collectors.toList()));
                });
            });
            lastTabApiName = CollectionUtils.notEmpty(tabApiNames) ? tabApiNames.get(tabApiNames.size() - 1) : null;
        } else {
            List<Map> leftStructure = getLeftStructure(layoutExt);
            List<Map> tabs = leftStructure.stream()
                    .filter(x -> tabsComponents.contains(x.get(API_NAME)))
                    .collect(Collectors.toList());
            lastTabApiName = CollectionUtils.notEmpty(tabs) ? (String) tabs.get(tabs.size() - 1).get(API_NAME) : null;
        }

        //有导航
        if (navigation != null) {
            List<String> childComponents = navigation.getComponents();
            int newIndex = Math.min(childComponents.size(), Math.max(0, index));
            childComponents.addAll(newIndex, newComponentApiNames);
            return;
        }

        //有页签
        if (!Strings.isNullOrEmpty(lastTabApiName)) {
            Optional<IComponent> componentOptional = layoutExt.getComponentByApiName(lastTabApiName);
            if (!componentOptional.isPresent()) {
                return;
            }
            TabsComponent tabsComponent = (TabsComponent) componentOptional.get();
            Pair<List<TabSection>, List<String>> pair = buildTabSections(newComponents, newComponentApiNameMaps);
            List<Map> tabList = (List<Map>) tabsComponent.getContainerDocument().computeIfAbsent(ITabsComponent.TABS, k -> Lists.newArrayList());
            List<List<String>> componentsList = (List<List<String>>) tabsComponent.getContainerDocument().computeIfAbsent(ITabsComponent.COMPONENTS, k -> Lists.newArrayList());
            int newIndex = Math.min(tabList.size(), Math.max(0, index));
            tabList.addAll(newIndex, pair.getKey().stream().map(x -> x.getContainerDocument()).collect(Collectors.toList()));
            componentsList.addAll(newIndex, pair.getValue().stream().map(x -> Lists.newArrayList(x)).collect(Collectors.toList()));
            return;
        }

        //没有导航也没有页签
        if (!disableOutContainer) {
            addComponentsToLeftLayout(layoutExt, newComponents, index);
        }
    }

    public static void addComponentsToLeftLayout(LayoutExt layoutExt, List<IComponent> newComponents, int index) {
        if (CollectionUtils.empty(newComponents) || !layoutExt.isNewLayout()) {
            return;
        }
        List<String> newComponentApiNames = newComponents.stream().map(x -> x.getName()).collect(Collectors.toList());
        List<Map> newComponentApiNameMaps = newComponents.stream().map(x -> convertToMap(x.getName())).collect(Collectors.toList());
        if (layoutExt.isV3Layout()) {
            List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
            List<String> firstColumn = ((List<List<String>>) rows.get(rows.size() - 1).get(COMPONENTS)).get(0);
            int newIndex = Math.min(firstColumn.size(), Math.max(0, index));
            firstColumn.addAll(newIndex, newComponentApiNames);
        } else {
            List<Map> leftStructure = getLeftStructure(layoutExt);
            int newIndex = Math.min(leftStructure.size(), Math.max(0, index));
            leftStructure.addAll(newIndex, newComponentApiNameMaps);
        }
    }

    public static void addComponentsToLeftLayout(LayoutExt layoutExt, List<IComponent> newComponents, List<String> targets) {
        if (CollectionUtils.empty(newComponents) || !layoutExt.isNewLayout()) {
            return;
        }
        List<String> newComponentApiNames = newComponents.stream().map(x -> x.getName()).collect(Collectors.toList());
        List<Map> newComponentApiNameMaps = newComponents.stream().map(x -> convertToMap(x.getName())).collect(Collectors.toList());
        if (layoutExt.isV3Layout()) {
            List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
            List<String> firstColumn = ((List<List<String>>) rows.get(rows.size() - 1).get(COMPONENTS)).get(0);
            int index = firstColumn.size();
            for (String target : targets) {
                if (firstColumn.contains(target)) {
                    index = firstColumn.indexOf(target);
                    break;
                }
            }
            firstColumn.addAll(index, newComponentApiNames);
        } else {
            List<Map> leftStructure = getLeftStructure(layoutExt);
            List<String> componentsInStructure = leftStructure.stream().map(x -> (String) x.get(API_NAME)).collect(Collectors.toList());
            int index = componentsInStructure.size();
            for (String target : targets) {
                if (componentsInStructure.contains(target)) {
                    index = componentsInStructure.indexOf(target);
                    break;
                }
            }
            leftStructure.addAll(index, newComponentApiNameMaps);
        }
    }


    public static void addComponentsToRightLayout(LayoutExt layoutExt, List<IComponent> newComponents, int index) {
        if (CollectionUtils.empty(newComponents) || !layoutExt.isNewLayout()) {
            return;
        }
        if (!hasRightStructure(layoutExt)) {
            return;
        }
        List<String> newComponentApiNames = newComponents.stream().map(x -> x.getName()).collect(Collectors.toList());
        List<Map> newComponentApiNameMaps = newComponents.stream().map(x -> convertToMap(x.getName())).collect(Collectors.toList());
        if (layoutExt.isV3Layout()) {
            List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
            List<String> rightColumn = ((List<List<String>>) rows.get(rows.size() - 1).get(COMPONENTS)).get(1);
            int newIndex = Math.min(rightColumn.size(), Math.max(0, index));
            rightColumn.addAll(newIndex, newComponentApiNames);
        } else {
            List<Map> rightStructure = getRightStructure(layoutExt);
            int newIndex = Math.min(rightStructure.size(), Math.max(0, index));
            rightStructure.addAll(newIndex, newComponentApiNameMaps);
        }
    }

    public static List<Map> getLeftStructure(LayoutExt layoutExt) {
        return (List<Map>) ((Map) layoutExt.getLayoutStructure().get(BODY)).get(LEFT);
    }

    public static List<Map> getRightStructure(LayoutExt layoutExt) {
        return (List<Map>) ((Map) layoutExt.getLayoutStructure().get(BODY)).get(RIGHT);
    }

    public static List<Map> getLayoutStructure(LayoutExt layoutExt) {
        return (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
    }

    public static boolean hasRightStructure(LayoutExt layoutExt) {
        if (!layoutExt.isNewLayout()) {
            return false;
        }
        if (layoutExt.isV3Layout()) {
            List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
//            List<List<String>> columns = (List<List<String>>) rows.get(1).get(COMPONENTS);
            List<List<String>> columns = ((List<List<String>>) rows.get(rows.size() - 1).get(COMPONENTS));
            return columns.size() > 1;
        }
        return true;
    }

    public static List<Map> getLayoutPlugins(LayoutExt layoutExt) {
        return Optional.ofNullable(layoutExt.getLayoutStructure())
                .map(it -> (Map) it.get(PLUGINS))
                .map(it -> (List<Map>) it.get(LAYOUT))
                .orElseGet(Lists::newArrayList);
    }

    public static String getFieldAlign(LayoutExt layoutExt) {
        return Optional.ofNullable(layoutExt.getLayoutStructure())
                .map(it -> (String) it.get(FIELD_ALIGN))
                .orElse(null);
    }

    public static Boolean getIsTitleHelpText(LayoutExt layoutExt) {
        return Optional.ofNullable(layoutExt.getLayoutStructure())
                .map(it -> (Boolean) it.get(IS_TILE_HELP_TEXT))
                .orElse(false);
    }

    public static Boolean getIsOpenTileHelpText(LayoutExt layoutExt) {
        return Optional.ofNullable(layoutExt.getLayoutStructure())
                .map(it -> (Boolean) it.get(IS_OPEN_TILE_HELP_TEXT))
                .orElse(false);
    }

    public static void setIsTitleHelpText(LayoutExt layoutExt, Boolean isTitleHelpText) {
        Map<String, Object> layoutStructure = layoutExt.getLayoutStructure();
        if (Objects.isNull(layoutStructure)) {
            layoutStructure = Maps.newHashMap();
        }
        layoutStructure.put(IS_TILE_HELP_TEXT, isTitleHelpText);
        layoutExt.setLayoutStructure(layoutStructure);
    }

    public static void setIsOpenTileHelpText(LayoutExt layoutExt, Boolean isOpenTileHelpText) {
        Map<String, Object> layoutStructure = layoutExt.getLayoutStructure();
        if (Objects.isNull(layoutStructure)) {
            layoutStructure = Maps.newHashMap();
        }
        layoutStructure.put(IS_OPEN_TILE_HELP_TEXT, isOpenTileHelpText);
        layoutExt.setLayoutStructure(layoutStructure);
    }

    public static void setFieldAlign(LayoutExt layoutExt, String fieldAlign) {
        Map<String, Object> layoutStructure = layoutExt.getLayoutStructure();
        if (Objects.isNull(layoutStructure)) {
            layoutStructure = Maps.newHashMap();
        }
        layoutStructure.put(FIELD_ALIGN, fieldAlign);
        layoutExt.setLayoutStructure(layoutStructure);
    }

    public static void syncTitleNameWithMobile(LayoutExt layoutExt, LayoutExt mobileLayout) {
        if (Objects.isNull(layoutExt) || !layoutExt.isEditLayout() || Objects.isNull(mobileLayout)) {
            return;
        }
        Map<String, Object> layoutStructure = layoutExt.getLayoutStructure();
        if (Objects.isNull(layoutStructure) || Objects.isNull(layoutStructure.get(TITLE_NAME))) {
            return;
        }
        Map<String, Object> mobileLayoutLayoutStructure = mobileLayout.getLayoutStructure();
        if (Objects.isNull(mobileLayoutLayoutStructure)) {
            mobileLayoutLayoutStructure = Maps.newHashMap();
        }
        mobileLayoutLayoutStructure.put(TITLE_NAME, layoutStructure.get(TITLE_NAME));
        mobileLayout.setLayoutStructure(mobileLayoutLayoutStructure);
    }

    public static void syncLayoutPlugins(LayoutExt webLayout, LayoutExt mobileLayout) {
        List<Map> layoutPlugins = getLayoutPlugins(webLayout);
        if (CollectionUtils.empty(layoutPlugins)) {
            return;
        }
        List<Map> plugins = layoutPlugins.stream()
                .filter(plugin -> CLIENT_TYPE_APP.equals(plugin.get(CLIENT_TYPE)))
                .collect(Collectors.toList());

        ((Map) mobileLayout.getLayoutStructure()
                .computeIfAbsent(PLUGINS, it -> Maps.newHashMap()))
                .put(LAYOUT, plugins);
    }

    private static void addDefaultComponentsAndStructure(LayoutExt layoutExt, ObjectDescribeExt describeExt, List<IComponent> components,
                                                         List<Map> leftStructureList, String containerApiName, List<Map> relatedList, boolean isDetail) {
        List<IButton> buttons = CollectionUtils.nullToEmpty(layoutExt.getButtons());
        // 过滤隐藏、禁用的按钮
        List<String> hiddenButtons = CollectionUtils.nullToEmpty(layoutExt.getHiddenButtons());
        buttons.removeIf(button -> !LayoutButtonExt.of(button).isActive() || hiddenButtons.contains(button.getName()));

        SimpleComponent simpleComponent = LayoutComponents.buildHeadInfoComponent();
        if (isDetail) {
            simpleComponent.setButtons(buttons);
        } else {
            List<IButton> buttonList = Lists.newArrayList();
            buttons.forEach(x -> {
                IButton button = new Button();
                button.setName(x.getName());
                button.setActionType(x.getActionType());
                buttonList.add(button);
            });
            simpleComponent.setButtons(buttonList);
        }
        components.add(simpleComponent);

        //摘要信息
        LayoutComponents.addTopInfoComponentByLayout(layoutExt, components, leftStructureList, describeExt);
        //摘要卡片
        LayoutComponents.addSummaryCardComponent(describeExt.getApiName(), components, leftStructureList, layoutExt);

        LayoutComponents.addOtherComponents(leftStructureList, components, relatedList);

        //页签容器
        ITabsComponent tabsComponent = LayoutComponents.buildTabsComponent();
        tabsComponent.setName(containerApiName);
        Pair<List<TabSection>, List<String>> pair = buildTabSections(components, relatedList);
        tabsComponent.setTabs(pair.getKey());
        pair.getValue().forEach(x -> ComponentExt.of(tabsComponent).addComponent(x));

        components.add(tabsComponent);
        Map container = convertToMap(containerApiName);
        leftStructureList.add(container);

        //详细信息
        layoutExt.getFormComponent().ifPresent(x -> {
            x.setTargetFieldApiName(describeExt.getObjectDescribe());
        });
    }

    private static Pair<List<TabSection>, List<String>> buildTabSections(List<IComponent> components, List<Map> relatedList) {
        if (CollectionUtils.empty(relatedList)) {
            return Pair.of(Lists.newArrayList(), Lists.newArrayList());
        }
        List<TabSection> tabSections = Lists.newArrayList();
        List<String> componentNames = Lists.newArrayList();
        relatedList.stream().forEach(x -> {
            String relatedApiName = (String) x.get(API_NAME);
            components.stream().filter(y -> relatedApiName.equals(y.getName())).findFirst().ifPresent(y -> {
                TabSection tabSection = new TabSection();
                tabSection.setApiName("tab_" + y.getName());
                tabSection.setHeader(y.getHeader());
                TabSectionExt.of(tabSection).setNameI18nKey(ComponentExt.of(y).getNameI18nKey());
                tabSections.add(tabSection);
                componentNames.add(relatedApiName);
            });
        });
        return Pair.of(tabSections, componentNames);
    }

    private static List<Map> getRightComponentInfo(List<IComponent> components) {
        List<Map> rightResult = Lists.newArrayList();
        components.stream().filter(x -> ComponentExt.of(x).isTeamComponent()).findFirst().ifPresent(x ->
                rightResult.add(convertToMap(x.getName())));

        components.stream().filter(x -> ComponentExt.of(x).isSaleLog()).findFirst().ifPresent(x -> {
            x.setHeader(I18N.text(I18NKey.FOLLOW_UP_DYNAMIC));
            rightResult.add(convertToMap(x.getName()));
        });

        return rightResult;
    }

    public static List<Map> getOrderComponentsStructure(LayoutExt layoutExt, List<IComponent> detailComponentList,
                                                        List<IComponent> relateComponentList) {
        if (layoutExt.isNewLayout()) {
            return Lists.newArrayList();
        }
        List<IComponent> components = Lists.newArrayList();
        layoutExt.getOperationLogComponent().ifPresent(components::add);

        components.addAll(detailComponentList);
        components.addAll(relateComponentList);
        layoutExt.getFormComponent().ifPresent(detailInfoComponent -> components.add(detailInfoComponent));
        layoutExt.removeHiddenComponents(components);
        List<IComponent> finalGroupComponentList = ComponentOrder.order(components, layoutExt);

        List<String> leftTabsApiNames = Lists.newArrayList(ComponentExt.DETAIL_INFO_COMPONENT_NAME, ComponentExt.TOP_INFO_COMPONENT_NAME);
        List<String> componentApiNames = components.stream().map(x -> x.getName()).collect(Collectors.toList());

        List<Map> leftTabsComponentApiNames = LayoutComponents.getLeftTabsComponentApiNames(componentApiNames, Lists.newArrayList(), finalGroupComponentList, leftTabsApiNames,
                layoutExt.getDefaultComponent(), Lists.newArrayList());

        return leftTabsComponentApiNames;
    }

    public static Map convertToMap(String apiName) {
        Map<String, String> map = Maps.newHashMap();
        map.put(API_NAME, apiName);

        return map;
    }

    public static Map<String, Object> getDefaultAbstractLayoutStructure() {
        Map<String, Object> structure = Maps.newHashMap();
        Map<String, Object> row = Maps.newLinkedHashMap();
        row.put(COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
        List<List<String>> components = Lists.newArrayList();
        components.add(Lists.newArrayList(ComponentExt.TABLE_COMPONENT));
        row.put(COMPONENTS, components);
        structure.put("layout", Lists.newArrayList(row));
        return structure;
    }

    public static void handleIsTileHelpText(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        Boolean isTileHelpText = LayoutStructure.getIsTitleHelpText(layoutExt);
        if (!layoutExt.isEnableMobileLayout()) {
            LayoutStructure.setIsTitleHelpText(layoutExt, isTileHelpText);
            return;
        }
        LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
        if (LayoutStructure.getIsOpenTileHelpText(mobileLayout)) {
            LayoutStructure.setIsTitleHelpText(layoutExt, LayoutStructure.getIsTitleHelpText(mobileLayout));
        } else {
            LayoutStructure.setIsTitleHelpText(layoutExt, isTileHelpText);
        }
    }

    public static void processComponentNamesList(LayoutExt layoutExt, List<String> componentNamesRemoved, List<String> copyComponentNames) {
        if (layoutExt.isV3Layout()) {
            List<Map> rows = (List<Map>) layoutExt.getLayoutStructure().get(LAYOUT);
            rows.forEach(row -> {
                List<List<String>> columns = (List<List<String>>) row.get(COMPONENTS);
                LayoutExt.processComponentNames(columns, componentNamesRemoved, copyComponentNames);
            });
        }
    }
}
