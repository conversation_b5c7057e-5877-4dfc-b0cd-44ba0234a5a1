package com.facishare.paas.appframework.metadata.layout.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo.ButtonRenderType;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.IRelatedObjectList;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.metadata.ComponentExt.NAME_I18N_KEY;

/**
 * create by zhaoju on 2020/11/13
 */
public class ListComponentExt {

    public static final String COMPONENT_TYPE_LIST = "list";
    public static final String GENERAL_INFO = "general_info";
    public static final String FEATURE_HIDE_SETTING = "feature_hide_setting";
    public static final String HIDE_TYPE = "hide_type";
    public static final String BUTTON_INFO = "button_info";
    public static final String SCENE_INFO = "scene_info";
    private static final String VIEW_INFO = "view_info";
    private static final String FILTER_INFO = "filter_info";
    private static final String FILTERS_INFO = "filters_info";
    private static final String RENDER_TYPE_INFO = "render_type_info";
    public static final String LIST_COMPONENT = "list_component";
    public static final String IS_EXPOSED_BUTTON = "is_exposed";
    public static final String LIST_NORMAL_BUTTONS = "list_normal_buttons";
    public static final String LIST_BATCH_BUTTONS = "list_batch_buttons";

    //列表页查询结果当前页的汇总字段
    public static final String SUMMARY_INFO = "summary_info";
    //列表页查询结果所有页的汇总字段
    public static final String ALL_PAGE_SUMMARY_INFO = "all_page_summary_info";
    //列表页查询结果的汇总字段，包括当前页、所有页和选中的数据
    public static final String AGGREGATE_FIELD_INFO = "aggregate_field_info";

    public static final String ENABLE_SELECTED_LAYOUT = "enable_selected_layout";

    private static final String ATTRIBUTES = "attributes";
    public static final String DEFINE_VIEW_INFO = "define_view_info";

    public static final String STATISTICS_RENDER = "statistics_render";

    public static final String STATISTICS_RENDER_HIDDEN_FIELD = "hidden";

    public static final String STATISTICS_RENDER_ORDER = "order";

    public static final String K_SIDE_LIST_FILTER = "side_list_filter";

    public static final String K_ITEMS_SOURCE = "items_source";

    public static final String V_ITEMS_SOURCE_CUSTOM = "custom";

    public static final String K_CUSTOM_ITEMS = "custom_items";

    public static final List<String> V_CUSTOM_ITEMS_DEFAULT = Lists.newArrayList("name");

    public static final String K_TOP_LIST_FILTER = "top_list_filter";

    public static final String K_TOP_LIST_FILTER_SWITCH = "switch";

    public static final String K_M_DISPLAY_TOTALS = "display_totals";
    public static final String K_M_SELECTED_DISPLAY_TOTALS = "selected_display_totals";

    public static final String K_RELATED_DISPLAY_MODE_STYLE = "display_mode_style";

    public static final String V_RELATED_DISPLAY_MODE_STYLE_DROP_DOWN = "drop_down";

    public static final String V_RELATED_DISPLAY_MODE_STYLE_TILE = "tile";

    public static final String RELATED_LIST_TYPE = "relatedlist";

    public static final String RELATED_LIST_FIELD_API_NAME = "field_api_name";

    public static final String COMPONENT_FLAG = "componentFlag";

    @Getter
    @Delegate
    private IComponent component;

    private ListComponentExt(IComponent component) {
        this.component = component;
    }

    public static ListComponentExt of(IComponent component) {
        return new ListComponentExt(component);
    }

    public static ListComponentExt of(Map<String, Object> map) {
        return new ListComponentExt(new CommonComponent(map));
    }

    public static List<ISummaryComponentInfo> filterSummaryInfos(IObjectDescribe describe,
                                                                 List<ISummaryComponentInfo> summaryInfos,
                                                                 Collection<String> authorizedFields) {
        if (CollectionUtils.empty(summaryInfos)) {
            return Lists.newArrayList();
        }
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        return summaryInfos.stream()
                .filter(info -> objectDescribeExt.isFieldActive(info.getFieldName()))
                .filter(info -> authorizedFields.contains(info.getFieldName()))
                .collect(Collectors.toList());
    }

    public String getNameI18nKey() {
        return (String) get(NAME_I18N_KEY);
    }

    public void setNameI18nKey(String nameI18nKey) {
        set(NAME_I18N_KEY, nameI18nKey);
    }

    public ListComponentExt fillSceneInfoPageType(String pageType) {
        getSceneInfo().stream().filter(x -> Strings.isNullOrEmpty(x.getPageType())).forEach(x -> x.setPageType(pageType));
        return this;
    }

    public ListComponentExt fillButtonInfoPageType(String pageType) {
        getButtonInfo().stream().filter(x -> Strings.isNullOrEmpty(x.getPageType())).forEach(x -> x.setPageType(pageType));
        return this;
    }

    public List<IListComponentInfo> getButtonInfo() {
        List<Map> list = get(BUTTON_INFO, List.class);
        return CollectionUtils.empty(list) ? Collections.emptyList()
                : list.stream().map(ListComponentInfo::new).collect(Collectors.toList());
    }

    public void resetButtonInfos(List<IListComponentInfo> buttonInfos) {
        set(BUTTON_INFO, new ArrayList<>());
        buttonInfos.forEach(this::addButtonInfo);
    }

    public void addButtonInfo(IListComponentInfo buttonInfo) {
        if (null == get(BUTTON_INFO)) {
            set(BUTTON_INFO, new ArrayList<>());
        }
        Object list = get(BUTTON_INFO);
        if (list instanceof ArrayList) {
            Map document = ((ListComponentInfo) buttonInfo).getContainerDocument();
            ((ArrayList) list).add(document);
        }
    }

    public List<IScenesComponentInfo> getSceneInfo() {
        List<Map> list = get(SCENE_INFO, List.class);
        return CollectionUtils.empty(list) ? Lists.newArrayList()
                : list.stream().map(ScenesComponentInfo::new).collect(Collectors.toList());
    }

    public Optional<IScenesComponentInfo> getSceneInfoByPageType(String pageType) {
        return getSceneInfo().stream().filter(x -> Objects.equals(pageType, x.getPageType())).findFirst();
    }

    public Optional<GeneralComponentInfo> getGeneralInfoByPageType(String pageType) {
        if (!isEnableSelectedLayout()) {
            return getGeneralInfo().stream().filter(x -> Objects.equals(pageType, x.getPageType())).findFirst();
        }
        return getGeneralInfo().stream().filter(x -> Objects.equals(IComponentInfo.PAGE_TYPE_LIST, x.getPageType())).findFirst();
    }

    public List<GeneralComponentInfo> getGeneralInfo() {
        List<Map> list = get(GENERAL_INFO, List.class);
        return CollectionUtils.empty(list) ? Lists.newArrayList()
                : list.stream().map(GeneralComponentInfo::new).collect(Collectors.toList());
    }

    public void resetSceneInfos(List<IScenesComponentInfo> sceneInfos) {
        set(SCENE_INFO, new ArrayList<>());
        sceneInfos.forEach(this::addSceneInfo);
    }

    public void addSceneInfo(IScenesComponentInfo sceneInfo) {
        if (null == get(SCENE_INFO)) {
            set(SCENE_INFO, new ArrayList<>());
        }
        Object list = get(SCENE_INFO);
        if (list instanceof ArrayList) {
            Map document = ((ScenesComponentInfo) sceneInfo).getContainerDocument();
            ((ArrayList) list).add(document);
        }
    }

    public List<IRenderTypeComponentInfo> getRenderTypeInfo() {
        List<Map> list = get(RENDER_TYPE_INFO, List.class);

        return CollectionUtils.empty(list) ? Lists.newArrayList()
                : list.stream().map(RenderTypeComponentInfo::new).collect(Collectors.toList());
    }

    public Optional<IRenderTypeComponentInfo> getRenderTypeInfoByPageType(String pageType) {
        Supplier<Map<String, IRenderTypeComponentInfo>> supplier = () -> getRenderTypeInfo().stream()
                .collect(Collectors.toMap(IComponentInfo::getPageType, Function.identity()));
        return getByRenderPageType(pageType, supplier, it -> {
            String renderPageType = Strings.isNullOrEmpty(pageType) ? IComponentInfo.PAGE_TYPE_LIST : pageType;
            it.setPageType(renderPageType);
            return it;
        });
    }

    public void resetRenderTypeInfos(List<IRenderTypeComponentInfo> renderTypeInfos) {
        set(RENDER_TYPE_INFO, new ArrayList<>());
        renderTypeInfos.forEach(this::addRenderTypeInfo);
    }

    public void addRenderTypeInfo(IRenderTypeComponentInfo renderTypeInfo) {
        if (null == get(RENDER_TYPE_INFO)) {
            set(RENDER_TYPE_INFO, new ArrayList<>());
        }
        Object list = get(RENDER_TYPE_INFO);
        if (list instanceof ArrayList) {
            Map document = ((RenderTypeComponentInfo) renderTypeInfo).getContainerDocument();
            ((ArrayList) list).add(document);
        }
    }

    public List<String> getFiltersInfos(String listType) {
        Supplier<Map<String, IFiltersComponentInfo>> supplier = () -> getFiltersComponentInfo().stream()
                .collect(Collectors.toMap(IComponentInfo::getPageType, Function.identity()));

        return getByRenderPageType(listType, supplier, it -> {
            String renderPageType = Strings.isNullOrEmpty(listType) ? IComponentInfo.PAGE_TYPE_LIST : listType;
            it.setPageType(renderPageType);
            return it;
        }).map(IFiltersComponentInfo::getFields)
                .orElseGet(Lists::newArrayList);
    }

    public List<IFiltersComponentInfo> getFiltersComponentInfo() {
        List<Map> filters = get(FILTERS_INFO, List.class);
        if (filters == null) {
            List<String> filterInfos = getFilterInfos();
            if (CollectionUtils.notEmpty(filterInfos)) {
                return Lists.newArrayList(FiltersComponentInfo.ofFilterInfos(filterInfos, IComponentInfo.PAGE_TYPE_LIST));
            }
        }

        return CollectionUtils.empty(filters)
                ? Lists.newArrayList(FiltersComponentInfo.ofFilterInfos(Lists.newArrayList(), IComponentInfo.PAGE_TYPE_LIST))
                : filters.stream().map(FiltersComponentInfo::new).collect(Collectors.toList());
    }

    public List<String> getFilterInfos() {
        Object value = get(FILTER_INFO);
        if (null == value) {
            return Lists.newArrayList();
        }
        String str;
        if (value instanceof String) {
            str = (String) value;
        } else {
            str = JSON.toJSONString(value);
        }
        return JSONObject.parseObject(str, List.class);
    }

    public void resetFiltersInfos(List<IFiltersComponentInfo> filtersInfoList) {
        set(FILTERS_INFO, new ArrayList<>());
        filtersInfoList.forEach(this::addFiltersInfo);
    }

    public void addFiltersInfo(IFiltersComponentInfo filtersInfoList) {
        if (null == get(FILTERS_INFO)) {
            set(FILTERS_INFO, new ArrayList<>());
        }
        Object list = get(FILTERS_INFO);
        if (list instanceof ArrayList) {
            Map document = ((FiltersComponentInfo) filtersInfoList).getContainerDocument();
            ((ArrayList) list).add(document);
        }
    }

    public List<IViewComponentInfo> getViewInfos() {
        List<Map> list = get(VIEW_INFO, List.class);
        return CollectionUtils.empty(list) ? Collections.emptyList()
                : list.stream().map(ViewComponentInfo::new).collect(Collectors.toList());
    }

    public void resetViewInfos(List<IViewComponentInfo> viewInfos) {
        set(VIEW_INFO, new ArrayList<>());
        viewInfos.forEach(this::addViewInfo);
    }

    private void addViewInfo(IViewComponentInfo viewInfo) {
        if (null == get(VIEW_INFO)) {
            set(VIEW_INFO, new ArrayList<>());
        }
        Object list = get(VIEW_INFO);
        if (list instanceof ArrayList) {
            Map document = ((ViewComponentInfo) viewInfo).getContainerDocument();
            ((ArrayList) list).add(document);
        }
    }

    public Optional<IListComponentInfo> getButtonInfoByUsePageAndRenderPageType(ButtonUsePageType usePageType,
                                                                                String pageType) {
        Supplier<Map<String, IListComponentInfo>> supplier = () -> getButtonInfo().stream()
                .filter(it -> Objects.equals(it.getRenderType(), ButtonRenderType.getTypeByUsePage(usePageType)))
                .collect(Collectors.toMap(IListComponentInfo::getPageType, Function.identity(), (x, y) -> x));

        return getByRenderPageType(pageType, supplier, it -> {
            String renderPageType = Strings.isNullOrEmpty(pageType) ? IComponentInfo.PAGE_TYPE_LIST : pageType;
            // 相关列表的通用按钮外漏个数,不需要沿用大列表的配置
            if (!Objects.equals(renderPageType, it.getPageType())
                    && IComponentInfo.PAGE_TYPE_RELATED.equals(renderPageType)
                    && Objects.equals(it.getRenderType(), ButtonRenderType.getTypeByUsePage(ButtonUsePageType.ListNormal))) {
                it.setExposedButton(null);
            }
            it.setPageType(renderPageType);
            return it;
        });
    }

    public <T> Optional<T> getByRenderPageType(String pageType,
                                               Supplier<Map<String, T>> supplier,
                                               Function<T, T> mapper) {
        String renderPageType = getRenderPageType(pageType);
        Map<String, T> renderPageTypeMap = supplier.get();

        T listComponentInfo = renderPageTypeMap.get(renderPageType);
        if (Objects.nonNull(listComponentInfo)) {
            return Optional.of(listComponentInfo).map(mapper);
        }
        if (IComponentInfo.PAGE_TYPE_LIST.equals(renderPageType)) {
            return Optional.empty();
        }
        return Optional.ofNullable(renderPageTypeMap.get(IComponentInfo.PAGE_TYPE_LIST)).map(mapper);
    }

    public List<IButton> filterButtonByUsePage(List<IButton> buttons, ButtonUsePageType usePageType, String pageType) {
        return filterButtonByUsePage(buttons, usePageType, pageType, IButton::getName,
                (button, isExposed) -> LayoutButtonExt.of(button).setIsExposedButton(isExposed));
    }

    public List<IUdefButton> filterUdefButtonsByUsePage(List<IUdefButton> udefButtons, ButtonUsePageType usePageType,
                                                        String renderPageType) {
        return filterButtonByUsePage(udefButtons, usePageType, renderPageType, IUdefButton::getApiName,
                (button, isExposed) -> ButtonExt.of(button).setIsExposedButton(isExposed));
    }

    private <T> List<T> filterButtonByUsePage(List<T> buttons, ButtonUsePageType usePageType, String renderPageType,
                                              Function<T, String> function, BiConsumer<T, Boolean> consumer) {
        if (usePageType == null) {
            return Collections.emptyList();
        }
        Optional<IListComponentInfo> buttonInfoOpt = getButtonInfoByUsePageAndRenderPageType(usePageType, renderPageType);
        if (!buttonInfoOpt.isPresent()) {
            return buttons;
        }
        IListComponentInfo buttonInfo = buttonInfoOpt.get();
        List<String> order = CollectionUtils.nullToEmpty(buttonInfo.getOrder());
        if (!order.contains(ObjectAction.REFERENCE_CREATE.getButtonApiName())) {
            int index = order.indexOf(ObjectAction.CREATE.getButtonApiName());
            if (index != -1) {
                order.add(index + 1, ObjectAction.REFERENCE_CREATE.getButtonApiName());
            }
        }
        order.add(0, ObjectAction.UPDATE_GDPR.getButtonApiName());
        List<String> hidden = CollectionUtils.nullToEmpty(buttonInfo.getHidden());

        int exposedButton = ButtonRenderType.fromUsePageType(usePageType).getExposedButtonDefaultSize(buttonInfo);
        AtomicInteger atomicInteger = new AtomicInteger();
        return CollectionUtils.sortByGivenOrder(buttons, order, function).stream()
                .filter(it -> !hidden.contains(function.apply(it)))
                .peek(it -> {
                    // exposedButton 小于0,则代表用户没有设置过外漏
                    if (exposedButton < 0) {
                        return;
                    }
                    // 列表页批量按钮、外漏的用描述中的 is_exposed_button 表示
                    consumer.accept(it, atomicInteger.getAndIncrement() < exposedButton);
                })
                .collect(Collectors.toList());
    }

    private String getRenderPageType(String renderPageType) {
        if (!isEnableSelectedLayout() && IComponentInfo.PAGE_TYPE_SELECTED.equals(renderPageType)) {
            return IComponentInfo.PAGE_TYPE_LIST;
        }
        return Strings.isNullOrEmpty(renderPageType) ? IComponentInfo.PAGE_TYPE_LIST : renderPageType;
    }

    private IScenesComponentInfo getSceneInfoSilently(String pageType) {
        Supplier<Map<String, IScenesComponentInfo>> supplier = () -> getSceneInfo().stream()
                .collect(Collectors.toMap((IComponentInfo::getPageType), it -> it, (x, y) -> x));

        return getByRenderPageType(pageType, supplier, it -> {
            String renderPageType = Strings.isNullOrEmpty(pageType) ? IComponentInfo.PAGE_TYPE_LIST : pageType;
            it.setPageType(renderPageType);
            return it;
        }).orElseGet(ScenesComponentInfo::defaultSceneInfo);
    }

    public List<ISearchTemplate> filterTemplate(List<ISearchTemplate> templates, CustomSceneConfig customSceneConfig,
                                                String renderPageType) {
        IScenesComponentInfo sceneInfo = getSceneInfoSilently(renderPageType);
        List<String> hidden = CollectionUtils.nullToEmpty(sceneInfo.getHidden());
        List<String> order = CollectionUtils.nullToEmpty(sceneInfo.getOrder());
        List<ISearchTemplate> result;

        // 场景有个人配置，不需要再根据布局中配置的场景顺序排序
        if (Objects.nonNull(customSceneConfig) && CollectionUtils.notEmpty(customSceneConfig.getOrderBy())) {
            result = templates.stream()
                    .filter(it -> !hidden.contains(it.getApiName()))
                    .collect(Collectors.toList());
        } else {
            result = CollectionUtils.sortByGivenOrder(templates, order, ISearchTemplate::getApiName).stream()
                    .filter(it -> !hidden.contains(it.getApiName()))
                    .collect(Collectors.toList());
        }

        // 如果最终过滤的结果为空，下发「全部」场景
        if (CollectionUtils.empty(result)) {
            templates.stream()
                    .filter(it -> SearchTemplateExt.DefaultScene.ALL.getApiName().equals(it.getApiName()))
                    .findFirst()
                    .ifPresent(result::add);
        }

        // 没有默认场景，将第一个场景设置为默认场景
        if (!SearchTemplateExt.hasDefault(result) && CollectionUtils.notEmpty(result)) {
            result.get(0).setIsDefault(true);
        }
        return result;
    }

    public List<IButton> getListNormalButtons() {
        List<Map> list = get(LIST_NORMAL_BUTTONS, List.class);
        return CollectionUtils.empty(list) ? Lists.newArrayList()
                : list.stream().map(Button::new).collect(Collectors.toList());
    }

    public void resetListNormalButtons(List<IButton> buttons) {
        set(LIST_NORMAL_BUTTONS, new ArrayList<>());
        buttons.forEach(this::addListNormalButton);
    }

    private void addListNormalButton(IButton button) {
        if (null == get(LIST_NORMAL_BUTTONS)) {
            set(LIST_NORMAL_BUTTONS, new ArrayList<>());
        }
        Object list = get(LIST_NORMAL_BUTTONS);
        if (list instanceof ArrayList) {
            ((ArrayList) list).add(Document.parse(button.toJsonString()));
        }
    }

    public List<IButton> getListBatchButtons() {
        List<Map> list = get(LIST_BATCH_BUTTONS, List.class);
        return CollectionUtils.empty(list) ? Lists.newArrayList()
                : list.stream().map(Button::new).collect(Collectors.toList());
    }

    public void resetListBatchButtons(List<IButton> buttons) {
        set(LIST_BATCH_BUTTONS, new ArrayList<>());
        buttons.forEach(this::addListBatchButton);
    }

    private void addListBatchButton(IButton button) {
        if (null == get(LIST_BATCH_BUTTONS)) {
            set(LIST_BATCH_BUTTONS, new ArrayList<>());
        }
        Object list = get(LIST_BATCH_BUTTONS);
        if (list instanceof ArrayList) {
            ((ArrayList) list).add(Document.parse(button.toJsonString()));
        }
    }

    private List<ISummaryComponentInfo> getSummaryComponentInfo() {
        List<Map> summaryInfo = get(SUMMARY_INFO, List.class);
        return CollectionUtils.empty(summaryInfo) ? Lists.newArrayList()
                : summaryInfo.stream().map(SummaryComponentInfo::new).collect(Collectors.toList());
    }

    public List<ISummaryComponentInfo> getSummaryComponentInfoByPageType(String pageType) {
        if (!enableAggregate()) {
            return getSummaryComponentInfo();
        }

        if (hideTotalsMobileByPageType(pageType)) {
            return Collections.emptyList();
        }
        return getAggregateInfosStreamByPageType(pageType)
                .filter(it -> Objects.equals(it.getRenderType(), IAggregateComponentInfo.RENDER_TYPE_CURRENT))
                .findFirst()
                .map(IAggregateComponentInfo::getSummaryFields)
                .orElse(Collections.emptyList());
    }

    public void resetSummaryComponentInfo(List<ISummaryComponentInfo> summaryComponentInfos) {
        set(SUMMARY_INFO, new ArrayList<>());
        summaryComponentInfos.forEach(this::addSummaryComponentInfo);
    }

    private void addSummaryComponentInfo(ISummaryComponentInfo summaryComponentInfo) {
        if (null == get(SUMMARY_INFO)) {
            set(SUMMARY_INFO, new ArrayList<>());
        }
        Object list = get(SUMMARY_INFO);
        if (list instanceof ArrayList) {
            Map document = ((SummaryComponentInfo) summaryComponentInfo).getContainerDocument();
            ((ArrayList) list).add(document);
        }
    }

    private List<ISummaryComponentInfo> getAllPageSummaryComponentInfo() {
        List<Map> summaryInfo = get(ALL_PAGE_SUMMARY_INFO, List.class);
        return CollectionUtils.empty(summaryInfo) ? Lists.newArrayList()
                : summaryInfo.stream().map(SummaryComponentInfo::new).collect(Collectors.toList());
    }

    public List<ISummaryComponentInfo> getAllPageSummaryComponentInfoByPageType(String pageType) {
        if (!enableAggregate()) {
            return getAllPageSummaryComponentInfo();
        }
        if (hideTotalsMobileByPageType(pageType)) {
            return Collections.emptyList();
        }
        // 相关列表的聚合值,需要沿用大列表的配置
        if (IComponentInfo.PAGE_TYPE_RELATED.equals(pageType)) {
            pageType = IComponentInfo.PAGE_TYPE_LIST;
        }
        return getAggregateInfosStreamByPageType(pageType)
                .filter(it -> Objects.equals(it.getRenderType(), IAggregateComponentInfo.RENDER_TYPE_ALL))
                .findFirst()
                .map(IAggregateComponentInfo::getSummaryFields)
                .orElse(Collections.emptyList());
    }

    public void setAllPageSummaryComponentInfo(List<ISummaryComponentInfo> summaryComponentInfos) {
        List<Map> summaryComponentInfoList = summaryComponentInfos.stream()
                .map(x -> Document.parse(x.toJsonString())).collect(Collectors.toList());
        set(ALL_PAGE_SUMMARY_INFO, summaryComponentInfoList);
    }

    public List<ISummaryComponentInfo> getSelectedDataSummaryComponentInfoByPageType(String pageType) {
        if (!enableAggregate()) {
            return Collections.emptyList();
        }

        if (hideTotalsMobileByPageType(pageType)) {
            return Collections.emptyList();
        }
        return getAggregateInfosStreamByPageType(pageType)
                .filter(it -> Objects.equals(it.getRenderType(), IAggregateComponentInfo.RENDER_TYPE_SELECTED))
                .findFirst()
                .map(IAggregateComponentInfo::getSummaryFields)
                .orElse(Collections.emptyList());
    }

    /**
     * 根据页面类型判断是否显示移动端聚合值
     *
     * @param pageType
     * @return
     */
    private boolean hideTotalsMobileByPageType(String pageType) {
        if (!LayoutContext.isMobileLayout()) {
            return false;
        }
        if ((IComponentInfo.PAGE_TYPE_LIST.equals(pageType) || IComponentInfo.PAGE_TYPE_RELATED.equals(pageType)) && isDisplayTotalsFromMobile()) {
            return false;
        }
        return !IComponentInfo.PAGE_TYPE_SELECTED.equals(pageType) || !isDisplayTotalsFromSelected();
    }

    private Stream<IAggregateComponentInfo> getAggregateInfosStreamByPageType(String pageType) {
        // 相关列表的聚合值,需要沿用大列表的配置
        if (IComponentInfo.PAGE_TYPE_RELATED.equals(pageType)) {
            pageType = IComponentInfo.PAGE_TYPE_LIST;
        }
        String renderPageType = getRenderPageType(pageType);
        return getAggregateInfosStream()
                .filter(it -> Objects.equals(it.getPageType(), renderPageType));
    }

    public List<IAggregateComponentInfo> getAggregateInfosByPageType(String pageType) {
        return getAggregateInfosStreamByPageType(pageType).collect(Collectors.toList());
    }

    public List<IAggregateComponentInfo> getAggregateInfos() {
        return getAggregateInfosStream().collect(Collectors.toList());
    }

    public void resetAggregateInfos(List<IAggregateComponentInfo> aggregateComponentInfos) {
        List<Map> summaryComponentInfoList = aggregateComponentInfos.stream()
                .map(x -> Document.parse(x.toJsonString())).collect(Collectors.toList());
        set(AGGREGATE_FIELD_INFO, summaryComponentInfoList);
    }

    private Stream<IAggregateComponentInfo> getAggregateInfosStream() {
        List<Map> list = get(AGGREGATE_FIELD_INFO, List.class);
        if (CollectionUtils.empty(list)) {
            return Stream.empty();
        }
        return list.stream()
                .map(AggregateComponentInfo::new);
    }

    public boolean enableAggregate() {
        Object value = get(AGGREGATE_FIELD_INFO);
        return Objects.nonNull(value);
    }

    public List<String> getSummaryFieldNames() {
        if (enableAggregate()) {
            return getAggregateInfosStream()
                    .map(IAggregateComponentInfo::getSummaryFields)
                    .flatMap(Collection::stream)
                    .map(ISummaryComponentInfo::getFieldName)
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<String> result = getSummaryComponentInfo().stream()
                .map(ISummaryComponentInfo::getFieldName)
                .collect(Collectors.toList());

        getAllPageSummaryComponentInfo()
                .forEach(it -> result.add(it.getFieldName()));
        return result;
    }

    public boolean isEnableSelectedLayout() {
        Object value = get(ENABLE_SELECTED_LAYOUT);
        if (value == null) {
            return false;
        }
        return Boolean.valueOf(value.toString());
    }

    public void setEnableSelectedLayout(Boolean enableSelectedLayout) {
        set(ENABLE_SELECTED_LAYOUT, enableSelectedLayout);
    }

    public Optional<IViewComponentInfo> getViewInfoByName(String name) {
        return getViewInfos().stream().filter(it -> Objects.equals(it.getName(), name)).findFirst();
    }

    public void setAttribute(String key, Object value) {
        getAttributes().put(key, value);
    }

    public <T> T getAttribute(String key) {
        return (T) getAttributes().get(key);
    }

    private Map<String, Object> getAttributes() {
        Map<String, Object> attributeMap = get(ATTRIBUTES, Map.class);
        if (Objects.nonNull(attributeMap)) {
            return attributeMap;
        }
        attributeMap = Maps.newHashMap();
        set(ATTRIBUTES, attributeMap);
        return attributeMap;
    }

    public void fillAttributeFromLayout(ILayout listLayout) {
        setAttribute(ILayout.ENABLE_MOBILE_LAYOUT, listLayout.getEnableMobileLayout());
        String fieldAlign = LayoutStructure.getFieldAlign(LayoutExt.of(listLayout));
        setAttribute(LayoutStructure.FIELD_ALIGN, fieldAlign);
    }

    public void defineViewInfo(String viewName) {
        List<String> defineViewInfo = getDefineViewInfo();
        if (defineViewInfo.contains(viewName)) {
            return;
        }
        defineViewInfo.add(viewName);
        set(DEFINE_VIEW_INFO, defineViewInfo);
    }

    public boolean isDefineViewInfo(String viewName) {
        return getDefineViewInfo().contains(viewName);
    }

    private List<String> getDefineViewInfo() {
        List<String> defineViewInfo = get(DEFINE_VIEW_INFO, List.class);
        if (Objects.nonNull(defineViewInfo)) {
            return defineViewInfo;
        }
        defineViewInfo = Lists.newArrayList();
        set(DEFINE_VIEW_INFO, defineViewInfo);
        return defineViewInfo;
    }

    public List<String> getTabHiddenMasterField() {
        Map<String, Object> render = get(STATISTICS_RENDER, Map.class, Maps.newHashMap());
        return (List<String>) render.getOrDefault(STATISTICS_RENDER_HIDDEN_FIELD, Lists.newArrayList());
    }

    public List<String> getTabShowMasterField() {
        Map<String, Object> render = get(STATISTICS_RENDER, Map.class, Maps.newHashMap());
        return (List<String>) render.getOrDefault(STATISTICS_RENDER_ORDER, Lists.newArrayList());
    }

    public String getRefObjectApiName() {
        return get(IRelatedObjectList.REF_OBJECT_API_NAME, String.class);
    }

    public List<IListComponentInfo> getButtonInfoIfAbsentInit() {
        List<IListComponentInfo> buttonInfo = getButtonInfo();
        if (CollectionUtils.notEmpty(buttonInfo)) {
            return buttonInfo;
        }
        return Lists.newArrayList(ListComponentInfo.listNormal(), ListComponentInfo.listBatch(), ListComponentInfo.listSingle());
    }

    public List<IScenesComponentInfo> getSceneInfoOrDefault() {
        List<IScenesComponentInfo> sceneInfo = getSceneInfo();
        if (CollectionUtils.notEmpty(sceneInfo)) {
            return sceneInfo;
        }
        return Lists.newArrayList(ScenesComponentInfo.defaultSceneInfo());
    }

    /**
     * 移动端列表页侧栏可筛选元素配置
     * 是否启用快速筛选 -> 是：顶部外露可筛选元素
     * 自定义元素 -> 根据布局，设置自定义可筛选元素
     * 沿用移动端摘要布局 -> 返回空List，使用保底策略（没有可筛选字段时，使用移动端摘要布局显示元素）
     *
     * @return
     * @see com.facishare.paas.appframework.core.predef.controller.BaseListHeaderController#fillDefaultShowField(ILayout, List)
     */
    public List<String> getSideListFilterItems() {
        return Optional.ofNullable(get(K_SIDE_LIST_FILTER, Map.class))
                .filter(side -> StringUtils.equals(V_ITEMS_SOURCE_CUSTOM, (String) side.get(K_ITEMS_SOURCE)))
                .map(side -> (List<String>) side.getOrDefault(K_CUSTOM_ITEMS, V_CUSTOM_ITEMS_DEFAULT))
                .orElse(Lists.newArrayList());
    }

    public Boolean getTopListFilterSwitch() {
        return Optional.ofNullable(get(K_TOP_LIST_FILTER, Document.class))
                .map(topFilter -> topFilter.get(K_TOP_LIST_FILTER_SWITCH, Boolean.class))
                .orElse(true);
    }

    public boolean isDisplayTotalsFromMobile() {
        return BooleanUtils.isNotFalse(get(K_M_DISPLAY_TOTALS, Boolean.class));
    }

    /**
     * 选数据列表的显示汇总字段开关是新开关，在没有配置的时候使用大列表的开关
     *
     * @return
     */
    public boolean isDisplayTotalsFromSelected() {
        if (!isEnableSelectedLayout()) {
            return isDisplayTotalsFromMobile();
        }
        Boolean value = get(K_M_SELECTED_DISPLAY_TOTALS, Boolean.class);
        if (Objects.isNull(value)) {
            return isDisplayTotalsFromMobile();
        }
        return BooleanUtils.isNotFalse(value);
    }

    public void removeDisplayTotals() {
        Object value = get(K_M_DISPLAY_TOTALS);
        if (Objects.nonNull(value)) {
            set(K_M_DISPLAY_TOTALS, null);
        }
        value = get(K_M_SELECTED_DISPLAY_TOTALS);
        if (Objects.nonNull(value)) {
            set(K_M_SELECTED_DISPLAY_TOTALS, null);
        }
    }

    /**
     * 相关列表组件展示样式：平铺
     * 按业务类型分组平铺展示
     * 默认平铺展示
     *
     * @return
     */
    public boolean isRelatedListTileMode() {
        String mode = Optional.ofNullable(get(ListComponentExt.K_RELATED_DISPLAY_MODE_STYLE, String.class))
                .orElse(ListComponentExt.V_RELATED_DISPLAY_MODE_STYLE_TILE);
        return StringUtils.equals(ListComponentExt.V_RELATED_DISPLAY_MODE_STYLE_TILE, mode);
    }

    public String getRelatedListFieldApiName() {
        return get(RELATED_LIST_FIELD_API_NAME, String.class);
    }
}
