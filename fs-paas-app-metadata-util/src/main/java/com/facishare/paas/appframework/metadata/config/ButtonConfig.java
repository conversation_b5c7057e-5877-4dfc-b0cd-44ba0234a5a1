package com.facishare.paas.appframework.metadata.config;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.button.ButtonDefineType;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.PRINT;
import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_720;

/**
 * create by zhaoju on 2019/09/18
 */
@Slf4j
public class ButtonConfig {
    public static final String UDEF_BUTTON = "udef_button";
    public static final String DEFAULT_BUTTON = "default_button";
    private static Table<String, String, IUdefButtonConfig> BUTTON_CONFIG_TABLE = HashBasedTable.create();
    private static Table<String, String, List<String>> DEFAULT_BATCH_BUTTON_CONFIG = HashBasedTable.create();
    private static Table<String, String, GrayRule> DEFAULT_BATCH_BUTTON_FILTER_CONFIG = HashBasedTable.create();
    public static Table<String, String, List<IFilter>> BUTTON_SYSTEM_FILTERS = HashBasedTable.create();

    private static List<String> DEFAULT_BUTTON_ORDER = Lists.newArrayList();
    private static Map<String, List<String>> PRM_DEFAULT_BATCH_BUTTON_CONFIG = Maps.newHashMap();
    private static Map<String, List<String>> PRM_DEFAULT_BATCH_BUTTON_CONFIG_720 = Maps.newHashMap();
    private static int BULK_ACTION_LIMIT_SIZE;

    private static Map<String, List<String>> MOBILE_BULK_BUTTON_BLACK_ACTION_CONFIG = Maps.newHashMap();
    private static Set<String> MOBILE_BULK_BUTTON_BLACK_ACTION = Sets.newHashSet();
    // 支持PRM的对象列表（自定义对象都支持）
    private static List<String> SUPPORT_PRM_WHITE_OBJECT_LIST = Lists.newArrayList();
    private static List<String> NEW_SUPPORT_PRM_WHITE_OBJECT_LIST = Lists.newArrayList();

    //相关列表的ListHeader需要屏蔽的按钮
    //key的格式：相关对象的api_name|主对象的api_name|关联字段的api_name，value是按钮的actionCode列表
    public static Map<String, List<String>> RELATED_LIST_HEADER_REMOVE_BUTTONS = Maps.newHashMap();
    //主从一起新建编辑页从对象列表需要屏蔽的通用按钮
    //key是从对象的apiName，value是需要过滤的按钮api_name(格式：Batch_Lookup_Add_button_关联字段的apiName)列表
    public static Map<String, List<String>> MASTER_DETAIL_EDIT_DETAIL_LIST_NORMAL_REMOVE_BUTTONS = Maps.newHashMap();
    //主从一起新建编辑页从对象列表需要屏蔽的批量按钮
    //key是从对象的apiName，value是需要过滤的按钮api_name列表
    public static Map<String, List<String>> MASTER_DETAIL_EDIT_DETAIL_LIST_BATCH_REMOVE_BUTTONS = Maps.newHashMap();
    //主从一起新建编辑页从对象列表需要屏蔽的单条按钮
    //key是从对象的apiName，value是需要过滤的按钮api_name列表
    public static Map<String, List<String>> MASTER_DETAIL_EDIT_DETAIL_LIST_SINGLE_REMOVE_BUTTONS = Maps.newHashMap();

    // 预置按钮支持自定义入参自定义的灰度企业名单
    private static Map<String, Set<String>> SYSTEM_BUTTON_PARAM_TENANT_GRAY = Maps.newHashMap();
    // 预置按钮支持编辑动作灰度企业名单
    private static Map<String, Set<String>> SYSTEM_BUTTON_ACTION_TENANT_GRAY = Maps.newHashMap();

    private static Set<String> BUTTON_COUNT_GRAY_TENANT = Sets.newHashSet();
    private volatile static int BUTTON_COUNT_GRAY;

    private static Map<String, List<String>> DESIGNER_BUTTON_BLACKLIST = Maps.newHashMap();
    public static List<String> LIST_LAYOUT_LIST_NORMAL_BUTTONS = Lists.newArrayList();
    private static List<String> LIST_LAYOUT_BUTTONS_WEB_ONLY = Lists.newArrayList();
    private static List<String> LIST_LAYOUT_BUTTONS_MOBILE_ONLY = Lists.newArrayList();

    private static Map<String, Map<String, GrayRule>> LIST_LAYOUT_BUTTONS_DISPLAY_BY_TENANT = Maps.newHashMap();
    private static Set<String> BULK_PRINT_GRAY_EI = Sets.newHashSet();

    private static Set<String> BUTTON_REDIRECT_ACTION_GRAY_EI = Sets.newHashSet();
    private static Set<String> BUTTON_REDIRECT_ACTION_GRAY_OBJ = Sets.newHashSet();

    private static Map<String, Set<String>> BUTTON_REDIRECT_ACTION_GRAY = Maps.newHashMap();

    private static Map<String, Set<String>> OBJECT_BUTTON_BLACKLIST = Maps.newHashMap();

    private static Set<String> bigObjectSupportButtonList = Sets.newHashSet();

    private static Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    private static final String ALL = "ALL";

    static {
        ConfigFactory.getConfig("fs-paas-udef-button-config", ChangeListenerHolder.create(config -> {
            log.warn("reload config fs-paas-udef-button-config,content:{}", config.getString());
            parseFromConfig(config, "udef_button_config");
            DEFAULT_BUTTON_ORDER = getListFromConfig(config, "default.button.order");

            DEFAULT_BATCH_BUTTON_CONFIG = parseBatchButtonConfig(config, "default.batch.button.config");
            DEFAULT_BATCH_BUTTON_FILTER_CONFIG = parseBatchButtonFilterConfig(config, "default.batch.button.filter.config");
            PRM_DEFAULT_BATCH_BUTTON_CONFIG = parseMapFromConfig(config, "prm.default.batch.button.config");
            PRM_DEFAULT_BATCH_BUTTON_CONFIG_720 = parseMapFromConfig(config, "prm.default.batch.button.config_720");
            BULK_ACTION_LIMIT_SIZE = config.getInt("bulk.action.limit.size", 5);

            BUTTON_SYSTEM_FILTERS = parseButtonSystemFiltersConfig(config, "button.system.filters");

            // 移动端不支持的批量操作名单
            MOBILE_BULK_BUTTON_BLACK_ACTION_CONFIG = parseMapFromConfig(config, "mobile.bulk.button.black.action.config");
            // 不支持批量的操作对象
            MOBILE_BULK_BUTTON_BLACK_ACTION = getSetFromConfig(config, "mobile.bulk.button.black.action");

            RELATED_LIST_HEADER_REMOVE_BUTTONS = parseMapFromConfig(config, "relatedListHeader.removeButtons");
            MASTER_DETAIL_EDIT_DETAIL_LIST_NORMAL_REMOVE_BUTTONS = parseMapFromConfig(config, "masterDetailEdit.detailListNormal.removeButtons");
            MASTER_DETAIL_EDIT_DETAIL_LIST_BATCH_REMOVE_BUTTONS = parseMapFromConfig(config, "masterDetailEdit.detailListBatch.removeButtons");
            MASTER_DETAIL_EDIT_DETAIL_LIST_SINGLE_REMOVE_BUTTONS = parseMapFromConfig(config, "masterDetailEdit.detailListSingle.removeButtons");

//            SUPPORT_PRM_WHITE_OBJECT_LIST = getSetFromConfig(config, "support_prm_white_object_list");

            SYSTEM_BUTTON_PARAM_TENANT_GRAY = generateFieldListMapFromConfigStr(config, "system_button_param_tenant_gray");

            SYSTEM_BUTTON_ACTION_TENANT_GRAY = generateFieldListMapFromConfigStr(config, "system_button_action_tenant_gray");

            // 按钮个数灰度校验
            BUTTON_COUNT_GRAY_TENANT = getSetFromConfig(config, "button_count_gray_tenant");
            BUTTON_COUNT_GRAY = config.getInt("button_count_gray", 15);

            // 设计器不展示的按钮黑名单
            DESIGNER_BUTTON_BLACKLIST = parseMapFromConfig(config, "designer_button_blacklist");

            LIST_LAYOUT_LIST_NORMAL_BUTTONS = ImmutableList.copyOf(getListFromConfig(config, "list_layout_list_normal_buttons"));
            LIST_LAYOUT_BUTTONS_WEB_ONLY = getListFromConfig(config, "list_layout_buttons_web_only");
            LIST_LAYOUT_BUTTONS_MOBILE_ONLY = getListFromConfig(config, "list_layout_buttons_mobile_only");
            LIST_LAYOUT_BUTTONS_DISPLAY_BY_TENANT = buildButtonDisplayGray(ContentParseUtil.parseListFromConfig(config, "list_layout_buttons_display_by_tenant"));
            // 灰度批量打印的企业名单
            BULK_PRINT_GRAY_EI = getSetFromConfig(config, "bulk_print_gray_ei");
            BUTTON_REDIRECT_ACTION_GRAY_EI = getSetFromConfig(config, "button_redirect_action_gray_ei");
            BUTTON_REDIRECT_ACTION_GRAY_OBJ = getSetFromConfig(config, "button_redirect_action_gray_obj");
            bigObjectSupportButtonList = getSetFromConfig(config, "big_object_support_button_list");
            BUTTON_REDIRECT_ACTION_GRAY = generateFieldListMapFromConfigStr(config, "button_redirect_action_gray");
            OBJECT_BUTTON_BLACKLIST = generateFieldListMapFromConfigStr(config, "object_button_blacklist");
        }));

        ConfigFactory.getInstance().getConfig("fs-crm-prm-config", config -> {
            SUPPORT_PRM_WHITE_OBJECT_LIST = JSON.parseArray(config.get("menu_items"), String.class);
            NEW_SUPPORT_PRM_WHITE_OBJECT_LIST = JSON.parseArray(config.get("new_menu_items"), String.class);
            SUPPORT_PRM_WHITE_OBJECT_LIST.addAll(NEW_SUPPORT_PRM_WHITE_OBJECT_LIST);
        });
    }

    private static Table<String, String, List<IFilter>> parseButtonSystemFiltersConfig(IConfig config, String key) {
        Map<String, Map<String, List<Map>>> map = parseMapFromConfig(config, key);
        Table<String, String, List<IFilter>> result = HashBasedTable.create();
        if (CollectionUtils.empty(map)) {
            return result;
        }
        map.forEach((describeApiName, value) -> {
            value.forEach((buttonApiName, data) -> {
                result.put(describeApiName, buttonApiName, JSON.parseArray(JSON.toJSONString(data), IFilter.class));
            });
        });
        return result;
    }

    private static Table<String, String, List<String>> parseBatchButtonConfig(IConfig config, String key) {
        Map<String, Map<String, List<String>>> map = parseMapFromConfig(config, key);
        Table<String, String, List<String>> result = HashBasedTable.create();
        if (CollectionUtils.empty(map)) {
            return result;
        }
        map.forEach((describeApiName, value) -> {
            value.forEach((property, data) -> {
                result.put(describeApiName, property, data);
            });
        });
        return result;
    }

    private static Table<String, String, GrayRule> parseBatchButtonFilterConfig(IConfig config, String key) {
        Map<String, Map<String, String>> map = parseMapFromConfig(config, key);
        Table<String, String, GrayRule> result = AppFrameworkConfig.parseGrayRuleTable(map);
        return result;
    }

    private static Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newTreeSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static List<String> getListFromConfig(IConfig config, String key) {
        return Lists.newArrayList(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    @SuppressWarnings("unchecked")
    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Maps.newHashMap();
        }
        return JSON.parseObject(data, Map.class);
    }

    /**
     * 获取按钮配置信息
     *
     * @param config
     * @param key
     */
    private static void parseFromConfig(IConfig config, String key) {
        Map<String, Map<String, Map>> map = parseMapFromConfig(config, key);
        if (CollectionUtils.empty(map)) {
            log.warn("udef_button_config is empty");
            return;
        }
        Table<String, String, IUdefButtonConfig> result = HashBasedTable.create();
        map.forEach((describeApiName, value) -> {
            value.forEach((buttonApiName, data) -> {
                result.put(describeApiName, buttonApiName, UdefButtonConfig.fromMap(data));
            });
        });
        BUTTON_CONFIG_TABLE = result;
    }

    private static Map<String, Set<String>> generateFieldListMapFromConfigStr(IConfig config, String key) {
        String configStr = config.get(key);
        if (Strings.isNullOrEmpty(configStr)) {
            return Collections.emptyMap();
        }
        // s的example:
        // AccountObj,SalesOrderObj:create_time,created_by,Owner,Status;ProductObj:create_time
        Map<String, Set<String>> result = Maps.newHashMap();
        for (String eachStr : configStr.split(";")) {
            Set<String> values = Sets.newLinkedHashSet();
            Collections.addAll(values, eachStr.split(":")[1].split(","));
            List<String> keys = Lists.newArrayList();
            Collections.addAll(keys, eachStr.split(":")[0].split(","));
            for (String k : keys) {
                result.putIfAbsent(k, values);
            }
        }
        return result;
    }

    public static IUdefButtonConfig getDefineConfig(String describeApiName, String buttonApiName) {
        IUdefButtonConfig config = BUTTON_CONFIG_TABLE.get(describeApiName, buttonApiName);
        if(Objects.isNull(config)) {
            return null;
        }
        return config.copy();
    }

    public static IUdefButtonConfig getConfig(String describeApiName, String buttonApiName) {
        if (BUTTON_CONFIG_TABLE.contains(describeApiName, buttonApiName)) {
            IUdefButtonConfig udefButtonConfig = BUTTON_CONFIG_TABLE.get(describeApiName, buttonApiName);
            return udefButtonConfig.copy();
        }
        if (BUTTON_CONFIG_TABLE.contains(DefObjConstants.UDOBJ, buttonApiName)) {
            IUdefButtonConfig result = BUTTON_CONFIG_TABLE.get(DefObjConstants.UDOBJ, buttonApiName).copy();
            result.setDescribeApiName(describeApiName);
            return result;
        }
        String buttonName = ObjectDescribeExt.isCustomObject(buttonApiName) ? UDEF_BUTTON : DEFAULT_BUTTON;
        IUdefButtonConfig iUdefButtonConfig = BUTTON_CONFIG_TABLE.get(DefObjConstants.UDOBJ, buttonName);
        IUdefButtonConfig result = Optional.ofNullable(iUdefButtonConfig).map(IUdefButtonConfig::copy).orElseThrow(() ->
                new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        result.setApiName(buttonApiName);
        result.setDescribeApiName(describeApiName);
        return result;
    }

    public static List<IUdefButton> sortedByDefaultButtonOrder(List<IUdefButton> targetButtonList, String describeApiName) {
        if (CollectionUtils.empty(targetButtonList)) {
            return Lists.newArrayList();
        }
        // 默认排序，系统按钮排在前面
        targetButtonList.sort(Comparator.comparing(it -> ButtonDefineType.of(it.getDefineType())));
        // 按配置文件排序
        targetButtonList.sort(Comparator.comparingInt(o -> DEFAULT_BUTTON_ORDER.contains(o.getApiName()) ?
                DEFAULT_BUTTON_ORDER.indexOf(o.getApiName()) : Integer.MAX_VALUE));
        return targetButtonList;
    }

    public static List<IButton> generateByListBatchButtonOrder(List<IUdefButton> targetButtonList, IObjectDescribe describe) {
        List<IButton> buttons = CollectionUtils.nullToEmpty(targetButtonList).stream()
                .map(ButtonExt::of)
                .map(ButtonExt::toButton)
                .collect(Collectors.toList());

        List<IButton> buttonList = getByTemplates(describe.getApiName(), buttons, describe.getTenantId());
        return CollectionUtils.sortByGivenOrder(buttonList, getOrderTemplate(describe.getApiName(), describe.getTenantId()),
                IButton::getAction);
    }

    public static List<IButton> generateButtonsByOrder(String apiName, String tenantId) {
        List<String> orderTemplate = getOrderTemplate(apiName, tenantId);
        return orderTemplate.stream()
                .map(ObjectAction::of)
                .map(ObjectAction::createButton)
                .collect(Collectors.toList());
    }

    /**
     * 校验某租户下的某个预制对象或者所有自定义对象是否要显示按钮
     *
     * @param buttonActionCode buttonActionCode
     * @param objApiName       objApiName
     * @param tenantId         tenantId
     * @return
     */
    public static boolean filter(String buttonActionCode, String objApiName, String tenantId) {
        //1、某个预制对象或者所有自定义对象有单独配置
        String objApiNameIdx = ObjectDescribeExt.isCustomObject(objApiName) ? DefObjConstants.UDOBJ : objApiName;
        GrayRule grayRule = DEFAULT_BATCH_BUTTON_FILTER_CONFIG.get(objApiNameIdx, buttonActionCode);
        if (Objects.nonNull(grayRule)) {
            return grayRule.isAllow(tenantId);
        }
        //2、所有对象有全局配置
        GrayRule defaultRule = DEFAULT_BATCH_BUTTON_FILTER_CONFIG.get(DefObjConstants.ALLOBJ, buttonActionCode);
        if (Objects.nonNull(defaultRule)) {
            return defaultRule.isAllow(tenantId);
        }
        //1和2无配置则显示，1和2同时可命中使用1
        return true;
    }

    // 根据配置文件，生成不同对象的按钮
    public static List<IButton> getByTemplates(String apiName, List<IButton> buttons, String tenantId) {
        List<IButton> incrementAction = getIncrementActionName(apiName).stream()
                .filter((actionCode) -> filter(actionCode, apiName, tenantId))
                .map(ObjectAction::of)
                .map(ObjectAction::createButton)
                .collect(Collectors.toList());

        // 追加需要新增的，移除需要删除的
        List<String> removeActionName = getRemoveActionName(apiName, tenantId);
        buttons.addAll(incrementAction);
        buttons.removeIf(x -> removeActionName.contains(x.getAction()));
        return buttons;
    }

    private static List<String> getIncrementActionName(String apiName) {
        if (DEFAULT_BATCH_BUTTON_CONFIG.containsRow(apiName)) {
            return CollectionUtils.nullToEmpty(DEFAULT_BATCH_BUTTON_CONFIG.get(apiName, "increment"));
        }
        return CollectionUtils.nullToEmpty(DEFAULT_BATCH_BUTTON_CONFIG.get(DefObjConstants.UDOBJ, "increment"));
    }

    private static List<String> getRemoveActionName(String apiName, String tenantId) {
        if (!DEFAULT_BATCH_BUTTON_CONFIG.containsRow(apiName)) {
            return Collections.emptyList();
        }
        List<String> standard = DEFAULT_BATCH_BUTTON_CONFIG.get(DefObjConstants.UDOBJ, "order");
        List<String> template = getOrderTemplate(apiName, tenantId);
        // 出现在标准中，但不在模板中
        return CollectionUtils.nullToEmpty(standard).stream().filter(x -> !template.contains(x)).collect(Collectors.toList());

    }

    private static List<String> getOrderTemplate(String apiName, String tenantId) {
        List<String> orders;
        if (DEFAULT_BATCH_BUTTON_CONFIG.contains(apiName, "order")) {
            orders = DEFAULT_BATCH_BUTTON_CONFIG.get(apiName, "order");
        } else {
            orders = DEFAULT_BATCH_BUTTON_CONFIG.get(DefObjConstants.UDOBJ, "order");
        }
        //default.batch.button.config走一个default.batch.button.config.filter的过滤
        List<String> buttonOrders = CollectionUtils.nullToEmpty(orders).stream()
                .filter((actionCode) -> filter(actionCode, apiName, tenantId))
                .collect(Collectors.toList());

        if (UdobjGrayUtil.isObjectAndTenantGray("follow_gray", tenantId, apiName)) {
            buttonOrders.add(ObjectAction.FOLLOW.getActionCode());
            buttonOrders.add(ObjectAction.UNFOLLOW.getActionCode());
        }

        if (isGrayBulkPrint(tenantId)) {
            buttonOrders.add(ObjectAction.PRINT.getActionCode());
            return buttonOrders;
        }
        return buttonOrders;
    }

    // 需求地址http://wiki.firstshare.cn/pages/viewpage.action?pageId=110351552
    public static void filterByPrmDefaultBatchButtonConfig(List<IButton> buttons, String apiName) {
        Map<String, List<String>> prmDefaultBatchButtonConfig = getPrmDefaultBatchButtonConfig();
        String key = prmDefaultBatchButtonConfig.containsKey(apiName) ? apiName : DefObjConstants.UDOBJ;
        List<String> buttonApiNames = prmDefaultBatchButtonConfig.get(key);
        buttons.removeIf(x -> needRemove(buttonApiNames, x));
    }

    /**
     * 保留指定的预置按钮，保留 UIPaaS 按钮，保留自定义按钮。
     * 其余按钮删除
     *
     * @param buttonApiNames 需要保留的预置按钮
     * @param button
     * @return
     */
    private static boolean needRemove(List<String> buttonApiNames, IButton button) {
        if (buttonApiNames.contains(button.getName())) {
            return false;
        }
        LayoutButtonExt layoutButtonExt = LayoutButtonExt.of(button);
        if (layoutButtonExt.isUIPaaS()) {
            return false;
        }
        return layoutButtonExt.isSystemButton();
    }

    private static Map<String, List<String>> getPrmDefaultBatchButtonConfig() {
        if (RequestUtil.isMobileRequestBeforeVersion(VERSION_720)) {
            return PRM_DEFAULT_BATCH_BUTTON_CONFIG;
        }
        return PRM_DEFAULT_BATCH_BUTTON_CONFIG_720;
    }

    public static boolean isRealTimeJob(int size) {
        return size <= BULK_ACTION_LIMIT_SIZE;
    }

    public static void generatedButtonSystemFilter(IUdefButton button) {
        if (!ButtonExt.of(button).isSystemButton()) {
            return;
        }
        List<IFilter> filters = getFiltersByApiName(button.getDescribeApiName(), button.getApiName());
        ButtonExt.of(button).addFilter(filters);
    }

    private static List<IFilter> getFiltersByApiName(String describeApiName, String buttonApiName) {
        if (BUTTON_SYSTEM_FILTERS.contains(describeApiName, buttonApiName)) {
            return BUTTON_SYSTEM_FILTERS.get(describeApiName, buttonApiName);
        }
        return CollectionUtils.nullToEmpty(BUTTON_SYSTEM_FILTERS.get(DefObjConstants.UDOBJ, buttonApiName));
    }

    public static boolean isMobileBulkButtonBlackActionObject(String objectApiName) {
        return MOBILE_BULK_BUTTON_BLACK_ACTION.contains(objectApiName);
    }

    public static boolean isMobileBulkButtonBlackAction(String describeApiName, String action) {
        return getActions(describeApiName).contains(action);
    }

    private static Set<String> getActions(String describeApiName) {
        Set<String> defaultActions = Sets.newHashSet(MOBILE_BULK_BUTTON_BLACK_ACTION_CONFIG.get(DefObjConstants.UDOBJ));
        if (MOBILE_BULK_BUTTON_BLACK_ACTION_CONFIG.containsKey(describeApiName)) {
            defaultActions.addAll(MOBILE_BULK_BUTTON_BLACK_ACTION_CONFIG.get(describeApiName));
        }
        return defaultActions;
    }

    public static boolean isSupportPRM(IObjectDescribe describe) {
        // 新建对象的时候，describe为空，此时需要将其视为自定义对象
        if (describe == null) {
            return true;
        }
        // 自定义对象和白名单中的预置对象支持 PRM
        if (ObjectDescribeExt.of(describe).isCustomObject()) {
            return true;
        }
        return SUPPORT_PRM_WHITE_OBJECT_LIST.contains(describe.getApiName());
    }

    public static boolean isSupportPRM(String objectApiName) {
        if (Strings.isNullOrEmpty(objectApiName)) {
            return true;
        }
        if (ObjectDescribeExt.isCustomObject(objectApiName)) {
            return true;
        }
        return SUPPORT_PRM_WHITE_OBJECT_LIST.contains(objectApiName);
    }

    public static boolean isDefaultButtonParamGray(String describeApiName, String buttonApiName, String tenantId) {
        Set<String> tenantGary = SYSTEM_BUTTON_PARAM_TENANT_GRAY.getOrDefault(String.format("%s.%s",
                ObjectDescribeExt.isCustomObject(describeApiName) ? DefObjConstants.UDOBJ : describeApiName, buttonApiName), Collections.emptySet());
        if (tenantGary.contains(ALL)) {
            return true;
        }
        return tenantGary.contains(tenantId);
    }

    public static boolean isDefaultButtonActionGray(String describeApiName, String buttonApiName, String stage, String tenantId) {
        Set<String> tenantGary = SYSTEM_BUTTON_ACTION_TENANT_GRAY.getOrDefault(String.format("%s.%s.%s",
                ObjectDescribeExt.isCustomObject(describeApiName) ? DefObjConstants.UDOBJ : describeApiName, buttonApiName, stage), Collections.emptySet());
        if (tenantGary.contains(ALL)) {
            return true;
        }
        return tenantGary.contains(tenantId);
    }

    public static boolean isButtonCountGrayTenant(String tenantId) {
        return BUTTON_COUNT_GRAY_TENANT.contains(ALL) || BUTTON_COUNT_GRAY_TENANT.contains(tenantId);
    }

    public static int getButtonGrayCount() {
        return BUTTON_COUNT_GRAY;
    }

    public static boolean isDesignerButtonBlacklist(String objectApiName, String buttonApiName) {
        List<String> blacklist = DESIGNER_BUTTON_BLACKLIST.get(objectApiName);
        if (CollectionUtils.empty(blacklist)) {
            return false;
        }
        return blacklist.contains(buttonApiName);
    }

    public static boolean isListLayoutWebOnlyButtons(String buttonApiName) {
        return LIST_LAYOUT_BUTTONS_WEB_ONLY.contains(buttonApiName);
    }


    @Deprecated
    public static boolean isListLayoutMobileOnlyButtons(String buttonApiName) {
        return LIST_LAYOUT_BUTTONS_MOBILE_ONLY.contains(buttonApiName) && !LIST_LAYOUT_BUTTONS_WEB_ONLY.contains(buttonApiName);
    }

    public static boolean isGrayBulkPrint(String tenantId) {
        return BULK_PRINT_GRAY_EI.contains(ALL) || BULK_PRINT_GRAY_EI.contains(tenantId);
    }

    public static boolean isGray(String tenantId, Set<String> eiList) {
        return CollectionUtils.notEmpty(eiList) || eiList.contains(ALL) || eiList.contains(tenantId);
    }

    public static boolean isButtonRedirectActionGray(String tenantId, String objectApiName) {
        // 优先使用新的配置
        if (CollectionUtils.notEmpty(BUTTON_REDIRECT_ACTION_GRAY)) {
            if (ObjectDescribeExt.isCustomObject(objectApiName)) {
                objectApiName = AppFrameworkConfig.UDOBJ;
            }
            Set<String> tenantIds = BUTTON_REDIRECT_ACTION_GRAY.get(objectApiName);
            if (CollectionUtils.empty(tenantIds)) {
                return false;
            }
            return tenantIds.contains(ALL) || tenantIds.contains(tenantId);
        }
        if (BUTTON_REDIRECT_ACTION_GRAY_EI.contains(ALL) || BUTTON_REDIRECT_ACTION_GRAY_EI.contains(tenantId)) {
            if (ObjectDescribeExt.isCustomObject(objectApiName)) {
                return true;
            }
            return BUTTON_REDIRECT_ACTION_GRAY_OBJ.contains(objectApiName);
        }
        return false;
    }

    public static Set<String> getObjectButtonBlacklist(String objectApiName) {
        if (CollectionUtils.empty(OBJECT_BUTTON_BLACKLIST)) {
            return Collections.emptySet();
        }
        return Collections.unmodifiableSet(OBJECT_BUTTON_BLACKLIST.getOrDefault(objectApiName, Collections.emptySet()));
    }

    public static Set<String> getBigObjectSupportButtonList() {
        return Collections.unmodifiableSet(bigObjectSupportButtonList);
    }

    public static Map<String, Map<String, GrayRule>> buildButtonDisplayGray(List<Map<String, Object>> list) {
        Map<String, Map<String, GrayRule>> grayRuleMap = Maps.newHashMap();
        if (CollectionUtils.empty(list)) {
            return grayRuleMap;
        }

        list.forEach(map -> {
            Map<String, String> rs = ((Map<String, String>) map.get("gray_rules"));
            String apiName = ((String) map.get("api_name"));
            if (StringUtils.isBlank(apiName) || MapUtils.isEmpty(rs)) {
                return;
            }
            Map<String, GrayRule> grayRules = Maps.newHashMap();
            rs.forEach((terminal, rule) -> {
                grayRules.put(terminal, new GrayRule(rule));
            });
            grayRuleMap.put(apiName, grayRules);
        });

        return grayRuleMap;
    }

    /**
     * 列表页按钮显示灰度
     * 注意：如果该按钮移动端显示则需要判断该按钮是否在只在Web端显示
     *
     * @param buttonApi
     * @param terminal
     * @param tenantId
     * @return
     */
    public static boolean listLayoutButtonDisplayGray(String buttonApi, String terminal, String tenantId) {
        // 如果是打印按钮则必须前提灰度批量打印
        if (StringUtils.equals(buttonApi, PRINT.getButtonApiName()) && !isGrayBulkPrint(tenantId)) {
            return false;
        }
        return Optional.ofNullable(LIST_LAYOUT_BUTTONS_DISPLAY_BY_TENANT.get(buttonApi))
                .map(grayRuleMap -> grayRuleMap.get(terminal))
                .map(grayRule -> grayRule.isAllow(tenantId))
                .orElse(false);
    }


}
