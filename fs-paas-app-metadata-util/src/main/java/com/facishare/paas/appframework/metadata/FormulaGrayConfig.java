package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Set;

/**
 * Created by zhouwr on 2018/10/30
 */
@Slf4j
public final class FormulaGrayConfig {

    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings();

    private static final String ALL = "ALL";

    private static Set<String> grayFieldValidateTenantIds = Sets.newHashSet();
    private static Set<String> cloneWhiteTenantIds = Sets.newHashSet();

    private static final TimeRange DEFAULT_CHECK_CALCULATION_TAME_RANGE = new TimeRange(LocalTime.of(7, 0), LocalTime.of(19, 0));
    private static TimeRange checkCalculationTimeRange;
    private static int calculationStartTimeDelayMinutes;
    @Getter
    private static int dataNumForNightCalculation;
    @Getter
    private static int countFactorForNightCalculation;

    private static final ZoneId ZONE_ID_CHINA = ZoneId.of("Asia/Shanghai");

    static {
        ConfigFactory.getConfig("fs-paas-formula-gray-config", config -> {
            log.warn("reload config fs-paas-formula-gray-config,content:{}", config.getString());
            grayFieldValidateTenantIds = getSetFromConfig(config, "gray.field.validate.tenant.ids");
            cloneWhiteTenantIds = getSetFromConfig(config, "gray.clone.white.tenant.ids");

            checkCalculationTimeRange = parseTimeRange(config, "checkCalculationTimeRange", DEFAULT_CHECK_CALCULATION_TAME_RANGE);
            calculationStartTimeDelayMinutes = config.getInt("calculationStartTimeDelayMinutes", 10);
            dataNumForNightCalculation = config.getInt("dataNumForNightCalculation", 1000000);
            countFactorForNightCalculation = config.getInt("countFactorForNightCalculation", 1000);
        });
    }

    private FormulaGrayConfig() {
    }

    private static Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static TimeRange parseTimeRange(IConfig config, String key, TimeRange defaultValue) {
        String timeRangeText = config.get(key);
        if (StringUtils.isBlank(timeRangeText)) {
            return defaultValue;
        }
        String[] values = StringUtils.split(timeRangeText, "-");
        if (values.length != 2) {
            return defaultValue;
        }
        try {
            return TimeRange.builder()
                    .startTime(LocalTime.parse(values[0]))
                    .endTime(LocalTime.parse(values[1]))
                    .build();
        } catch (Exception e) {
            log.warn("parseTimeRange failed,key:{},value:{}", key, timeRangeText);
            return defaultValue;
        }
    }

    public static boolean isInFieldValidateWhiteList(String tenantId) {
        return grayFieldValidateTenantIds.contains(ALL) || grayFieldValidateTenantIds.contains(tenantId);
    }

    /**
     * 判断是否是数据复制白名单企业
     * <br>
     * 复制，计算相关字段需要被计算
     *
     * @param tenantId
     * @return
     */
    public static boolean isInCloneWhiteList(String tenantId) {
        return CollectionUtils.notEmpty(cloneWhiteTenantIds) && cloneWhiteTenantIds.contains(tenantId);
    }

    public static boolean isCurrentTimeInCheckCalculationTimeRange() {
        if (Objects.isNull(checkCalculationTimeRange)) {
            return false;
        }
        LocalTime now = LocalTime.now(ZONE_ID_CHINA);
        return checkCalculationTimeRange.contains(now);
    }

    public static long getCalculationStartTime() {
        LocalDateTime now = LocalDateTime.now(ZONE_ID_CHINA);
        return now.with(checkCalculationTimeRange.getEndTime())
                .plusMinutes(calculationStartTimeDelayMinutes)
                .atZone(ZONE_ID_CHINA)
                .toInstant()
                .toEpochMilli();
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    private static class TimeRange {
        private LocalTime startTime;
        private LocalTime endTime;

        public boolean contains(LocalTime time) {
            return !time.isBefore(startTime) && !time.isAfter(endTime);
        }
    }

}
