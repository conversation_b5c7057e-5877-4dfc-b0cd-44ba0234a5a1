package com.facishare.paas.appframework.metadata.search;

import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;

import java.util.List;

/**
 * 规定包含 {@link IFilter} 的节点只能为子节点
 * 父节点的 {@link Connector} 表示所有子节点之间的关系
 * <p>
 * create by <PERSON><PERSON><PERSON> on 2020/06/28
 */
public interface SearchQuery {

    SearchQuery and(IFilter filter);

    SearchQuery and(List<IFilter> filters);

    /**
     * 当前节点和入参中的 queries 为且关系
     *
     * @param queries
     * @return 返回新的节点。当前节点和参数中的节点都为新节点的子节点
     */
    SearchQuery and(SearchQuery... queries);

    SearchQuery or(IFilter... filters);

    /**
     * 当前节点和入参中的 queries 为或关系
     *
     * @param queries
     * @return 返回新的节点。当前节点和参数中的节点都为新节点的子节点
     */
    SearchQuery or(SearchQuery... queries);

    void remove(SearchQuery searchQuery);

    void add(SearchQuery... queries);

    boolean isEmpty();

    ISearchTemplateQuery toSearchTemplateQuery();

    IFilter getFilter();

    boolean isFilterNode();

    /**
     * @return 当前接节点所有子节点
     */
    List<SearchQuery> getSearchQueryContainer();

    Connector getConnector();

    enum Connector {
        AND, OR,
        ;
    }

}
