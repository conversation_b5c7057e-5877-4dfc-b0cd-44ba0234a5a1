package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.layout.ComponentI18NKeys;
import com.facishare.paas.appframework.metadata.layout.EditLayout;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.component.FiltersComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.RelatedListFormComponentExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/1/31
 */
@Slf4j
public class ComponentExt {

    public static int DEFAULT_ORDER = 9999;

    public static final String WHERES = "wheres";
    public static final String FILTERS = "filters";

    public static final String TYPE_FORM_TABLE = "form_table";
    public static final String TYPE_TEXT_COMPONENT = "text";
    public static final String TYPE_TASK_LIST_COMPONENT = "task_list";
    public static final String TYPE_TASK_LIST_MOBILE_COMPONENT = "task_list_mobile";
    public static final String TYPE_AI_INSIGHT_COMPONENT = "ai_component";

    public static final String TEAM_COMPONENT_NAME = "relevant_team_component";
    public static final String TOP_COMPONENT_NAME = "top_component";
    public static final String MIDDLE_COMPONENT_NAME = "middle_component";
    public static final String CHART_GROUP_NAME = "chart_group";
    public static final String OPERATION_LOG_COMPONENT_NAME = "operation_log";
    public final static String PAYMENT_DESCRIBE_API_NAME = "payment_record";
    public final static String PAYMENT_COMPONENT_NAME = "payment_recordrelated_list_generate_by_UDObjectServer__c";
    public final static String BPM_RELATED_NAME = "BPM_related_list";

    public static final String HEAD_INFO_COMPONENT_NAME = "head_info";
    public static final String TOP_INFO_COMPONENT_NAME = "top_info";
    public static final String SALE_LOG_COMPONENT_NAME = "sale_log";
    public static final String SERVICE_LOG_COMPONENT_NAME = "service_log";
    public static final String SUMMARY_CARD_COMPONENT_NAME = "summary_card_component";
    public static final String DETAIL_INFO_COMPONENT_NAME = "form_component";
    public static final String TABS_COMPONENT_NAME = "tabs_component";
    public static final String FRAME_COMPONENT_NAME = "frame_component";
    public static final String CHART_COMPONENT_NAME = "chart_component";
    public static final String ACCOUNT_HIERARCHY_COMPONENT_NAME = "account_hierarchy_component";
    public static final String CONTACT_RELATION_COMPONENT_NAME = "contact_relation_component";
    public final static String ATTACH_COMPONENT_NAME = "attach_component";
    public final static String NAVIGATION_COMPONENT_NAME = "navigation";
    public final static String PRODUCT_ATTRIBUTE_COMPONENT_NAME = "product_attribute_component";
    public final static String ACCOUNT_OPERATION_MAP_NAME = "account_operation_map";
    public final static String LIST_COMPONENT_NAME = "list_component";
    public final static String SHORTCUT_COMPONENT_NAME = "shortcut";
    public final static String ELECTRONIC_SIGN_COMPONENT_NAME = "electronic_sign";
    public final static String SUMMARY_INFO = "summary_info";

    public static final String APPROVAL_COMPONENT = "approval_component";
    public static final String STAGE_COMPONENT = "stage_component";
    public static final String BPM_COMPONENT = "bpm_component";

    public static final String NAME_COMPONENT = "name_component";

    public static final String DEFINE_TYPE = "define_type";
    public static final String FORM_COMPONENT = "form_component";
    public static final String DETAIL_INFO = "detail_info";
    public static final String MASTER_DETAIL_SUFFIX = "_md_group_component";
    public final static String ATTACH_REF_OBJECT_NAME = "AttachObj";

    public static final String IS_UNPOPULAR = "is_unpopular";
    public static final String LIMIT = "limit";
    public static final String NAME_I18N_KEY = "nameI18nKey";
    public static final String FIELD_API_NAME = "field_api_name";
    public static final String BUTTON_INFO = "button_info";

    public static final String DISPLAY_RULE = "display_rule";
    public static final String TABLE_COMPONENT = "table_component";
    public static final String IS_DEPEND = "is_depend";

    public static final String FLOW_LAYOUT_API_NAME = "flow_layout_api_name";

    public static final String TILE_RELATED_LIST = "tile_related_list";

    public static final String IS_DISABLED = "isDisabled";

    public static final String NAME_I18N_KEY_LIST = "nameI18nKeyList";

    private static final String I18N_INFO_MAP = "i18nInfoMap";
    private static final String I18N_INFO_LIST = "i18nInfoList";
    private static final String SHOW_HEADER = "show_header";
    private static final String CONTENT = "content";
    public static final String FIELD_SECTION = "fieldSection";
    public static final String NEW_HEADER = "newHeader";

    public static final String AI_CHAT_COMPONENT_NAME = "ai_chat_component";


    private static final Set<String> PROTECTED_PROPS = Sets.newHashSet(IComponent.NAME, IComponent.TYPE, IComponent.HEADER,
            IRelatedObjectList.REF_OBJECT_API_NAME, IRelatedObjectList.RELATED_LIST_NAME, IRelatedObjectList.FIELD_API_NAME,
            IComponent.BUTTONS);

    @Getter
    @Delegate
    private IComponent component;

    private ComponentExt(IComponent component) {
        this.component = component;
    }

    public static ComponentExt of(IComponent component) {
        return new ComponentExt(component);
    }

    public static String getConfigKey(String objectApiName) {
        return "component_config_" + objectApiName;
    }

    private static String getTransKey(String objectApiName, String layoutApiName, String componentApiName, String apiName) {
        return "bizComponent." + objectApiName + "." + layoutApiName + "." + componentApiName + "." + apiName;
    }

    public Collection<String> getBiComponentNameI18nKeyList() {
        String cardId = get("CardID", String.class);
        if (StringUtils.isBlank(cardId)) {
            return Lists.newArrayList();
        }
        List<String> result = Lists.newArrayList();
        result.add("Bi.Custom.Realtime.RptName." + cardId + ".Label");
        result.add("Bi.Custom.Realtime.StatName." + cardId + ".Label");
        result.add("Bi.Custom.Realtime.DashBoardName." + cardId + ".Label");
        return result;
    }

    public List<I18nInfo> getI18nInfoList(String objectApiName, String layoutApiName) {
        if (Objects.isNull(component.get(I18N_INFO_LIST))) {
            return Lists.newArrayList();
        }
        try {
            List i18nInfoList = (List) component.get(I18N_INFO_LIST);
            if (CollectionUtils.empty(i18nInfoList)) {
                return Lists.newArrayList();
            }
            List<I18nInfo> i18nInfos = Lists.newArrayList();
            for (Object object : i18nInfoList) {
                if (object instanceof Map) {
                    i18nInfos.add(I18nInfo.from((Map<String, Object>) object));
                } else if (object instanceof I18nInfo) {
                    i18nInfos.add((I18nInfo) object);
                } else {
                    log.error("getI18nInfoList error, object is not I18nInfo or Map!type:{}", object.getClass());
                }
            }
            i18nInfos = i18nInfos.stream().filter(x -> StringUtils.isBlank(x.getType())).collect(Collectors.toList());
            i18nInfos.stream().filter(x -> StringUtils.isBlank(x.getCustomKey()))
                    .forEach(x -> x.setCustomKey(getTransKey(objectApiName,
                            layoutApiName, component.getName(), StringUtils.defaultIfBlank(x.getApiName(), "d" + RandomStringUtils.random(4)))));
            return i18nInfos;
        } catch (Exception e) {
            log.error("getI18nInfoList error", e);
            return Lists.newArrayList();
        }
    }

    public List<I18nInfo> getFieldSectionI18nInfoList(String customKey) {
        if (Objects.isNull(component.get(I18N_INFO_LIST))) {
            return Lists.newArrayList();
        }
        try {
            List i18nInfoList = (List) component.get(I18N_INFO_LIST);
            if (CollectionUtils.empty(i18nInfoList)) {
                return Lists.newArrayList();
            }
            List<I18nInfo> i18nInfos = Lists.newArrayList();
            for (Object object : i18nInfoList) {
                if (object instanceof Map) {
                    i18nInfos.add(I18nInfo.from((Map<String, Object>) object));
                } else if (object instanceof I18nInfo) {
                    i18nInfos.add((I18nInfo) object);
                } else {
                    log.error("getI18nInfoList error, object is not I18nInfo or Map!type:{}", object.getClass());
                }
            }
            i18nInfos.stream().filter(x -> StringUtils.isBlank(x.getCustomKey()))
                    .filter(x -> FIELD_SECTION.equals(x.getType()))
                    .forEach(x -> x.setCustomKey(customKey));
            return i18nInfos;
        } catch (Exception e) {
            log.error("getI18nInfoList error", e);
            return Lists.newArrayList();
        }
    }

    public List<I18nInfo> getFieldSectionI18nInfoList() {
        if (Objects.isNull(component.get(I18N_INFO_LIST))) {
            return Lists.newArrayList();
        }
        try {
            List i18nInfoList = (List) component.get(I18N_INFO_LIST);
            if (CollectionUtils.empty(i18nInfoList)) {
                return Lists.newArrayList();
            }
            List<I18nInfo> i18nInfos = Lists.newArrayList();
            for (Object object : i18nInfoList) {
                if (object instanceof Map) {
                    i18nInfos.add(I18nInfo.from((Map<String, Object>) object));
                } else if (object instanceof I18nInfo) {
                    i18nInfos.add((I18nInfo) object);
                } else {
                    log.error("getFieldSectionI18nInfoList error, object is not I18nInfo or Map!type:{}", object.getClass());
                }
            }
            i18nInfos.stream().filter(x -> StringUtils.isBlank(x.getCustomKey()))
                    .filter(x -> FIELD_SECTION.equals(x.getType()));
            return i18nInfos;
        } catch (Exception e) {
            log.error("getFieldSectionI18nInfoList error", e);
            return Lists.newArrayList();
        }
    }

    public List<I18nInfo> getExcludeFieldSectionI18nInfoList() {
        if (Objects.isNull(component.get(I18N_INFO_LIST))) {
            return Lists.newArrayList();
        }
        try {
            List i18nInfoList = (List) component.get(I18N_INFO_LIST);
            if (CollectionUtils.empty(i18nInfoList)) {
                return Lists.newArrayList();
            }
            List<I18nInfo> i18nInfos = Lists.newArrayList();
            for (Object object : i18nInfoList) {
                if (object instanceof Map) {
                    i18nInfos.add(I18nInfo.from((Map<String, Object>) object));
                } else if (object instanceof I18nInfo) {
                    i18nInfos.add((I18nInfo) object);
                } else {
                    log.error("getExcludeFieldSectionI18nInfoList error, object is not I18nInfo or Map!type:{}", object.getClass());
                }
            }
            i18nInfos.stream().filter(x -> StringUtils.isBlank(x.getCustomKey()))
                    .filter(x -> !FIELD_SECTION.equals(x.getType()));
            return i18nInfos;
        } catch (Exception e) {
            log.error("getExcludeFieldSectionI18nInfoList error", e);
            return Lists.newArrayList();
        }
    }


    public List<I18nInfo> getI18nInfoListBySectionApiName(String sectionApiName, String customKey) {
        if (Objects.isNull(component.get(I18N_INFO_LIST))) {
            return Lists.newArrayList();
        }
        try {
            List i18nInfoList = (List) component.get(I18N_INFO_LIST);
            if (CollectionUtils.empty(i18nInfoList)) {
                return Lists.newArrayList();
            }
            List<I18nInfo> i18nInfos = Lists.newArrayList();
            for (Object object : i18nInfoList) {
                if (object instanceof Map) {
                    i18nInfos.add(I18nInfo.from((Map<String, Object>) object));
                } else if (object instanceof I18nInfo) {
                    i18nInfos.add((I18nInfo) object);
                } else {
                    log.error("getI18nInfoList error, object is not I18nInfo or Map!type:{}", object.getClass());
                }
            }
            i18nInfos = i18nInfos.stream().filter(x -> FIELD_SECTION.equals(x.getType()))
                    .filter(x -> Objects.equals(sectionApiName, x.getApiName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(i18nInfos)) {
                return Lists.newArrayList();
            }
            i18nInfos.get(0).setCustomKey(customKey);
            return i18nInfos;
        } catch (Exception e) {
            log.error("getI18nInfoList error", e);
            return Lists.newArrayList();
        }
    }


    public boolean isShowHeader() {
        return BooleanUtils.isTrue(get(SHOW_HEADER, Boolean.class));
    }

    public static String getDetailComponentName(String objectApiName) {
        return objectApiName + MASTER_DETAIL_SUFFIX;
    }

    public static String getDetailLayoutTabNameKey(String objectApiName, String layoutApiName, String tabApiName) {
        return "layout." + objectApiName + ".detail." + layoutApiName + ".tabs." + tabApiName + ".header";
    }

    public static String getListLayoutTabNameKey(String objectApiName, String layoutApiName, String tabApiName) {
        return "layout." + objectApiName + ".list." + layoutApiName + ".tabs." + tabApiName + ".header";
    }

    public static String getDetailLayoutGroupName(String objectApiName, String layoutApiName, String groupApiName) {
        return "layout." + objectApiName + ".detail." + layoutApiName + ".group." + groupApiName + ".header";
    }

    public static String getEditLayoutGroupName(String objectApiName, String layoutApiName, String fieldSectionApiName) {
        return "layout." + objectApiName + ".edit." + layoutApiName + ".group." + fieldSectionApiName + ".header";
    }

    public static String getEditLayoutComponentNameKey(String objectApiName, String layoutApiName, String componentApiName) {
        return "layout." + objectApiName + ".edit." + layoutApiName + ".component." + componentApiName + ".header";
    }

    public static String getDetailComponentNameKey(String objectApiName, String layoutApiName, String componentApiName) {
        return "layout." + objectApiName + ".detail." + layoutApiName + ".component." + componentApiName + ".header";
    }

    public static String getComponentNameKey(String objectApiName, String layoutApiName, String componentApiName, String layoutType) {
        if (LayoutTypes.DETAIL.equals(layoutType)) {
            return getDetailComponentNameKey(objectApiName, layoutApiName, componentApiName);
        } else if (LayoutTypes.EDIT.equals(layoutType)) {
            return getEditLayoutComponentNameKey(objectApiName, layoutApiName, componentApiName);
        } else if (LayoutTypes.LIST_LAYOUT.equals(layoutType)) {
            return getListComponentKey(objectApiName, layoutApiName, componentApiName);
        }
        return "";
    }

    public static String getTextComponentContentKey(String objectApiName, String layoutApiName, String componentApiName, String layoutType) {
        return "layout." + objectApiName + "." + layoutType + "." + layoutApiName + ".component." + componentApiName + ".content";
    }


    public static String getTopNavigationKey(String objectApiName, String layoutApiName, String labelIndex) {
        return "layout." + objectApiName + ".list." + layoutApiName + ".page." + labelIndex + ".header";
    }

    public static String getListComponentKey(String objectApiName, String layoutApiName, String componentApiName) {
        return "layout." + objectApiName + ".list." + layoutApiName + ".component." + componentApiName + ".header";
    }

    public static void removeEmptyOrDuplicateComponent(List<Map<String, Object>> components) {
        if (CollectionUtils.empty(components)) {
            return;
        }
        Iterator<Map<String, Object>> iterator = components.iterator();
        Set<String> apiNames = Sets.newHashSet();
        while (iterator.hasNext()) {
            Map<String, Object> component = iterator.next();
            String apiName = (String) component.get(IComponent.NAME);
            if (Strings.isNullOrEmpty(apiName) || !apiNames.add(apiName)) {
                iterator.remove();
            }
        }
    }

    public Map toMap() {
        return ((DocumentBasedBean) component).getContainerDocument();
    }

    public String getFlowLayoutApiName() {
        return (String) get(FLOW_LAYOUT_API_NAME);
    }

    public String getRefObjectApiName() {
        return (String) get(IRelatedObjectList.REF_OBJECT_API_NAME);
    }

    public void setDefaultValue() {
        setOrder(DEFAULT_ORDER);
        setIsHidden(false);
    }

    public void setUnpopular(boolean unpopular) {
        set(IS_UNPOPULAR, unpopular);
    }

    public boolean isUnpopular() {
        return Boolean.TRUE.equals(get(IS_UNPOPULAR));
    }

    public boolean isDepend() {
        return Boolean.TRUE.equals(get(IS_DEPEND));
    }

    public void removeButtonByActions(List<String> actions) {
        List<IButton> buttonList = getButtons();
        if (CollectionUtils.empty(buttonList)) {
            return;
        }
        buttonList.removeIf(x -> actions.contains(x.getAction()));
        setButtons(buttonList);
    }

    public void removeNewHeader() {
        toMap().remove(NEW_HEADER);
    }

    public void removeButtonByActionsDeeply(List<String> actions) {
        if (component instanceof IGroupComponent) {
            GroupComponentExt.of((IGroupComponent) component).removeButtonByActionsDeeply(actions);
            return;
        }
        if (component instanceof IMultiTableComponent) {
            MultiTableComponentExt.of((IMultiTableComponent) component).removeButtonByActionsDeeply(actions);
            return;
        }

        removeButtonByActions(actions);
    }

    public void addButton(IButton button) {
        List<IButton> buttonList = getButtons();
        if (buttonList == null) {
            buttonList = Lists.newArrayList();
        }
        buttonList.add(button);
        setButtons(buttonList);
    }

    public void addComponent(String component) {
        List<List<String>> components = (List<List<String>>) toMap().computeIfAbsent(ITabsComponent.COMPONENTS, k -> Lists.newArrayList());
        components.add(Lists.newArrayList(component));
    }

    public void setLimit(int limit) {
        set(LIMIT, limit);
    }

    public boolean isFormType() {
        return IComponent.TYPE_FORM.equals(getType());
    }

    public boolean isGroupComponent() {
        return IComponent.TYPE_GROUP.equals(getType());
    }

    public boolean isMasterDetailComponent() {
        return StringUtils.endsWith(getName(), MASTER_DETAIL_SUFFIX);
    }

    public boolean isRelatedList() {
        return IComponent.TYPE_RELATEDLIST.equals(getType());
    }

    public boolean isTileRelatedList() {
        return TILE_RELATED_LIST.equals(getType());
    }

    public boolean isTeamComponent() {
        return TEAM_COMPONENT_NAME.equals(getName());
    }

    public boolean isSaleLog() {
        return SALE_LOG_COMPONENT_NAME.equals(getName());
    }

    public boolean isOperationLog() {
        return OPERATION_LOG_COMPONENT_NAME.equals(getName());
    }

    public boolean isPaymentComponent() {
        return PAYMENT_COMPONENT_NAME.equals(getName());
    }

    public boolean isTabs() {
        return IComponent.TYPE_TABS.equals(getType());
    }

    public boolean isIFrameComponent() {
        return IComponent.TYPE_IFRAME.equals(getType());
    }

    public boolean isCustomComponent() {
        return IComponent.TYPE_CUSTOM.equals(getType());
    }

    public boolean isChartComponent() {
        return IComponent.TYPE_CHART.equals(getType());
    }

    public boolean isBiDashboard() {
        return "biDashboardCom".equals(getType());
    }

    public boolean isBiComponent() {
        return isChartComponent() || isBiInsightChartComponent() || isBiFilterComponent() || isBiDashboard();
    }

    public boolean isBiInsightChartComponent() {
        return "biInsightChart".equals(getType());
    }

    public boolean isBiFilterComponent() {
        return FiltersComponentExt.COMPONENT_TYPE_FILTERS.equals(getType());
    }

    public boolean isAccountHierarchy() {
        return IComponent.TYPE_ACCOUNT_HIERARCHY.equals(getType());
    }

    public boolean isContactRelation() {
        return CONTACT_RELATION_COMPONENT_NAME.equals(getName());
    }

    public boolean isOldAttachComponent() {
        return isRelatedList() && ATTACH_REF_OBJECT_NAME.equals(((IRelatedObjectList) component).getRefObjectApiName());
    }

    public boolean isAttachComponent() {
        return ATTACH_COMPONENT_NAME.equals(getName());
    }

    public boolean isHeadInfoComponent() {
        return HEAD_INFO_COMPONENT_NAME.equals(getName());
    }

    public boolean isNavigation() {
        return IComponent.TYPE_NAVIGATION.equals(getType());
    }

    public boolean isTabComponent() {
        return IComponent.TYPE_TAB.equals(getType());
    }

    public boolean isGrid() {
        return IComponent.TYPE_GRID.equals(getType());
    }

    public boolean isListComponent() {
        return LIST_COMPONENT_NAME.equals(getName());
    }

    public boolean isRelatedBusinessComponent() {
        if (isMasterDetailComponent()) {
            return true;
        }
        if (isRelatedList()) {
            return !ComponentI18NKeys.isCommonComponent(getName(), LayoutTypes.DETAIL);
        }
        return false;
    }

    public boolean isFormTable() {
        return TYPE_FORM_TABLE.equals(getType());
    }

    public boolean isSummaryKeyInfo() {
        return SUMMARY_INFO.equals(getType());
    }

    public boolean isRelatedListForm() {
        return RelatedListFormComponentExt.isRelatedListFormComponent(component);
    }

    public boolean isAiInsightComponent() {
        return TYPE_AI_INSIGHT_COMPONENT.equals(getType());
    }

    public boolean containsComponent(String componentApiName) {
        if (isTabs()) {
            List<List<String>> componentsList = ((TabsComponent) component).getComponents();
            return CollectionUtils.nullToEmpty(componentsList).stream().anyMatch(x -> x.contains(componentApiName));
        }
        if (isTabComponent()) {
            return ((ITabComponent) component).getComponents().contains(componentApiName);
        }
        if (isNavigation()) {
            return ((INavigationComponent) component).getComponents().contains(componentApiName);
        }
        if (isGrid()) {
            List<List<String>> componentsList = GridComponentExt.of((IGridComponent) component).getComponents();
            return componentsList.stream().anyMatch(x -> x.contains(componentApiName));
        }
        return false;
    }

    public boolean isContainerComponent() {
        return isTabs() || isTabComponent() || isNavigation() || isGrid() || isFormTable();
    }

    public boolean isAIChatComponent() {
        return AI_CHAT_COMPONENT_NAME.equals(getType());
    }

    public List<String> getChildComponents() {
        if (isTabs()) {
            List<List<String>> componentsList = ((TabsComponent) component).getComponents();
            return CollectionUtils.nullToEmpty(componentsList).stream()
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (isTabComponent()) {
            return ((ITabComponent) component).getComponents();
        }
        if (isNavigation()) {
            return ((INavigationComponent) component).getComponents();
        }
        if (isGrid()) {
            List<List<String>> componentsList = GridComponentExt.of((IGridComponent) component).getComponents();
            return CollectionUtils.nullToEmpty(componentsList).stream()
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (isFormTable()) {
            FormTable formTable = FormTable.of(component);
            return formTable.getTextComponentApiNameList();
        }
        return Collections.emptyList();
    }

    public String getNameI18nKey() {
        return (String) get(NAME_I18N_KEY);
    }

    public void setNameI18nKey(String nameI18nKey) {
        set(NAME_I18N_KEY, nameI18nKey);
    }

    public void setHeaderByI18nKey(String tenantId, String i18nKey) {
        if (Strings.isNullOrEmpty(i18nKey)) {
            return;
        }
        component.setHeader(I18NExt.getOrDefault(i18nKey, getHeader()));
    }

    public void setButtonInfo(List<ListComponentInfo> buttonInfos) {
        List<Map> mapList = buttonInfos.stream().map(x -> x.getContainerDocument()).collect(Collectors.toList());
        set(BUTTON_INFO, mapList);
    }

    public List<ListComponentInfo> getButtonInfo() {
        List<Map> mapList = (List<Map>) get(BUTTON_INFO);
        if (CollectionUtils.empty(mapList)) {
            return Lists.newArrayList();
        }
        return mapList.stream().map(ListComponentInfo::new).collect(Collectors.toList());
    }

    public String getContent() {
        String content = get(CONTENT, String.class);
        return Strings.nullToEmpty(content);
    }

    public void setContent(String content) {
        set(CONTENT, Strings.nullToEmpty(content));
    }


    public void syncProps(List<IComponent> components) {
        if (CollectionUtils.empty(components)) {
            return;
        }
        components.stream().filter(x -> x.getName().equals(getName())).findFirst().ifPresent(x -> syncProps(x));
    }

    public void syncProps(IComponent component) {
        Map<String, Object> oldMap = ComponentExt.of(component).toMap();
        oldMap.keySet().stream()
                .filter(k -> !PROTECTED_PROPS.contains(k))
                .forEach(k -> {
                    if (!IRelatedObjectList.INCLUDE_FIELDS.equals(k) || isMasterDetailComponent()) {
                        set(k, oldMap.get(k));
                    }
                });
    }

    public void syncComponentInfo(List<IComponent> components, BiConsumer<IComponent, IComponent> sync) {
        Objects.requireNonNull(sync);
        if (CollectionUtils.empty(components)) {
            return;
        }
        components.stream()
                .filter(it -> it.getName().equals(getName()))
                .findFirst()
                .ifPresent(it -> sync.accept(this.getComponent(), it));
    }

    public boolean enableRelatedListConfig() {
        return toMap().containsKey(ListComponentExt.SCENE_INFO);
    }

    public void mergeDetailObjButtons(IObjectDescribe detailDescribe) {
        mergeDetailObjButtons(detailDescribe, false);
    }

    public void mergeDetailObjButtons(IObjectDescribe detailDescribe, boolean isMobileLayout) {
        List<ListComponentInfo> buttonInfo = getButtonInfo();
        //从对象通用按钮
        mergeDetailObjButtons(buttonInfo, ListComponentInfo.ButtonRenderType.LIST_NORMAL, detailDescribe, isMobileLayout);
        //从对象批量按钮
        mergeDetailObjButtons(buttonInfo, ListComponentInfo.ButtonRenderType.LIST_BATCH, detailDescribe);
        //从对象单条按钮
        mergeDetailObjButtons(buttonInfo, ListComponentInfo.ButtonRenderType.LIST_SINGLE, detailDescribe);
        setButtonInfo(buttonInfo);
    }

    private void mergeDetailObjButtons(List<ListComponentInfo> buttonInfo, ListComponentInfo.ButtonRenderType renderType, IObjectDescribe detailDescribe) {
        mergeDetailObjButtons(buttonInfo, renderType, detailDescribe, false);
    }

    private void mergeDetailObjButtons(List<ListComponentInfo> buttonInfo, ListComponentInfo.ButtonRenderType renderType, IObjectDescribe detailDescribe, boolean isMobileLayout) {
        List<String> defaultButtonNames;
        switch (renderType) {
            case LIST_NORMAL:
                defaultButtonNames = EditLayout.buildDefaultListNormalDetailObjButtonNames(detailDescribe, isMobileLayout);
                break;
            case LIST_BATCH:
                defaultButtonNames = EditLayout.buildDefaultListBatchDetailObjButtonNames(detailDescribe.getApiName());
                break;
            case LIST_SINGLE:
                defaultButtonNames = EditLayout.buildDefaultListSingleDetailObjButtonNames(detailDescribe.getTenantId(),
                        detailDescribe.getApiName());
                break;
            default:
                defaultButtonNames = Lists.newArrayList();
                break;
        }
        Optional<ListComponentInfo> oldListComponentInfo = buttonInfo.stream().filter(x -> renderType.getType().equals(x.getRenderType())).findFirst();
        if (oldListComponentInfo.isPresent()) {
            oldListComponentInfo.get().mergeWithButtonNames(defaultButtonNames);
        } else {
            ListComponentInfo newListComponentInfo = (ListComponentInfo) ListComponentInfo.buildComponentInfo(renderType.getType(), null);
            newListComponentInfo.setOrder(defaultButtonNames);
            buttonInfo.add(newListComponentInfo);
        }
    }

    public void setUnDeletable(boolean unDeletable) {
        set("unDeletable", unDeletable);
    }

    public void setCardLayout(Map cardLayout) {
        set("card_layout", cardLayout);
    }


    public boolean filterComponentsByWheres(IObjectData objectData, IObjectDescribe describe) {
        if (Objects.isNull(objectData)) {
            return true;
        }
        List<Wheres> wheres = handleWheres(describe);

        if (CollectionUtils.empty(wheres)) {
            return true;
        }
        return componentCan(objectData, describe, wheres);
    }

    private List<Wheres> handleWheres(IObjectDescribe describe) {
        List<Wheres> wheres = getWheres();
        //如果字段被禁用，按钮的条件不生效
        return WheresExt.handleWheresByDescribe(wheres, describe);
    }

    public List<Wheres> getWheres() {
        List<Map> list = get(WHERES, List.class);
        List<Wheres> result = Lists.newArrayList();
        if (CollectionUtils.empty(list)) {
            return result;
        }
        for (Map map : list) {
            Wheres wheres = new Wheres();
            wheres.setConnector(Where.CONN.OR.toString());
            List<Map> maps = (List<Map>) map.get(FILTERS);
            List<IFilter> filters = Lists.newArrayList();
            for (Map map2 : maps) {
                Filter filter = new Filter();
                try {
                    filter.fromJsonString(JacksonUtils.toJson(map2));
                } catch (Exception e) {
                    log.warn("getWheres fail, name:{}", getName(), e);
                }
                filters.add(filter);
            }
            wheres.setFilters(filters);
            result.add(wheres);
        }
        return result;
    }

    private void setWheres(List<Wheres> wheres) {
        if (null == wheres) {
            return;
        }
        get(WHERES, List.class).clear();
        wheres.forEach(this::addWheres);
    }

    private void addWheres(Wheres wheres) {
        Object list = component.get(WHERES);
        if ((list instanceof List)) {
            ((List) list).add(JSON.parseObject(new Gson().toJson(wheres)));
        }

    }

    private boolean componentCan(IObjectData objectData, IObjectDescribe describe, List<Wheres> wheres) {
        IFilter filter = FilterExt.of(Operator.EQ, IObjectData.ID, objectData.getId()).getFilter();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(wheres, filter);
        try {
            ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                    .describeExt(ObjectDescribeExt.of(describe))
                    .queryExt(queryExt)
                    .filterLabel(describe.getDisplayName() + "." + getName())
                    .build();
            return CollectionUtils.notEmpty(dataFilter.doFilter(Lists.newArrayList(objectData)));
        } catch (Exception e) {
            log.warn("error in parallelFilterComponents, describeApiName:{},dataId:{},componentName:{}",
                    describe.getApiName(), objectData.getId(), getName(), e);
        }
        return false;
    }

    public void convert2CustomZone(IObjectDescribe describe) {
        convertFilterZone(describe, FilterExt::convert2CustomZone);
    }

    public void convert2SystemZone(IObjectDescribe describe) {
        convertFilterZone(describe, FilterExt::convert2SystemZone);
    }

    private void convertFilterZone(IObjectDescribe describe, BiConsumer<ObjectDescribeExt, IFilter> biConsumer) {
        List<Wheres> wheres = getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        CollectionUtils.nullToEmpty(wheres).stream()
                .map(Wheres::getFilters)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .forEach(filter -> biConsumer.accept(describeExt, filter));
        setWheres(wheres);
    }

    public String getRenderType() {
        return (String) get("render_type");
    }

    public void setRenderType(String renderType) {
        set("render_type", renderType);
    }


    public boolean isSummaryInfoComponent() {
        return SUMMARY_INFO.equals(getType());
    }

    public boolean isTextComponent() {
        return ComponentExt.TYPE_TEXT_COMPONENT.equals(getType());
    }

    public boolean isNameComponent() {
        return ComponentExt.NAME_COMPONENT.equals(getType());
    }

    public List<IButton> filterAndSortButtons(List<IButton> buttons, ListComponentInfo.ButtonRenderType renderType) {
        List<ListComponentInfo> buttonInfo = getButtonInfo();
        if (CollectionUtils.notEmpty(buttonInfo)) {
            Optional<ListComponentInfo> listComponentInfo = buttonInfo.stream().filter(x -> renderType.getType().equals(x.getRenderType())).findFirst();
            if (listComponentInfo.isPresent()) {
                buttons = listComponentInfo.get().filterAndSortButtons(buttons);
            }
        }
        return buttons;
    }

    public boolean isLayoutSourceFromObject() {
        return "object".equals(get("layout_source"));
    }

    public boolean isLayoutSourceFromFlow() {
        String layoutSource = (String) get("layout_source");
        return Strings.isNullOrEmpty(layoutSource) || "flow".equals(layoutSource);
    }

    public List<String> getNameI18nKeyList() {
        return CollectionUtils.nullToEmpty(get(NAME_I18N_KEY_LIST, List.class));
    }

    /**
     * null跟随Web端，需要返回true
     *
     * @return
     */
    public Boolean getDisplayTotalsFromMobile() {
        return BooleanUtils.isNotFalse(get(ListComponentExt.K_M_DISPLAY_TOTALS, Boolean.class));
    }

    public String getPluginApiName() {
        return (String) get("plugin_api_name");
    }

    public void setOriginalName(String originalDescribeApiName) {
        if (!Strings.isNullOrEmpty(originalDescribeApiName)) {
            set("original_name", getDetailComponentName(originalDescribeApiName));
        }
    }

    public String getOriginalName() {
        return get("original_name", String.class);
    }

    public String getIdOrName() {
        String id = get(IObjectData.ID, String.class);
        if (!Strings.isNullOrEmpty(id)) {
            return id;
        }
        return getName();
    }

    public void setI18nInfo(List<I18nInfo> newI18nInfoList, boolean isFromManage) {
        if (CollectionUtils.empty(newI18nInfoList)) {
            return;
        }
        if (!isFromManage) {
            Map<String, I18nInfo> i18nInfoMap = Maps.newHashMap();
            newI18nInfoList.forEach(x -> i18nInfoMap.put(x.getApiName(), x));
            set(I18N_INFO_MAP, i18nInfoMap);
        } else {
            set(I18N_INFO_LIST, newI18nInfoList);
        }
    }


    public String getHeader() {
        String newHeader = get(NEW_HEADER, String.class);
        if (isGrid()) {
            if (StringUtils.isNotEmpty(newHeader)) {
                return newHeader;
            }
        }
        return component.getHeader();
    }

    public void setComponentHeaderAndCleanUp(String newHeader) {
        if (isGrid()) {
            if (StringUtils.isNotEmpty(get(NEW_HEADER, String.class))) {
                removeNewHeader();
            }
        }
        component.setHeader(newHeader);
    }

    public String getVersion() {
        return (String) get("version");
    }

    public static String getRealKeyWithComponent(IComponent component, String layoutType, String objectApiName,
                                                 String layoutApiName, String refLayoutApiName, List<String> innerTextComponentApiNames) {
        ComponentExt componentExt = ComponentExt.of(component);
        //没有开启引用布局，直接返回
        if (StringUtils.isEmpty(refLayoutApiName)) {
            return getComponentNameKey(objectApiName, layoutApiName, component.getName(), layoutType);
        }
        //不是表格组件和文本框组件直接返回
        if (!componentExt.isTextComponent() && !componentExt.isFormTable()) {
            return getComponentNameKey(objectApiName, layoutApiName, component.getName(), layoutType);
        }
        //如果不是表格里的文本框组件则正常拼key返回
        if (componentExt.isTextComponent() && CollectionUtils.empty(innerTextComponentApiNames)) {
            return getComponentNameKey(objectApiName, layoutApiName, component.getName(), layoutType);
        }
        //是表格组件或者表格里的文本框组件，则拼引用的布局apiName和edit类型 返回
        return getComponentNameKey(objectApiName, refLayoutApiName, component.getName(), LayoutTypes.EDIT);
    }
}