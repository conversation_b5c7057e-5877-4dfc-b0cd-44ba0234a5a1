package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.button.ButtonType;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.metadata.ButtonExt.REDIRECT_UIPAAS;
import static com.facishare.paas.appframework.metadata.layout.component.ListComponentExt.IS_EXPOSED_BUTTON;

/**
 * create by zhaoju on 2020/02/25
 */
@Slf4j
public class LayoutButtonExt implements IButton {
    private static final long serialVersionUID = 8256725248880876885L;

    public static final String TERMINAL = "terminal";
    public static final String PAGE_TYPE = "page_type";

    public static final String TERMINAL_TYPE_WEB = LayoutAgentType.WEB.getCode();
    public static final String TERMINAL_TYPE_MOBILE = LayoutAgentType.MOBILE.getCode();

    public static final String PAGE_TYPE_LIST = IComponentInfo.PAGE_TYPE_LIST;
    public static final String PAGE_TYPE_SELECTED = IComponentInfo.PAGE_TYPE_SELECTED;


    public static final String BUTTON_CATEGORY = "button_category";
    public static final String BUTTON_CATEGORY_CHANGE_ORDER = "change_order";


    @Getter
    @Delegate
    @NonNull
    private IButton button;

    private LayoutButtonExt(IButton button) {
        this.button = button;
    }

    public static LayoutButtonExt of(IButton button) {
        return new LayoutButtonExt(button);
    }

    public static LayoutButtonExt of(Map<String, Object> data) {
        return new LayoutButtonExt(new Button(data));
    }

    @Deprecated
    public static List<IButton> handleTerminal(List<IButton> buttons) {
        buttons.forEach(button -> {
            List<String> terminals = Lists.newArrayList();
            if (ButtonConfig.isListLayoutWebOnlyButtons(button.getName())) {
                terminals.add(TERMINAL_TYPE_WEB);
            }
            if (ButtonConfig.isListLayoutMobileOnlyButtons(button.getName())) {
                terminals.add(TERMINAL_TYPE_MOBILE);
            }
            if (CollectionUtils.notEmpty(terminals)) {
                LayoutButtonExt.of(button).setTerminal(terminals);
            }
        });
        return buttons;
    }

    public static List<IButton> handleTerminal(List<IButton> buttons, String tenantId) {
        buttons.forEach(button -> {
            String apiName = button.getName();
            List<String> supportTerminals = Lists.newArrayList();
            if (ButtonConfig.isListLayoutWebOnlyButtons(apiName)
                    || ButtonConfig.listLayoutButtonDisplayGray(apiName, TERMINAL_TYPE_WEB, tenantId)) {
                supportTerminals.add(TERMINAL_TYPE_WEB);
            }
            if (ButtonConfig.listLayoutButtonDisplayGray(apiName, TERMINAL_TYPE_MOBILE, tenantId)) {
                supportTerminals.add(TERMINAL_TYPE_MOBILE);
            }

            LayoutButtonExt.of(button).setTerminal(supportTerminals);
        });
        return buttons;
    }

    public boolean isActive() {
        return BooleanUtils.isNotFalse(get("isActive", Boolean.class));
    }

    public void setIsActive(Boolean isActive) {
        set("isActive", isActive);
    }

    public boolean isCustomButton() {
        return !isSystemButton();
    }

    public boolean isSystemButton() {
        return ACTION_TYPE_DEFAULT.equals(getActionType()) || "system".equals(getActionType());
    }

    public void setParamForm(List<Map> paramForm) {
        set(IUdefButton.PARAM_FORM, paramForm);
    }

    public List<Map> getParamForm() {
        return (List) this.get(IUdefButton.PARAM_FORM, ArrayList.class);
    }

    public boolean isUIPaaS() {
        return REDIRECT_UIPAAS.equals(button.get(IUdefButton.REDIRECT_TYPE, String.class));
    }

    public boolean isAIAgent() {
        return ButtonExt.REDIRECT_AI_AGENT.equals(button.get(IUdefButton.REDIRECT_TYPE, String.class));
    }

    public boolean isUIAction() {
        return ButtonType.REDIRECT.getId().equals(button.get(IUdefButton.BUTTON_TYPE, String.class));
    }

    public Map toMap() {
        return ((Button) button).getContainerDocument();
    }

    public void setIsExposedButton(boolean isExposed) {
        button.set(IS_EXPOSED_BUTTON, isExposed);
    }

    public void setTerminal(List<String> terminals) {
        set(TERMINAL, StringUtils.join(terminals, "|"));
    }

    public List<String> getTerminal() {
        String terminal = (String) get(TERMINAL);
        if (StringUtils.isEmpty(terminal)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(StringUtils.split(terminal, "|"));
    }

    public void setRenderPageType(List<String> pageType) {
        set(LayoutButtonExt.PAGE_TYPE, StringUtils.join(pageType, "|"));
    }

    public List<String> getRenderPageType() {
        String terminal = (String) get(LayoutButtonExt.PAGE_TYPE_LIST);
        if (StringUtils.isEmpty(terminal)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(StringUtils.split(terminal, "|"));
    }

    public boolean isMobileButton() {
        List<String> terminal = getTerminal();
        return CollectionUtils.empty(terminal) || terminal.contains(LayoutAgentType.MOBILE.getCode());
    }

    public void setRenderType(String renderType) {
        set("render_type", renderType);
    }

    public void setLookupFieldName(String lookupFieldName) {
        set("lookup_field_name", lookupFieldName);
    }

    public void setLookupObjectName(String lookupObjectName) {
        set("lookup_object_name", lookupObjectName);
    }

    public void setIsChangeOutOwner() {
        set("is_change_out_owner", true);
    }

    /**
     * 设置按钮是否在变更单对象上
     */
    public void setButtonCategory(String buttonCategory) {
        set(BUTTON_CATEGORY, buttonCategory);
    }
}
