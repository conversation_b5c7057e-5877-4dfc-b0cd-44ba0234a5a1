package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.component.*;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.describe.DateTimeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.DateTimeRangeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.WhatFieldDescribe;
import com.facishare.paas.metadata.impl.describe.WhatListFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.*;
import com.facishare.paas.metadata.impl.ui.layout.component.*;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static com.facishare.paas.appframework.core.i18n.I18NKey.LAYOUT_BASIC_INFO;
import static com.facishare.paas.appframework.metadata.ComponentExt.NAME_COMPONENT;
import static com.facishare.paas.appframework.metadata.UIEventTrigger.ADD_LINE;
import static com.facishare.paas.appframework.metadata.UIEventTrigger.DELETE_LINE;
import static com.facishare.paas.appframework.metadata.layout.component.ListComponentExt.STATISTICS_RENDER;

/**
 * Created by zhouwr on 2017/10/11
 */
@Slf4j
public class LayoutExt implements ILayout {
    public final static String BUSI_CODE = "1009";
    public final static String PAYMENT_DESCRIBE_API_NAME = "payment_record";
    public final static String Add_LAYOUT_TYPE = LayoutTypes.ADD;
    public final static String Edit_LAYOUT_TYPE = LayoutTypes.EDIT;
    public final static String IS_DEAL_UI = "is_deal_ui";
    public final static String MOBILE_LAYOUT = "mobile_layout";

    public final static String MOBILE_LIST_LAYOUT = LayoutTypes.LIST;

    public final static List<String> HAS_COUNT_LIMIT_LAYOUT_TYPES =
            Collections.unmodifiableList(Lists.newArrayList(ILayout.DETAIL_LAYOUT_TYPE, ILayout.LIST_LAYOUT_TYPE, ListLayoutExt.LIST_LAYOUT, LayoutTypes.EDIT));
    public final static List<String> SIMPLE_TYPE_FILTER_COMPONENTS = Lists.newArrayList(ComponentExt.HEAD_INFO_COMPONENT_NAME, ComponentExt.SUMMARY_CARD_COMPONENT_NAME);

    private static final String LIST_SINGLE_EXPOSED = "list_single_exposed";
    public static final String SCENE_RENDER_TYPE = "SCENE_RENDER_TYPE";
    public static final String NAMESPACE_FLOW = "flow";

    public static final String BUTTON_STYLE = "button_style";

    // i18n中属于layout一级而非components的api
    public static List<String> layoutI18nApis = Lists.newArrayList(ILayout.DISPLAY_NAME);

    @Getter
    @Delegate
    private ILayout layout;

    private LayoutExt(ILayout layout) {
        if (layout instanceof LayoutExt) {
            this.layout = ((LayoutExt) layout).getLayout();
        } else {
            this.layout = layout;
        }
    }

    public static LayoutExt of(Map map) {
        return of(new Layout(map));
    }

    public static LayoutExt of(ILayout layout) {
        return new LayoutExt(layout);
    }

    /**
     * 处理卡片视图中的字段
     *
     * @param components
     * @param unauthorizedFields
     * @param describe
     * @param user
     */
    public static void resetHeadInfoComponent(List<IComponent> components, Set<String> unauthorizedFields, IObjectDescribe describe, User user) {
        components.stream().filter(x -> ComponentExt.of(x).isHeadInfoComponent()).findFirst().ifPresent(headInfo -> {
            Map cardLayout = headInfo.get("card_layout", Map.class);
            if (CollectionUtils.empty(cardLayout)) {
                return;
            }
            NewTableComponentExt newTableComponentExt = new NewTableComponentExt(cardLayout);
            newTableComponentExt.formatNewLayoutByDescribe(describe, unauthorizedFields, user, false);
        });
    }

    public Map<String, Object> toMap() {
        return this.convert().getContainerDocument();
    }

    private Layout convert() {
        return (Layout) layout;
    }

    public ILayout copy() {
        return LayoutExt.of(CopyOnWriteMap.copy(toMap())).getLayout();
    }

    public boolean isFlowLayout() {
        return NAMESPACE_FLOW.equals(getNamespace());
    }

    public boolean isWhatListLayout() {
        return LayoutTypes.WHAT_LIST.equals(getLayoutType());
    }

    public void setUsedInfo(String usedInfo) {
        set("used_info", usedInfo);
    }

    public String getDefaultEditLayoutName() {
        return Edit_LAYOUT_TYPE + "_" + getName();
    }

    public List<DocumentBaseEntity> getFieldShowList(List<Map<String, Object>> fieldListConfig) {
        return getFieldShowList(fieldListConfig, true);
    }

    public List<DocumentBaseEntity> getFieldShowList(List<Map<String, Object>> fieldListConfig, boolean defaultShow) {
        List<LayoutRuleExt.FieldConfig> validFieldListConfig = getFieldConfigList(CollectionUtils.nullToEmpty(fieldListConfig), defaultShow);
        return validFieldListConfig.stream().map(this::fieldConfigToMap).collect(Collectors.toList());
    }

    public void fixUIEvent() {
        if (Objects.isNull(this.layout)) {
            return;
        }
        if ((this.isNewLayout() || this.isEditLayout() || this.isDetailLayout())
                && this.containUIEvnt()) {
            List<Map<String, Object>> uiEvents = this.getEvents();
            uiEvents.forEach(event -> {
                if (Objects.isNull(event)) {
                    return;
                }
                UIEventExt uiEventExt = UIEventExt.of(event);
                // 从对象事件 && 触发条件: [新增 or 删除]
                if (uiEventExt.isDetailObjectEvent() &&
                        uiEventExt.containTrigger(uiEventExt.getTriggers(), Arrays.asList(ADD_LINE, DELETE_LINE))) {
                    if (CollectionUtils.notEmpty(uiEventExt.getTriggerFieldApiNames())) {
                        log.info("Dirty UI data was discovered, tenantId: {}, layoutId: {}, layout: {}",
                                layout.getTenantId(), layout.getId(), layout.toJsonString());
                        uiEventExt.setTriggerFieldApiNames(Collections.emptyList());
                    }
                }
            });
        }
    }

    public List<LayoutRuleExt.FieldConfig> getFieldConfigList(List<Map<String, Object>> fieldListConfig, boolean defaultShow) {
        List<String> fieldList = getFieldList();
        if (CollectionUtils.empty(fieldList)) {
            return Lists.newArrayList();
        }

        List<LayoutRuleExt.FieldConfig> validFieldListConfig = fieldListConfig.stream()
                .map(this::mapToFieldConfig)
                .filter(x -> fieldList.contains(x.getTuple().getKey()))
                .collect(Collectors.toList());
        Set<String> fieldConfigSet = validFieldListConfig.stream().map(x -> x.getTuple().getKey()).collect(Collectors.toSet());

        fieldList.stream().filter(x -> !fieldConfigSet.contains(x) && !ObjectDescribeExt.isSystemField(x))
                .forEach(x -> validFieldListConfig.add(LayoutRuleExt.FieldConfig.of(Tuple.of(x, defaultShow), null)));
        fieldList.stream().filter(x -> !fieldConfigSet.contains(x) && ObjectDescribeExt.isSystemField(x))
                .forEach(x -> validFieldListConfig.add(LayoutRuleExt.FieldConfig.of(Tuple.of(x, defaultShow), null)));

        if (DefObjConstants.flowInvisibleFieldNameMap.containsKey(getRefObjectApiName())) {
            // 删掉需要被过滤掉的表头字段
            return validFieldListConfig.stream()
                    .filter(x -> !DefObjConstants.flowInvisibleFieldNameMap.get(getRefObjectApiName())
                            .contains(x.getTuple().getKey()))
                    .collect(Collectors.toList());
        }
        return validFieldListConfig;
    }

    private DocumentBaseEntity fieldConfigToMap(LayoutRuleExt.FieldConfig field) {
        return field.fieldConfigToMap();
    }

    public LayoutRuleExt.FieldConfig mapToFieldConfig(Map<String, Object> map) {
        return LayoutRuleExt.FieldConfig.fromMap(map);
    }

    public List<String> getFieldList() {
        List<String> fieldList = Lists.newArrayList();
        getFormComponents().forEach(x -> fieldList.addAll(x.getFieldList()));
        getFormTables().forEach(x -> fieldList.addAll(x.getFieldNameList()));
        return fieldList;
    }

    /**
     * 获取布局中有序的字段
     *
     * @param needFormComponent 是否包含表单组件中的字段
     * @return
     */
    public List<IFormField> getOrderLayoutFieldList(boolean needFormComponent) {
        List<IFormField> formFieldList = Lists.newArrayList();
        LayoutComponents.restoreComponentOrder(this);
        List<IComponent> orderComponents = ComponentOrder.order(getComponentsSilently());
        orderComponents.forEach(x -> {
            if (ComponentExt.of(x).isFormTable()) {
                formFieldList.addAll(FormTable.of(x).getFields(false));
            }
            if (needFormComponent && ComponentExt.of(x).isFormType()) {
                formFieldList.addAll(FormComponentExt.of((IFormComponent) x).getFormFields());
            }
        });
        return formFieldList;
    }


    /**
     * 将每个表格中的字段放入字段分组中
     *
     * @return 整个布局的所有字段分组
     */
    public List<IFieldSection> getOrderLayoutFieldList() {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        LayoutComponents.restoreComponentOrder(this);
        List<IComponent> orderComponents = ComponentOrder.order(getComponentsSilently());
        orderComponents.forEach(x -> {
            if (ComponentExt.of(x).isFormTable()) {
                fieldSections.add(buildFieldSection(FormTable.of(x).getFields()));
            }
            if (ComponentExt.of(x).isFormType()) {
                fieldSections.addAll(FormComponentExt.of((IFormComponent) x).getFieldSections());
            }
        });
        return fieldSections;
    }

    private IFieldSection buildFieldSection(List<IFormField> formFields) {
        IFieldSection fieldSection = new FieldSection();
        fieldSection.setFields(formFields);
        fieldSection.setName("form_table_" + RandomStringUtils.randomAlphanumeric(3));
        fieldSection.setShowHeader(false);
        return fieldSection;
    }

    public List<String> getOrderFieldList() {
        List<String> fieldList = Lists.newArrayList();
        LayoutComponents.restoreComponentOrder(this);
        List<IComponent> orderComponents = ComponentOrder.order(getComponentsSilently());
        orderComponents.forEach(x -> {
            if (ComponentExt.of(x).isFormType()) {
                fieldList.addAll(FormComponentExt.of((IFormComponent) x).getFieldList());
            }
            if (ComponentExt.of(x).isFormTable()) {
                fieldList.addAll(FormTable.of(x).getFieldNameList());
            }
        });
        return fieldList;
    }

    public void clearDuplicatedFormField() {
        Set<String> existedFields = Sets.newHashSet();
        //处理表单组件字段重复
        List<FormComponentExt> formComponents = getFormComponents();
        for (FormComponentExt formComponentExt : formComponents) {
            formComponentExt.removeDuplicatedFormFieldByFormComponent(existedFields);
        }
        //处理表格组件字段重复
        List<FormTable> formTables = getFormTables();
        for (FormTable formTable : formTables) {
            formTable.removeDuplicatedFormFieldByFormTableComponent(existedFields);
        }
    }

    public void fillFormComponent() {
        if (!isDetailLayout() && !isEditLayout()) {
            return;
        }
        if (isDetailLayout()) {
            getFormComponent().ifPresent(x -> {
                if (!ComponentExt.FORM_COMPONENT.equals(x.getName())) {
                    x.setName(ComponentExt.FORM_COMPONENT);
                }
                if (Objects.isNull(x.get("_id"))) {
                    x.set("_id", ComponentExt.FORM_COMPONENT);
                }
                if (Strings.isNullOrEmpty(x.getHeader())) {
                    x.setHeader(I18N.text(I18NKey.DETAIL_INFO));
                }
                //form_component不支持隐藏
                if (x.isHidden()) {
                    x.setIsHidden(false);
                }
            });
            if (isV2Layout()) {
                List<Map> leftStructure = LayoutStructure.getLeftStructure(this);
                resetDetailInfoName(leftStructure);
                List<Map> rightStructure = LayoutStructure.getRightStructure(this);
                resetDetailInfoName(rightStructure);
            }
        }
    }

    public void removeDuplicateComponents() {
        if (!isDetailLayout()) {
            return;
        }
        if (!Utils.CASES_API_NAME.equals(getRefObjectApiName())) {
            return;
        }
        List<IComponent> components = getComponentsSilently();
        List<String> componentNames = components.stream().map(x -> x.getName()).collect(Collectors.toList());

        //service_log和sale_log同时存在的话只保留service_log，并将service_log重命名为sale_log
        if (componentNames.contains(ComponentExt.SERVICE_LOG_COMPONENT_NAME)) {
            components.removeIf(x -> ComponentExt.SALE_LOG_COMPONENT_NAME.equals(x.getName()));
            components.stream().filter(x -> ComponentExt.SERVICE_LOG_COMPONENT_NAME.equals(x.getName())).forEach(x ->
                    x.setName(ComponentExt.SALE_LOG_COMPONENT_NAME));
            setComponents(components);
            return;
        }

        //没有service_log，则去掉多余的sale_log
        List<IComponent> saleLogs = components.stream().filter(x -> ComponentExt.SALE_LOG_COMPONENT_NAME.equals(x.getName()))
                .collect(Collectors.toList());
        List<IComponent> showSaleLogs = saleLogs.stream().filter(x -> !x.isHidden()).collect(Collectors.toList());
        List<IComponent> hiddenSaleLogs = saleLogs.stream().filter(x -> x.isHidden()).collect(Collectors.toList());
        if (saleLogs.size() > 1) {
            if (CollectionUtils.notEmpty(showSaleLogs)) {
                showSaleLogs.remove(0);
                components.removeAll(showSaleLogs);
                components.removeAll(hiddenSaleLogs);
            } else {
                hiddenSaleLogs.remove(0);
                components.removeAll(hiddenSaleLogs);
            }
            setComponents(components);
        }
    }

    private void resetDetailInfoName(List<Map> components) {
        if (CollectionUtils.empty(components)) {
            return;
        }
        components.forEach(x -> {
            if (ComponentExt.DETAIL_INFO.equals(x.get(IComponent.NAME))) {
                x.put(IComponent.NAME, ComponentExt.FORM_COMPONENT);
            }
            resetDetailInfoName((List<Map>) x.get("children"));
        });
    }

    public void fillDefaultComponent() {
        if (!isDetailLayout()) {
            return;
        }
        if (Strings.isNullOrEmpty(getDefaultComponent())) {
            setDefaultComponent(ComponentExt.DETAIL_INFO_COMPONENT_NAME);
        }
    }

    public List<FormTable> getFormTables() {
        return getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isFormTable())
                .map(x -> FormTable.of(x))
                .collect(Collectors.toList());
    }

    public List<FormComponentExt> getFormComponents() {
        return getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isFormType())
                .map(x -> FormComponentExt.of((IFormComponent) x))
                .collect(Collectors.toList());
    }

    public Optional<FormComponentExt> getFormComponent() {
        List<IComponent> components = getComponentsSilently();
        Optional<FormComponentExt> opt = components.stream()
                .filter(component -> ComponentExt.FORM_COMPONENT.equals(component.getName()))
                .map(component -> FormComponentExt.of((IFormComponent) component))
                .findFirst();
        if (opt.isPresent()) {
            return opt;
        }
        return components.stream()
                .filter(component -> ComponentExt.of(component).isFormType())
                .map(component -> FormComponentExt.of((IFormComponent) component))
                .findFirst();
    }

    public Optional<GroupComponent> getRelatedComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> "relatedObject".equals(x.getName()))
                .map(x -> (GroupComponent) x)
                .findFirst();
    }

    public Optional<GroupComponent> getDetailInfoComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> "detailInfo".equals(x.getName()))
                .map(x -> (GroupComponent) x)
                .findFirst();
    }

    public Optional<SimpleComponent> getTopInfoComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> ILayout.TOP_INFO.equals(x.getName()))
                .map(x -> (SimpleComponent) x)
                .findFirst();
    }

    public Optional<GroupComponent> getOtherInfoComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> "otherInfo".equals(x.getName()))
                .map(x -> (GroupComponent) x)
                .findFirst();
    }

    public Optional<TableComponent> getTableComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> x instanceof TableComponent)
                .map(x -> (TableComponent) x)
                .findFirst();
    }

    public Optional<WhatComponent> getWhatComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> x instanceof WhatComponent)
                .map(x -> (WhatComponent) x)
                .findFirst();
    }

    public List<ISuspendedComponentInfo> getSuspendedComponent() {
        List<Map> suspendedComponentList = (List<Map>) get("suspendedComponent", List.class);
        if (!CollectionUtils.notEmpty(suspendedComponentList)) {
            return Lists.newArrayList();
        }
        return suspendedComponentList.stream()
                .map(SuspendedComponentInfo::new).collect(Collectors.toList());
    }

    public void setSuspendedComponent(List<ISuspendedComponentInfo> suspendedComponentInfos) {
        if (CollectionUtils.empty(suspendedComponentInfos)) {
            return;
        }
        List<Map> list = suspendedComponentInfos.stream()
                .map(x -> ((SuspendedComponentInfo) x).getContainerDocument()).collect(Collectors.toList());
        this.set("suspendedComponent", list);
    }

    public Optional<SimpleComponent> getTopInfoComponentSilently() {
        try {
            IComponent topInfo = getTopInfo();
            return Optional.ofNullable((SimpleComponent) topInfo);
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_TOP_INFO_ERROR, e);
        }
    }

    public List<IComponent> getMasterDetailComponents() {
        return getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isMasterDetailComponent())
                .collect(Collectors.toList());
    }

    public List<TopInfoComponent> getTopInfoComponents() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> StringUtils.equals(x.getType(), ComponentExt.TOP_INFO_COMPONENT_NAME))
                .map(x -> (TopInfoComponent) x)
                .collect(Collectors.toList());
    }

    public List<String> getMasterDetailComponentNames() {
        return getMasterDetailComponents().stream()
                .map(x -> x.getName())
                .collect(Collectors.toList());
    }

    public List<RelatedObjectList> getRelatedListComponents() {
        return getComponentsSilently().stream()
                .filter(x -> x instanceof RelatedObjectList)
                .map(x -> (RelatedObjectList) x)
                .collect(Collectors.toList());
    }

    public List<RelatedListFormComponentExt> getRelatedListFormComponents() {
        return getComponentsSilently().stream()
                .filter(RelatedListFormComponentExt::isRelatedListFormComponent)
                .map(RelatedListFormComponentExt::of)
                .collect(Collectors.toList());
    }

    public List<String> getRelatedListComponentNames() {
        return getRelatedListComponents().stream()
                .map(x -> x.getName())
                .collect(Collectors.toList());
    }

    public Optional<IComponent> getComponentByApiName(String apiName) {
        return getComponentsSilently().stream()
                .filter(x -> apiName.equals(x.getName()))
                .findFirst();
    }

    public List<IComponent> getComponentByApiNames(List<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return Lists.newArrayList();
        }
        return getComponentsSilently().stream()
                .filter(x -> apiNames.contains(x.getName()))
                .collect(Collectors.toList());
    }

    public List<IComponent> getComponentByTypes(List<String> types) {
        if (CollectionUtils.empty(types)) {
            return Lists.newArrayList();
        }
        return getComponentsSilently().stream()
                .filter(it -> types.contains(it.getType()))
                .collect(Collectors.toList());
    }

    public int getComponentOrder(String apiName) {
        List<IComponent> components = getComponentsSilently();
        for (int i = 0; i < components.size(); i++) {
            IComponent component = components.get(i);
            if (apiName.equals(component.getName())) {
                if (component.isHidden()) {
                    return ComponentExt.DEFAULT_ORDER + i;
                }
                return component.getOrder();
            }
        }
        return ComponentExt.DEFAULT_ORDER + components.size();
    }

    public int getDetailComponentOrder(String objectApiName) {
        return getComponentOrder(ComponentExt.getDetailComponentName(objectApiName));
    }

    public boolean containsComponent(String apiName) {
        return getComponentByApiName(apiName).isPresent();
    }

    public boolean isComponentHidden(String apiName) {
        //流程布局只要不在布局里就是隐藏
        if (isFlowLayout() && !containsComponent(apiName)) {
            return true;
        }
        if (CollectionUtils.nullToEmpty(getHiddenComponents()).contains(apiName)) {
            return true;
        }
        if (!isNewLayout()) {
            Optional<IComponent> component = getComponentByApiName(apiName);
            return component.isPresent() && component.get().isHidden();
        }
        return false;
    }

    public List<IComponent> getComponentsSilently() {
        try {
            List<IComponent> components = getComponents();
            if (Objects.isNull(components)) {
                return Lists.newArrayList();
            }
            return components;
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_COMPONENTS_ERROR, e);
        }
    }

    public void removeOthersExceptFormComponent() {
        List<IComponent> components = getComponentsSilently();
        components.removeIf(x -> !IComponent.TYPE_FORM.equals(x.getType()));
        setComponents(components);
    }

    public void retainComponentsWithField() {
        List<IComponent> components = getComponentsSilently();
        components.removeIf(x -> !IComponent.TYPE_FORM.equals(x.getType())
                && !ComponentExt.TYPE_FORM_TABLE.equals(x.getType())
                && !ComponentExt.TYPE_TEXT_COMPONENT.equals(x.getType()));
        setComponents(components);
    }

    public List<IComponent> removeRefComponentsWithEditLayout() {
        List<IComponent> components = getComponentsSilently();
        List<String> textComponentNamesInTable = getTextComponentNamesInTable();
        List<IComponent> needRemoveComponents = components.stream().filter(x -> IComponent.TYPE_FORM.equals(x.getType())
                || ComponentExt.TYPE_FORM_TABLE.equals(x.getType())
                || (ComponentExt.TYPE_TEXT_COMPONENT.equals(x.getType()) && textComponentNamesInTable.contains(x.getName())))
                .collect(Collectors.toList());
        List<String> needRemoveComponentNames = needRemoveComponents.stream()
                .map(IComponent::getName)
                .collect(Collectors.toList());
        components.removeIf(x -> needRemoveComponentNames.contains(x.getName()));
        setComponents(components);
        return needRemoveComponents;
    }

    /**
     * 获取表格中的文本框组件apiName
     *
     * @return
     */
    public List<String> getTextComponentNamesInTable() {
        List<String> componentNamesInTable = Lists.newArrayList();
        getFormTables().forEach(formTable -> {
            List<String> textComponentNames = formTable.getComponentApiNameByType(ComponentExt.TYPE_TEXT_COMPONENT);
            componentNamesInTable.addAll(textComponentNames);
        });
        return componentNamesInTable;
    }

    public static List<String> getTextComponentNamesInTable(List<IComponent> components) {
        List<String> componentNamesInTable = Lists.newArrayList();
        components.stream()
                .filter(x -> ComponentExt.of(x).isFormTable())
                .map(FormTable::of)
                .forEach(formTable -> {
                    List<String> textComponentNames = formTable.getComponentApiNameByType(ComponentExt.TYPE_TEXT_COMPONENT);
                    componentNamesInTable.addAll(textComponentNames);
                });
        return componentNamesInTable;
    }

    public void removeLayoutStructure() {
        toMap().remove(LAYOUT_STRUCTURE);
    }

    public <T extends IComponent> void removeHiddenComponents(List<T> components) {
        components.removeIf(x -> isComponentHidden(x.getName()));
    }

    public void removeComponents(List<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        List<IComponent> components = getComponentsSilently();
        components.removeIf(x -> apiNames.contains(x.getName()));
        setComponents(components);
    }

    public <T extends IComponent> void setComponentOrder(List<T> components) {
        setComponentOrder(components, Lists.newArrayList());
    }

    public <T extends IComponent> void setComponentOrder(List<T> components, List<IComponent> componentConfig) {
        if (CollectionUtils.empty(components)) {
            return;
        }

        componentConfig = CollectionUtils.nullToEmpty(componentConfig);
        resetComponentOrder(componentConfig);
        Map<String, IComponent> componentConfigOrder = componentConfig.stream().collect(Collectors.toMap(x -> x.getName(), x -> x, (x, y) -> x));

        components.forEach(x -> {
            Optional<IComponent> component = getComponentByApiName(x.getName());
            if (component.isPresent()) {
                x.setOrder(component.get().getOrder());
                x.setIsHidden(component.get().isHidden());
            } else {
                ComponentExt.of(x).setDefaultValue();
                x.setIsHidden(isComponentHidden(x.getName()));
            }
            if (x.getOrder() == ComponentExt.DEFAULT_ORDER && ("sale_log".equals(x.getName()) || "service_log".equals(x.getName()))) {
                x.setOrder(0);
            }
            if (componentConfigOrder.containsKey(x.getName())) {
                x.setOrder(componentConfigOrder.get(x.getName()).getOrder());
                ComponentExt.of(x).setUnpopular(ComponentExt.of(componentConfigOrder.get(x.getName())).isUnpopular());
            } else {
                x.setOrder(x.getOrder() + componentConfigOrder.size() + 1);
                //用户设置过导航顺序的对象，新增的相关对象或从对象放到隐藏组件的最后
                if (CollectionUtils.notEmpty(componentConfigOrder)
                        && !ComponentExt.of(x).isUnpopular()
                        && (ComponentExt.of(x).isRelatedList() || ComponentExt.of(x).isMasterDetailComponent())) {
                    ComponentExt.of(x).setUnpopular(true);
                }
            }
        });

        List<T> orderedComponents = ComponentOrder.order(components);
        resetComponentOrder(orderedComponents);
    }

    private <T extends IComponent> void resetComponentOrder(List<T> components) {
        for (int i = 0; i < components.size(); i++) {
            components.get(i).setOrder(i + 1);
        }
    }

    public static LayoutExt buildPaymentListLayout() {
        ILayout layout = new Layout();
        layout.setAgentType("agent_type_mobile");
        layout.set("is_show_fieldname", true);
        ITableComponent tableComponent = new TableComponent();
        tableComponent.setRefObjectApiName(PAYMENT_DESCRIBE_API_NAME);
        List<ITableColumn> tableColumnList = buildPaymentTableColumnList(true);
        tableComponent.setIncludeFields(tableColumnList);
        layout.setComponents(Lists.newArrayList(tableComponent));

        return LayoutExt.of(layout);
    }

    public static List<ITableColumn> buildPaymentTableColumnList(boolean isAll) {
        TableColumn orderNoField = new TableColumn();
        orderNoField.setRenderType(IFieldType.TEXT);
        orderNoField.setName("name");
        orderNoField.setLabelName(I18N.text(I18NKey.RECEIPT_ORDER_NO));
        orderNoField.set("api_name", "name");

        TableColumn amountField = new TableColumn();
        amountField.setRenderType(IFieldType.TEXT);
        amountField.setName("amount");
        amountField.setLabelName(I18N.text(I18NKey.RECEIPT_AMOUNT));
        amountField.set("api_name", "amount");

        List<ITableColumn> list = Lists.newArrayList(orderNoField, amountField);
        if (isAll) {
            TableColumn feeField = new TableColumn();
            feeField.setRenderType(IFieldType.TEXT);
            feeField.setName("fee");
            feeField.setLabelName(I18N.text(I18NKey.SERVICE_FEE));
            feeField.set("api_name", "fee");
            list.add(feeField);
        }

        TableColumn payStatusField = new TableColumn();
        payStatusField.setRenderType(IFieldType.TEXT);
        payStatusField.setName("payStatus");
        payStatusField.setLabelName(I18N.text(I18NKey.STATUS));
        payStatusField.set("api_name", "payStatus");
        list.add(payStatusField);

        if (isAll) {
            TableColumn payTypeField = new TableColumn();
            payTypeField.setRenderType(IFieldType.TEXT);
            payTypeField.setName("payType");
            payTypeField.setLabelName(I18N.text(I18NKey.PAY_TYPE));
            payTypeField.set("api_name", "payType");
            list.add(payTypeField);
        }

        TableColumn finishTimeField = new TableColumn();
        finishTimeField.setRenderType(IFieldType.TEXT);
        finishTimeField.setName("finishTime");
        finishTimeField.setLabelName(I18N.text(I18NKey.PAY_FINISH_TIME));
        finishTimeField.set("api_name", "finishTime");
        list.add(finishTimeField);

        if (isAll) {
            TableColumn transTimeField = new TableColumn();
            transTimeField.setRenderType(IFieldType.TEXT);
            transTimeField.setName("transTime");
            transTimeField.setLabelName(I18N.text(I18NKey.TRANSACTION_TIME));
            transTimeField.set("api_name", "transTime");
            list.add(transTimeField);
            TableColumn remarkField = new TableColumn();
            remarkField.setRenderType(IFieldType.TEXT);
            remarkField.setName("remark");
            remarkField.setLabelName(I18N.text(I18NKey.REMARK));
            remarkField.set("api_name", "remark");
            list.add(remarkField);
            TableColumn payEnterpriseNameField = new TableColumn();
            payEnterpriseNameField.setRenderType(IFieldType.TEXT);
            payEnterpriseNameField.setName("payEnterpriseName");
            payEnterpriseNameField.setLabelName(I18N.text(I18NKey.PAY_ENTERPRISE_NAME));
            payEnterpriseNameField.set("api_name", "payEnterpriseName");
            list.add(payEnterpriseNameField);
        }
        return list;
    }

    public TableComponent getTableComponentByRecordType(RelatedObjectDescribeStructure detailObject,
                                                        IRecordTypeOption recordTypeOption) {
        if (Objects.isNull(layout)) {
            return new TableComponent();
        }

        IObjectDescribe detailObjectDescribe = detailObject.getRelatedObjectDescribe();

        TableComponent tableComponent = new TableComponent();
        tableComponent.setName(detailObjectDescribe.getApiName() + "_table_component");
        tableComponent.setRelatedListName(detailObject.getRelatedListName());
        tableComponent.setHeader(recordTypeOption.getLabel());
        tableComponent.setIncludeFields(getTableComponent().map(TableComponent::getIncludeFields).orElse(Lists.newArrayList()));
        tableComponent.setRefObjectApiName(recordTypeOption.getApiName());
        return tableComponent;
    }

    public static ILayout buildDefaultMobileListLayout(IObjectDescribe describe) {
        ITableComponent tableComponent = buildDefaultTableComponent(describe);
        String displayNameI18nKey = I18NKey.DEFAULT_MOBILE_LIST_LAYOUT_DISPLAY_NAME;
        ILayout layout = new Layout();
//        layout.setId(IdGenerator.get());
        layout.setTenantId(describe.getTenantId());
        layout.setComponents(Lists.newArrayList(tableComponent));
        layout.setRefObjectApiName(describe.getApiName());
        layout.setLayoutType(ILayout.LIST_LAYOUT_TYPE);
        layout.setName("layout_mobile_" + describe.getApiName());
        layout.setDisplayName(I18N.text(displayNameI18nKey));
        layout.setPackage("CRM");
        layout.setAgentType("agent_type_mobile");
        layout.setCreatedBy(User.SUPPER_ADMIN_USER_ID);
        layout.setCreateTime(System.currentTimeMillis());
        layout.setLastModifiedBy(User.SUPPER_ADMIN_USER_ID);
        layout.setLastModifiedTime(layout.getCreateTime());
        //设置为默认布局
        layout.setIsDefault(true);

        LayoutExt layoutExt = LayoutExt.of(layout);
        Localization localization = I18nClient.getInstance().get(displayNameI18nKey, Long.parseLong(describe.getTenantId()));
        layoutExt.setI18nInfos(Lists.newArrayList(I18nInfo.builder()
                .apiName(Layout.DISPLAY_NAME)
                .build()
                .convertLanguageInfo(localization)));

        return layout;
    }

    private static ITableComponent buildDefaultTableComponent(IObjectDescribe describe) {
        ITableComponent tableComponent = new TableComponent();
        tableComponent.setName("table_component");
        IFieldDescribe nameField = describe.getFieldDescribe(IObjectData.NAME);
        ITableColumn nameColumn = TableColumnExt.of(nameField).getTableColumn();
        List<ITableColumn> tableColumns = Lists.newArrayList(nameColumn);
        ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(IObjectData.CREATED_BY).ifPresent(x ->
                tableColumns.add(TableColumnExt.of(x).getTableColumn()));
        ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(IObjectData.LAST_MODIFIED_TIME).ifPresent(x ->
                tableColumns.add(TableColumnExt.of(x).getTableColumn()));
        tableComponent.setIncludeFields(Lists.newArrayList(nameColumn));
        return tableComponent;
    }

    public static ILayout buildSalesOrderProductListLayout() {
        TableComponent tableComponent = new TableComponent();

        TableColumn tableColumn2 = new TableColumn();
        TableColumn tableColumn3 = new TableColumn();
        TableColumn tableColumn4 = new TableColumn();

        tableColumn2.setName("sales_price");
        tableColumn2.setRenderType("currency");
        tableColumn2.setLabelName(I18N.text(I18NKey.UNIT_PRICE));
        tableColumn3.setName("quantity");
        tableColumn3.setRenderType("number");
        tableColumn3.setLabelName(I18N.text(I18NKey.QUANTITY));
        tableColumn4.setName("subtotal");
        tableColumn4.setRenderType("currency");
        tableColumn4.setLabelName(I18N.text(I18NKey.SUBTOTAL));
        tableComponent.setIncludeFields(Lists.newArrayList(tableColumn2, tableColumn3, tableColumn4));
        tableComponent.setName(ObjectAPINameMapping.SalesOrderProduct.getApiName() + "_table_component");
        tableComponent.setRelatedListName("order_id_list");

        ILayout layout = new Layout();
        layout.setComponents(Lists.newArrayList(tableComponent));
        layout.setName("sales_order_product_list_layout");
        layout.setLayoutType(ILayout.LIST_LAYOUT_TYPE);

        return layout;
    }

    public static ILayout buildReturnGoodsInvoiceProductListLayout() {
        TableComponent tableComponent = new TableComponent();

        TableColumn tableColumn2 = new TableColumn();
        TableColumn tableColumn3 = new TableColumn();
        TableColumn tableColumn4 = new TableColumn();

        tableColumn2.setName("returned_product_price");
        tableColumn2.setRenderType("currency");
        tableColumn2.setLabelName(I18N.text(I18NKey.RETURN_UNIT_PRICE));

        tableColumn3.setName("quantity");
        tableColumn3.setRenderType("number");
        tableColumn3.setLabelName(I18N.text(I18NKey.QUANTITY));

        tableColumn4.setName("subtotal");
        tableColumn4.setRenderType("currency");
        tableColumn4.setLabelName(I18N.text(I18NKey.SUBTOTAL));

        tableComponent.setIncludeFields(Lists.newArrayList(tableColumn2, tableColumn3, tableColumn4));
        tableComponent.setName(ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName() + "_table_component");
        tableComponent.setRelatedListName("order_id_list");

        ILayout layout = new Layout();
        layout.setComponents(Lists.newArrayList(tableComponent));
        layout.setName("return_goods_invoice_product_list_layout");
        layout.setLayoutType(ILayout.LIST_LAYOUT_TYPE);

        return layout;
    }

    public Optional<IFormField> getField(String fieldName) {
        Optional<IFormField> formField = getFormComponents().stream().flatMap(x -> x.stream())
                .filter(x -> fieldName.equals(x.getFieldName())).findFirst();
        if (!formField.isPresent()) {
            formField = getFormTables().stream().flatMap(x -> x.getFields().stream())
                    .filter(x -> fieldName.equals(x.getFieldName())).findFirst();
        }
        return formField;
    }

    public boolean containsField(String fieldName) {
        return getField(fieldName).isPresent();
    }

    public boolean isFieldInLayout(String fieldName) {
        return getField(fieldName).isPresent();
    }

    public boolean isSectionInLayout(String sectionName) {
        return getFieldSection(sectionName).isPresent();
    }

    public Optional<IFieldSection> getFieldSection(String sectionName) {
        Optional<FormComponentExt> formComponentExt = getFormComponent();
        if (formComponentExt.isPresent()) {
            return formComponentExt.get().getFieldSection(sectionName);
        }
        return Optional.empty();
    }

    public void addFields(List<IFieldDescribe> fieldDescribes) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        Set<String> oldFieldNames = Sets.newHashSet(getFieldList());
        fieldDescribes.stream()
                .filter(fieldDescribe -> !oldFieldNames.contains(fieldDescribe.getApiName()))
                .forEach(fieldDescribe ->
                        getFormComponent().ifPresent(x -> x.addField(fieldDescribe, FieldDescribeExt.of(fieldDescribe).isReadOnly(),
                                FieldDescribeExt.of(fieldDescribe).isRequired(), fieldDescribe.getType())));
    }

    public void addField(IFieldDescribe fieldDescribe, FieldLayoutPojo fieldLayoutPojo) {
        if (containsField(fieldDescribe.getApiName())) {
            return;
        }
        getFormComponent().ifPresent(x -> x.addField(fieldDescribe, fieldLayoutPojo.isReadonly(),
                fieldLayoutPojo.isRequired(), fieldLayoutPojo.getRenderType()));
    }

    public void addGroupField(IFieldDescribe groupField, List<IFieldDescribe> fieldList) {
        getFormComponent().ifPresent(x -> x.addGroupField(groupField, fieldList));
    }

    public void addField(IFieldDescribe fieldDescribe, List<IFieldDescribe> fieldList, FieldLayoutPojo fieldLayoutPojo) {
        if (FieldDescribeExt.of(fieldDescribe).isGroupBizField()) {
            addOrUpdateGroupBizField(fieldDescribe, fieldList, fieldLayoutPojo);
        } else if (IFieldType.GROUP.equals(fieldDescribe.getType())
                && GroupField.GROUP_TYPE_DATE_TIME_RANGE.equals(((GroupField) fieldDescribe).getGroupType())) {
            addOrUpdateDateRangeGroupField(fieldDescribe, fieldList, fieldLayoutPojo);
        } else if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
            addGroupField(fieldDescribe, fieldList);
        } else {
            addField(fieldDescribe, fieldLayoutPojo);
        }
    }

    public void addChangeOrderSection(List<IFieldDescribe> fieldDescribes, boolean addFirst) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        // 移除布局中已有的变更原因字段
        removeFields(Lists.newArrayList(ObjectDataExt.CHANGED_REASON));
        getFormComponent().ifPresent(it -> {
            List<IFormField> formFields = FormFieldExt.buildFromFields(fieldDescribes);
            IFieldSection section = new FieldSection();
            section.setName("change_order_section");
            section.set("column", 2);
            section.set("is_show", true);
            section.set("tab_index", "ltr");
            section.setHeader(I18NExt.text(I18NKey.CHANGE_ORDER));
            section.setFields(formFields);
            List<IFieldSection> fieldSections = it.getFieldSections();
            if (addFirst) {
                fieldSections.add(0, section);
            } else {
                fieldSections.add(section);
            }
            it.setFieldSections(fieldSections);
        });
    }

    public void updateField(IFieldDescribe fieldDescribe, FieldLayoutPojo fieldLayoutPojo) {
        try {
            boolean readonly = Objects.equals(fieldDescribe.getType(), IFieldType.COUNT) ? true : fieldLayoutPojo.isReadonly();
            if (fieldLayoutPojo.isShow()) {
                String renderType = fieldLayoutPojo.getRenderType();
                if (Strings.isNullOrEmpty(renderType)) {
                    renderType = fieldDescribe.getType();
                }
                FormField formField = FormComponentExt.generateFormField(fieldDescribe.getApiName(),
                        renderType, readonly, fieldLayoutPojo.isRequired());

                //将只读和必填合并到布局里的字段上，其他属性保持不变，如果直接覆盖的话，会把其他属性弄丢了
                FormField formFieldInLayout = (FormField) getField(fieldDescribe.getApiName()).orElse(formField);
                formFieldInLayout.setRequired(FormFieldExt.of(formField).required());
                formFieldInLayout.setReadOnly(FormFieldExt.of(formField).readOnly());
                formFieldInLayout.setRenderType(FormFieldExt.of(formField).getRenderType());

                onFieldUpdate(fieldDescribe.getApiName(), formFieldInLayout.getContainerDocument());
                getFormTables().forEach(formTable -> formTable.onFieldUpdate(fieldDescribe.getApiName(), formField.getContainerDocument()));
            } else {
                onFieldDelete(fieldDescribe);
                //表格组件只能自己处理，元数据不知道这个组件
                getFormTables().forEach(formTable -> formTable.onFieldDelete(fieldDescribe));
            }
        } catch (MetadataServiceException e) {
            log.error("updateField error,fieldDescribe:{},fieldLayoutPojo:{}", fieldDescribe.toJsonString(), fieldLayoutPojo, e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
    }

    public void updateGroupField(IFieldDescribe groupField, List<IFieldDescribe> fieldList, boolean isShow) {
        getFormComponent().ifPresent(x -> x.updateGroupField(groupField, fieldList, isShow));
    }

    public void updateWhatField(WhatFieldDescribe whatFieldDescribe, IFieldDescribe whatIdFieldDescribe, List<IFieldDescribe> fieldList, FieldLayoutPojo fieldLayoutPojo) {
        boolean isSectionInLayout = isSectionInLayout(whatFieldDescribe.getApiName());
        boolean isFieldInLayout = isFieldInLayout(whatFieldDescribe.getIdFieldApiName());
        if (fieldLayoutPojo.isShow()) {
            if (!isSectionInLayout && !isFieldInLayout) {
                addGroupField(whatFieldDescribe, fieldList);
            }
        } else {
            if (isSectionInLayout) {
                updateGroupField(whatFieldDescribe, fieldList, fieldLayoutPojo.isShow());
            } else if (isFieldInLayout) {
                updateField(whatIdFieldDescribe, fieldLayoutPojo);
            }
        }
    }

    public void updateWhatListField(WhatListFieldDescribe whatListFieldDescribe, IFieldDescribe whatIdFieldDescribe, List<IFieldDescribe> fieldList, FieldLayoutPojo fieldLayoutPojo) {
        boolean isSectionInLayout = isSectionInLayout(whatListFieldDescribe.getApiName());
        boolean isFieldInLayout = isFieldInLayout(whatListFieldDescribe.getIdFieldApiName());
        if (fieldLayoutPojo.isShow()) {
            if (!isSectionInLayout && !isFieldInLayout) {
                addGroupField(whatListFieldDescribe, fieldList);
            }
        } else {
            if (isSectionInLayout) {
                updateGroupField(whatListFieldDescribe, fieldList, fieldLayoutPojo.isShow());
            } else if (isFieldInLayout) {
                updateField(whatIdFieldDescribe, fieldLayoutPojo);
            }
        }
    }

    public void updateField(IFieldDescribe fieldDescribe, List<IFieldDescribe> fieldList, FieldLayoutPojo fieldLayoutPojo) {
        if (FieldDescribeExt.of(fieldDescribe).isGroupBizField()) {
            addOrUpdateGroupBizField(fieldDescribe, fieldList, fieldLayoutPojo);
        } else if (IFieldType.GROUP.equals(fieldDescribe.getType())
                && GroupField.GROUP_TYPE_DATE_TIME_RANGE.equals(((GroupField) fieldDescribe).getGroupType())) {
            addOrUpdateDateRangeGroupField(fieldDescribe, fieldList, fieldLayoutPojo);
        } else if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
            boolean isSectionInLayout = isSectionInLayout(fieldDescribe.getApiName());
            if (!isSectionInLayout && fieldLayoutPojo.isShow()) {
                addGroupField(fieldDescribe, fieldList);
            }

            if (isSectionInLayout) {
                updateGroupField(fieldDescribe, fieldList, fieldLayoutPojo.isShow());
            }
        } else {
            addOrUpdateField(fieldDescribe, fieldLayoutPojo);
        }
    }

    private void addOrUpdateGroupBizField(IFieldDescribe fieldDescribe, List<IFieldDescribe> fieldList, FieldLayoutPojo fieldLayoutPojo) {
        CollectionUtils.nullToEmpty(fieldList).stream()
                .findFirst()
                .ifPresent(x -> {
                    fieldLayoutPojo.setRenderType(x.getType());
                    addOrUpdateField(x, fieldLayoutPojo);
                });
    }

    private void addOrUpdateDateRangeGroupField(IFieldDescribe fieldDescribe, List<IFieldDescribe> fieldList, FieldLayoutPojo fieldLayoutPojo) {
        String startTimeFieldApiName = ((DateTimeRangeFieldDescribe) fieldDescribe).getStartTimeFieldApiName();
        CollectionUtils.nullToEmpty(fieldList).stream()
                .filter(x -> StringUtils.equals(startTimeFieldApiName, x.getApiName()))
                .findFirst()
                .ifPresent(x -> {
                    fieldLayoutPojo.setRenderType(x.getType());
                    addOrUpdateField(x, fieldLayoutPojo);
                });
    }

    public void addOrUpdateField(IFieldDescribe fieldDescribe, FieldLayoutPojo fieldLayoutPojo) {
        boolean isFieldInLayout = isFieldInLayout(fieldDescribe.getApiName());
        if (!isFieldInLayout && fieldLayoutPojo.isShow()) {
            addField(fieldDescribe, fieldLayoutPojo);
        }
        if (isFieldInLayout) {
            updateField(fieldDescribe, fieldLayoutPojo);
        }
    }

    public void removeOwnerOrDepartmentFieldIfIsDetailObj(IObjectDescribe describe) {
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            removeFields(Sets.newHashSet(ObjectDataExt.OWNER, ObjectDataExt.OWNER_DEPARTMENT));
        }
    }

    public void removeUnsupportedFields(User user) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735) || user.isOutUser()) {
            getFormComponents().forEach(x -> x.removeFieldByTypes(IFieldType.DIMENSION));
            getFormTables().forEach(x -> x.removeFieldByTypes(IFieldType.DIMENSION));
        }
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
            getFormComponents().forEach(x -> x.removeFieldByTypes(IFieldType.OUT_EMPLOYEE));
            getFormTables().forEach(x -> x.removeFieldByTypes(IFieldType.OUT_EMPLOYEE));
        }
    }

    public void removeExportUnsupportedFields() {
        getFormComponents().forEach(x -> x.removeFieldByTypes(IFieldType.OUT_EMPLOYEE, IFieldType.OUT_DEPARTMENT));
        getFormTables().forEach(x -> x.removeFieldByTypes(IFieldType.OUT_EMPLOYEE, IFieldType.OUT_DEPARTMENT));
    }

    public void removeFieldByTypes(ObjectDescribeExt describe, String... fieldTypes) {
        getFormComponents().forEach(x -> x.removeFieldByTypes(describe, fieldTypes));
        getFormTables().forEach(x -> x.removeFieldByTypes(describe, fieldTypes));
    }

    public void removeFields(Collection<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        LayoutExt.of(this.layout).getFormComponents().forEach(x -> x.removeFields(Sets.newHashSet(fieldNames)));
        LayoutExt.of(this.layout).getFormTables().forEach(x -> x.removeFields(Sets.newHashSet(fieldNames)));
    }

    public void hideFields(Collection<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        LayoutExt.of(this.layout).getFormComponents().forEach(x -> x.hideFields(Sets.newHashSet(fieldNames)));
        LayoutExt.of(this.layout).getFormTables().forEach(x -> x.hideFields(Sets.newHashSet(fieldNames)));
    }

    public void setReadOnly(Set<String> readOnlyField, boolean readOnly) {
        if (CollectionUtils.empty(readOnlyField)) {
            return;
        }
        getFormComponents().forEach(x -> x.setReadOnly(readOnlyField, readOnly));
        getFormTables().forEach(x -> x.setReadOnly(readOnlyField, readOnly));
    }

    public static IRelatedRecordComponent buildModifyRecordComponent() {
        IRelatedRecordComponent component = new RelatedRecordComponent();
        component.setName(ComponentExt.OPERATION_LOG_COMPONENT_NAME);
        component.setType(IComponent.TYPE_RELATED_RECORD);
        component.setHeader(I18N.text(I18NKey.MODIFY_LOG));

        FormField userField = new FormField();
        userField.setRenderType(IFieldType.EMPLOYEE);
        userField.setFieldName("user_id");

        FormField messageField = new FormField();
        messageField.setRenderType(IFieldType.TEXT);
        messageField.setFieldName("log_msg");

        FormField timeField = new FormField();
        timeField.setRenderType(IFieldType.DATE_TIME);
        timeField.setFieldName("operation_time");

        component.addOneField(userField, "user");
        component.addOneField(messageField, "message");
        component.addOneField(timeField, "operation_time");

        ComponentExt.of(component).setDefaultValue();
        ComponentExt.of(component).setLimit(1);

        return component;
    }

    public static String getRenderType(String fieldApiName, String type) {
        return getRenderType(UDOBJ, fieldApiName, type);
    }

    public static String getRenderType(String objectApiName, String fieldApiName, String type) {
        if (!ObjectDescribeExt.isSFAObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        Set<String> changeRenderApiNameSet = DefObjConstants.changeToEmployeeRenderTypeListFormObjectMap.get(objectApiName);
        if (changeRenderApiNameSet != null && changeRenderApiNameSet.contains(fieldApiName)) {
            return IFieldType.EMPLOYEE;
        }
        return type;
    }

    public void addTopInfoComponentButton(IButton button) {
        Optional<SimpleComponent> topInfoComponent = getTopInfoComponent();
        if (topInfoComponent.isPresent()) {
            List<IButton> btnList = topInfoComponent.get().getButtons();
            if (CollectionUtils.empty(btnList)) {
                btnList = Lists.newArrayList();
            }
            btnList.add(button);
            topInfoComponent.get().setButtons(btnList);
        }
    }

    public void addDefaultDataOwnDepartment() {
        getFormComponent().ifPresent(x -> {
            boolean match = x.getFieldSections().stream().anyMatch(fieldSection -> fieldSection.getFields().stream()
                    .anyMatch(formField -> FormFieldExt.of(formField).isDataOwnDepartment()));
            if (match) {
                return;
            }
            x.getBaseFieldSection().ifPresent(fieldSection ->
                    fieldSection.addFields(Lists.newArrayList(FormFieldExt.buildDataOwnDepartment())));
        });
    }

    public LayoutExt fillRelevantTeamField() {
        getFormComponent().ifPresent(x -> {
            boolean match = x.getFieldSections()
                    .stream()
                    .anyMatch(fieldSection ->
                            fieldSection.getFields()
                                    .stream()
                                    .anyMatch(formField ->
                                            Objects.equals(formField.getFieldName(), ObjectDataExt.RELEVANT_TEAM)));
            if (match) {
                return;
            }
            IFieldSection fieldSection = x.getBaseFieldSection().orElseGet(() -> x.getFieldSection().orElse(null));
            if (Objects.nonNull(fieldSection)) {
                fieldSection.addFields(Lists.newArrayList(FormFieldExt.buildRelevantTeam()));
            }
        });
        return this;
    }

    public void layoutDescriptionNullToEmpty() {
        if (Objects.nonNull(layout) && Objects.isNull(getLayoutDescription())) {
            setLayoutDescription("");
        }
    }

    public boolean isFieldInUIEventTriggerList(String fieldName) {
        List<Map<String, Object>> uiEvents = getEvents();
        if (CollectionUtils.empty(uiEvents)) {
            return false;
        }
        return uiEvents.stream().anyMatch(x -> UIEventExt.of(x).isFieldInTriggerList(fieldName));
    }

    public boolean containUIEvnt() {
        return CollectionUtils.notEmpty(getEvents());
    }

    public void handleLayoutByLayoutRule(List<LayoutRuleInfo> layoutRule, String layoutType) {
        if (CollectionUtils.empty(layoutRule)) {
            return;
        }
        layoutRule.stream().map(x -> LayoutRuleExt.of(x))
                .filter(x -> x.isPageTypeRule())
                .filter(x -> StringUtils.equals(layoutType, x.getRule().getPageTriggerMode()))
                .findFirst().ifPresent(x -> {
            LayoutRuleExt.PageBranch pageBranch = x.getPageBranch();
            if (Objects.isNull(pageBranch)) {
                return;
            }
            List<String> hideField = pageBranch.getHideField();
            if (CollectionUtils.notEmpty(hideField)) {
                hideFields(Sets.newHashSet(hideField));
            }

            List<String> readonlyField = pageBranch.getReadonlyField();
            if (CollectionUtils.notEmpty(readonlyField)) {
                setReadOnly(Sets.newHashSet(readonlyField), true);
            }
        });
    }

    public Optional<IRelatedRecordComponent> getOperationLogComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> StringUtils.equals(x.getName(), ComponentExt.OPERATION_LOG_COMPONENT_NAME))
                .map(x -> (IRelatedRecordComponent) x)
                .findFirst();
    }

    public List<IFrameComponent> getFrameComponent() {
        List<IComponent> components = getComponentsSilently();
        return components.stream()
                .filter(x -> StringUtils.equals(x.getType(), IComponent.TYPE_IFRAME))
                .map(x -> (IFrameComponent) x)
                .collect(Collectors.toList());
    }

    public Optional<IComponent> getHeadInfoComponent() {
        return getComponentByApiName(ComponentExt.HEAD_INFO_COMPONENT_NAME);
    }

    public Optional<IComponent> getSummaryCardComponent() {
        return getComponentByApiName(ComponentExt.SUMMARY_CARD_COMPONENT_NAME);
    }

    public List<ITabsComponent> getTabsComponents() {
        return getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isTabs())
                .map(x -> (ITabsComponent) x)
                .collect(Collectors.toList());
    }

    public Optional<INavigationComponent> getNavigationComponent() {
        return getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isNavigation())
                .map(x -> (INavigationComponent) x)
                .findFirst();
    }

    public void removeHeadInfoComponent() {
        List<IComponent> components = getComponentsSilently();
        components.removeIf(it -> ComponentExt.HEAD_INFO_COMPONENT_NAME.equals(it.getName()));
        setComponents(components);
    }

    public Optional<TopInfoComponent> getNewTopInfoComponent() {
        return getComponentByApiName(ComponentExt.TOP_INFO_COMPONENT_NAME).map(x -> (TopInfoComponent) x);
    }

    public Optional<IComponent> getNameComponent() {
        return getComponentByApiName(NAME_COMPONENT);
    }

    public Optional<IComponent> getTeamComponent() {
        return getComponentByApiName(ComponentExt.TEAM_COMPONENT_NAME);
    }

    public void fillComponentId(List<IComponent> components) {
        components.forEach(x -> x.set("_id", x.getName()));
    }

    public void removeTopInfo() {
        toMap().remove(ComponentExt.TOP_INFO_COMPONENT_NAME);
    }

    public void fillHiddenComponentInfo(List<IComponent> components) {
        List<String> hiddenComponents = getHiddenComponents();
        if (CollectionUtils.notEmpty(hiddenComponents)) {
            components.stream().filter(x -> hiddenComponents.contains(x.getName())).forEach(x -> x.setIsHidden(true));
            setHiddenComponents(Lists.newArrayList());
        }
    }

    public List<ICustomComponent> getCustomComponents() {
        return getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isCustomComponent())
                .map(x -> (ICustomComponent) x)
                .collect(Collectors.toList());
    }

    public List<IComponent> filterComponents(List<IComponent> components) {
        if (CollectionUtils.empty(components)) {
            return Lists.newArrayList();
        }
        if (!isNewLayout()) {
            setComponentOrder(components);
        }
        removeHiddenComponents(components);
        return components;
    }

    public void buildTabShowMasterField(List<IComponent> components, ObjectDescribeExt describeExt) {

        for (IComponent component : components) {
            ComponentExt componentExt = ComponentExt.of(component);
            if (componentExt.isMasterDetailComponent()) {
                if (isEnableMobileLayout()
                        && componentExt.enableRelatedListConfig()
                        && ObjectUtils.isNotEmpty(component.get(STATISTICS_RENDER))) {
                    //1. 移动端详情开启“独立配置布局”（enable_mobile_layout == ture）
                    //2. 该Tab开启独立配置（scene_info != null）
                    //3. 该Tab开启独立配置后配置过布局
                    //新逻辑：依赖对象描述，可展示对象所有可用字段，暂时只过滤出统计该Tab从对象，特殊处理：新加字段默认展示
                    ListComponentExt listComponentExt = ListComponentExt.of(component);
                    List<Count> masterFields = describeExt.getCountFields(listComponentExt.getRefObjectApiName());
                    Set<String> all = masterFields.stream().map(Count::getApiName).collect(Collectors.toSet());
                    Set<String> hidden = Sets.newHashSet(listComponentExt.getTabHiddenMasterField());
                    List<String> order = listComponentExt.getTabShowMasterField();
                    actualShowItem(all, hidden, order, true);
                } else {
                    ((MultiTableComponent) component).remove(STATISTICS_RENDER);
                }
            }
        }
    }

    public void syncRelatedListProperty(List<IComponent> components) {
        List<IComponent> relatedLists = components.stream().filter(x -> ComponentExt.of(x).isRelatedList())
                .map(x -> (RelatedObjectList) x)
                .collect(Collectors.toList());
        syncProperty(relatedLists);
    }

    public void syncProperty(List<IComponent> components) {
        components.stream().forEach(x -> getComponentByApiName(x.getName()).ifPresent(y ->
                ComponentExt.of(x).syncProps(y)));
    }

    public List<IComponent> filterCustomComponents(List<ICustomComponent> newCustomComponents) {
        if (CollectionUtils.empty(newCustomComponents)) {
            return Lists.newArrayList();
        }
        Map<String, ICustomComponent> newCustomComponentMap = newCustomComponents.stream()
                .collect(Collectors.toMap(x -> x.getCustomCompApiName(), x -> x));
        List<IComponent> customComponents = getCustomComponents().stream()
                .filter(x -> newCustomComponentMap.containsKey(x.getCustomCompApiName()))
                .peek(x -> {
                    ICustomComponent newCustomComponent = newCustomComponentMap.get(x.getCustomCompApiName());
                    x.setCustomCompUrl(newCustomComponent.getCustomCompUrl());
                })
                .collect(Collectors.toList());
        return customComponents;
    }

    public List<IButton> getButtonOrder() {
        Optional<IComponent> headInfo = getHeadInfoComponent();
        // 新布局
        if (headInfo.isPresent()) {
            return headInfo.get().getButtons();
        }
        return getButtons();
    }

    public void setButtonOrder(List<IButton> buttonOrder) {
        Optional<IComponent> headInfo = getHeadInfoComponent();
        // 新布局
        if (headInfo.isPresent()) {
            headInfo.get().setButtons(buttonOrder);
        } else {
            setButtons(buttonOrder);
        }
    }

    public boolean isListLayout() {
        return ListLayoutExt.LIST_LAYOUT.equals(getLayoutType());
    }

    public boolean isFlowTaskLayout() {
        return LayoutTypes.FLOW_TASK_LIST.equals(getLayoutType());
    }

    public boolean isDetailLayout() {
        return ILayout.DETAIL_LAYOUT_TYPE.equals(getLayoutType());
    }

    public boolean isEditLayout() {
        return LayoutTypes.EDIT.equals(getLayoutType());
    }

    public boolean isNewLayout() {
        return CollectionUtils.notEmpty(layout.getLayoutStructure());
    }

    public boolean isV2Layout() {
        if (!isNewLayout()) {
            return false;
        }
        return !getLayoutStructure().containsKey(LayoutStructure.LAYOUT);
    }

    public boolean isV3Layout() {
        if (!isNewLayout()) {
            return false;
        }
        return getLayoutStructure().containsKey(LayoutStructure.LAYOUT);
    }

    public boolean isMobileListLayout() {
        return MOBILE_LIST_LAYOUT.equals(getLayoutType());
    }

    public void setDefaultStructureToList() {
        this.setLayoutStructure(LayoutStructure.getDefaultAbstractLayoutStructure());
    }

    public boolean isEnableMobileLayout() {
        return Boolean.TRUE.equals(getEnableMobileLayout()) && CollectionUtils.notEmpty(getMobileLayout());
    }

    public boolean isEnableSidebarLayout() {
        return Boolean.TRUE.equals(getEnableSidebarLayout()) && CollectionUtils.notEmpty(getSidebarLayout());
    }


    public List<IButton> order(List<IButton> orderedButtons, List<IButton> allButtons) {
        if (CollectionUtils.empty(orderedButtons)) {
            return allButtons;
        }

        Map<String, Integer> orderButtonInfo = Maps.newHashMap();
        int orderButtonSize = orderedButtons.size();
        for (int i = 0; i < orderButtonSize; i++) {
            orderButtonInfo.put(orderedButtons.get(i).getName(), i);
        }

        List<LayoutExt.OrderedButton> orderedButtonList = Lists.newArrayList();
        for (int j = 0; j < allButtons.size(); j++) {
            LayoutExt.OrderedButton orderedButton = new LayoutExt.OrderedButton(allButtons.get(j), orderButtonInfo.getOrDefault(allButtons.get(j).getName(), orderButtonSize++));
            orderedButtonList.add(orderedButton);
        }
        return orderedButtonList.stream().sorted().map(x -> x.getButton()).collect(Collectors.toList());
    }

    /**
     * @return 列表组件
     * @deprecated use {@link ListLayoutExt#getFirstListComponent()} instead
     */
    @Deprecated
    public Optional<ListComponentExt> getListLayoutComponent() {
        return ListLayoutExt.of(layout).getFirstListComponent();
    }

    public boolean needTagInfo() {
        return getTableComponent()
                .map(TableComponentExt::of)
                .filter(TableComponentExt::isShowTag)
                .isPresent();
    }

    public List<String> getSummaryInfoComponentApiNames() {
        return getComponentsSilently().stream()
                .filter(component -> ComponentExt.of(component).isSummaryKeyInfo())
                .map(IComponent::getName)
                .collect(Collectors.toList());

    }

    public List<SummaryKeyComponentInfo> getSummaryKeyComponentInfos() {
        // 详情页中布局类型为summary_info是「关键信息组件」，列表页为「汇总字段组件」
        return getComponentsSilently().stream()
                .filter(component -> ComponentExt.of(component).isSummaryKeyInfo()
                        && LayoutTypes.DETAIL.equals(getLayoutType()))
                .map(SummaryKeyComponentInfo::of)
                .collect(Collectors.toList());

    }

    public void removeFormTableComponents() {
        setComponents(getComponentsSilently().stream()
                .filter(x -> !ComponentExt.of(x).isFormTable())
                .collect(Collectors.toList()));
    }

    public void validateRichTextFieldInLayout() {
        // HTML富文本校验
        List<IFormField> htmlRichTextFieldList = Lists.newArrayList();
        getFormComponents().forEach(x -> htmlRichTextFieldList.addAll(x.getFormFields().stream()
                .filter(f -> IFieldType.HTML_RICH_TEXT.equals(f.getRenderType()))
                .collect(Collectors.toList())));
        getFormTables().forEach(x ->
                htmlRichTextFieldList.addAll(x.getFields().stream()
                        .filter(f -> IFieldType.HTML_RICH_TEXT.equals(f.getRenderType()))
                        .collect(Collectors.toList())));
        
        String describeApiName = getRefObjectApiName();
        int htmlRichTextLimit = AppFrameworkConfig.getHtmlRichTextFieldMaxLimitByObject(describeApiName, layout.getTenantId());
        if (htmlRichTextFieldList.size() > htmlRichTextLimit) {
            throw new ValidateException(I18NExt.text(I18NKey.VALIDATE_RICH_TEXT_FIELD_COUNT, htmlRichTextLimit));
        }
        
        // 协同富文本校验
        List<IFormField> richTextFieldList = Lists.newArrayList();
        getFormComponents().forEach(x -> richTextFieldList.addAll(x.getFormFields().stream()
                .filter(f -> IFieldType.RICH_TEXT.equals(f.getRenderType()))
                .collect(Collectors.toList())));
        getFormTables().forEach(x ->
                richTextFieldList.addAll(x.getFields().stream()
                        .filter(f -> IFieldType.RICH_TEXT.equals(f.getRenderType()))
                        .collect(Collectors.toList())));
        
        int richTextLimit = AppFrameworkConfig.getRichTextFieldMaxLimitByObject(describeApiName, layout.getTenantId());
        if (richTextFieldList.size() > richTextLimit) {
            throw new ValidateException(I18NExt.text(I18NKey.VALIDATE_COOPERATIVE_RICH_TEXT_FIELD_COUNT, richTextLimit));
        }
    }

    public void fillAppId(String appId) {
        String bindingAppId = getBindingAppId();
        if (Strings.isNullOrEmpty(bindingAppId)) {
            setBindingAppId(appId);
        }
    }

    public List<IComponent> getTextComponents() {
        return getComponentsSilently().stream().filter(x -> ComponentExt.of(x).isTextComponent()).collect(Collectors.toList());
    }

    @AllArgsConstructor
    @Data
    private static class OrderedButton implements Comparable<LayoutExt.OrderedButton> {
        private IButton button;
        private int order;

        @Override
        public int compareTo(@NotNull LayoutExt.OrderedButton o) {
            return Integer.compare(this.order, o.order);
        }
    }

    public List<String> parseAllFields() {
        List<String> fieldList = getFieldList();
        if (CollectionUtils.notEmpty(fieldList)) {
            return fieldList;
        }
        Optional<TableComponent> tableComponent = getTableComponent();
        return tableComponent.isPresent() ? TableComponentExt.of(tableComponent.get()).getFieldListAnyway() : Lists.newArrayList();
    }

    public Set<String> getParents(Set<String> children) {
        if (CollectionUtils.empty(children)) {
            return Sets.newHashSet();
        }
        List<IComponent> components = getComponentsSilently().stream()
                .filter(x -> !children.contains(x.getName()))
                .collect(Collectors.toList());
        Set<String> result = Sets.newHashSet();
        Set<String> parents;
        do {
            parents = getParentComponents(children, components);
            result.addAll(parents);
            components.removeIf(x -> result.contains(x.getName()));
        } while (CollectionUtils.notEmpty(parents));
        return result;
    }

    private Set<String> getParentComponents(Set<String> children, List<IComponent> components) {
        if (CollectionUtils.empty(children) || CollectionUtils.empty(components)) {
            return Sets.newHashSet();
        }
        return components.stream()
                .filter(x -> children.stream().anyMatch(y -> ComponentExt.of(x).containsComponent(y)))
                .map(IComponent::getName)
                .collect(Collectors.toSet());
    }

    public void removeUIEvent() {
        ((Layout) layout).getContainerDocument().remove(Layout.EVENTS);
    }

    public void removeUnActiveButton() {
        List<IButton> buttons = getButtonOrder();
        if (CollectionUtils.notEmpty(buttons)) {
            buttons.removeIf(x -> !LayoutButtonExt.of(x).isActive());
            setButtonOrder(buttons);
        }
    }

    public void setDefaultIncludeFields(IObjectDescribe objectDescribe) {
        if (!isMobileListLayout()) {
            // 不是移动端摘要布局直接返回
            return;
        }

        TableComponentExt tableComponentExt = getTableComponent().map(TableComponentExt::of).orElse(null);
        if (Objects.nonNull(tableComponentExt)) {
            tableComponentExt.robust();
            if (tableComponentExt.enableNewLayout()) {// 新布局暂时不需要补充默认字段
                return;
            }
        }

        List<String> list = Optional.ofNullable(tableComponentExt)
                .map(TableComponentExt::getFieldListAnyway)
                .orElse(null);
        if (CollectionUtils.empty(list)) {
            ITableComponent tableComponent = buildDefaultTableComponent(objectDescribe);
            LayoutExt.of(layout).setComponents(Lists.newArrayList(tableComponent));
        }
    }

    public List<String> getLayoutTransKeys() {
        if (!RequestUtil.isCepRequest()) {
            return Lists.newArrayList();
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<IComponent> components = layoutExt.getComponentsSilently();
        List<String> keys = Lists.newArrayList();
        for (IComponent component : components) {
            //增加组件多语key
            ComponentExt componentExt = ComponentExt.of(component);
            if (Objects.equals(LayoutTypes.EDIT, layoutExt.getLayoutType())) {
                keys.add(ComponentExt.getEditLayoutComponentNameKey(layoutExt.getRefObjectApiName(), layoutExt.getName(), component.getName()));
            } else {
                keys.add(ComponentExt.getDetailComponentNameKey(layoutExt.getRefObjectApiName(), layoutExt.getName(), component.getName()));
            }
            //增加运用布局的key
            keys.add(ComponentExt.getRealKeyWithComponent(component, layoutExt.getLayoutType(), getRefObjectApiName(),
                    layoutExt.getName(), layoutExt.getReferenceFieldConfig(), layoutExt.getTextComponentNamesInTable()));
            if (componentExt.isTextComponent()) {
                keys.add(ComponentExt.getTextComponentContentKey(layoutExt.getRefObjectApiName(), layoutExt.getName(), component.getName(), layoutExt.getLayoutType()));
            }

            if (!Strings.isNullOrEmpty(componentExt.getNameI18nKey())) {
                keys.add(componentExt.getNameI18nKey());
            }
            if (CollectionUtils.notEmpty(componentExt.getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName()))) {
                keys.addAll(componentExt.getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName()).stream()
                        .filter(x -> Objects.nonNull(x) && StringUtils.isNotEmpty(x.getCustomKey()))
                        .map(I18nInfo::getCustomKey).collect(Collectors.toList()));
            }

            //页签
            if (componentExt.isTabs()) {
                ITabsComponent tabsComponent = (ITabsComponent) component;
                List<TabSection> tabs = tabsComponent.getTabs();
                //所有的页签拼key
                keys.addAll(tabs.stream().map(x ->
                        ComponentExt.getDetailLayoutTabNameKey(layout.getRefObjectApiName(), layout.getName(), x.getApiName()))
                        .collect(Collectors.toList()));
                //将nameI18nKey放入keys中
                keys.addAll(tabs.stream().map(x -> {
                    TabSectionExt tabSectionExt = TabSectionExt.of(x);
                    return tabSectionExt.getNameI18nKey();
                }).filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }
            //分组
            if (componentExt.isFormType()) {
                FormComponentExt formComponent = FormComponentExt.of((IFormComponent) component);
                List<IFieldSection> fieldSections = formComponent.getFieldSections();

                keys.add(LAYOUT_BASIC_INFO);
                if (layoutExt.isDetailLayout()) {
                    if (StringUtils.isBlank(layoutExt.getReferenceFieldConfig())) {
                        keys.addAll(fieldSections.stream().map(x ->
                                ComponentExt.getDetailLayoutGroupName(layout.getRefObjectApiName(), layout.getName(), x.getName()))
                                .collect(Collectors.toList()));
                    } else {
                        keys.addAll(fieldSections.stream().map(x ->
                                ComponentExt.getEditLayoutGroupName(layout.getRefObjectApiName(), layoutExt.getReferenceFieldConfig(), x.getName()))
                                .collect(Collectors.toList()));
                    }
                } else {
                    keys.addAll(fieldSections.stream().map(x ->
                            ComponentExt.getEditLayoutGroupName(layout.getRefObjectApiName(), layout.getName(), x.getName()))
                            .collect(Collectors.toList()));
                }
            }
        }
        if (layoutExt.isEnableMobileLayout()) {
            LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
            List<IComponent> mobileComponents = mobileLayout.getComponentsSilently();
            List<String> finalKeys = keys;
            mobileComponents.forEach(x -> {
                if (Objects.equals(LayoutTypes.EDIT, layoutExt.getLayoutType())) {
                    finalKeys.add(ComponentExt.getEditLayoutComponentNameKey(layoutExt.getRefObjectApiName(), layoutExt.getName(), x.getName()));
                } else {
                    finalKeys.add(ComponentExt.getDetailComponentNameKey(layoutExt.getRefObjectApiName(), layoutExt.getName(), x.getName()));
                }
                if (!Strings.isNullOrEmpty(ComponentExt.of(x).getNameI18nKey())) {
                    finalKeys.add(ComponentExt.of(x).getNameI18nKey());
                }
            });
            mobileLayout.getSuspendedComponent().forEach(suspendedComponentInfo -> {
                ComponentExt componentExt = ComponentExt.of(suspendedComponentInfo);
                if (Objects.equals(LayoutTypes.DETAIL, layoutExt.getLayoutType())
                        && CollectionUtils.notEmpty(componentExt.getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName()))) {
                    finalKeys.addAll(componentExt.getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName()).stream()
                            .filter(x -> Objects.nonNull(x) && StringUtils.isNotEmpty(x.getCustomKey()))
                            .map(I18nInfo::getCustomKey).collect(Collectors.toList()));
                }
                if (!Strings.isNullOrEmpty(ComponentExt.of(suspendedComponentInfo).getNameI18nKey())) {
                    finalKeys.add(ComponentExt.of(suspendedComponentInfo).getNameI18nKey());
                }
            });
            keys = finalKeys.stream().distinct().collect(Collectors.toList());
        }
        return keys;
    }

    /**
     * 如果有一批元素（记为all）且可以动态增加或者减少，如果只是记录隐藏元素（hidden）或者显示元素（order）（order用List接口可以记录order）
     * 如果我们只是记录其中显示元素或者隐藏元素时，当这一批元素动态变化时，我们只能将新元素默认归为相反的一面，比如只记录显示order，当有新元素
     * 时，无法区分新元素和隐藏的元素只能归为默认新元素为隐藏元素。
     * 同时记录hidden和order与all比较可以找到丢失元素和新元素，另可以通过指定bornToShow来指定新元素默认展示与隐藏。
     *
     * @param all        所有可显示的元素
     * @param hidden     隐藏元素
     * @param order      显示元素，带顺序
     * @param bornToShow true:新元素显示, false:新元素隐藏
     * @return
     */
    public static List<String> actualShowItem(Set<String> all, Set<String> hidden, List<String> order, boolean bornToShow) {
        //删除无效元素
        order.removeAll(Sets.difference(Sets.newHashSet(order), all));
        hidden.removeAll(Sets.difference(Sets.newHashSet(hidden), all));
        //将未设置的新元素同步为显示或者隐藏
        all.forEach(item -> {
            if (!hidden.contains(item) && !order.contains(item)) {
                if (bornToShow) {
                    order.add(item);
                } else {
                    hidden.add(item);
                }
            }
        });
        return order;
    }

    //    转移动端表格组件中的字段从上到下，从左到右排列在基本信息组件后
    public void syncFieldsFromFormTable(LayoutExt mobileLayout) {
        if (CollectionUtils.empty(getFormTables())) {
            return;
        }
        List<IFormField> orderFormList = getOrderLayoutFieldList(false);
        if (Objects.isNull(mobileLayout)) {
            getFormComponent().flatMap(FormComponentExt::getFieldSection).ifPresent(fieldSection -> fieldSection.addFields(orderFormList));
        } else {
            mobileLayout.getFormComponent().flatMap(FormComponentExt::getFieldSection).ifPresent(fieldSection -> fieldSection.addFields(orderFormList));
        }

    }

    /**
     * 新建数据时的从对象字段列表和详情页从对象字段列表字段顺序处理逻辑
     * 将表单表格中的字段按照布局中顺序进行排列
     * 移动端和表格组件为空的布局不走排序逻辑，布局类型是新建编辑页的不走字段排序逻辑
     */
    public void syncFieldsFromFormTableByLayoutOrder(String layoutType, boolean isReplaceFormTable) {
        if (LayoutContext.isMobileLayout() || CollectionUtils.empty(getFormTables())) {
            return;
        }
        if (!isReplaceFormTable && (LayoutTypes.ADD.equals(layoutType) || LayoutTypes.EDIT.equals(layoutType))) {
            return;
        }
        //将表格中的字段放入新的fieldSection中
        List<IFieldSection> orderFormTableFieldList = getOrderLayoutFieldList();
        if (CollectionUtils.empty(orderFormTableFieldList)) {
            return;
        }
        //替换组件中之前的fieldSection
        if (getFormComponent().isPresent()) {
            FormComponentExt formComponent = getFormComponent().get();
            formComponent.setFieldSections(orderFormTableFieldList);
            List<IComponent> components = getComponentsSilently();
            components.removeIf(x -> ComponentExt.of(x).isFormType() && !StringUtils.equals(x.getName(), formComponent.getName()));
            setComponents(components);
        }
    }

    public void initButtonStyle(String tenantId, int exposedNum) {
        IButtonStyleInfo buttonStyleInfo = new ButtonStyleInfo();
        buttonStyleInfo.setExposedNum(exposedNum);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MOBILE_BUTTON_MIX_GRAY, tenantId)) {
            buttonStyleInfo.setButtonAlign(IButtonStyleInfo.RIGHT);
            buttonStyleInfo.setEditButtonLocation(IButtonStyleInfo.TOP);
        } else {
            buttonStyleInfo.setDisplayMode(IButtonStyleInfo.ICON);
        }
        buttonStyleInfo.setHighlightBtn(Lists.newArrayList());
        setButtonStyle(buttonStyleInfo);
    }

    public void setButtonStyle(IButtonStyleInfo buttonStyleInfo) {
        set(BUTTON_STYLE, Document.parse(buttonStyleInfo.toJsonString()));
    }

    public IButtonStyleInfo getButtonStyle() {
        Map map = (Map) this.get(BUTTON_STYLE);
        if (map == null) {
            return new ButtonStyleInfo();
        }
        return new ButtonStyleInfo(map);
    }


    public static void fillNameComponent(LayoutExt mobileLayout, LayoutExt webLayout, List<IComponent> components) {
        if (!mobileLayout.isNewLayout()) {
            return;
        }
        if (mobileLayout.getHiddenComponents().contains(ComponentExt.NAME_COMPONENT)) {
            return;
        }
        List<String> componentApiNames = components.stream().map(IComponent::getName).collect(Collectors.toList());

        //如果当前布局的组件中存在主属性组件，则给主属性组件中补card_layout
        if (componentApiNames.contains(NAME_COMPONENT)) {
            Optional<IComponent> headInfoComponent = webLayout.getHeadInfoComponent();
            Map cardLayout = headInfoComponent.map(x -> x.get("card_layout", Map.class)).orElse(null);
            if (CollectionUtils.notEmpty(cardLayout)) {
                components.stream().filter(x -> NAME_COMPONENT.equals(x.getName())).findFirst().ifPresent(x -> x.set("card_layout", cardLayout));
            }
            return;
        }
        //如果不存在则补一个主属性组件
        components.add(LayoutComponents.buildNameComponent(webLayout));
        if (mobileLayout.isV3Layout()) {
            List<Map> rows = (List<Map>) mobileLayout.getLayoutStructure().get(LayoutStructure.LAYOUT);
            List<String> firstColumn = ((List<List<String>>) rows.get(rows.size() - 1).get(COMPONENTS)).get(0);
            firstColumn.add(0, ComponentExt.NAME_COMPONENT);
        } else {
            List<Map> leftStructure = LayoutStructure.getLeftStructure(mobileLayout);
            leftStructure.add(0, LayoutStructure.convertToMap(NAME_COMPONENT));
        }
    }


    /**
     * 将web端布局header_info组件中的cardLayout同步到移动端的name_component组件中
     *
     * @param webLayout
     */
    public void syncCardLayout(LayoutExt webLayout) {
        Map cardLayout = webLayout.getHeadInfoComponent().map(x -> x.get("card_layout", Map.class)).orElse(null);
        if (CollectionUtils.notEmpty(cardLayout)) {
            getNameComponent().ifPresent(x -> ComponentExt.of(x).setCardLayout(cardLayout));
        }
    }

    public static String getRelatedObjectComponentApiName(RelatedObjectDescribeStructure relatedObjectDescribeStructure) {
        return String.format("%s_%s_related_list", relatedObjectDescribeStructure.getRelatedObjectDescribe().getApiName(), relatedObjectDescribeStructure.getFieldApiName());
    }

    public static String getRelatedListIssueKey(RelatedObjectDescribeStructure relatedObjectDescribeStructure) {
        return getRelatedListIssueKey(relatedObjectDescribeStructure.getRelatedObjectDescribe().getApiName(),
                relatedObjectDescribeStructure.getFieldApiName());
    }

    public static String getRelatedListIssueKey(String objectApiName, String fieldApiName) {
        return String.format("%s_%s|related_list_issue", objectApiName, fieldApiName);
    }

    public String getReferenceFieldConfig() {
        if (!isDetailLayout()) {
            return null;
        }
        Optional<FormComponentExt> formComponent = getFormComponent();
        return formComponent.map(formComponentExt -> FormComponentExt.of(formComponentExt).getReferenceFieldConfig()).orElse(null);
    }

    public void removeRelatedListComponents() {
        if (!isDetailLayout() && !isEditLayout()) {
            return;
        }
        if (isDetailLayout()) {
            removeComponents(component -> ComponentExt.of(component).isRelatedList());
            if (isEnableMobileLayout()) {
                LayoutExt.of(getMobileLayout()).removeComponents(component -> ComponentExt.of(component).isRelatedList());
            }
        }
        if (isEditLayout()) {
            removeComponents(component -> ComponentExt.of(component).isRelatedListForm());
            if (isEnableMobileLayout()) {
                LayoutExt.of(getMobileLayout()).removeComponents(component -> ComponentExt.of(component).isRelatedListForm());
            }
        }
        //将不在components中的组件从layout_structure里清理掉
        LayoutStructure.clearLayoutStructureByComponents(this, getComponentsSilently());
        if (isEnableMobileLayout()) {
            LayoutExt mobileLayoutExt = LayoutExt.of(this.getMobileLayout());
            LayoutStructure.clearLayoutStructureByComponents(mobileLayoutExt, mobileLayoutExt.getComponentsSilently());
        }
    }

    private void removeComponents(Predicate<IComponent> condition) {
        List<String> needToDelComponents = getComponentsSilently()
                .stream()
                .filter(condition)
                .map(IComponent::getName)
                .distinct()
                .collect(Collectors.toList());
        removeComponents(needToDelComponents);
    }

    public static void processComponentNames(List<List<String>> sourceComponentNamesList, List<String> componentNamesRemoved, List<String> addComponentNames) {
        CollectionUtils.nullToEmpty(sourceComponentNamesList).forEach(componentNames -> {
            componentNames.removeIf(componentNamesRemoved::contains);
            if (CollectionUtils.nullToEmpty(componentNames).contains(ComponentExt.FORM_COMPONENT)) {
                componentNames.removeIf(x -> addComponentNames.contains(x) && !ComponentExt.FORM_COMPONENT.equals(x));
                int index = componentNames.indexOf(ComponentExt.FORM_COMPONENT);
                componentNames.remove(index);
                componentNames.addAll(index, addComponentNames);
            }
        });
    }

    public void removeTopInfoComponentFields(List<String> needRemovedFieldApiNames) {
        if (CollectionUtils.empty(needRemovedFieldApiNames)) {
            return;
        }
        Predicate<IFormField> formFieldPredicate = fieldSection -> !needRemovedFieldApiNames.contains(fieldSection.getFieldName());
        retainTopInfoComponentFieldsByPredicate(formFieldPredicate);
    }

    public void retainTopInfoComponentFieldsByPredicate(Predicate<IFormField> formFieldPredicate) {
        getNewTopInfoComponent().ifPresent(x -> {
            List<IFormField> formFields = x.getFieldSections().stream()
                    .filter(formFieldPredicate)
                    .collect(Collectors.toList());
            x.setFieldSections(formFields);
        });
        if (isEnableMobileLayout()) {
            LayoutExt.of(getMobileLayout()).getNewTopInfoComponent().ifPresent(x -> {
                if (ComponentExt.of(x).isDepend()) {
                    List<IFormField> formFields = x.getFieldSections().stream()
                            .filter(formFieldPredicate)
                            .collect(Collectors.toList());
                    x.setFieldSections(formFields);
                }
            });
        }
    }

    public void completeDateTimeComponentFields(IObjectDescribe objectDescribe) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        if (LayoutContext.isMobileLayout()) {
            return;
        }
        List<DateTimeRangeFieldDescribe> dateTimeRangeFields = objectDescribeExt.getDateTimeRangeFields();
        dateTimeRangeFields.forEach(dateTimeRangeField -> {
            String startTimeFieldApiName = dateTimeRangeField.getStartTimeFieldApiName();
            if (objectDescribeExt.containsField(startTimeFieldApiName)) {
                FieldLayoutPojo pojo = new FieldLayoutPojo();
                DateTimeFieldDescribe endTimeField = dateTimeRangeField.getEndTimeField(objectDescribe);
                pojo.setReadonly(FieldDescribeExt.of(endTimeField).isReadOnly());
                pojo.setRequired(endTimeField.isRequired());
                pojo.setRenderType(endTimeField.getType());
                addField(endTimeField, pojo);
            }
        });
    }

    public List<I18nInfo> getLayoutInfos() {
        return this.layout.getI18nInfoList().stream()
                .map(x -> I18nInfo.from((Map<String, Object>) x))
                .collect(Collectors.toList());
    }

    //
    public void addI18nInfo(List<I18nInfo> i18nInfos) {
        List<I18nInfo> i18nInfoList = CollectionUtils.nullToEmpty(getLayoutInfos());
        i18nInfoList.addAll(i18nInfos);
        setI18nInfos(i18nInfoList);
    }

    public String getObjApi() {
        String layoutType = getLayoutType();
        String refObjectApiName = getRefObjectApiName();
        String whatApiName = getWhatApiName();
        String objApi = LayoutTypes.TODO_TYPES.contains(layoutType) ? whatApiName : refObjectApiName;
        return StringUtils.stripToEmpty(objApi);
    }


    public void setI18nInfos(List<I18nInfo> i18nInfos) {
        this.layout.setI18nInfoList(i18nInfos.stream()
                .map(I18nInfo::toMap)
                .collect(Collectors.toList())
        );
    }

    public void removeI18n() {  // 不可以直接操作this.layout, 其get方法返回的是map的复制
        List<I18nInfo> needRemove = getLayoutInfos();
        needRemove.removeIf(x -> layoutI18nApis.contains(x.getApiName()));
        setI18nInfos(needRemove);
    }

    @Override
    public Boolean isDefault() {
        return Objects.nonNull(this.layout) && BooleanUtils.isTrue(this.layout.isDefault());
    }
}