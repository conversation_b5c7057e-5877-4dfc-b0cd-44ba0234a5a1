package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.EmployeeFieldDescribe;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * Created by zhouwr on 2018/8/22
 */
public class QuoteExt {

    @Getter
    @Delegate
    private Quote quote;

    private QuoteExt(Quote quote) {
        this.quote = quote;
    }

    public static QuoteExt of(Quote quote) {
        return new QuoteExt(quote);
    }

    public Tuple<String, String> parseQuoteField() {
        String[] parts = StringUtils.split(getQuoteField(), ".");
        return Tuple.of(StringUtils.substringBeforeLast(parts[0], "__r"), parts[1]);
    }

    public static String buildQuoteField(String lookupField, String quotedField) {
        return lookupField + "__r." + quotedField;
    }

    public static String getQuotedObjectApiName(IObjectDescribe describe, String refFieldName) {
        Optional<IFieldDescribe> fieldOptional = ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(refFieldName);
        if (!fieldOptional.isPresent()) {
            return "";
        }
        FieldDescribeExt fieldExt = FieldDescribeExt.of(fieldOptional.get());
        if (fieldExt.isMasterDetailField()) {
            MasterDetail masterDetail = fieldExt.getFieldDescribe();
            return masterDetail.getTargetApiName();
        }
        IObjectReferenceField referenceField = fieldExt.getFieldDescribe();
        return referenceField.getTargetApiName();
    }

    public boolean usingLastModifiedTime() {
        return Objects.equals(parseQuoteField().getValue(), DBRecord.LAST_MODIFIED_TIME);
    }

    public void syncSingleFlag(IFieldDescribe fieldDescribe) {
        Optional.ofNullable(fieldDescribe.get(EmployeeFieldDescribe.IS_SINGLE, Boolean.class))
                .ifPresent(isSingle -> set(EmployeeFieldDescribe.IS_SINGLE, isSingle));

    }
}
