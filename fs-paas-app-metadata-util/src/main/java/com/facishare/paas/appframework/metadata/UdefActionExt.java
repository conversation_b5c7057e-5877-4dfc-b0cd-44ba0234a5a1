package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2019/10/11
 */
@Data
@AllArgsConstructor(staticName = "of")
public class UdefActionExt {
    public static final String PRE = "pre";
    public static final String POST = "post";
    public static final String CURRENT = "current";
    public static final String BULK = "bulk";
    public static final String REDIRECT = "redirect";
    @Delegate
    private IUdefAction udefAction;

    public void computeActionStage(IUdefButton button) {
        if (Strings.isNullOrEmpty(getStage())) {
            setStage(getStageType(ButtonExt.of(button)));
        }
    }

    private String getStageType(ButtonExt buttonExt) {
        if (buttonExt.isSystemButton()) {
            if (ButtonActionType.CustomFunction.getId().equals(getActionType())) {
                return PRE;
            }
            return udefAction.getStage();
        }
        if (ButtonActionType.SendCRMRemind.getId().equals(getActionType())
                || ButtonActionType.SendMail.getId().equals(getActionType())) {
            return POST;
        }
        return CURRENT;
    }

    public static Map<String, List<IUdefAction>> getActionMapForStage(List<IUdefAction> actionList) {
        Map<String, List<IUdefAction>> actionMap = Maps.newHashMap();
        actionList.forEach(action -> {
            final String stage = String.valueOf(action.getStage());
            actionMap.computeIfAbsent(stage,x -> Lists.newArrayList()).add(action);
        });
        return actionMap;
    }

    private static Map<String, List<IUdefAction>> getActionMapForType(List<IUdefAction> actionList) {
        Map<String, List<IUdefAction>> actionMap = Maps.newHashMap();
        actionList.forEach(action -> {
            final String type = String.valueOf(action.getActionType());
            actionMap.computeIfAbsent(type, x -> Lists.newArrayList()).add(action);
        });
        return actionMap;
    }

    public boolean isCustomFunctionAction() {
        return IAcionType.CUSTOM_FUNCTION.equals(getActionType());
    }

    public boolean isCustomBizAction() {
        return IAcionType.CUSTOM_BIZ.equals(getActionType());
    }

    public boolean isFunctionAction() {
        return isCustomFunctionAction() || IAcionType.UI_EVENT.equals(getActionType());
    }
}
