package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.button.ButtonDefineType;
import com.facishare.paas.appframework.metadata.button.ButtonType;
import com.facishare.paas.appframework.metadata.button.DefaultButtonAction;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig;
import com.facishare.paas.appframework.metadata.config.UdefButtonConfig;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.util.ObjectAction.*;
import static com.facishare.paas.appframework.metadata.layout.component.ListComponentExt.IS_EXPOSED_BUTTON;

/**
 * Created by linqy on 2018/01/11
 */
@Slf4j
public class ButtonExt {

    private static final String defaultButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"\",\n" +
            "                \"api_name\": \"\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"\",\n" +
            "                \"button_type\": \"common\",\n" +
            "                \"use_pages\": \"\",\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";
    @Getter
    @Delegate
    private IUdefButton button;

    public static final String ACTION_SOURCE_FLOW = "flow";

    public static final Set<String> IMPORT_EXPORT_BUTTON_NAME = ImmutableSet.of(BATCH_IMPORT.getDefaultButtonApiName(),
            BATCH_EXPORT.getDefaultButtonApiName(), EXPORT_FILE.getDefaultButtonApiName());

    public static final Set<ObjectAction> CREATE_UPDATE_ACTIONS = ImmutableSet.of(CREATE, UPDATE, INTELLIGENTFORM);
    public static final Set<String> SLAVE_CREATE_WITH_MASTER_BLACK_LIST_ACTIONS = ImmutableSet.of(LOCK.getButtonApiName(),
            UNLOCK.getButtonApiName(), INVALID.getButtonApiName(), CLONE.getButtonApiName(), RECOVER.getDefaultButtonApiName());
    public static final Set<String> SLAVE_BLACK_LIST_ACTIONS = ImmutableSet.of(CHANGE_OWNER.getButtonApiName(),
            DELETE_TEAM_MEMBER.getButtonApiName(), EDIT_TEAM_MEMBER.getButtonApiName(),
            ADD_TEAM_MEMBER.getButtonApiName(), RECOVER.getButtonApiName(), START_STAGE_PROPELLOR.getDefaultButtonApiName());
    public static final Set<String> PERSONNELOBJ_SHOW_BUTTON_SET = ImmutableSet.of(BulkStop.getButtonApiName(), UPDATE.getButtonApiName(), LOCK.getButtonApiName(), UNLOCK.getButtonApiName());
    public static final Set<String> SLAVE_HIDDEN_ACTIONS = ImmutableSet.of(INVALID.getButtonApiName(),
            LOCK.getButtonApiName(), UNLOCK.getButtonApiName());
    public static final Set<String> PARTNER_BUTTON_BLACK_LIST_IN_DESIGNER = ImmutableSet.of(CHANGE_PARTNER.getButtonApiName(),
            CHANGE_PARTNER_OWNER.getButtonApiName(), DELETE_PARTNER.getButtonApiName(), TRANSFER_PARTNER.getButtonApiName());

    public static final Set<String> TEAM_MEMBER_BUTTON_API_NAME = ImmutableSet.of(ADD_TEAM_MEMBER.getButtonApiName(),
            EDIT_TEAM_MEMBER.getButtonApiName(), DELETE_TEAM_MEMBER.getButtonApiName());

    public static final Set<String> LEADS_CONVERT_BUTTON_API_NAME = ImmutableSet.of("button_convertNewOpportunityObj__c",
            "button_convertOpportunity__c", "button_convertContact__c", "button_convertAccount__c",
            "button_convertPartnerContact__c", "button_convertPartner__c");

    public static final String DEFAULT_ADD_BUTTON_API_NAME = CREATE.getButtonApiName();
    public static final String DEFAULT_EDIT_BUTTON_API_NAME = UPDATE.getButtonApiName();
    public static final String DEFAULT_TRANSFER_BUTTON_API_NAME = TRANSFER.getButtonApiName();

    public static final String DEFAULT_IMPORT_BUTTON_API_NAME = "Import_button_default";
    public static final String COMMON_BUTTON_TYPE = "common";

    public static final String QIXIN_CHAT_GROUP_ACTION = "QixinChatGroup";
    public static final String QIXIN_CHAT_GROUP_API_NAME = "Qixin_chat_group";

    public static final Set<String> DEFAULT_BUTTON_API_NAME_LIST = ImmutableSet.of(DEFAULT_EDIT_BUTTON_API_NAME, DEFAULT_ADD_BUTTON_API_NAME);

    private static final List<String> SUPPORT_DEFAULT_BUTTON_SFA_OBJECT_LIST = Collections.unmodifiableList(Lists.newArrayList(
            ObjectAPINameMapping.SalesOrder.getApiName(),
            ObjectAPINameMapping.Account.getApiName(),
            Utils.CONTACT_API_NAME,
            ObjectAPINameMapping.Opportunity.getApiName(),
            ObjectAPINameMapping.Leads.getApiName(),
            //ObjectAPINameMapping.ReturnedGoodsInvoice.getApiName(),
            ObjectAPINameMapping.SalesOrderProduct.getApiName(),
            ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName()


    ));
    // 支持锁定展示的 系统按钮
    private static final Set<String> SHOW_WHEN_LOCK_BUTTON = ImmutableSet.of(ObjectAction.ALLOCATE_MAIN_DATA.getButtonApiName(), ObjectAction.CHANGE_STATES.getButtonApiName());

    private static final String defaultAddButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_add_button_id\",\n" +
            "                \"api_name\": \"Add_button_default\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + CREATE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"redirect\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.ListNormal.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultEditButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_edit_button_id\",\n" +
            "                \"api_name\": \"Edit_button_default\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + UPDATE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"redirect\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Detail.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultAddSaveButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_add_save_button_id\",\n" +
            "                \"api_name\": \"" + CREATE_SAVE.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + CREATE_SAVE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Create.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultEditSaveButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_edit_save_button_id\",\n" +
            "                \"api_name\": \"" + UPDATE_SAVE.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + UPDATE_SAVE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Edit.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultRecoverButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_edit_save_button_id\",\n" +
            "                \"api_name\": \"" + RECOVER.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + RECOVER.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.RecycleBin.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultChangeButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_change_button_id\",\n" +
            "                \"api_name\": \"" + CHANGE.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + CHANGE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.REDIRECT.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Detail.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";
    //language=JSON
    private static final String defaultEffectiveButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_effective_button_id\",\n" +
            "                \"api_name\": \"" + EFFECTIVE.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + EFFECTIVE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Detail.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultTransFormButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_transform_button_id\",\n" +
            "                \"api_name\": \"" + TRANSFORM.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + TRANSFORM.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.TRANSFORM.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Detail.getId() + "\", \"" + ButtonUsePageType.DataList.getId() + "\", \"" + ButtonUsePageType.ListBatch.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultReferenceCreatebuttonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_reference_create_button_id\",\n" +
            "                \"api_name\": \"" + REFERENCE_CREATE.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + REFERENCE_CREATE.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.TRANSFORM.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.ListNormal.getId() + "\", \"" + ButtonUsePageType.Create.getId() + "\", \"" + ButtonUsePageType.Edit.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultStartBpmButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_start_bpm_button_id\",\n" +
            "                \"api_name\": \"" + START_BPM.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + START_BPM.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Detail.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    private static final String defaultStartStagePropellorButtonJson = "{\n" +
            "                \"wheres\": [],\n" +
            "                \"param_form\": [],\n" +
            "                \"actions\": [],\n" +
            "                \"_id\": \"standard_start_stage_propellor_button_id\",\n" +
            "                \"api_name\": \"" + START_STAGE_PROPELLOR.getButtonApiName() + "\",\n" +
            "                \"description\": \"\",\n" +
            "                \"label\": \"" + START_STAGE_PROPELLOR.getI18NKey() + "\",\n" +
            "                \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "                \"use_pages\": [\"" + ButtonUsePageType.Detail.getId() + "\"],\n" +
            "                \"is_active\": true,\n" +
            "                \"is_deleted\": false,\n" +
            "                \"version\": 1\n" +
            "            }";

    public static final String defaultFollowButton = "{\n" +
            "    \"wheres\": [],\n" +
            "    \"param_form\": [],\n" +
            "    \"actions\": [],\n" +
            "    \"_id\": \"standard_follow_button_id\",\n" +
            "    \"api_name\": \"" + FOLLOW.getButtonApiName() + "\",\n" +
            "    \"description\": \"\",\n" +
            "    \"label\": \"" + FOLLOW.getI18NKey() + "\",\n" +
            "    \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "    \"use_pages\": [\n" +
            "        \"" + ButtonUsePageType.Detail.getId() + "\",\n" +
            "        \"" + ButtonUsePageType.ListBatch.getId() + "\"\n" +
            "    ],\n" +
            "    \"is_active\": true,\n" +
            "    \"is_deleted\": false,\n" +
            "    \"version\": 1\n" +
            "}";


    public static final String defaultUnfollowButton = "{\n" +
            "    \"wheres\": [],\n" +
            "    \"param_form\": [],\n" +
            "    \"actions\": [],\n" +
            "    \"_id\": \"standard_unfollow_button_id\",\n" +
            "    \"api_name\": \"" + UNFOLLOW.getButtonApiName() + "\",\n" +
            "    \"description\": \"\",\n" +
            "    \"label\": \"" + UNFOLLOW.getI18NKey() + "\",\n" +
            "    \"button_type\": \"" + ButtonType.COMMON.getId() + "\",\n" +
            "    \"use_pages\": [\n" +
            "        \"" + ButtonUsePageType.Detail.getId() + "\",\n" +
            "        \"" + ButtonUsePageType.ListBatch.getId() + "\"\n" +
            "    ],\n" +
            "    \"is_active\": true,\n" +
            "    \"is_deleted\": false,\n" +
            "    \"version\": 1\n" +
            "}";


    //跳转类型的Button 是UIPAAS 还是跳转URL
    public static final String REDIRECT_UIPAAS = "uipaas";
    public static final String REDIRECT_URL = "url";
    public static final String REDIRECT_AI_AGENT = "ai_agent";

    public static final Set<String> ADD_EDIT_BUTTON_API_NAME = ImmutableSet.of(CREATE.getButtonApiName(),
            UPDATE.getButtonApiName(), CREATE_SAVE.getButtonApiName(),
            UPDATE_SAVE.getButtonApiName());

    private ButtonExt(IUdefButton button) {
        if (button instanceof ButtonExt) {
            this.button = ((ButtonExt) button).getButton();
        } else {
            this.button = button;
        }
    }

    public static ButtonExt of(IUdefButton button) {
        return new ButtonExt(button);
    }

    public static ButtonExt of(Map button) {
        return new ButtonExt(new UdefButton(button));
    }

    public static IUdefButton getDefaultButtonByApiName(String buttonApiName, String describeApiName) {
        IUdefButton button = DefaultButtonAction.getByButtonApiName(buttonApiName).getButton();
        button.setDescribeApiName(describeApiName);
        return button;
    }

    public static IUdefButton generateSystemButton() {
        UdefButton button = new UdefButton();
        button.fromJsonString(defaultButtonJson);
        button.setDefineType("system");
        return button;
    }

    public static IUdefButton convertAndGenerateButton(String buttonApiName, IObjectDescribe objectDescribe, User user) {
        if (!ADD_EDIT_BUTTON_API_NAME.contains(buttonApiName)
                && !RECOVER.getButtonApiName().equals(buttonApiName)
                && !EFFECTIVE.getButtonApiName().equals(buttonApiName)
                && !CHANGE.getButtonApiName().equals(buttonApiName)
                && !TRANSFORM.getButtonApiName().equals(buttonApiName)
                && !REFERENCE_CREATE.getButtonApiName().equals(buttonApiName)
                && !START_BPM.getButtonApiName().equals(buttonApiName)
                && !START_STAGE_PROPELLOR.getButtonApiName().equals(buttonApiName)
                && !FOLLOW.getButtonApiName().equals(buttonApiName)
                && !UNFOLLOW.getButtonApiName().equals(buttonApiName)
        ) {
            return null;
        }
        return generateButton(objectDescribe, buttonApiName, user);
    }

    private static IUdefButton generateButton(IObjectDescribe objectDescribe, String buttonApiName, User user) {
        switch (getByButtonApiName(buttonApiName)) {
            case CREATE:
                return generateStandardAddButton(user, objectDescribe);
            case UPDATE:
                return generateStandardEditButton(user, objectDescribe);
            case CREATE_SAVE:
                return generateStandardAddSaveButton(user, objectDescribe);
            case UPDATE_SAVE:
                return generateStandardEditSaveButton(user, objectDescribe);
            case RECOVER:
                return generateStandardRecoverButton(user, objectDescribe);
            case CHANGE:
                return generateStandardChangeButton(user, objectDescribe);
            case EFFECTIVE:
                return generateStandardEffectiveButton(user, objectDescribe);
            case TRANSFORM:
                return generateStandardTransFormButton(user, objectDescribe);
            case REFERENCE_CREATE:
                return generateStandardReferenceCreateButton(user, objectDescribe);
            case START_BPM:
                return generateStandardStartBpmButton(user, objectDescribe);
            case START_STAGE_PROPELLOR:
                return generateStandardStartStagePropellorButton(user, objectDescribe);
            case FOLLOW:
                return generateStandardFollowButton(user, objectDescribe);
            case UNFOLLOW:
                return generateStandardUnfollowButton(user, objectDescribe);
            default:
                return null;
        }
    }


    public Map<String, Object> toMap() {
        return this.convert().getContainerDocument();
    }

    private UdefButton convert() {
        return (UdefButton) button;
    }

    public static IUdefButton generateStandardAddButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultAddButtonJson, objectDescribe);
    }

    public static IUdefButton generateStandardAddSaveButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultAddSaveButtonJson, objectDescribe);
    }

    private static IUdefButton generateSystemButton(User user, String defaultAddButtonJson, IObjectDescribe objectDescribe) {
        UdefButton button = new UdefButton();
        button.fromJsonString(defaultAddButtonJson);
        button.setTenantId(user.getTenantId());
        button.setCreatedBy(objectDescribe.getCreatedBy());
        button.setLastModifiedBy(objectDescribe.getCreatedBy());
        button.setCreateTime(objectDescribe.getCreateTime());
        button.setLastModifiedTime(objectDescribe.getCreateTime());
        button.setDescribeApiName(objectDescribe.getApiName());
        button.set("define_type", "system");
        //国际化label
        button.setLabel(I18N.text(button.getLabel()));
        button.setId(IdGenerator.get());
        return button;
    }

    public static IUdefButton generateStandardEditButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultEditButtonJson, objectDescribe);
    }

    public static IUdefButton generateStandardEditSaveButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultEditSaveButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardRecoverButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultRecoverButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardChangeButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultChangeButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardEffectiveButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultEffectiveButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardReferenceCreateButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultReferenceCreatebuttonJson, objectDescribe);
    }

    private static IUdefButton generateStandardTransFormButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultTransFormButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardStartBpmButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultStartBpmButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardStartStagePropellorButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultStartStagePropellorButtonJson, objectDescribe);
    }

    private static IUdefButton generateStandardFollowButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultFollowButton, objectDescribe);
    }

    private static IUdefButton generateStandardUnfollowButton(User user, IObjectDescribe objectDescribe) {
        return generateSystemButton(user, defaultUnfollowButton, objectDescribe);
    }

    public static IUdefButton getDefaultButton(User user, String buttonApiName, IObjectDescribe objectDescribe) {
        return generateButton(objectDescribe, buttonApiName, user);
    }

    public boolean isSystemButton() {
        return ButtonDefineType.SYSTEM.getId().equals(getDefineType());
    }

    public boolean isNotEditableButton() {
        return isSystemButton() || ButtonType.REDIRECT.getId().equals(getButtonType());
    }

    public static Button generateQinxinGroupButton() {
        Button button = new Button();
        button.setAction("QixinChatGroup");
        button.setName("Qixin_chat_group");
        button.setLabel(I18N.text(I18NKey.COMPANY_MESSAGE_GROUP));
        return button;
    }

    public IButton toButton() {
        IButton button = convertToButton();
        button.setName(getApiName());
        button.setLabel(getLabel());
        button.setActionType(getActionType());
        button.setAction(getButtonActionCode());
        return button;
    }

    private IButton convertToButton() {
        if (!isSystemButton()) {
            return new Button(toMap());
        }

        if (ButtonConfig.isDefaultButtonParamGray(getDescribeApiName(), getApiName(), RequestContextManager.getContext().getTenantId())) {
            return new Button(toMap());
        }

        return new Button();
    }

    public String getActionType() {
        if (StringUtils.endsWith(getApiName(), "__c") && !Objects.equals(getDefineType(), IObjectDescribe.DEFINE_TYPE_CUSTOM)) {
            //服务通预置了自定义按钮，define_type不是custom，且__c结尾，这时移动端判断actionType不是custom，
            // 就不走自定义按钮的逻辑，导致有问题
            return IButton.ACTION_TYPE_CUSTOM;
        }
        return Objects.isNull(getDefineType()) ? IButton.ACTION_TYPE_CUSTOM : getDefineType();
    }

    private String getButtonActionCode() {
        if (isSystemButton() && getByButtonApiName(getApiName()) != UNKNOWN_ACTION) {
            return getByButtonApiName(getApiName()).getActionCode();
        }
        return getApiName();
    }

    public void setAction(String actionCode) {
        set("action", actionCode);
    }

    public String getActionCode() {
        return get("action_code", String.class);
    }

    public void setActionCode(String actionCode) {
        set("action_code", actionCode);
    }

    public IUdefButtonConfig generateButtonConfig() {
        if (isConvert()) {
            // 先尝试获取配置文件中的配置
            IUdefButtonConfig config = ButtonConfig.getDefineConfig(getDescribeApiName(), getApiName());
            if (config != null) {
                return config;
            }
            // 如果没有配置,使用默认的转换按钮配置
            return UdefButtonConfig.generateConvertButtonConfig(getApiName(), getDescribeApiName());
        }
        if (!isSystemButton()) {
            return null;
        }
        IUdefButtonConfig config = ButtonConfig.getConfig(getDescribeApiName(), getApiName());
        if (isNotEditableButton()) {
            config.notEnable();
        }
        return config;
    }


    public IUdefButtonConfig generateButtonConfig(IObjectDescribe describe) {
        IUdefButtonConfig buttonConfig = generateButtonConfig();
        if (Objects.isNull(buttonConfig)) {
            return null;
        }
        buttonConfig.handleByDescribe(describe);
        return buttonConfig;
    }

    public static List<IUdefButtonConfig> findButtonConfig(List<IUdefButton> buttonList) {
        return buttonList.stream()
                .map(ButtonExt::of)
                .map(ButtonExt::generateButtonConfig)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<Wheres> handleWheres(IObjectDescribe describe) {
        List<Wheres> wheres = getWheres();
        //如果字段被禁用，按钮的条件不生效
        return WheresExt.handleWheresByDescribe(wheres, describe);
    }

    public void addStageActions(String actionId, String stage) {
        List<Map> stageActions = getStageActions();
        HashMap<String, Object> stageAction = Maps.newHashMap();
        stageAction.put("action_id", actionId);
        stageAction.put("stage", stage);
        stageActions.add(stageAction);
    }

    public List<Map> getStageActions() {
        List<Map> stageActions = get("stage_actions", List.class);
        if (CollectionUtils.empty(stageActions)) {
            List<Map> result = Lists.newArrayList();
            set("stage_actions", result);
            return result;
        }
        return stageActions;
    }

    public String getStage(String actionType) {
        if (isSystemButton()) {
            if (ButtonActionType.CustomFunction.getId().equals(actionType)) {
                return "pre";
            }
            return null;
        }
        if (ButtonActionType.SendCRMRemind.getId().equals(actionType) || ButtonActionType.SendMail.getId().equals(actionType)) {
            return "post";
        }
        return "current";
    }

    public static List<IUdefButton> filterButtonsWheres(IObjectData objectData, IObjectDescribe describe, List<IUdefButton> buttons) {
        if (CollectionUtils.empty(buttons)) {
            return Lists.newArrayList();
        }
        return buttons.stream().map(x -> ButtonExt.of(x).filterButtonByWheres(objectData, describe)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<IUdefButton> filterButtonsWheres(IObjectData objectData, IObjectDescribe describe, List<IUdefButton> buttons,
                                                        Map<String, List<Wheres>> wheresMap) {
        if (CollectionUtils.empty(buttons)) {
            return Lists.newArrayList();
        }
        return buttons.stream().map(x -> ButtonExt.of(x).filterButtonByWheres(objectData, describe, wheresMap.get(x.getApiName())))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public IUdefButton filterButtonByWheres(IObjectData objectData, IObjectDescribe describe) {
        List<Wheres> wheres = handleWheres(describe);
        return filterButtonByWheres(objectData, describe, wheres);
    }

    /**
     * @param objectData 数据
     * @param describe   描述
     * @return true 符合条件 false 不符合条件
     */
    public boolean checkButtonConditions(IObjectData objectData, IObjectDescribe describe) {
        return Objects.nonNull(filterButtonByWheres(objectData, describe));
    }

    public IUdefButton filterButtonByWheres(IObjectData objectData, IObjectDescribe describe, List<Wheres> wheres) {
        if (!isActive()) {
            return null;
        }
        if (Objects.isNull(objectData)) {
            return getButton();
        }
        String describeApiName = describe.getApiName();
        if (!getDescribeApiName().equals(describeApiName)) {
            return null;
        }
        if (!isActive() || isDeleted()) {
            return null;
        }
        if (CollectionUtils.empty(wheres)) {
            return getButton();
        }
        if (!buttonCan(objectData, describe, wheres)) {
            return null;
        }
        return getButton();
    }

    private boolean buttonCan(IObjectData objectData, IObjectDescribe describe, List<Wheres> wheres) {
        IFilter filter = FilterExt.of(Operator.EQ, IObjectData.ID, objectData.getId()).getFilter();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(wheres, filter);
        try {
            ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                    .describeExt(ObjectDescribeExt.of(describe))
                    .queryExt(queryExt)
                    .filterLabel(I18N.text(I18NKey.CUSTOM_BUTTON) + describe.getDisplayName() + "." + getLabel())
                    .build();
            return CollectionUtils.notEmpty(dataFilter.doFilter(Lists.newArrayList(objectData)));
        } catch (Exception e) {
            log.warn("error in parallelFilterButtons, describeApiName:{},dataId:{},button:{}", describe.getApiName(),
                    objectData.getId(), this, e);
        }
        return false;
    }

    public void handleButtonParam(User user, IObjectData objectData, IObjectDescribe describe, Map<String, IObjectFieldExtra> fieldExtraMap, Set<String> readonlyFields) {
        handleButtonParam(user, objectData, describe, fieldExtraMap, readonlyFields, true);
    }

    public void handleButtonParam(User user, IObjectData objectData, IObjectDescribe describe, Map<String, IObjectFieldExtra> fieldExtraMap, Set<String> readonlyFields, boolean needAddExtPropertyWithExt) {
        //人员（多选）、部门（多选）、查找关联（多选）最多选择数量
        Map<String, IObjectDescribe> describeExtMap = Maps.newHashMap();
        if (needAddExtPropertyWithExt) {
            ObjectDescribeExt.of(describe).addExtPropertyWithExt(describeExtMap);
        }
        IObjectDescribe describeExt = describeExtMap.get(describe.getApiName());

        List<IParamForm> paramForms = ParamForm.fromList(getParamForm()).stream()
                .map(it -> (ParamForm) it)
                .filter(paramForm -> !isOuterEmpOrDept(user, paramForm))
                .peek(paramForm -> handleParamFormDescribe(user, objectData, describe, describeExt, fieldExtraMap, readonlyFields, paramForm))
                .collect(Collectors.toList());
        setParamForm(ParamForm.toParamMap(paramForms));
    }

    private boolean isOuterEmpOrDept(User user, ParamForm paramForm) {
        return user.isOutUser()
                && (IFieldType.EMPLOYEE.equals(paramForm.getType()) || IFieldType.DEPARTMENT.equals(paramForm.getType())
                || IFieldType.EMPLOYEE_MANY.equals(paramForm.getType()) || IFieldType.DEPARTMENT_MANY.equals(paramForm.getType()));
    }

    private void handleParamFormDescribe(User user, IObjectData objectData, IObjectDescribe describe, IObjectDescribe describeExt,
                                         Map<String, IObjectFieldExtra> fieldExtraMap, Set<String> readonlyFields, ParamForm paramForm) {
        if (!Objects.equals(paramForm.getObjectApiName(), describe.getApiName())) {
            return;
        }

        ObjectDescribeExt.of(describe).getFieldDescribeSilently(paramForm.convertToFieldApiName())
                .ifPresent(fieldDescribe -> {
                    FieldDescribeExt fieldDescribeExt;
                    if (describeExt != null && describeExt.containsField(fieldDescribe.getApiName())) {
                        fieldDescribeExt = FieldDescribeExt.of(describeExt.getFieldDescribe(fieldDescribe.getApiName()).copy());
                    } else {
                        fieldDescribeExt = FieldDescribeExt.of(fieldDescribe.copy());
                    }
                    //合并字段扩展描述
                    if (CollectionUtils.notEmpty(fieldExtraMap)) {
                        IObjectFieldExtra fieldExtra = fieldExtraMap.get(paramForm.convertToFieldApiName());
                        fieldDescribeExt.mergeFromObjectFieldExtra(fieldExtra);
                    }
                    // 设置只读
                    fieldDescribeExt.setReadOnly(readonlyFields.contains(fieldDescribe.getApiName()));
                    // 移除三角关系等
                    fieldDescribeExt.removeRelatedObjectFilter(user);
                    // 合并字段描述
                    paramForm.mergeFromFieldDescribe(fieldDescribeExt);
                    // 设置默认值
                    if (Objects.nonNull(objectData)) {
                        paramForm.set(IFieldDescribe.DEFAULT_VALUE, objectData.get(paramForm.convertToFieldApiName()));
                    }
                });
    }

    public static void filterButtonsByBlacklist(List<IButton> buttons, String objectApiName) {
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        //根据黑名单过滤按钮
        Set<String> blackList = ButtonConfig.getObjectButtonBlacklist(objectApiName);
        buttons.removeIf(x -> blackList.contains(x.getName()));
    }

    public static void filterButtonsByBlacklist(List<IButton> buttons, boolean bigObject) {
        if (CollectionUtils.empty(buttons) || !bigObject) {
            return;
        }
        //大对象黑名单按钮过滤
        buttons.removeIf(x -> !ButtonConfig.getBigObjectSupportButtonList().contains(x.getName()));
    }

    public static void filterButtons(IObjectDescribe objectDescribe, List<IUdefButton> customButton) {
        filterButtonList(objectDescribe, customButton, IUdefButton::getApiName);
    }

    public static List<IButton> filterLayoutButtons(IObjectDescribe objectDescribe, List<IButton> buttons) {
        return filterButtonList(objectDescribe, buttons, IButton::getName);
    }

    private static <T> List<T> filterButtonList(IObjectDescribe objectDescribe, List<T> customButton,
                                                Function<T, String> function) {
        if (CollectionUtils.empty(customButton)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(objectDescribe)) {
            return customButton;
        }
        //配置了不显示从对象入口的屏蔽新建编辑按钮
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMasterAndHiddenDetailButton()) {
            customButton.removeIf(x -> ButtonExt.CREATE_UPDATE_ACTIONS.stream().anyMatch(a -> a.getDefaultButtonApiName().equals(function.apply(x))));
        }
        //680主从审批的灰度企业的主从一起新建的从去掉锁定、解锁、作废、复制、恢复按钮
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMasterAndInGrayList()) {
            customButton.removeIf(x -> ButtonExt.SLAVE_CREATE_WITH_MASTER_BLACK_LIST_ACTIONS.contains(function.apply(x)));
        }
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            customButton.removeIf(x -> ButtonExt.SLAVE_BLACK_LIST_ACTIONS.contains(function.apply(x)));
            customButton.removeIf(it -> CHANGE_PARTNER_OWNER.getButtonApiName().equals(function.apply(it)));

        }
        // 人员对象，预置按钮屏蔽新建保存按钮
        if (Utils.PERSONNEL_OBJ_API_NAME.equals(objectDescribe.getApiName())) {
            customButton.removeIf(x -> !ObjectDescribeExt.isCustomObject(function.apply(x))
                    && !PERSONNELOBJ_SHOW_BUTTON_SET.contains(function.apply(x)));
        }

        //根据黑名单过滤按钮
        Set<String> blackList = ButtonConfig.getObjectButtonBlacklist(objectDescribe.getApiName());
        customButton.removeIf(x -> blackList.contains(function.apply(x)));

        customButton.removeIf(button -> objectDescribe.isBigObject() && !ButtonConfig.getBigObjectSupportButtonList().contains(function.apply(button)));
        return customButton;
    }

    public boolean isBulkButton() {
        return isBulkButton(true);
    }

    public boolean isBulkButton(boolean filterCustom) {
        // filterCustom 为false 设计器不过滤列表页批量的自定义按钮
        if (!filterCustom && !isSystemButton()) {
            return false;
        }
        return CollectionUtils.notEmpty(getUsePages()) && getUsePages().contains(ButtonUsePageType.ListBatch.getId());
    }

    public boolean isPersonnelObjHiddenButton() {
        if (!Utils.PERSONNEL_OBJ_API_NAME.equals(getDescribeApiName())) {
            return false;
        }
        return isSystemButton() && !PERSONNELOBJ_SHOW_BUTTON_SET.contains(getApiName());
    }

    public IUdefButtonConfig generateButtonConfigByBulkConfig(IObjectDescribe describe, User user) {
        IUdefButtonConfig buttonConfig = generateButtonConfig(describe);
        //buttonConfig == null 直接return null;
        if (buttonConfig == null) {
            return null;
        }
        ButtonFilterText.getByApiNameAndActionCode(getDescribeApiName(), getByButtonApiName(getApiName()).getActionCode())
                .ifPresent(it -> buttonConfig.setFilterText(it.getFilterTest()));

        // 线索的无效和跟进中按钮支持编辑参数
        editParamForm(describe, buttonConfig, user.getTenantId());
        // 灰度名单中的企业，Add_button_default 和 Edit_button_default 不支持配置按钮后动作
        handleButtonConfig(user.getTenantId(), describe.getApiName(), buttonConfig);
        // 灰度企业，指定对象的 「新建」、「编辑」 按钮 支持 redirect 动作
        handleButtonConfigByRedirect(user.getTenantId(), describe.getApiName(), buttonConfig);

        // 开启灰度后，相应的按钮支持放开相应动作
        handleButtonConfigAction(describe.getApiName(), buttonConfig.getApiName(), getTenantId(), buttonConfig);
        return buttonConfig;
    }

    private void handleButtonConfigAction(String describeApiName, String buttonApiName, String tenantId, IUdefButtonConfig buttonConfig) {
        if (ButtonConfig.isDefaultButtonActionGray(describeApiName, buttonApiName, "current", tenantId)) {
            buttonConfig.supportCurrentAction();
        }
    }

    private void handleButtonConfigByRedirect(String tenantId, String objectApiName, IUdefButtonConfig buttonConfig) {
        if (Objects.isNull(buttonConfig)) {
            return;
        }
        // 判断是否为 新建保存、编辑保存 按钮
        if (!isAddSaveOrEditSaveButton(tenantId, objectApiName, buttonConfig.getApiName())) {
            return;
        }
        if (ButtonConfig.isButtonRedirectActionGray(tenantId, objectApiName)) {
            buttonConfig.supportRedirectAction();
        }
    }

    private boolean isAddSaveOrEditSaveButton(String tenantId, String objectApiName, String buttonApiName) {
        if (AppFrameworkConfig.isAddEditUIActionGray(tenantId, objectApiName)) {
            return CREATE_SAVE.getButtonApiName().equals(buttonApiName) || UPDATE_SAVE.getButtonApiName().equals(buttonApiName);
        } else {
            return CREATE.getButtonApiName().equals(buttonApiName) || UPDATE.getButtonApiName().equals(buttonApiName);
        }
    }

    private void handleButtonConfig(String tenantId, String objectApiName, IUdefButtonConfig buttonConfig) {
        if (Objects.isNull(buttonConfig)) {
            return;
        }
        // 开启灰度后，预置的新建、编辑按钮不可以配置后动作
        if (AppFrameworkConfig.isAddEditUIActionGray(tenantId, objectApiName)) {
            if (DEFAULT_BUTTON_API_NAME_LIST.contains(buttonConfig.getApiName())) {
                buttonConfig.unSupportPostAction();
            }

            if (DEFAULT_EDIT_BUTTON_API_NAME.equals(buttonConfig.getApiName())
                    && AppFrameworkConfig.isEditUIButtonDisplayConditions(tenantId, objectApiName)) {
                buttonConfig.supportWheres();
            }
        }
    }

    private void editParamForm(IObjectDescribe describe, IUdefButtonConfig buttonConfig, String tenantId) {
        if (buttonConfig == null) {
            return;
        }
        if (ButtonConfig.isDefaultButtonParamGray(describe.getApiName(), getApiName(), tenantId)) {
            buttonConfig.editParamForm();
        }
    }

    public boolean isConvert() {
        return ButtonType.CONVERT.getId().equals(getButtonType());
    }

    public void addFilter(List<IFilter> filters) {
        List<Wheres> wheres = getWheres();
        if (CollectionUtils.empty(wheres)) {
            Wheres newWheres = new Wheres();
            newWheres.setFilters(filters);
            setWheres(Lists.newArrayList(newWheres));
            return;
        }
        wheres.stream().map(Wheres::getFilters).forEach(x -> x.addAll(filters));
    }

    public void removeSystemFilter() {
        List<Wheres> wheres = getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        wheres.forEach(where -> where.getFilters().removeIf(filter -> FilterExt.of(filter).isSystemFilter()));
    }

    public ButtonExt fillButtonLabel(String describeApiName) {
        ObjectAction objectAction = getByButtonApiName(getApiName());
        if (objectAction == UNKNOWN_ACTION) {
            String buttonLabelKey = GetI18nKeyUtil.getUdefButtonLabelKey(describeApiName, getApiName());
            // 获取当前语言环境的翻译值
            String buttonLabel = I18NExt.getOnlyText(buttonLabelKey);
            if (!Strings.isNullOrEmpty(buttonLabel)) {
                setLabel(buttonLabel);
            }
            return this;
        }
        setLabel(objectAction.getActionLabel());
        return this;
    }

    public String getButtonI18NLabelKey(String describeApiName) {
        ObjectAction objectAction = getByButtonApiName(getApiName());
        if (objectAction == UNKNOWN_ACTION) {
            return GetI18nKeyUtil.getUdefButtonLabelKey(describeApiName, getApiName());
        }
        return objectAction.getI18NKey();
    }

    public boolean isUIAction() {
        return ButtonType.REDIRECT.getId().equals(getButtonType());
    }

    public boolean isCommonButton() {
        return ButtonType.COMMON.getId().equals(getButtonType());
    }

    public boolean isListNormalButton() {
        return CollectionUtils.nullToEmpty(getUsePages()).contains(ButtonUsePageType.ListNormal.getId());
    }

    public boolean isTransFormButton() {
        return ButtonType.TRANSFORM.getId().equals(getButtonType());
    }

    public boolean isUseInCreateOrEdit() {
        return getUsePages().stream()
                .anyMatch(it -> ButtonUsePageType.Edit.getId().equals(it) || ButtonUsePageType.Create.getId().equals(it));
    }

    public boolean isShowWhenLock() {
        // 转换按钮默认在数据锁定时展示
        if (isConvert()) {
            return true;
        }
        // 系统预置按钮默认不展示
        if (isSystemButton()) {
            return SHOW_WHEN_LOCK_BUTTON.contains(getApiName());
        }
        return BooleanUtils.isTrue(getLockDataShowButton());
    }

    public void fillIsShowWhenLock() {
        if (Objects.nonNull(getLockDataShowButton())) {
            return;
        }
        if (isSystemButton()) {
            setLockDataShowButton(null);
            return;
        }
        setLockDataShowButton(isShowWhenLock());
    }

    public void handleButtonDescribe() {
        setId(IdGenerator.get());
        if (DEFAULT_ADD_BUTTON_API_NAME.equals(getApiName())) {
            setApiName(CREATE_SAVE.getButtonApiName());
            setLabel(CREATE_SAVE.getActionLabel());
        } else if (DEFAULT_EDIT_BUTTON_API_NAME.equals(getApiName())) {
            setApiName(UPDATE_SAVE.getButtonApiName());
            setLabel(UPDATE_SAVE.getActionLabel());
        }
    }

    public void changeButtonDescribe() {
        setButtonType(ButtonType.REDIRECT.getId());
        if (DEFAULT_ADD_BUTTON_API_NAME.equals(getApiName())) {
            setUsePages(Lists.newArrayList(ButtonUsePageType.ListNormal.getId()));
        } else if (DEFAULT_EDIT_BUTTON_API_NAME.equals(getApiName())) {
            setUsePages(Lists.newArrayList(ButtonUsePageType.Detail.getId()));
        }
    }

    public void setIsExposedButton(boolean isExposed) {
        button.set(IS_EXPOSED_BUTTON, isExposed);
    }

    public IUdefButton copy() {
        if (Objects.isNull(button)) {
            return null;
        }
        return of(CopyOnWriteMap.copy(toMap())).getButton();
    }

    public void handlePrintButtonUsePage() {
        if (Objects.isNull(button)) {
            return;
        }
        if (ObjectAction.PRINT.getButtonApiName().equals(button.getApiName())) {
            List<String> usePages;
            if (RequestUtil.isMobileOrH5Request()) {
                usePages = Lists.newArrayList(ButtonUsePageType.Detail.getId());
            } else {
                usePages = Lists.newArrayList(ButtonUsePageType.Detail.getId(), ButtonUsePageType.ListBatch.getId());
            }
            button.setUsePages(usePages);
        }
    }

    public void convertDateFieldFilter2System(Supplier<IObjectDescribe> supplier) {
        Objects.requireNonNull(supplier);
        convertDateFieldFilter(supplier, FilterExt::convert2SystemZone);
    }

    public void convertDateFieldFilter2Custom(Supplier<IObjectDescribe> supplier) {
        Objects.requireNonNull(supplier);
        convertDateFieldFilter(supplier, FilterExt::convert2CustomZone);
    }

    private void convertDateFieldFilter(Supplier<IObjectDescribe> supplier, BiConsumer<ObjectDescribeExt, IFilter> biConsumer) {
        Objects.requireNonNull(supplier);
        List<Wheres> wheres = getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(supplier.get());
        CollectionUtils.nullToEmpty(wheres).stream()
                .map(Wheres::getFilters)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .forEach(filter -> biConsumer.accept(describeExt, filter));
        setWheres(wheres);
    }

    enum ButtonFilterText {
        UDOBJ_LOCK(DefObjConstants.UDOBJ, LOCK.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        UDOBJ_UNLOCK(DefObjConstants.UDOBJ, UNLOCK.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_LOCK),
        UDOBJ_CHANGE_OWNER(DefObjConstants.UDOBJ, CHANGE_OWNER.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        UDOBJ_INVALID(DefObjConstants.UDOBJ, INVALID.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        UDOBJ_CHANGE_PARTNER(DefObjConstants.UDOBJ, CHANGE_PARTNER.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        UDOBJ_CHANGE_PARTNER_OWNER(DefObjConstants.UDOBJ, CHANGE_PARTNER_OWNER.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        UDOBJ_START_STAGE_PROPELLOR(DefObjConstants.UDOBJ, START_STAGE_PROPELLOR.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),

        ACCOUNT_CHOOSE(Utils.ACCOUNT_API_NAME, CHOOSE.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_LIFE_DISTRIBUTION_STATUS_IS_NORMAL, I18NKey.BUTTON_FILTER_TEXT_HIGH_SEAS_IS_NOT_EMPTY, I18NKey.BUTTON_FILTER_TEXT_HIGH_SEAS_MEMBER),
        ACCOUNT_MOVE(Utils.ACCOUNT_API_NAME, MOVE.getActionCode()),
        ACCOUNT_RETURN(Utils.ACCOUNT_API_NAME, RETURN.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_IS_ALLOCATED),
        ACCOUNT_ALLOCATE(Utils.ACCOUNT_API_NAME, ALLOCATE.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_LIFE_STATUS_IS_NORMAL, I18NKey.BUTTON_FILTER_TEXT_HIGH_SEAS_ADMINISTRATOR),
        ACCOUNT_TAKE_BACK(Utils.ACCOUNT_API_NAME, TAKE_BACK.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_IS_ALLOCATED, I18NKey.BUTTON_FILTER_TEXT_HIGH_SEAS_IS_NOT_EMPTY, I18NKey.BUTTON_FILTER_TEXT_HIGH_SEAS_ADMINISTRATOR),

        LEADS_RETURN(Utils.LEADS_API_NAME, RETURN.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_UNASSIGNED),
        LEADS_CHOOSE(Utils.LEADS_API_NAME, CHOOSE.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_IS_UNASSIGNED, I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_IS_NOT_EMPTY, I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_MEMBER),
        LEADS_MOVE(Utils.LEADS_API_NAME, MOVE.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_IS_NOT_EMPTY),
        LEADS_ALLOCATE(Utils.LEADS_API_NAME, ALLOCATE.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_ADMINISTRATOR, I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_IS_NOT_EMPTY, I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_TRANSFORMED),
        LEADS_TAKE_BACK(Utils.LEADS_API_NAME, TAKE_BACK.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_IS_NOT_EMPTY, I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_UNASSIGNED, I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_TRANSFORMED, I18NKey.BUTTON_FILTER_TEXT_LEADS_POOL_ADMINISTRATOR),
        LEADS_COLLECT_TO(Utils.LEADS_API_NAME, COLLECT_TO.getActionCode()),
        LEADS_MarkMQL(Utils.LEADS_API_NAME, MarkMQL.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_NOT_TRANSFORMED),
        LEADS_TRANSFER(Utils.LEADS_API_NAME, TRANSFER.getActionCode(),
                I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_UN_PROCESSED, I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_PROCESSED, I18NKey.BUTTON_FILTER_TEXT_BIZ_STATUS_CLOSED),

        VISIT_ROUTE_STORE_COPY(Utils.VISIT_ROUTE_OBJ, STORE_COPY.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        VISIT_ROUTE_STORE_TRANSFER(Utils.VISIT_ROUTE_OBJ, STORE_TRANSFER.getActionCode(), I18NKey.BUTTON_FILTER_TEXT_OBJECT_LOCK_STATUS_IS_UNLOCK),
        ;
        /**
         * 对象apiName
         */
        private String describeApiName;
        /**
         * 按钮actionCode
         */
        private String actionCode;
        /**
         * 多语言的key
         */
        private List<String> textKey;

        ButtonFilterText(String describeApiName, String actionCode, String... testKey) {
            this.describeApiName = describeApiName;
            this.actionCode = actionCode;
            this.textKey = Lists.newArrayList(testKey);
        }

        public List<String> getFilterTest() {
            return textKey.stream().map(I18N::text).collect(Collectors.toList());
        }

        private static Table<String, String, ButtonFilterText> table = HashBasedTable.create();

        static {
            Stream.of(values()).forEach(it -> table.put(it.describeApiName, it.actionCode, it));
        }

        public static Optional<ButtonFilterText> getByApiNameAndActionCode(String apiName, String actionCode) {
            if (table.contains(apiName, actionCode)) {
                return Optional.ofNullable(table.get(apiName, actionCode));
            }
            return Optional.ofNullable(table.get(DefObjConstants.UDOBJ, actionCode));
        }
    }
}
