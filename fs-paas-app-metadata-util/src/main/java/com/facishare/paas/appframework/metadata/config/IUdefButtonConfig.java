package com.facishare.paas.appframework.metadata.config;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/09/18
 */
public interface IUdefButtonConfig {
    String DESCRIEB_API_NAME = "descrieb_api_name";
    String TENANT_ID = "tenant_id";
    String BUTTON = "button";
    String API_NAME = "api_name";

    String EDIT = "edit";
    String DELETE = "delete";
    String ENABLE = "enable";
    int UN_USABLE = 0;

    String CONFIG = "config";
    String FILTER_TEXT = "filter_text";

    String CONTROL_LEVEL = "controlLevel";

    String getDescribeApiName();

    void setDescribeApiName(String descriebApiName);

    String getApiName();

    void setApiName(String apiName);

    String toJson();

    int getEdit();

    void notDelete();

    void notEnable();

    int getEnable();

    Map toMap();

    IUdefButtonConfig copy();

    void handleByDescribe(IObjectDescribe describe);

    void setFilterText(List<String> helpText);

    void editParamForm();

    void unSupportPostAction();

    void supportCurrentAction();

    void supportRedirectAction();

    void supportWheres();

    void setControlLevel(String controlLevel);

}
