package com.facishare.paas.appframework.metadata.config;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.google.common.collect.Maps;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * create by zhao<PERSON> on 2019/09/18
 */
public class UdefButtonConfig extends DocumentBasedBean implements IUdefButtonConfig {

    private static final long serialVersionUID = 6201061895057093430L;

    public UdefButtonConfig() {
    }

    private UdefButtonConfig(Map map) {
        super(map);
    }

    public static IUdefButtonConfig fromMap(Map map) {
        return new UdefButtonConfig(map);
    }

    public static IUdefButtonConfig fromJson(String jsonStr) {
        return new UdefButtonConfig(Document.parse(jsonStr));
    }

    public static IUdefButtonConfig generateBulkCustomButtonConfig(String apiName, String describeApiName) {
        UdefButtonConfig buttonConfig = new UdefButtonConfig();
        buttonConfig.setApiName(apiName);
        buttonConfig.setDescribeApiName(describeApiName);

        Map<String, Integer> usePages = Maps.newHashMap();
        usePages.put("list_batch", 0);

        Map<String, Object> attr = Maps.newHashMap();
        attr.put("use_pages", usePages);
        buttonConfig.set("attr", attr);
        return buttonConfig;
    }

    public static IUdefButtonConfig generateConvertButtonConfig(String apiName, String describeApiName) {
        UdefButtonConfig buttonConfig = new UdefButtonConfig();
        buttonConfig.setApiName(apiName);
        buttonConfig.setDescribeApiName(describeApiName);
        buttonConfig.notDelete();
        buttonConfig.notEnable();

        // 【映射按钮-设计器】- 映射按钮应该不能配置“批量”支持无意义
        // https://www.tapd.cn/54330609/bugtrace/bugs/view?bug_id=1154330609001184212&url_cache_key=5bf815b0d80d197785f9846a82558442
        Map<String, Integer> usePages = Maps.newHashMap();
        usePages.put("list_batch", 0);

        Map<String, Object> attr = Maps.newHashMap();
        attr.put("use_pages", usePages);
        buttonConfig.set("attr", attr);
        return buttonConfig;
    }

    @Override
    public String toJson() {
        return JSON.toJSONString(getContainerDocument());
    }

    @Override
    public int getEdit() {
        Integer edit = get(EDIT, Integer.class);
        return Objects.isNull(edit) ? UN_USABLE : edit;
    }

    @Override
    public void notDelete() {
        set(DELETE, 0);
    }

    @Override
    public void notEnable() {
        set(ENABLE, UN_USABLE);
    }

    @Override
    public int getEnable() {
        Integer enable = get(ENABLE, Integer.class);
        return Objects.isNull(enable) ? 0 : enable;
    }

    @Override
    public Map toMap() {
        return getContainerDocument();
    }

    @Override
    public IUdefButtonConfig copy() {
        return new UdefButtonConfig((Map) copy(map));
    }

    @Override
    public void handleByDescribe(IObjectDescribe describe) {
        if (!ButtonExt.SLAVE_HIDDEN_ACTIONS.contains(getApiName())) {
            return;
        }

        if (!ObjectDescribeExt.of(describe).isSlaveObject()) {
            return;
        }
        Map<String, Object> attr = getAttr();
        if (Objects.isNull(attr)) {
            return;
        }

        Object o = attr.get("stage_actions");
        if (o instanceof Map) {
            Map<String, Object> stageActions = (Map<String, Object>) o;
            stageActions.put("post", getConfMap(0));
            stageActions.put("current", getConfMap(0));
            stageActions.put("pre", getConfMap(0));
        }
    }

    @Override
    public void setFilterText(List<String> helpText) {
        Map<String, Object> attr = CollectionUtils.nullToEmpty(getAttr());
        attr.put(FILTER_TEXT, helpText);
        setAttr(attr);
    }

    @Override
    public void editParamForm() {
        Map<String, Object> attr = getAttr();
        Map<String, Object> paramForm = Maps.newHashMap();
        paramForm.put(EDIT, 1);
        attr.put("param_form", paramForm);
    }

    @Override
    public void unSupportPostAction() {
        Map<String, Object> attr = getAttr();
        if (Objects.isNull(attr)) {
            return;
        }
        Object o = attr.get("stage_actions");
        if (o instanceof Map) {
            Map<String, Object> stageActions = (Map<String, Object>) o;
            stageActions.put("post", getConfMap(0));
        }
    }

    @Override
    public void supportRedirectAction() {
        Map<String, Object> attr = getAttr();
        if (Objects.isNull(attr)) {
            return;
        }
        Object o = attr.get("stage_actions");
        if (o instanceof Map) {
            Map<String, Object> stageActions = (Map<String, Object>) o;
            Map<String, Integer> confMap = getConfMap(1);
            confMap.put("display", 1);
            stageActions.put("redirect", confMap);
        }
    }

    @Override
    public void supportCurrentAction() {
        Map<String, Object> attr = getAttr();
        if (Objects.isNull(attr)) {
            return;
        }
        Object o = attr.get("stage_actions");
        if (o instanceof Map) {
            Map<String, Object> stageActions = (Map<String, Object>) o;
            stageActions.put("current", getConfMap(1));
        }
    }

    @Override
    public void supportWheres() {
        Map<String, Object> attr = getAttr();
        if (Objects.isNull(attr)) {
            return;
        }

        Map<String, Object> wheres = Maps.newHashMap();
        wheres.put(EDIT, 1);
        attr.put("wheres", wheres);
    }

    @Override
    public void setControlLevel(String controlLevel) {
        set(CONTROL_LEVEL, controlLevel);
    }

    private void setAttr(Map<String, Object> attr) {
        set("attr", attr);
    }

    private Map<String, Object> getAttr() {
        return get("attr", Map.class);
    }

    private Map<String, Integer> getConfMap(int value) {
        Map<String, Integer> config = Maps.newHashMap();
        config.put(EDIT, value);
        return config;
    }

    @Override
    public String getDescribeApiName() {
        return get(DESCRIEB_API_NAME, String.class);
    }

    @Override
    public void setDescribeApiName(String describeApiName) {
        set(DESCRIEB_API_NAME, describeApiName);
    }

    @Override
    public String getApiName() {
        return get(API_NAME, String.class);
    }

    @Override
    public void setApiName(String apiName) {
        set(API_NAME, apiName);
    }
}
