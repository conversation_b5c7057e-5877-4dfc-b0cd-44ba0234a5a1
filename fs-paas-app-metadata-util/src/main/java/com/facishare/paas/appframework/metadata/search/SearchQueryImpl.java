package com.facishare.paas.appframework.metadata.search;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <pre>
 *      (|)
 *   (&)    (&)
 * (&)  (&)  3
 * 1     2
 *
 * (((1) and (2)) or (3))
 * </pre>
 * create by zhaoju on 2020/06/28
 */
public final class SearchQueryImpl implements SearchQuery {

    private IFilter filter;
    /**
     * 所有子节点的关系
     * {@link Connector#AND} 且关系
     * {@link Connector#OR} 或关系
     *
     * @see Connector
     */
    private Connector connector;
    /**
     * 子节点
     */
    private List<SearchQuery> queries;

    private SearchQueryImpl(IFilter filter) {
        this(Connector.AND);
        this.filter = filter;
    }

    private SearchQueryImpl(Connector connector) {
        this.connector = connector;
        this.queries = Lists.newArrayList();
    }

    private SearchQueryImpl(IFilter filter, Connector connector) {
        this.connector = connector;
        this.queries = Lists.newArrayList();
        this.filter = filter;
    }

    private SearchQueryImpl(List<SearchQuery> queries, Connector connector) {
        this.connector = connector;
        this.queries = queries;
    }

    public static SearchQuery connector(Connector connector) {
        return new SearchQueryImpl(connector);
    }

    public static SearchQuery filter(IFilter filter) {
        return new SearchQueryImpl(filter);
    }

    public static SearchQuery andFilter(IFilter filter) {
        return new SearchQueryImpl(filter, Connector.AND);
    }

    public static SearchQuery andSearchQuery(List<SearchQuery> queries) {
        return new SearchQueryImpl(queries, Connector.AND);
    }

    public static SearchQuery orSearchQuery(List<SearchQuery> queries) {
        return new SearchQueryImpl(queries, Connector.OR);
    }

    public static SearchQuery filters(List<IFilter> filters) {
        return filters(filters, true);
    }


    public static SearchQuery filters(List<IFilter> filterList, boolean needGroup) {
        // 不需要分组的直接全当 and 处理
        if (!needGroup) {
            return getSearchQueryNotGroup(filterList);
        }

        List<IFilter> notGroupFilters = filterList.stream().filter(it -> Objects.isNull(it.getFilterGroup())).collect(Collectors.toList());
        return FilterExt.FiltersContainer.of(filterList)
                .filter(it -> Objects.nonNull(it.getFilterGroup()))
                .group()
                .map(SearchQueryImpl::getSearchQueryNotGroup)
                .reduce(SearchQuery::or)
                .map(it -> it.and(notGroupFilters))
                .orElse(getSearchQueryNotGroup(notGroupFilters));
    }

    private static SearchQuery getSearchQueryNotGroup(List<IFilter> filterList) {
        SearchQuery searchQuery = new SearchQueryImpl(Connector.AND);
        searchQuery.add(filterList.stream().map(SearchQueryImpl::filter).toArray(SearchQuery[]::new));
        return searchQuery;
    }

    public static SearchQuery wheres(List<Wheres> wheres) {
        SearchQuery searchQuery = new SearchQueryImpl(Connector.OR);
        searchQuery.add(wheres.stream().map(it -> getSearchQueryNotGroup(it.getFilters())).toArray(SearchQuery[]::new));
        return searchQuery;
    }

    @Override
    public SearchQuery and(IFilter filter) {
        return and(SearchQueryImpl.filter(filter));
    }

    @Override
    public SearchQuery and(List<IFilter> filters) {
        return and(filters.stream().map(SearchQueryImpl::filter).toArray(SearchQuery[]::new));
    }

    @Override
    public SearchQuery and(SearchQuery... queries) {
        SearchQueryImpl parent = new SearchQueryImpl(Connector.AND);
        parent.add(this);
        parent.add(queries);
        return parent;
    }

    @Override
    public SearchQuery or(IFilter... filters) {
        return or(Stream.of(filters).map(SearchQueryImpl::filter).toArray(SearchQuery[]::new));
    }

    @Override
    public SearchQuery or(SearchQuery... queries) {
        SearchQueryImpl parent = new SearchQueryImpl(Connector.OR);
        parent.add(this);
        parent.add(queries);
        return parent;
    }

    @Override
    public void add(SearchQuery... queries) {
        if (isFilterNode()) {
            throw new IllegalArgumentException("filter node can not add query!");
        }
        this.queries.addAll(Arrays.asList(queries));
    }

    @Override
    public void remove(SearchQuery searchQuery) {
        this.queries.remove(searchQuery);
    }

    @Override
    public ISearchTemplateQuery toSearchTemplateQuery() {
        ISearchTemplateQuery searchQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        PatternBuilder patternBuilder = generatedPattern(this, filters);
        searchQuery.setFilters(filters);
        searchQuery.setPattern(patternBuilder.render());
        return searchQuery;
    }

    private PatternBuilder generatedPattern(SearchQuery searchQuery, List<IFilter> filters) {
        PatternBuilder patternBuilder = PatternBuilder.empty(searchQuery.getConnector().toString());
        if (searchQuery.isEmpty()) {
            return patternBuilder;
        }
        if (searchQuery.isFilterNode()) {
            return Optional.ofNullable(RequestContextManager.getContext())
                    .map(RequestContext::getTenantId)
                    .filter(ei -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EFFECTIVE_FILTER, ei))
                    .map(it -> {
                        FilterExt filterExt = FilterExt.of(searchQuery.getFilter());
                        if (!filterExt.isEffectiveFilter()) {
                            return patternBuilder;
                        }
                        filters.add(filterExt.getFilter());
                        return patternBuilder.append(filters.size());
                    })
                    .orElseGet(() -> {
                        filters.add(searchQuery.getFilter());
                        return patternBuilder.append(filters.size());
                    });
        }

        return searchQuery.getSearchQueryContainer().stream()
                .map(it -> generatedPattern(it, filters))
                .filter(it -> !it.isEmpty())
                .reduce(patternBuilder, PatternBuilder::merge);
    }

    @Override
    public boolean isEmpty() {
        return filter == null && CollectionUtils.empty(queries);
    }

    @Override
    public IFilter getFilter() {
        return filter;
    }

    @Override
    public boolean isFilterNode() {
        return filter != null && CollectionUtils.empty(queries);
    }

    @Override
    public List<SearchQuery> getSearchQueryContainer() {
        return queries;
    }

    @Override
    public Connector getConnector() {
        return connector;
    }

    @EqualsAndHashCode
    private static class PatternBuilder {
        @Getter
        private boolean isEmpty;

        private StringBuilder stringBuilder;
        private final String prefix;
        private final String delimiter;
        private final String suffix;
        private int appendCount;

        private PatternBuilder(boolean isEmpty, String delimiter, String prefix, String suffix) {
            this.isEmpty = isEmpty;
            this.delimiter = delimiter;
            this.prefix = prefix;
            this.suffix = suffix;
        }

        public static PatternBuilder empty(String delimiter) {
            return new PatternBuilder(true, delimiter, "(", ")");
        }

        public PatternBuilder append(String str) {
            getStringBuilder().append(str);
            this.isEmpty = false;
            this.appendCount++;
            return this;
        }

        public PatternBuilder append(int i) {
            return append(String.valueOf(i));
        }

        public PatternBuilder merge(PatternBuilder other) {
            if (other == null || other.isEmpty()) {
                return this;
            }
            if (other.needPrefixAndSuffix()) {
                getStringBuilder().append(other.stringBuilder).append(other.suffix);
            } else {
                getStringBuilder().append(other.stringBuilder, other.prefix.length(), other.stringBuilder.length());
            }
            this.isEmpty = false;
            this.appendCount++;
            return this;
        }

        public String render() {
            if (isEmpty) {
                return null;
            }
            if (Strings.isNullOrEmpty(suffix)) {
                return stringBuilder.toString();
            }
            int initialLength = stringBuilder.length();
            String result = stringBuilder.append(suffix).toString();
            stringBuilder.setLength(initialLength);
            return result;
        }

        private StringBuilder getStringBuilder() {
            if (stringBuilder == null) {
                return stringBuilder = new StringBuilder().append(prefix);
            }
            return stringBuilder.append(" ").append(delimiter).append(" ");
        }

        private boolean needPrefixAndSuffix() {
            return appendCount > 1;
        }
    }
}
