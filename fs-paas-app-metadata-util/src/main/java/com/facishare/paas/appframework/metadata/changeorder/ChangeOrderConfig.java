package com.facishare.paas.appframework.metadata.changeorder;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2023/3/24
 */
@Slf4j
public final class ChangeOrderConfig {
    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    private static Map<String, ChangeOrderConfigItem> changeOrderOriginalDescribeGray = Maps.newHashMap();
    private static Map<String, ChangeOrderConfigItem> changeOrderChangeDescribeGray = Maps.newHashMap();
    private static IObjectDescribe baseChangeOrderDescribe = null;
    private static IObjectDescribe baseChangeOrderDetailDescribe = null;
    private static IObjectDescribe originalDescribeFields = null;

    private static Map<String, GrayRule> changeOrderSupportFieldAttr = Maps.newHashMap();
    private static GrayRule changeOrderQuoteFieldAttr;

    private final static String UDOBJ = "udobj";

    private ChangeOrderConfig() {
    }

    private static final List<String> CHANGE_ORDER_PACKAGE_FIELDS = ImmutableList.of(ObjectDataExt.ORIGINAL_DATA, ObjectDataExt.CHANGED_DATA,
            ObjectDataExt.ORIGINAL_DETAIL_DATA, ObjectDataExt.CHANGED_TYPE, ObjectDataExt.VERSION_NUMBER, ObjectDataExt.CHANGED_BY,
            ObjectDataExt.CHANGED_TIME, ObjectDataExt.CHANGED_REASON, ObjectDataExt.EFFECTIVE_STATUS, ObjectDataExt.EFFECTIVE_BY,
            ObjectDataExt.EFFECTIVE_TIME, IObjectData.OWNER);

    static {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            log.warn("reload config fs-paas-appframework-config");
            String gray = config.get("change_order_describe_gray");
            List<ChangeOrderConfigItem> items = JacksonUtils.fromJson(gray, new TypeReference<List<ChangeOrderConfigItem>>() {
            });
            if (Objects.nonNull(items)) {
                changeOrderOriginalDescribeGray = items.stream()
                        .collect(Collectors.toMap(ChangeOrderConfigItem::getDescribeApiName, Function.identity()));
                changeOrderChangeDescribeGray = items.stream()
                        .collect(Collectors.toMap(ChangeOrderConfigItem::getChangeOrderApiName, Function.identity()));
            }

            changeOrderSupportFieldAttr = getFsGrayRule(config, "change_order_support_field_attr");
            changeOrderQuoteFieldAttr = new GrayRule(config.get("change_order_quote_field_attr", ""));

            baseChangeOrderDescribe = getObjectDescribe(config.get("change_order_describe_json"), Helper.CHANGE_ORDER_DESCRIBE_JSON);
            baseChangeOrderDetailDescribe = getObjectDescribe(config.get("change_order_detail_describe_json"), Helper.CHANGE_ORDER_DETAIL_DESCRIBE_JSON);
            originalDescribeFields = getObjectDescribe(config.get("original_describe_fields_json"), Helper.ORIGINAL_DESCRIBE_FIELDS_JSON);
        });
    }

    /**
     * {"AccountObj,LeadsObj":"white:74255|78057"}
     *
     * @param config
     * @param confKey
     * @return
     */
    public static Map<String, GrayRule> getFsGrayRule(IConfig config, String confKey) {
        Map<String, GrayRule> result = Maps.newHashMap();
        Map<String, String> stringObjectMap = parseMapFromConfig(config, confKey);
        if (CollectionUtils.empty(stringObjectMap)) {
            return Collections.emptyMap();
        }
        stringObjectMap.forEach((key, val) -> {
            GrayRule grayRule = new GrayRule(val);
            CONFIG_SPLITTER.split(key).forEach(it -> result.putIfAbsent(it, grayRule));
        });
        return result;
    }

    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
        try {
            Map map = JSON.parseObject(data, Map.class);
            return Collections.unmodifiableMap(map);
        } catch (Exception e) {
            log.error("parseMapFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
    }

    private static IObjectDescribe getObjectDescribe(String json, String defaultJson) {
        IObjectDescribe describe = new ObjectDescribe();
        if (!Strings.isNullOrEmpty(json)) {
            describe.fromJsonString(json);
        } else {
            describe.fromJsonString(defaultJson);
        }
        return describe;
    }

    //从对象的灰度跟着主对象走
    public static boolean changeOrderDescribeGray(String tenantId, String objectApiName) {
        if (ObjectDescribeExt.isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        ChangeOrderConfigItem item = changeOrderOriginalDescribeGray.get(objectApiName);
        return Objects.nonNull(item) && item.match(tenantId, objectApiName);
    }

    public static String getOriginalDescribeByConf(String tenantId, String objectApiName) {
        if (ObjectDescribeExt.isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        ChangeOrderConfigItem item = changeOrderChangeDescribeGray.get(objectApiName);
        if (Objects.isNull(item) || !item.matchByChangeOrderApiName(tenantId, objectApiName)) {
            return null;
        }
        return item.getDescribeApiName();
    }


    public static ChangeOrderConfigItem getChangeOrderConfigItem(String objectApiName) {
        if (ObjectDescribeExt.isCustomObject(objectApiName)) {
            objectApiName = UDOBJ;
        }
        return changeOrderOriginalDescribeGray.get(objectApiName);
    }

    public static IObjectDescribe getBaseChangeOrderDescribe(boolean slaveObject) {
        if (slaveObject) {
            return baseChangeOrderDetailDescribe.copy();
        }
        return baseChangeOrderDescribe.copy();
    }

    public static List<IFieldDescribe> getOriginalDescribeFields(String describeApiName) {
        List<IFieldDescribe> fieldDescribes = originalDescribeFields.copy().getFieldDescribes();
        if (!Strings.isNullOrEmpty(describeApiName)) {
            fieldDescribes.forEach(it -> it.setDescribeApiName(describeApiName));
        }
        return fieldDescribes;
    }

    public static Optional<IFieldDescribe> getChangedReasonField() {
        return ObjectDescribeExt.of(originalDescribeFields).stream()
                .filter(it -> ObjectDataExt.CHANGED_REASON.equals(it.getApiName()))
                .map(IFieldDescribe::copy)
                .findFirst();
    }

    public static boolean supportFieldAttr(String attr, String... fieldTypes) {
        return Stream.of(fieldTypes).allMatch(fieldType -> supportFieldAttr(attr, fieldType));
    }

    public static boolean supportFieldAttr(String attr, String fieldType) {
        GrayRule grayRule = changeOrderSupportFieldAttr.get(fieldType);
        if (Objects.isNull(grayRule)) {
            grayRule = changeOrderSupportFieldAttr.get("default");
        }
        if (Objects.isNull(grayRule)) {
            return true;
        }
        return grayRule.isAllow(attr);
    }

    public static boolean supportQuoteFieldAttr(String attr) {
        if (Objects.isNull(changeOrderQuoteFieldAttr)) {
            return false;
        }
        return changeOrderQuoteFieldAttr.isAllow(attr);
    }

    public static List<String> getChangeOrderPackageFields() {
        return CHANGE_ORDER_PACKAGE_FIELDS;
    }

    public static class ChangeOrderConfigItem {
        @Getter
        private final String describeApiName;
        private final GrayRule grayRule;
        @Getter
        private final String changeOrderApiName;
        @Getter
        private final String changeOrderStoreTableName;
        private final GrayRule changeFieldsGray;
        private final GrayRule supportFieldsGray;
        private final GrayRule supportFieldTypesGray;
        private final GrayRule originalFieldsGray;
        private final GrayRule changeFieldTypeGray;
        private final GrayRule originalFieldTypeGray;
        @Getter
        private final long originalCount;

        @JsonCreator
        public ChangeOrderConfigItem(@JsonProperty("describeApiName") String describeApiName,
                                     @JsonProperty("ei") GrayRule grayRule,
                                     @JsonProperty("changeOrderApiName") String changeOrderApiName,
                                     @JsonProperty("changeOrderStoreTableName") String changeOrderStoreTableName,
                                     @JsonProperty("supportFields") GrayRule supportFieldsGray,
                                     @JsonProperty("supportFieldTypes") GrayRule supportFieldTypesGray,
                                     @JsonProperty("changeFields") GrayRule changeFieldsGray,
                                     @JsonProperty("changeFieldType") GrayRule changeFieldTypeGray,
                                     @JsonProperty("originalFieldType") GrayRule originalFieldTypeGray,
                                     @JsonProperty("originalCount") long originalCount,
                                     @JsonProperty("originalFields") GrayRule originalFieldsGray) {
            this.describeApiName = describeApiName;
            this.grayRule = grayRule;
            this.changeOrderApiName = changeOrderApiName;
            this.changeOrderStoreTableName = changeOrderStoreTableName;
            this.supportFieldsGray = supportFieldsGray;
            this.changeFieldsGray = changeFieldsGray;
            this.originalFieldsGray = originalFieldsGray;
            this.changeFieldTypeGray = changeFieldTypeGray;
            this.originalFieldTypeGray = originalFieldTypeGray;
            this.originalCount = originalCount;
            this.supportFieldTypesGray = supportFieldTypesGray;
        }

        public boolean match(String tenantId, String apiName) {
            if (!Objects.equals(describeApiName, apiName)) {
                return false;
            }
            return grayRule.isAllow(tenantId);
        }

        public boolean matchByChangeOrderApiName(String tenantId, String apiName) {
            if (!Objects.equals(changeOrderApiName, apiName)) {
                return false;
            }
            return grayRule.isAllow(tenantId);
        }

        public boolean isSupportFields(String describeApiName, IFieldDescribe fieldDescribe) {
            if (ObjectDescribeExt.isCustomObject(describeApiName)) {
                describeApiName = UDOBJ;
            }
            if (!Objects.equals(this.describeApiName, describeApiName)) {
                return false;
            }
            String fieldName = fieldDescribe.getApiName();
            String fieldType = fieldDescribe.getType();
            return supportFieldsGray.isAllow(fieldName) && supportFieldTypesGray.isAllow(fieldType);
        }

        public boolean isChangeFields(String describeApiName, IFieldDescribe fieldDescribe) {
            if (ObjectDescribeExt.isCustomObject(describeApiName)) {
                describeApiName = UDOBJ;
            }
            if (!Objects.equals(this.describeApiName, describeApiName)) {
                return false;
            }
            return grayFields(fieldDescribe, changeFieldsGray, changeFieldTypeGray);
        }

        public boolean isOriginalFields(String describeApiName, IFieldDescribe fieldDescribe) {
            if (ObjectDescribeExt.isCustomObject(describeApiName)) {
                describeApiName = UDOBJ;
            }
            if (!Objects.equals(this.describeApiName, describeApiName)) {
                return false;
            }
            return grayFields(fieldDescribe, originalFieldsGray, originalFieldTypeGray);
        }

        private boolean grayFields(IFieldDescribe fieldDescribe, GrayRule originalFieldsGray, GrayRule originalFieldTypeGray) {
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (!fieldDescribeExt.isCustomField() && !originalFieldsGray.isAllow(fieldDescribe.getApiName())) {
                return false;
            }
            String type;
            if (fieldDescribeExt.isGroupField()) {
                type = ((GroupField) fieldDescribe).getGroupType();
            } else {
                type = fieldDescribe.getType();
            }
            return originalFieldTypeGray.isAllow(type);
        }
    }

    private static class Helper {

        //language=JSON
        private static final String CHANGE_ORDER_DESCRIBE_JSON = "{\n" +
                "  \"fields\": {\n" +
                "    \"name\": {\n" +
                "      \"type\": \"auto_number\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": true,\n" +
                "      \"label\": \"变更单编号\",\n" +
                "      \"api_name\": \"name\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"auto_number_type\": \"normal\",\n" +
                "      \"condition\": null,\n" +
                "      \"default_value\": \"{yyyy}-{mm}-{dd}-00001\",\n" +
                "      \"postfix\": \"\",\n" +
                "      \"prefix\": \"{yyyy}-{mm}-{dd}-\",\n" +
                "      \"serial_number\": 5,\n" +
                "      \"start_number\": 1\n" +
                "    },\n" +
                "    \"version_number\": {\n" +
                "      \"type\": \"auto_number\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": true,\n" +
                "      \"label\": \"版本号\",\n" +
                "      \"api_name\": \"version_number\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"auto_number_type\": \"normal\",\n" +
                "      \"condition\": null,\n" +
                "      \"default_value\": \"{yyyy}-{mm}-{dd}-00001\",\n" +
                "      \"postfix\": \"\",\n" +
                "      \"prefix\": \"{yyyy}-{mm}-{dd}-\",\n" +
                "      \"serial_number\": 5,\n" +
                "      \"start_number\": 1\n" +
                "    },\n" +
                "    \"changed_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更人\",\n" +
                "      \"is_single\": true,\n" +
                "      \"api_name\": \"changed_by\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"changed_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更时间\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"api_name\": \"changed_time\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"changed_reason\": {\n" +
                "      \"type\": \"long_text\",\n" +
                "      \"expression_type\": \"json\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更原因\",\n" +
                "      \"api_name\": \"changed_reason\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"max_length\": 100000\n" +
                "    },\n" +
                "    \"effective_status\": {\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"api_name\": \"effective_status\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"未生效\",\n" +
                "          \"value\": \"ineffective\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"正常\",\n" +
                "          \"value\": \"effective\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"已驳回\",\n" +
                "          \"value\": \"rejected\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"not_usable\": true,\n" +
                "          \"label\": \"其他\",\n" +
                "          \"value\": \"other\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"label\": \"生效状态\"\n" +
                "    },\n" +
                "    \"effective_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"生效人\",\n" +
                "      \"api_name\": \"effective_by\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"effective_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"time_zone\": \"\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"label\": \"生效时间\",\n" +
                "      \"api_name\": \"effective_time\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"object_describe_api_name\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": false,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"max_length\": 64,\n" +
                "      \"pattern\": \"\",\n" +
                "      \"label\": \"object_describe_api_name\",\n" +
                "      \"is_active\": true,\n" +
                "      \"api_name\": \"object_describe_api_name\",\n" +
                "      \"description\": \"object_describe_api_name\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"index_name\": \"api_name\"\n" +
                "    },\n" +
                "    \"created_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"is_single\": true,\n" +
                "      \"api_name\": \"created_by\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"label\": \"创建人\",\n" +
                "      \"is_active\": true,\n" +
                "      \"index_name\": \"crt_by\"\n" +
                "    },\n" +
                "    \"create_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"time_zone\": \"\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"label\": \"创建时间\",\n" +
                "      \"api_name\": \"create_time\",\n" +
                "      \"description\": \"create_time\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"index_name\": \"crt_time\"\n" +
                "    },\n" +
                "    \"last_modified_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"is_single\": true,\n" +
                "      \"api_name\": \"last_modified_by\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"is_active\": true,\n" +
                "      \"index_name\": \"md_by\",\n" +
                "      \"label\": \"最后修改人\"\n" +
                "    },\n" +
                "    \"last_modified_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"time_zone\": \"\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"label\": \"最后修改时间\",\n" +
                "      \"api_name\": \"last_modified_time\",\n" +
                "      \"description\": \"last_modified_time\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"index_name\": \"md_time\"\n" +
                "    },\n" +
                "    \"owner\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"负责人\",\n" +
                "      \"type\": \"employee\",\n" +
                "      \"is_required\": true,\n" +
                "      \"api_name\": \"owner\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": true,\n" +
                "      \"index_name\": \"owner\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"out_owner\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"外部负责人\",\n" +
                "      \"type\": \"employee\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"out_owner\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_single\": true,\n" +
                "      \"index_name\": \"o_owner\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"data_own_department\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"归属部门\",\n" +
                "      \"type\": \"department\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"data_own_department\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": true,\n" +
                "      \"index_name\": \"data_owner_dept_id\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"data_own_organization\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"归属组织\",\n" +
                "      \"type\": \"department\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"data_own_organization\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": true,\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"owner_department\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"text\",\n" +
                "      \"is_required\": false,\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"index_name\": \"owner_dept\",\n" +
                "      \"max_length\": 100,\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"label\": \"负责人主属部门\",\n" +
                "      \"api_name\": \"owner_department\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"lock_status\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"is_required\": false,\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"未锁定\",\n" +
                "          \"value\": \"0\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"锁定\",\n" +
                "          \"value\": \"1\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"not_usable\": true,\n" +
                "          \"label\": \"其他\",\n" +
                "          \"value\": \"other\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": false,\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"default_value\": \"0\",\n" +
                "      \"label\": \"锁定状态\",\n" +
                "      \"api_name\": \"lock_status\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"life_status\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"is_required\": false,\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"未生效\",\n" +
                "          \"value\": \"ineffective\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"审核中\",\n" +
                "          \"value\": \"under_review\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"正常\",\n" +
                "          \"value\": \"normal\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"变更中\",\n" +
                "          \"value\": \"in_change\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"作废\",\n" +
                "          \"value\": \"invalid\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"not_usable\": true,\n" +
                "          \"label\": \"其他\",\n" +
                "          \"value\": \"other\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"default_value\": \"normal\",\n" +
                "      \"label\": \"生命状态\",\n" +
                "      \"api_name\": \"life_status\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"original_data\": {\n" +
                "      \"type\": \"object_reference\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"源单\",\n" +
                "      \"api_name\": \"original_data\",\n" +
                "      \"action_on_target_delete\": \"set_null\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"extend_obj_data_id\": {\n" +
                "      \"is_index\": false,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"description\": \"extend_obj_data_id\",\n" +
                "      \"label\": \"extend_obj_data_id\",\n" +
                "      \"type\": \"text\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"extend_obj_data_id\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"max_length\": 100\n" +
                "    },\n" +
                "    \"change_order_rule\": {\n" +
                "      \"is_index\": false,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"description\": \"change_order_rule\",\n" +
                "      \"label\": \"change_order_rule\",\n" +
                "      \"type\": \"text\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"change_order_rule\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"max_length\": 100\n" +
                "    },\n" +
                "    \"is_deleted\": {\n" +
                "      \"type\": \"true_or_false\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": false,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"is_deleted\",\n" +
                "      \"api_name\": \"is_deleted\",\n" +
                "      \"description\": \"is_deleted\",\n" +
                "      \"default_value\": false,\n" +
                "      \"status\": \"released\",\n" +
                "      \"in_name\": \"is_del\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"tenant_id\": \"-100\",\n" +
                "  \"api_name\": \"XXXChangeObj\",\n" +
                "  \"display_name\": \"XXX变更对象\",\n" +
                "  \"package\": \"CRM\",\n" +
                "  \"is_active\": true,\n" +
                "  \"release_version\": \"6.4\",\n" +
                "  \"is_deleted\": false,\n" +
                "  \"define_type\": \"package\"\n" +
                "}";

        //language=JSON
        private static final String CHANGE_ORDER_DETAIL_DESCRIBE_JSON = "{\n" +
                "  \"fields\": {\n" +
                "    \"name\": {\n" +
                "      \"type\": \"auto_number\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": true,\n" +
                "      \"label\": \"变更单明细编号\",\n" +
                "      \"api_name\": \"name\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"auto_number_type\": \"normal\",\n" +
                "      \"condition\": null,\n" +
                "      \"default_value\": \"{yyyy}-{mm}-{dd}-00001\",\n" +
                "      \"postfix\": \"\",\n" +
                "      \"prefix\": \"{yyyy}-{mm}-{dd}-\",\n" +
                "      \"serial_number\": 5,\n" +
                "      \"start_number\": 1\n" +
                "    },\n" +
                "    \"original_detail_data\": {\n" +
                "      \"type\": \"object_reference\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"源单明细\",\n" +
                "      \"api_name\": \"original_detail_data\",\n" +
                "      \"action_on_target_delete\": \"set_null\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"changed_data\": {\n" +
                "      \"type\": \"master_detail\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更单\",\n" +
                "      \"api_name\": \"changed_data\",\n" +
                "      \"action_on_target_delete\": \"set_null\",\n" +
                "      \"is_create_when_master_create\": true,\n" +
                "      \"is_required_when_master_create\": true,\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"changed_type\": {\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"api_name\": \"changed_type\",\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"新建\",\n" +
                "          \"value\": \"add\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"删除\",\n" +
                "          \"value\": \"deleted\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"修改\",\n" +
                "          \"value\": \"update\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"not_usable\": true,\n" +
                "          \"label\": \"其他\",\n" +
                "          \"value\": \"other\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"status\": \"released\",\n" +
                "      \"label\": \"变更类型\"\n" +
                "    },\n" +
                "    \"object_describe_api_name\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": false,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"max_length\": 64,\n" +
                "      \"pattern\": \"\",\n" +
                "      \"label\": \"object_describe_api_name\",\n" +
                "      \"is_active\": true,\n" +
                "      \"api_name\": \"object_describe_api_name\",\n" +
                "      \"description\": \"object_describe_api_name\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"index_name\": \"api_name\"\n" +
                "    },\n" +
                "    \"created_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"is_single\": true,\n" +
                "      \"api_name\": \"created_by\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"label\": \"创建人\",\n" +
                "      \"is_active\": true,\n" +
                "      \"index_name\": \"crt_by\"\n" +
                "    },\n" +
                "    \"create_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"time_zone\": \"\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"label\": \"创建时间\",\n" +
                "      \"api_name\": \"create_time\",\n" +
                "      \"description\": \"create_time\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"index_name\": \"crt_time\"\n" +
                "    },\n" +
                "    \"last_modified_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"is_single\": true,\n" +
                "      \"api_name\": \"last_modified_by\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"is_active\": true,\n" +
                "      \"index_name\": \"md_by\",\n" +
                "      \"label\": \"最后修改人\"\n" +
                "    },\n" +
                "    \"last_modified_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"time_zone\": \"\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"label\": \"最后修改时间\",\n" +
                "      \"api_name\": \"last_modified_time\",\n" +
                "      \"description\": \"last_modified_time\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"index_name\": \"md_time\"\n" +
                "    },\n" +
                "    \"owner\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"负责人\",\n" +
                "      \"type\": \"employee\",\n" +
                "      \"is_required\": true,\n" +
                "      \"api_name\": \"owner\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": true,\n" +
                "      \"index_name\": \"owner\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"out_owner\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"外部负责人\",\n" +
                "      \"type\": \"employee\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"out_owner\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_single\": true,\n" +
                "      \"index_name\": \"o_owner\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"data_own_department\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"归属部门\",\n" +
                "      \"type\": \"department\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"data_own_department\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": true,\n" +
                "      \"index_name\": \"data_owner_dept_id\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"data_own_organization\": {\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"归属组织\",\n" +
                "      \"type\": \"department\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"data_own_organization\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": true,\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"owner_department\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"text\",\n" +
                "      \"is_required\": false,\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"index_name\": \"owner_dept\",\n" +
                "      \"max_length\": 100,\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"label\": \"负责人主属部门\",\n" +
                "      \"api_name\": \"owner_department\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"lock_status\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"is_required\": false,\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"未锁定\",\n" +
                "          \"value\": \"0\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"锁定\",\n" +
                "          \"value\": \"1\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"not_usable\": true,\n" +
                "          \"label\": \"其他\",\n" +
                "          \"value\": \"other\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_single\": false,\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"default_value\": \"0\",\n" +
                "      \"label\": \"锁定状态\",\n" +
                "      \"api_name\": \"lock_status\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"life_status\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"is_required\": false,\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"未生效\",\n" +
                "          \"value\": \"ineffective\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"审核中\",\n" +
                "          \"value\": \"under_review\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"正常\",\n" +
                "          \"value\": \"normal\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"变更中\",\n" +
                "          \"value\": \"in_change\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"作废\",\n" +
                "          \"value\": \"invalid\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"not_usable\": true,\n" +
                "          \"label\": \"其他\",\n" +
                "          \"value\": \"other\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"default_value\": \"normal\",\n" +
                "      \"label\": \"生命状态\",\n" +
                "      \"api_name\": \"life_status\",\n" +
                "      \"status\": \"new\"\n" +
                "    },\n" +
                "    \"extend_obj_data_id\": {\n" +
                "      \"is_index\": false,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_unique\": false,\n" +
                "      \"description\": \"extend_obj_data_id\",\n" +
                "      \"label\": \"extend_obj_data_id\",\n" +
                "      \"type\": \"text\",\n" +
                "      \"is_required\": false,\n" +
                "      \"api_name\": \"extend_obj_data_id\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"max_length\": 100\n" +
                "    },\n" +
                "    \"is_deleted\": {\n" +
                "      \"type\": \"true_or_false\",\n" +
                "      \"define_type\": \"system\",\n" +
                "      \"is_index\": false,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"is_deleted\",\n" +
                "      \"api_name\": \"is_deleted\",\n" +
                "      \"description\": \"is_deleted\",\n" +
                "      \"default_value\": false,\n" +
                "      \"status\": \"released\",\n" +
                "      \"in_name\": \"is_del\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"tenant_id\": \"-100\",\n" +
                "  \"api_name\": \"XXXChangeObj\",\n" +
                "  \"display_name\": \"XXX明细变更对象\",\n" +
                "  \"package\": \"CRM\",\n" +
                "  \"is_active\": true,\n" +
                "  \"release_version\": \"6.4\",\n" +
                "  \"is_deleted\": false,\n" +
                "  \"define_type\": \"package\"\n" +
                "}";
        //language=JSON
        private static final String ORIGINAL_DESCRIBE_FIELDS_JSON = "{\n" +
                "  \"fields\": {\n" +
                "    \"version_number\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": true,\n" +
                "      \"label\": \"版本号\",\n" +
                "      \"api_name\": \"version_number\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"condition\": null\n" +
                "    },\n" +
                "    \"changed_by\": {\n" +
                "      \"type\": \"employee\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更人\",\n" +
                "      \"is_single\": true,\n" +
                "      \"api_name\": \"changed_by\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"changed_time\": {\n" +
                "      \"type\": \"date_time\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更时间\",\n" +
                "      \"date_format\": \"yyyy-MM-dd HH:mm:ss\",\n" +
                "      \"api_name\": \"changed_time\",\n" +
                "      \"status\": \"released\"\n" +
                "    },\n" +
                "    \"changed_reason\": {\n" +
                "      \"type\": \"long_text\",\n" +
                "      \"expression_type\": \"json\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_required\": false,\n" +
                "      \"is_unique\": false,\n" +
                "      \"label\": \"变更原因\",\n" +
                "      \"api_name\": \"changed_reason\",\n" +
                "      \"status\": \"released\",\n" +
                "      \"max_length\": 100000\n" +
                "    },\n" +
                "    \"changed_status\": {\n" +
                "      \"is_unique\": false,\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"is_required\": false,\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"正常\",\n" +
                "          \"value\": \"normal\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"变更中\",\n" +
                "          \"value\": \"in_change\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"default_value\": \"normal\",\n" +
                "      \"label\": \"变更状态\",\n" +
                "      \"api_name\": \"changed_status\",\n" +
                "      \"status\": \"released\"\n" +
                "    }\n" +
                "  " +
                "}\n" +
                "}";
    }

}
