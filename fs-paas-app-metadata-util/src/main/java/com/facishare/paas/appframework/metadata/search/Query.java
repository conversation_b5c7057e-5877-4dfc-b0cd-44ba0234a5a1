package com.facishare.paas.appframework.metadata.search;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.metadata.api.search.*;
import com.facishare.paas.metadata.impl.search.*;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.stream.Stream;

/**
 * create by z<PERSON><PERSON> on 2020/06/29
 */
@Builder
@ToString
@EqualsAndHashCode
@RequiredArgsConstructor
public class Query {
    @Builder.Default
    private int limit = 10;
    @Builder.Default
    private int offset = 0;
    @Builder.Default
    private List<OrderBy> orders = Lists.newArrayList();
    private SearchQuery searchQuery;

//    private String sqlString;
//    private String dataPermissionString;

    /**
     * 传值为 AccountObj.name 这种
     */
    @Builder.Default
    private List<String> whatFieldApiNames = Lists.newArrayList();
    private String whatDescribeApiName;

    /**
     * 1走数据权限
     * 0不走数据权限
     */
    private int permissionType;
    private IDataRightsParameter dataRightsParameter;

    /**
     * 是否精确查询总数
     * true  只返回精确总数，不返回数据
     * false 返回预估的总数，返回数据，列表页用
     * null 返回精确总数，精确数据
     */
    private Boolean findExplicitTotalNum;
    /**
     * 是否需要返回count
     * true  会返回count
     * false 不会返回count，totalNum给0
     * null 会返回count
     */
    private Boolean needReturnCountNum;
    /**
     * 是否需要返回quote字段
     * true  会返回引用字段
     * false 不会返回引用字段
     * null 会返回引用字段
     */
    private Boolean needReturnQuote;
    /**
     * 默认关联表的时候是用left join，这个参数控制使用right join
     */
    private Boolean needRightJoin;

    private IMultiRelationParameter multiRelationParameter;
    private ISpecifiedTableParameter specifiedTableParameter;
    private WhatSearchParameter whatSearchParameter;
    private RecursiveParameter recursiveParameter;
    private ITagParameter tagParameter;
    private String searchSource;
    private IGroupByParameter groupByParameter;
    /**
     * 用于解决深翻页的问题
     */
    private SearchAfter searchAfter;

    /**
     * 筛选场景
     */
    private transient ISearchTemplate searchTemplate;

    @Builder
    private Query(int limit,
                  int offset,
                  List<OrderBy> orders,
                  SearchQuery searchQuery,
                  List<String> whatFieldApiNames,
                  String whatDescribeApiName,
                  int permissionType,
                  IDataRightsParameter dataRightsParameter,
                  Boolean findExplicitTotalNum,
                  Boolean needReturnCountNum,
                  Boolean needReturnQuote,
                  Boolean needRightJoin,
                  IMultiRelationParameter multiRelationParameter,
                  ISpecifiedTableParameter specifiedTableParameter,
                  WhatSearchParameter whatSearchParameter,
                  RecursiveParameter recursiveParameter,
                  ITagParameter tagParameter,
                  String searchSource,
                  IGroupByParameter groupByParameter,
                  SearchAfter searchAfter,
                  ISearchTemplate searchTemplate) {
        this.limit = limit;
        this.offset = offset;
        this.orders = orders;
        this.searchQuery = searchQuery;
        this.whatFieldApiNames = whatFieldApiNames;
        this.whatDescribeApiName = whatDescribeApiName;
        this.permissionType = permissionType;
        this.dataRightsParameter = dataRightsParameter;
        this.findExplicitTotalNum = findExplicitTotalNum;
        this.needReturnCountNum = needReturnCountNum;
        this.needReturnQuote = needReturnQuote;
        this.needRightJoin = needRightJoin;
        this.multiRelationParameter = multiRelationParameter;
        this.specifiedTableParameter = specifiedTableParameter;
        this.whatSearchParameter = whatSearchParameter;
        this.recursiveParameter = recursiveParameter;
        this.tagParameter = tagParameter;
        this.searchSource = searchSource;
        this.groupByParameter = groupByParameter;
        this.searchAfter = searchAfter;
        this.searchTemplate = searchTemplate;
    }

    public static Query fromSearchTemplateQuery(ISearchTemplateQuery templateQuery) {
        SearchQuery searchQuery = SearchQueryImpl.filters(templateQuery.getFilters())
                .and(SearchQueryImpl.wheres(templateQuery.getWheres()));
        return Query.builder()
                .limit(templateQuery.getLimit())
                .offset(templateQuery.getOffset())
                .orders(templateQuery.getOrders())
                .searchAfter(templateQuery.getSearchAfter())
                .searchQuery(searchQuery)
                .permissionType(templateQuery.getPermissionType())
                .dataRightsParameter(templateQuery.getDataRightsParameter())
                .whatFieldApiNames(templateQuery.getWhatFieldApiNames())
                .whatDescribeApiName(templateQuery.getWhatDescribeApiName())
                .findExplicitTotalNum(templateQuery.getFindExplicitTotalNum())
                .needReturnCountNum(templateQuery.getNeedReturnCountNum())
                .needReturnQuote(templateQuery.getNeedReturnQuote())
                .needRightJoin(templateQuery.getNeedRightJoin())
                .multiRelationParameter(templateQuery.getMultiRelationParameter())
                .specifiedTableParameter(templateQuery.getSpecifiedTableParameter())
                .whatSearchParameter(templateQuery.getWhatSearchParameter())
                .recursiveParameter(templateQuery.getRecursiveParameter())
                .tagParameter(templateQuery.getTagParameter())
                .searchSource(templateQuery.getSearchSource())
                .build();
    }

    public ISearchTemplateQuery toSearchTemplateQuery() {
        ISearchTemplateQuery searchTemplateQuery = getSearchQuery()
                .map(SearchQuery::toSearchTemplateQuery)
                .orElseGet(SearchTemplateQuery::new);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setOffset(offset);
        searchTemplateQuery.setOrders(orders);
        searchTemplateQuery.setPermissionType(permissionType);
        searchTemplateQuery.setDataRightsParameter(dataRightsParameter);
        searchTemplateQuery.setWhatDescribeApiName(whatDescribeApiName);
        searchTemplateQuery.setWhatFieldApiNames(whatFieldApiNames);
        searchTemplateQuery.setFindExplicitTotalNum(findExplicitTotalNum);
        searchTemplateQuery.setNeedReturnCountNum(needReturnCountNum);
        searchTemplateQuery.setNeedReturnQuote(needReturnQuote);
        searchTemplateQuery.setNeedRightJoin(needRightJoin);
        searchTemplateQuery.setMultiRelationParameter(multiRelationParameter);
        searchTemplateQuery.setSpecifiedTableParameter(specifiedTableParameter);
        searchTemplateQuery.setWhatSearchParameter(whatSearchParameter);
        searchTemplateQuery.setTagParameter(tagParameter);
        searchTemplateQuery.setSearchSource(searchSource);
        searchTemplateQuery.setRecursiveParameter(recursiveParameter);
        searchTemplateQuery.setSearchAfter(searchAfter);
        return searchTemplateQuery;
    }

    public List<IFilter> getFilters() {
        if (Objects.nonNull(searchQuery)) {
            return Lists.newArrayList(getFiltersByQuery(searchQuery));
        }
        return Lists.newArrayList();
    }

    /**
     * 删除所有满足 filter 的 {@link SearchQuery}
     *
     * @param filter a predicate which returns {@code true} for elements to be removed
     * @return {@code true} if any elements were removed
     */
    public boolean removeIf(Predicate<SearchQuery> filter) {
        Objects.requireNonNull(filter);
        if (Objects.isNull(searchQuery)) {
            return false;
        }
        AtomicBoolean removed = new AtomicBoolean();
        if (remove(searchQuery, filter, removed)) {
            searchQuery = null;
        }
        return removed.get();
    }

    public void and(IFilter... filters) {
        SearchQuery searchQuery = getSearchQuery()
                .map(it -> it.and(Arrays.asList(filters)))
                .orElse(SearchQueryImpl.filters(Arrays.asList(filters)));
        setSearchQuery(searchQuery);
    }

    public void and(SearchQuery searchQuery) {
        if (Objects.isNull(searchQuery)) {
            return;
        }
        SearchQuery newQuery = getSearchQuery()
                .map(it -> it.and(searchQuery))
                .orElse(searchQuery);
        setSearchQuery(newQuery);
    }

    public void or(IFilter... filters) {
        SearchQuery searchQuery = getSearchQuery()
                .map(it -> it.or(filters))
                .orElseGet(() -> {
                    SearchQuery query = SearchQueryImpl.wheres(Collections.emptyList());
                    query.add(Stream.of(filters).map(SearchQueryImpl::filter).toArray(SearchQuery[]::new));
                    return query;
                });
        setSearchQuery(searchQuery);
    }

    public void handlePlainContentFilter() {
        Optional<IFilter> firstPlantFilter = getFilters().stream().filter(x -> Objects.equals(FilterExt.FilterValueTypes.PLAIN_CONTENT, x.getValueType())).findFirst();
        firstPlantFilter.ifPresent(iFilter -> removeIf(x -> {
            if (x.isFilterNode()) {
                IFilter filter = x.getFilter();
                return !Objects.equals(filter, iFilter) && Objects.equals(FilterExt.FilterValueTypes.PLAIN_CONTENT, filter.getValueType());
            }
            return false;
        }));
    }

    private boolean remove(SearchQuery searchQuery, Predicate<SearchQuery> filter, AtomicBoolean removed) {
        if (filter.test(searchQuery)) {
            removed.compareAndSet(false, true);
            return true;
        }
        searchQuery.getSearchQueryContainer().removeIf(it -> remove(it, filter, removed));
        return false;
    }

    private IFilter[] getFiltersByQuery(SearchQuery searchQuery) {
        if (searchQuery.isEmpty()) {
            return new IFilter[]{};
        }
        if (searchQuery.isFilterNode()) {
            return new IFilter[]{searchQuery.getFilter()};
        }
        return searchQuery.getSearchQueryContainer().stream()
                .flatMap(it -> Stream.of(getFiltersByQuery(it)))
                .toArray(IFilter[]::new);
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<OrderBy> getOrders() {
        return orders;
    }

    public void resetOrders(List<OrderBy> orders) {
        this.orders.clear();
        this.orders.addAll(orders);
    }

    public void addOrders(OrderBy... orders) {
        this.orders.addAll(Arrays.asList(orders));
    }

    public Optional<SearchQuery> getSearchQuery() {
        return Optional.ofNullable(searchQuery);
    }

    public void setSearchQuery(SearchQuery searchQuery) {
        this.searchQuery = searchQuery;
    }

    public List<String> getWhatFieldApiNames() {
        return whatFieldApiNames;
    }

    public void setWhatFieldApiNames(List<String> whatFieldApiNames) {
        this.whatFieldApiNames = whatFieldApiNames;
    }

    public String getWhatDescribeApiName() {
        return whatDescribeApiName;
    }

    public void setWhatDescribeApiName(String whatDescribeApiName) {
        this.whatDescribeApiName = whatDescribeApiName;
    }

    public int getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(int permissionType) {
        this.permissionType = permissionType;
    }

    public IDataRightsParameter getDataRightsParameter() {
        return dataRightsParameter;
    }

    public void setDataRightsParameter(IDataRightsParameter dataRightsParameter) {
        this.dataRightsParameter = dataRightsParameter;
    }

    public Boolean getFindExplicitTotalNum() {
        return findExplicitTotalNum;
    }

    public void setFindExplicitTotalNum(Boolean findExplicitTotalNum) {
        this.findExplicitTotalNum = findExplicitTotalNum;
    }

    public Boolean getNeedReturnCountNum() {
        return needReturnCountNum;
    }

    public void setNeedReturnCountNum(Boolean needReturnCountNum) {
        this.needReturnCountNum = needReturnCountNum;
    }

    public Boolean getNeedReturnQuote() {
        return needReturnQuote;
    }

    public void setNeedReturnQuote(Boolean needReturnQuote) {
        this.needReturnQuote = needReturnQuote;
    }

    public Boolean getNeedRightJoin() {
        return needRightJoin;
    }

    public void setNeedRightJoin(Boolean needRightJoin) {
        this.needRightJoin = needRightJoin;
    }

    public IMultiRelationParameter getMultiRelationParameter() {
        return multiRelationParameter;
    }

    public void setMultiRelationParameter(IMultiRelationParameter multiRelationParameter) {
        this.multiRelationParameter = multiRelationParameter;
    }

    public ISpecifiedTableParameter getSpecifiedTableParameter() {
        return specifiedTableParameter;
    }

    public void setSpecifiedTableParameter(ISpecifiedTableParameter specifiedTableParameter) {
        this.specifiedTableParameter = specifiedTableParameter;
    }

    public WhatSearchParameter getWhatSearchParameter() {
        return whatSearchParameter;
    }

    public void setWhatSearchParameter(WhatSearchParameter whatSearchParameter) {
        this.whatSearchParameter = whatSearchParameter;
    }

    public RecursiveParameter getRecursiveParameter() {
        return recursiveParameter;
    }

    public void setRecursiveParameter(RecursiveParameter recursiveParameter) {
        this.recursiveParameter = recursiveParameter;
    }

    public ITagParameter getTagParameter() {
        return tagParameter;
    }

    public void setTagParameter(ITagParameter tagParameter) {
        this.tagParameter = tagParameter;
    }

    public String getSearchSource() {
        return searchSource;
    }

    public void setSearchSource(String searchSource) {
        this.searchSource = searchSource;
    }

    public IGroupByParameter getGroupByParameter() {
        return groupByParameter;
    }

    public void setGroupByParameter(IGroupByParameter groupByParameter) {
        this.groupByParameter = groupByParameter;
    }

    public ISearchTemplate getSearchTemplate() {
        return searchTemplate;
    }

    public void setSearchTemplate(ISearchTemplate searchTemplate) {
        this.searchTemplate = searchTemplate;
    }

    public void mergeSearchQuery(SearchQuery searchQuery) {
        // 判空
        if (Objects.isNull(searchQuery)) {
            return;
        }
        Optional<SearchQuery> before = getSearchQuery();
        if (before.isPresent()) {
            SearchQuery after = before.get().and(searchQuery);
            setSearchQuery(after);
        } else {
            setSearchQuery(searchQuery);
        }
    }
}
