package com.facishare.paas.appframework.metadata.repository.model;


import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.mapping.Mapper;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

import java.io.Serializable;

@Data
public abstract class BaseEntity implements Serializable {
    @TextField(field = @ObjectField(apiName = IObjectData.ID, label = "id"))
    protected String id;

    @NumberField(field = @ObjectField(apiName = IObjectData.VERSION, label = "版本"))
    protected Integer version;

    @TextField(field = @ObjectField(apiName = IObjectData.TENANT_ID, label = "tenant_id"))
    protected String tenantId;

    @TextField(field = @ObjectField(apiName = IObjectData.DESCRIBE_API_NAME, label = "object_describe_api_name"))
    protected String objectDescribeApiName;

    @DateTimeField(field = @ObjectField(apiName = IObjectData.CREATE_TIME, label = "创建时间"))
    protected Long createTime;

    @EmployeeField(field = @ObjectField(apiName = IObjectData.CREATED_BY, label = "创建人"))
    protected String createBy;

    @DateTimeField(field = @ObjectField(apiName = IObjectData.LAST_MODIFIED_TIME, label = "修改时间"))
    protected Long lastModifiedTime;

    @EmployeeField(field = @ObjectField(apiName = IObjectData.LAST_MODIFIED_BY, label = "修改人"))
    protected String lastModifiedBy;

    @SuppressWarnings("unchecked")
    public <T extends BaseEntity> T convert(IObjectData objectData) {
        Mapper mapper = new Mapper();
        return (T) mapper.fromObjectData(objectData, getClass());
    }

    public IObjectData convertTo() {
        Mapper mapper = new Mapper();
        return mapper.toObjectData(this);
    }
}
