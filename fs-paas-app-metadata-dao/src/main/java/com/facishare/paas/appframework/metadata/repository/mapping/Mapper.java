package com.facishare.paas.appframework.metadata.repository.mapping;

import com.facishare.common.RefectionUtil;
import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.FieldInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/03/29
 */
@Slf4j
public class Mapper {

    public Mapper() {
    }

    public <T> IObjectData toObjectData(T entity) {
        Entity entityAnnotation = getEntityAnnotation(entity.getClass());

        Map<String, FieldInfo> fieldInfoMap = getFieldInfoMap(entity.getClass());
        IObjectData objectData = new ObjectData();
        for (Map.Entry<String, FieldInfo> item : fieldInfoMap.entrySet()) {
            String apiName = item.getKey();
            FieldInfo fieldInfo = item.getValue();
            try {
                fieldInfo.read(entity, objectData);
            } catch (Exception e) {
                log.warn("read fail, instance:{},field:{}", entity, apiName);
                throw new IllegalArgumentException(e);
            }
        }
        objectData.setDescribeApiName(entityAnnotation.apiName());
        return objectData;
    }

    public <T> T fromObjectData(IObjectData objectData, Class<T> clazz) {
        if (Objects.isNull(objectData)) {
            return null;
        }

        Map<String, FieldInfo> fieldInfoMap = getFieldInfoMap(clazz);

        T entity;
        try {
            entity = defaultConstructor(clazz);
        } catch (Exception e) {
            log.warn("fromObjectData defaultConstructor fail, class:{}", clazz);
            throw new IllegalArgumentException(e);
        }

        for (Map.Entry<String, FieldInfo> item : fieldInfoMap.entrySet()) {
            FieldInfo fieldInfo = item.getValue();
            try {
                fieldInfo.write(entity, objectData);
            } catch (IllegalAccessException e) {
                log.warn("write fail, instance:{},field:{}", entity, item.getKey());
                throw new IllegalArgumentException(e);
            }
        }
        return entity;
    }

    private <T> T defaultConstructor(Class<T> clazz) throws NoSuchMethodException, InstantiationException,
            IllegalAccessException, java.lang.reflect.InvocationTargetException {
        Constructor<? extends T> constructor = clazz.getDeclaredConstructor();
        if (!constructor.isAccessible()) {
            constructor.setAccessible(true);
        }
        return constructor.newInstance((Object[]) null);
    }

    private Entity getEntityAnnotation(Class<?> entityClass) {
        Entity entityAnnotation = entityClass.getAnnotation(Entity.class);
        if (Objects.isNull(entityAnnotation)) {
            throw new IllegalArgumentException("Not use @entity annotation");
        }
        return entityAnnotation;
    }

    private Map<String, FieldInfo> getFieldInfoMap(Class<?> clazz) {
        return Arrays.stream(RefectionUtil.getDeclaredAndInheritedFields(clazz, false))
                .filter(field -> !isIgnorable(field, true))
                .map(FieldInfo::getByField)
                .collect(Collectors.toMap(FieldInfo::getApiName, it -> it));
    }

    private boolean isIgnorable(Field field, boolean ignorableFinalField) {
        return Modifier.isTransient(field.getModifiers())
                || (ignorableFinalField && Modifier.isFinal(field.getModifiers()));
    }

}
