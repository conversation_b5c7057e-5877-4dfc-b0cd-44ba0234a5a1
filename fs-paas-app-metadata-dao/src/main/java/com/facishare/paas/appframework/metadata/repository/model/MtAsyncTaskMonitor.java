package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtAsyncTaskMonitor.MT_TASK_MONITOR_OBJ_API_NAME)
public class MtAsyncTaskMonitor extends BaseEntity {
    public static final String MT_TASK_MONITOR_OBJ_API_NAME = "AsyncTaskMonitorObj";
    public static final String TASK_ID = "task_id";
    public static final String BIZ_ID = "biz_id";
    public static final String BIZ_API_NAME = "biz_api_name";
    public static final String DESCRIBE_API_NAME = "describe_api_name";
    public static final String TASK_DESCRIBE_DEFAULT_VALUE = "task_describe_default_value";
    public static final String TASK_DESCRIBE = "task_describe";
    public static final String TASK_DESCRIBE_I18N_KEY = "task_describe_i18n_key";
    public static final String TASK_DESCRIBE_PARAMS_I18N_KEY = "task_describe_params_i18n_key";
    public static final String TASK_DESCRIBE_DEFAULT_PARAMS_VALUE = "task_describe_default_params_value";
    public static final String ERROR_MESSAGE = "error_message";
    public static final String RECORD_TYPE = "record_type";
    public static final String TASK_STATUS = "task_status";
    public static final String TASK_CREATE_TIME = "task_create_time";
    public static final String TASK_ESTIMATED_EXECUTION_TIME = "task_estimated_execution_time";
    public static final String TASK_EXECUTION_TIME = "task_execution_time";
    public static final String TASK_TOTAL_NUM = "task_total_num";
    public static final String COMPLETED_NUM = "completed_num";
    public static final String SCHEDULE = "schedule";
    public static final String END_TIME = "end_time";
    public static final String SUBMITTED_BY = "submitted_by";


    @TextField(field = @ObjectField(apiName = TASK_ID, label = "任务id"))
    private String taskId;
    @TextField(field = @ObjectField(apiName = BIZ_ID, label = "业务id"))
    private String bizId;
    @TextField(field = @ObjectField(apiName = BIZ_API_NAME, label = "业务apiName"))
    private String bizApiName;
    @TextField(field = @ObjectField(apiName = DESCRIBE_API_NAME, label = "对象apiName"))
    private String describeApiName;
    @TextField(field = @ObjectField(apiName = TASK_DESCRIBE_DEFAULT_VALUE, label = "任务描述默认值"))
    private String taskDescribeDefaultValue;
    @TextField(field = @ObjectField(apiName = TASK_DESCRIBE_I18N_KEY, label = "任务描述i18nkey"))
    private String taskDescribeI18nKey;
    @LongTextField(field = @ObjectField(apiName = TASK_DESCRIBE_PARAMS_I18N_KEY, label = "国际化key内占位参数列表"), fieldType = ObjectFieldType.JSON)
    private List<String> taskDescribeParamsI18nKey;
    @LongTextField(field = @ObjectField(apiName = TASK_DESCRIBE_DEFAULT_PARAMS_VALUE, label = "国际化默认词条"), fieldType = ObjectFieldType.JSON)
    private Map<String, String> taskDescribeDefaultParamsValue;
    @TextField(field = @ObjectField(apiName = ERROR_MESSAGE, label = "错误信息"))
    private String errorMessage;
    @TextField(field = @ObjectField(apiName = RECORD_TYPE, label = "任务类型"))
    private String recordType;
    @TextField(field = @ObjectField(apiName = TASK_STATUS, label = "任务状态"))
    private String taskStatus;
    @NumberField(field = @ObjectField(apiName = TASK_CREATE_TIME, label = "任务创建时间"))
    private Long taskCreateTime;
    @NumberField(field = @ObjectField(apiName = TASK_ESTIMATED_EXECUTION_TIME, label = "预计执行时间"))
    private Long taskEstimatedExecutionTime;
    @NumberField(field = @ObjectField(apiName = TASK_EXECUTION_TIME, label = "任务执行时间"))
    private Long taskExecutionTime;
    @NumberField(field = @ObjectField(apiName = TASK_TOTAL_NUM, label = "总数量"))
    private Long taskTotalNum;
    @NumberField(field = @ObjectField(apiName = COMPLETED_NUM, label = "完成数量"))
    private Long completedNum;
    @NumberField(field = @ObjectField(apiName = END_TIME, label = "完成时间"))
    private Long endTime;
    @EmployeeField(field = @ObjectField(apiName = SUBMITTED_BY, label = "提交人"))
    private String submittedBy;
    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "是否删除"))
    private Boolean deleted;

    public boolean completed() {
        if (Objects.equals("completed", taskStatus)) {
            return true;
        }
        if (Objects.nonNull(endTime)) {
            return true;
        }
        if (Objects.isNull(taskTotalNum) || taskTotalNum <= 0) {
            return true;
        }
        return Objects.nonNull(completedNum) && completedNum >= taskTotalNum;
    }

    public String jobStatusText() {
        //任务已完成，直接返回已完成状态
        if (completed()) {
            return I18NExt.getOrDefault(I18NKey.JOB_STATUS_END, taskStatus);
        }
        if ("be_queuing".equals(taskStatus)) {
            return I18NExt.getOrDefault(I18NKey.JOB_STATUS_INIT, taskStatus);
        }
        if ("in_process".equals(taskStatus)) {
            return I18NExt.getOrDefault(I18NKey.JOB_STATUS_PROCESSING, taskStatus);
        }
        if ("canceled".equals(taskStatus)) {
            return I18NExt.getOrDefault(I18NKey.JOB_STATUS_CANCELED, taskStatus);
        }
        return taskStatus;
    }

    public Long estimatedStartTime() {
        Long realStartTime = startTime();
        //任务已经开始执行，返回空
        if (Objects.nonNull(realStartTime)) {
            return null;
        }
        long now = System.currentTimeMillis();
        long delayTime = 60 * 1000;
        //指定了开始时间，返回开始时间或当前时间+60秒
        if (Objects.nonNull(taskEstimatedExecutionTime)) {
            return taskEstimatedExecutionTime > now ? taskEstimatedExecutionTime : now + delayTime;
        }
        //默认返回当前时间+60秒
        return now + delayTime;
    }

    public Long startTime() {
        //没有数据需要计算，返回创建时间
        if (Objects.isNull(taskTotalNum) || taskTotalNum <= 0) {
            return taskCreateTime;
        }
        //等待执行，返回空
        if (Objects.equals("be_queuing", taskStatus)) {
            return null;
        }
        //已经开始执行，返回执行时间
        if (Objects.nonNull(taskExecutionTime)) {
            return taskExecutionTime;
        }
        //指定了开始时间，返回开始时间或创建时间
        if (Objects.nonNull(taskEstimatedExecutionTime)) {
            return Objects.nonNull(taskCreateTime) ? Math.max(taskEstimatedExecutionTime, createTime) : taskEstimatedExecutionTime;
        }
        //默认返回创建时间
        return taskCreateTime;
    }

    public Long estimatedCompleteTime() {
        Long realCompleteTime = completeTime();
        //任务已经完成，返回空
        if (Objects.nonNull(realCompleteTime)) {
            return null;
        }
        int totalDataNum = Objects.nonNull(taskTotalNum) ? taskTotalNum.intValue() : 0;
        int completeDataNum = Objects.nonNull(completedNum) ? completedNum.intValue() : 0;
        long delayTime = 60 * 1000;
        long now = System.currentTimeMillis();
        Long realStartTime = startTime();
        Long estimatedStartTime = estimatedStartTime();
        long taskStartTime = ObjectUtils.firstNonNull(realStartTime, estimatedStartTime, now + delayTime);
        if (completeDataNum > 0 && completeDataNum < totalDataNum && now > taskStartTime) {
            // 已开始计算，根据已计算数据量计算预计完成时间，计算公式=当前时间+（当前时间-开始计算时间）*（总数据量-已计算数据量）/已计算数据量 + 60秒（预留时长）
            return now + (now - taskStartTime) * (totalDataNum - completeDataNum) / completeDataNum + delayTime;
        } else {
            // 未开始计算，根据预计开始时间计算预计完成时间，计算公式=预计开始时间+30(预估计算速度为每条/30ms)*总数据量+60秒(预留时长)
            return Math.max(taskStartTime, now) + 30L * totalDataNum + delayTime;
        }
    }

    public Long completeTime() {
        //任务已完成，返回完成时间
        if (Objects.nonNull(endTime)) {
            return endTime;
        }
        //任务已完成，但是没有更新完成时间，返回最后修改时间
        if (completed()) {
            return lastModifiedTime;
        }
        //任务未完成，返回空
        return null;
    }

}
