package com.facishare.paas.appframework.metadata.repository.api;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.BaseEntity;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;

import java.util.List;
import java.util.function.Consumer;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/4.
 */
public interface IRepository<T extends BaseEntity> {
    T create(User user, T entity);

    T update(User user, T entity);

    List<T> bulkCreate(User user, List<T> entityList);

    List<T> bulkUpdate(User user, List<T> entityList);

    List<T> bulkUpdate(IActionContext context, User user, List<T> entityList);

    List<T> bulkUpdateByFields(User user, List<T> entityList, List<String> updateFieldList);

    List<T> bulkUpdateByFields(User user, List<T> entityList, Consumer<List<IObjectData>> consumer, List<String> updateFieldList);

    List<T> bulkUpsert(User user, List<T> entityList);

    void bulkInvalidAndDelete(User user, List<T> entityList);

    QueryResult<T> findByQuery(User user, Query query, Class<T> objectType);

    List<T> findBy(User user, Query query, Class<T> objectType);

    void queryDataAndHandle(User user, Query query, int dataBatchSize, int maxQueryCount, Class<T> objectType, Consumer<List<T>> consumer);

    List<T> bulkInvalid(User user, List<T> entityList);

    List<T> bulkRecover(User user, List<T> entityList);

    List<T> bulkDelete(User user, List<T> entityList);

    void bulkDeleteWithInternalDescribe(User user, List<T> entityList);

    Integer findCountOnly(User user, Query query, Class<T> objectType);
}
