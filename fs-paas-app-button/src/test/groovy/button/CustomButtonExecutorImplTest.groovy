package button

import com.facishare.paas.appframework.button.CustomButtonExecutorImpl
import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.IParamForm
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * create by z<PERSON><PERSON> on 2020/02/12
 */
class CustomButtonExecutorImplTest extends Specification {
    CustomButtonExecutorImpl customButtonExecutor

    void setup() {
        customButtonExecutor = new CustomButtonExecutorImpl()
    }
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def "test checkParam no exception"() {
        given:
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder().args([:]).build()
        when:
        button.getParamForm() >> []
        customButtonExecutor.checkParam(button, describe, arg)
        then:
        noExceptionThrown()
    }

    def "test checkParam ValidateException"() {
        given:
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder().args([:]).build()
        when:
        button.getParamForm() >> [["api_name": "name", "is_required": true, "object_api_name": "AccountObj"]]
        customButtonExecutor.checkParam(button, describe, arg)
        then:
        thrown(ValidateException)
    }

    def "test isRequired"() {
        given:
        IParamForm paramForm = Mock(IParamForm)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IFieldDescribe fieldDescribe = Mock(IFieldDescribe)
        when:
        1 * paramForm.getIsRequired() >> isRequired
        getObjectApiNameNum * paramForm.getObjectApiName() >> objectApiName
        getParamApiNameNum * paramForm.getApiName() >> "name"

        getApiNameNum * describe.getApiName() >> describeApiName
        getFieldDescribeNum * describe.getFieldDescribe("name") >> fieldDescribe

        fieldDescribe.isRequired() >> fieldIsRequired

        def result = customButtonExecutor.isRequired(paramForm, describe)
        println(result)
        then:
        result == expect
        where:
        isRequired | objectApiName | describeApiName || expect | fieldIsRequired | getObjectApiNameNum | getApiNameNum | getParamApiNameNum | getFieldDescribeNum
        true       | ""            | ""              || true   | false           | 0                   | 0             | 0                  | 0

        false      | "123"         | "321"           || false  | false           | 1                   | 1             | 0                  | 0

        false      | "AccountObj"  | "AccountObj"    || true   | true            | 1                   | 1             | 1                  | 1

    }

    def "test isSystemButtonCustomParams"() {
        when:
        CustomButtonExecutorImpl customButtonExecutor = new CustomButtonExecutorImpl()
        IUdefButton button = Mock(IUdefButton)
        IParamForm paramForm = Mock(IParamForm)
        User user = new User("any", userId)

        button.getDefineType() >> buttonDefineType
        paramForm.getDefineType() >> paramDefineType
        then:
        println button.getDefineType()
        println paramForm.getDefineType()
        println user
        def result = customButtonExecutor.isSystemButtonCustomParams(button, paramForm, user)
        result == except
        where:
        buttonDefineType | paramDefineType | userId   || except
        "system"         | "custom"        | "-10000" || false
        "system"         | "system"        | "1000"   || true
        "custom"         | "system"        | "-10000" || true
        "system"         | "system"        | "-10000" || false
    }
}
