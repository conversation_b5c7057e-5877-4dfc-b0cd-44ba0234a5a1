package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.metadata.ButtonActionType;
import com.facishare.paas.appframework.metadata.UdefActionExt;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import static com.facishare.paas.appframework.metadata.ButtonActionType.*;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/06/20
 */
public enum ActionExecutorType {
    CONVERT(Convert),
    UPDATES(Updates),
    SEND_CRM_REMIND(SendCRMRemind),
    SEND_MAIL(SendMail),
    CURRENT_FUNCTION(CustomFunction, UdefActionExt.CURRENT),
    VALIDATE_FUNCTION(CustomFunction, UdefActionExt.PRE),
    POST_FUNCTION(CustomFunction, UdefActionExt.POST),
    BULK_FUNCTION(CustomFunction, UdefActionExt.BULK),
    POST_REDIRECT_FUNCTION(CustomFunction, UdefActionExt.REDIRECT),
    CUSTOM_BIZ(CustomBiz),
    UI_EVENT(UIEvent),
    AI_AGENT(AIAgent),
    ;

    private ButtonActionType actionType;
    private String actionStage;

    ActionExecutorType(ButtonActionType actionType) {
        this.actionType = actionType;
    }

    ActionExecutorType(ButtonActionType actionType, String actionStage) {
        this.actionType = actionType;
        this.actionStage = actionStage;
    }

    public String getActionStage() {
        return actionStage;
    }

    private static final Table<ButtonActionType, String, ActionExecutorType> actionExecutorTypeTable = HashBasedTable.create();

    static {
        for (ActionExecutorType type : values()) {
            actionExecutorTypeTable.put(type.actionType, Strings.nullToEmpty(type.actionStage), type);
        }
    }

    public static ActionExecutorType getActionType(ButtonActionType actionType, String actionStage) {
        if (CustomBiz.equals(actionType)) {
            return actionExecutorTypeTable.get(actionType, "");
        }
        if (Strings.isNullOrEmpty(actionStage) && actionExecutorTypeTable.containsRow(actionType)) {
            actionStage = CustomFunction == actionType ? UdefActionExt.CURRENT : "";
        }
        return actionExecutorTypeTable.get(actionType, actionStage);
    }

    public ButtonActionType getActionType() {
        return actionType;
    }
}
