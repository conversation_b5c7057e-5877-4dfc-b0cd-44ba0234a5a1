package com.facishare.paas.appframework.button.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.dto.BatchDataExecuteFunction;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.function.dto.batchDataExecuteTask;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/06/20
 */
@Slf4j
@Component
public class ValidateFuncAction extends AbstractFuncAction {

    private static final Set<String> SHIELD_FIELD_TYPES = ImmutableSet.of(
            IFieldType.QUOTE, IFieldType.COUNT
    );
    public static final String IGNORE_SENDING_REMIND = "ignoreSendingRemind";

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.VALIDATE_FUNCTION;
    }

    private boolean isBlock(Map ret) {
        Object block = (ret).get("block");
        if (block instanceof Boolean) {
            return (Boolean) block;
        }
        return true;
    }

    @Override
    protected void initDetails(User user, ButtonExecutor.Arg arg, Map<String, List<IObjectData>> detailData) {
        if (CollectionUtils.empty(detailData)) {
            return;
        }
        if (!AppFrameworkConfig.isValidationFunctionMergeDetailDataGray(user.getTenantId(), arg.getDescribeApiName())) {
            return;
        }
        detailData.forEach((apiName, details) -> {
            LongAdder longAdder = new LongAdder();
            // 这里需要生成数据编号，供后续自定义组件回填数据，触发计算时使用
            details.forEach(it -> {
                // 设置data中的 apiName 和 tenantId
                it.setDescribeApiName(apiName);
                it.setTenantId(user.getTenantId());
                it.set(ObjectDataExt.MARK_API_NAME, longAdder.toString());
                longAdder.increment();
            });
        });
    }

    @Override
    protected void validateObjectData(RunResult runResult, IObjectDescribe objectDescribe, User user, Collection<String> ignoreFields) {
        Map<String, Object> diffData = runResult.getDiffData();
        Map<String, Object> validDiffData = Maps.newHashMap();
        if (CollectionUtils.notEmpty(diffData)) {
            if (CollectionUtils.notEmpty(ignoreFields)) {
                List<String> keyList = diffData.keySet().stream()
                        .filter(it -> !ignoreFields.contains(it))
                        .collect(Collectors.toList());
                keyList.forEach(it -> validDiffData.put(it, diffData.get(it)));
            } else {
                validDiffData.putAll(diffData);
            }
            // 校验函数修改的主对象数据是否合法
            IObjectData funcModifyData = ObjectDataExt.of(validDiffData);
            ObjectDataExt.correctValue(user, Lists.newArrayList(funcModifyData), objectDescribe);
            metaDataMiscService.validateDataType(objectDescribe, Lists.newArrayList(funcModifyData), user);
        }
    }

    @Override
    protected void handleData(User user, IObjectData objectData, RunResult runResult, Collection<String> ignoreFields) {
        if (Objects.isNull(objectData)) {
            return;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_VALIDATE_FUNCTION_SUPPORT_RELEVANT_TEAM, user.getTenantId())) {
            ObjectDataExt.of(objectData).putAllIgnoreFields(runResult.getDiffData(), ignoreFields, ObjectDataExt.SPECIAL_RELEVANT_TEAM);
            return;
        }
        //Merge changed object data
        ObjectDataExt.of(objectData).putAllIgnoreFields(runResult.getDiffData(), ignoreFields);
    }

    @Override
    protected RunResult executeFunction(ButtonExecutor.Arg arg, ActionExecutorContext context, IUdefFunction function, Map<String, Object> functionArgMap, Map<String, List<IObjectData>> details) {
        if (arg.isTriggerBatchFunc()) {
            BatchDataExecuteFunction.Result result = functionLogicService.batchDataExecuteFunction(context.getUser(), function, arg.getDataIds(), functionArgMap);
            return RunResult.builder()
                    .success(result.isSuccess())
                    .functionResult(result.getFunctionResult())
                    .errorInfo(result.getErrorInfo())
                    .returnType(result.getReturnType())
                    .build();
        }
        return super.executeFunction(arg, context, function, functionArgMap, details);
    }

    @Override
    protected ButtonExecutor.Result handleResult(User user, ButtonExecutor.Arg arg, RunResult runResult) {
        ValidateResult validateResult = ValidateResult.fromRunResult(runResult);
        if (Objects.nonNull(validateResult)) {
            ButtonExecutor.Result result = validateResult.toButtonExecutorResult();
            result.setFunctionResult(runResult.getFunctionResult());
            return result;
        }
        //验证函数返回的是Map
        Object ret = runResult.getFunctionResult();
        // 兼容返回值为空map的情况
        if (AppFrameworkConfig.isValidationFunctionMergeDetailDataGray(user.getTenantId(), arg.getDescribeApiName())) {
            if (ret instanceof Map || Objects.isNull(ret)) {
                Map map = CollectionUtils.nullToEmpty((Map) ret);
                Object error = map.get("error");
                Object errorMessage = map.get("errorMessage");
                Object ignoreSendingRemind = Objects.isNull(map.get(IGNORE_SENDING_REMIND)) ? false : map.get(IGNORE_SENDING_REMIND);
                error = Objects.isNull(error) ? false : error;
                if (error instanceof Boolean) {
                    ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                            .hasReturnValue((Boolean) error)
                            .returnValue(getReturnValue((Boolean) error, errorMessage))
                            .block(isBlock(map))
                            .detailDataMergeResult(getDetailDataMergeResult(user, arg, runResult.getDiffDetails()))
                            .functionResult(ret)
                            .build();
                    if (ignoreSendingRemind instanceof Boolean) {
                        result.setIgnoreSendingRemind((Boolean) ignoreSendingRemind);
                    }
                    return result;
                }
            }
            return null;
        }
        if (ret instanceof Map) {
            Object error = ((Map) ret).get("error");
            Object errorMessage = ((Map) ret).get("errorMessage");
            Object ignoreSendingRemind = Objects.isNull(((Map) ret).get(IGNORE_SENDING_REMIND)) ? false : ((Map) ret).get(IGNORE_SENDING_REMIND);
            if (error instanceof Boolean) {
                ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                        .hasReturnValue((Boolean) error)
                        .returnValue(getReturnValue((Boolean) error, errorMessage))
                        .block(isBlock((Map) ret))
                        .functionResult(ret)
                        .build();
                if (ignoreSendingRemind instanceof Boolean) {
                    result.setIgnoreSendingRemind((Boolean) ignoreSendingRemind);
                }
                return result;
            }
        }
        return null;
    }

    private ButtonExecutor.DetailDataMergeResult getDetailDataMergeResult(User user, ButtonExecutor.Arg arg, Map<String, List<Map<String, Object>>> details) {
        if (!AppFrameworkConfig.isValidationFunctionMergeDetailDataGray(user.getTenantId(), arg.getDescribeApiName())) {
            return null;
        }
        if (CollectionUtils.empty(details)) {
            return null;
        }
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesCreateWithMaster(user.getTenantId(), arg.getDescribeApiName());
        if (CollectionUtils.empty(detailDescribes)) {
            return null;
        }
        Map<String, IObjectDescribe> detailObjectDescribeMap = detailDescribes.stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        Map<String, List<IObjectData>> oldDetails = arg.toDetails();
        // 设置从对象数据排序
        ObjectDataExt.setOrderForDetailData(ObjectDataExt.convertMap2Data(details));
        return diffDetail(oldDetails, details, detailObjectDescribeMap);
    }

    ButtonExecutor.DetailDataMergeResult diffDetail(Map<String, List<IObjectData>> oldDetail,
                                                    Map<String, List<Map<String, Object>>> newDetail,
                                                    Map<String, IObjectDescribe> describeMap) {
        List<IObjectData> detailsToAdd = Lists.newArrayList();
        List<IObjectData> detailsToUpdate = Lists.newArrayList();
        List<IObjectData> detailsToDelete = Lists.newArrayList();
        newDetail.forEach((apiName, details) -> {
            IObjectDescribe describe = describeMap.get(apiName);
            // 不是主从一起新建的从,不处理
            if (Objects.isNull(describe)) {
                log.warn("diffDetail object describe does not exist, apiName: {}, all detail object apiName: {}", apiName, describeMap.keySet());
                return;
            }
            Map<String, IObjectData> oldDataMap = CollectionUtils.nullToEmpty(oldDetail.get(apiName)).stream()
                    .map(ObjectDataExt::of)
                    .collect(Collectors.toMap(ObjectDataExt::getMark, ObjectDataExt::getObjectData));
            Set<String> markSet = Sets.newHashSet();
            for (Map<String, Object> detail : details) {
                ObjectDataExt newDetailData = ObjectDataExt.of(detail);
                // 补充临时id
                newDetailData.fillTemporaryId();
                String mark = newDetailData.getMark();
                IObjectData oldData = oldDataMap.get(mark);
                if (oldData == null) {
                    Map<String, Object> diffMap = diff(describe, newDetailData, new ObjectData());
                    ObjectDataExt dataExt = ObjectDataExt.of(diffMap);
                    // 补充对象apiName和企业信息
                    dataExt.setDescribeApiName(describe.getApiName());
                    dataExt.setTenantId(describe.getTenantId());
                    dataExt.setId(newDetailData.getId());
                    dataExt.setTemporaryId(newDetailData.getTemporaryId());
                    dataExt.setOrderBy(newDetailData.getOrderBy());
                    detailsToAdd.add(dataExt.getObjectData());
                    continue;
                }
                // diff 的逻辑和 UI 事件保持一致
                Map<String, Object> diffMap = diff(describe, newDetailData, oldData);

                ObjectDataExt dataExt = ObjectDataExt.of(diffMap);
                dataExt.setTenantId(describe.getTenantId());
                dataExt.setDescribeApiName(describe.getApiName());
                // 此处id需要保留函数返回的结果
                dataExt.setId(newDetailData.getId());
                dataExt.setTemporaryId(newDetailData.getTemporaryId());
                dataExt.setOrderBy(newDetailData.getOrderBy());
                detailsToUpdate.add(dataExt.getObjectData());
                markSet.add(mark);
            }
            List<IObjectData> dataList = oldDataMap.entrySet().stream()
                    .filter(it -> !markSet.contains(it.getKey()))
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
            detailsToDelete.addAll(dataList);
        });
        return ButtonExecutor.DetailDataMergeResult.of(detailsToAdd, detailsToUpdate, detailsToDelete);
    }

    private Map<String, Object> diff(IObjectDescribe describe, ObjectDataExt newDetailData, IObjectData oldData) {
        Map<String, Object> diffMap = ObjectDataExt.of(oldData).diff(newDetailData, describe);
        // 删除禁用字段以及禁止函数修改的字段
        removeFields(diffMap, describe);
        return diffMap;
    }

    private void removeFields(Map<String, Object> diffMap, IObjectDescribe describe) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe).getFieldDescribes();
        for (IFieldDescribe field : fields) {
            if (needRemoveField(field)) {
                diffMap.remove(field.getApiName());
            }
        }
    }

    private boolean needRemoveField(IFieldDescribe fieldDescribe) {
        if (BooleanUtils.isNotTrue(fieldDescribe.isActive())) {
            return true;
        }
        // 主从字段不能改
        if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
            return true;
        }
        // 引用,统计字段不能改
        if (SHIELD_FIELD_TYPES.contains(fieldDescribe.getType())) {
            return true;
        }
        // 多货币字段不能改
        return FieldDescribeExt.of(fieldDescribe).isMultiCurrencyFields();
    }

    private static Object getReturnValue(Boolean error, Object errorMessage) {
        // message 不能为空
        if (errorMessage != null) {
            String message = errorMessage.toString();
            if (StringUtils.isNotEmpty(message)) {
                return message;
            }
        }
        return BooleanUtils.isTrue(error) ? I18N.text(I18NKey.VALIDATE_FUNC_ERROR_MESSAGE) : null;
    }

    @Override
    protected ButtonExecutor.Result handleException(RuntimeException e, IUdefFunction udefFunction) {
        if (e instanceof ValidateException) {
            throw e;
        }
        Throwable rootCause = ExceptionUtils.getRootCause(e);
        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .build();
        if (rootCause instanceof SocketTimeoutException || rootCause instanceof FunctionTimeoutException) {
            result.setReturnValue(udefFunction.getApiName() + I18N.text(I18NKey.FUNC_TIMEOUT));
            return result;
        }
        if (e instanceof FunctionException) {
            String message = I18N.text(I18NKey.VERIFY_FUNC_FAIL) + e.getMessage();
            if (((FunctionException) e).getErrorCode() == AppFrameworkErrorCode.FUNCTION_USER_BIZ_ERROR.getCode()) {
                //函数中的业务异常属于用户通过Fx.message.throwErrorMessage抛出的异常直接提示即可。
                message = e.getMessage();
            }
            result.setReturnValue(message);
            return result;
        }

        result.setReturnValue(I18N.text(I18NKey.VERIFY_FUNC_FAIL));
        return result;
    }

    @Data
    public static class ValidateResult {

        private static final String VALIDATE_RESULT_RETURN_TYPE = "ValidateResult";

        private Boolean success;
        private String errorMessage;
        private Object data;
        private String nonBlockMessage;

        public static ValidateResult fromRunResult(RunResult runResult) {
            if (Objects.isNull(runResult)) {
                return ValidateResult.error("invoke function fail!");
            }
            if (VALIDATE_RESULT_RETURN_TYPE.equals(runResult.getReturnType())) {
                return JacksonUtils.fromJson(JacksonUtils.toJson(runResult.getFunctionResult()), ValidateResult.class);
            }
            return null;
        }

        public static ValidateResult from(batchDataExecuteTask.Result result) {
            if (Objects.isNull(result)) {
                return ValidateResult.error("invoke function fail!");
            }
            if (!result.isSuccess()) {
                return ValidateResult.error(result.getErrorInfo());
            }
            if (VALIDATE_RESULT_RETURN_TYPE.equals(result.getReturnType())) {
                return JacksonUtils.fromJson(JacksonUtils.toJson(result.getFunctionResult()), ValidateResult.class);
            }
            return null;
        }

        private static ValidateResult error(String errorMessage) {
            ValidateResult validateResult = new ValidateResult();
            validateResult.setErrorMessage(errorMessage);
            validateResult.setSuccess(false);
            return validateResult;
        }

        public ButtonExecutor.Result toButtonExecutorResult() {
            boolean error = Objects.nonNull(success) && BooleanUtils.isNotTrue(success);
            return ButtonExecutor.Result.builder()
                    .hasReturnValue(error)
                    .returnValue(getReturnValue(error, errorMessage))
                    .functionResult(this)
                    .build();
        }
    }
}
