package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2018/09/25
 */
public interface BulkCreateData {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String dataListJson;
        String describeApiName;
        boolean isTool;
        boolean triggerWorkFlow;
        Boolean calculateDefaultValue;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        @SerializedName("api_name")
        String describeApiName;

        @JSONField(name = "data_list")
        @JsonProperty(value = "data_list")
        @SerializedName("data_list")
        List<ObjectDataDocument> data;

    }

}
