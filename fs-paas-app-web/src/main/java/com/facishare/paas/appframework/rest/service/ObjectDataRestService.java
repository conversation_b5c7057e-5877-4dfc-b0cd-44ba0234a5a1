package com.facishare.paas.appframework.rest.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext.BizInfo;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.appframework.metadata.dto.DataConflictsResult;
import com.facishare.paas.appframework.metadata.dto.DataSnapshotResult;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.condition.AbstractConditions;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.paas.metadata.api.condition.RangeConditions;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IRelatedListQuery;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.support.JsonFieldHandler;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.toJSON;
import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

@Service
@Slf4j
public class ObjectDataRestService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private FieldDataConverterManager fieldDataConverterManager;
    @Autowired
    private DataPrivilegeService dataPrivilegeService;
    @Autowired
    private CustomSceneService customSceneService;
    @Autowired
    private CrmService crmService;
    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;
    @Autowired
    private EmployeeService employeeService;

    /**
     * 调用方name
     */
    private static final String OPENAPI_PEER_NAME = "OpenAPI";

    private static final String SMARTFORM_PEER_NAME = "SmartForm";


    @SuppressWarnings("unchecked")
    public CreateData.Result createObjectData(CreateData.Arg arg, RequestContext requestContext) {
        // 日志监控
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#createObjectData");
        IObjectData objectData = new ObjectData();
        CreateData.Result result = new CreateData.Result();
        //检查权限
        checkPrivilegeForOpenAPI(requestContext, arg.getDescAPIName());
        stopWatch.lap("checkPrivilegeForOpenAPI");

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescAPIName());
        stopWatch.lap("findObject");

        objectData.fromJsonString(arg.getJson());
        objectData.setDescribeId(describe.getId());
        objectData.setDescribeApiName(describe.getApiName());
        objectData.setTenantId(requestContext.getTenantId());
        //增加默认业务类型
        if (Strings.isNullOrEmpty(objectData.getRecordType()) && describe.containsField(MultiRecordType.RECORD_TYPE)) {
            objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        }
        stopWatch.lap("MultiRecordType");
        //设置默认生命状态
        if (describe.containsField(ObjectLifeStatus.LIFE_STATUS_API_NAME)) {
            if (StringUtils.equalsIgnoreCase(SMARTFORM_PEER_NAME, requestContext.getPeerName())) {
                ObjectDataExt.of(objectData).setLifeStatus(ObjectLifeStatus.INEFFECTIVE);
            } else if (Strings.isNullOrEmpty(ObjectDataExt.of(objectData).getLifeStatusText())) {
                ObjectDataExt.of(objectData).setLifeStatus(ObjectLifeStatus.NORMAL);
            }
        }
        stopWatch.lap("ObjectLifeStatus");

        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        if (DefObjConstants.PACKAGE_NAME_CRM.equals(describe.getPackage())
                && !ObjectDescribeExt.isSFAObject(describe.getApiName())
                && !Objects.equals(Boolean.TRUE, arg.getIsTool())) {
            List<TeamMemberInfoPoJo> relevantTeamFromObjectData = objectDataExt.getRelevantTeamFromObjectData();
            addRelevantTeam(objectDataExt, relevantTeamFromObjectData);
        }
        IActionContext context = ActionContextExt.of(requestContext.getUser(), requestContext)
                .setIsTool(arg.getIsTool())
                .setIsSpecifyTime(arg.getIsSpecifyTime())
                .getContext();

        MetaDataActionService.CreateAttributes createAttributes = MetaDataActionService.CreateAttributes.builder()
                .calculateDefaultValue(arg.getCalculateDefaultValue())
                .handleContext(x -> context)
                .build();

        IObjectData created = serviceFacade.saveObjectData(requestContext.getUser(), objectData, createAttributes);
        stopWatch.lap("saveObjectData");
        serviceFacade.log(requestContext.getUser(), EventType.ADD, ActionType.Add, describe, Lists.newArrayList(created));
        stopWatch.lap("recordLog");

        if (requestContext.isFromSmartForm()) {
            //对于智能表单，新建自定义对象触发审批流和工作流
            if (!ObjectDescribeExt.isSFAObject(arg.getDescAPIName())) {
                startCreateApprovalAndWorkFlow(requestContext, created);
            }
            stopWatch.lap("startCreateApprovalAndWorkFlow");
        } else if (Boolean.TRUE.equals(arg.getTriggerWorkFlow())) {
            //对于OpenAPI，新建自定义对象触发工作流
            if (!ObjectDescribeExt.isSFAObject(arg.getDescAPIName())) {
                startCreateWorkFlow(requestContext, created);
            }
            stopWatch.lap("startCreateWorkFlow");
        }
        stopWatch.logSlow(5000);

        result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        result.setObjectDataDocument(ObjectDataDocument.of(created));
        return result;
    }

    public BulkCreateData.Result batchCreateObjectData(BulkCreateData.Arg arg, RequestContext requestContext) {
        // 日志监控
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#batchCreateObjectData");
        JSONArray dataJsonArray = JSONArray.parseArray(arg.getDataListJson());
        stopWatch.lap("parseArray");
        if (CollectionUtils.empty(dataJsonArray)) {
            stopWatch.logSlow(500);
            return BulkCreateData.Result.builder().build();
        }
        //检查权限
        checkPrivilegeForOpenAPI(requestContext, arg.getDescribeApiName());
        stopWatch.lap("checkPrivilegeForOpenAPI");

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescribeApiName());
        stopWatch.lap("findObject");
        String describeId = describe.getId();
        List<IObjectData> objectDataList = dataJsonArray.stream().map(object -> {
            JSONObject json = object instanceof JSONObject ? (JSONObject) object : (JSONObject) toJSON(object);
            IObjectData objectData = new ObjectData();
            objectData.fromJsonString(json.toJSONString());
            objectData.setDescribeApiName(arg.getDescribeApiName());
            objectData.setTenantId(requestContext.getTenantId());
            objectData.setDescribeId(describeId);
            return objectData;
        }).collect(Collectors.toList());
        stopWatch.lap("jsonArrayToObjectDataList");

        //增加默认业务类型
        if (describe.containsField(MultiRecordType.RECORD_TYPE)) {
            objectDataList.stream()
                    .filter(objectData -> Strings.isNullOrEmpty(objectData.getRecordType()))
                    .forEach(objectData -> objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT));
        }
        stopWatch.lap("MultiRecordType");
        //设置默认生命状态
        if (describe.containsField(ObjectLifeStatus.LIFE_STATUS_API_NAME)) {
            objectDataList.forEach(objectData -> {
                if (StringUtils.equalsIgnoreCase(SMARTFORM_PEER_NAME, requestContext.getPeerName())) {
                    ObjectDataExt.of(objectData).setLifeStatus(ObjectLifeStatus.INEFFECTIVE);
                } else if (Strings.isNullOrEmpty(ObjectDataExt.of(objectData).getLifeStatusText())) {
                    ObjectDataExt.of(objectData).setLifeStatus(ObjectLifeStatus.NORMAL);
                }
            });
        }
        stopWatch.lap("ObjectLifeStatus");

        // 相关团队
        if (DefObjConstants.PACKAGE_NAME_CRM.equals(describe.getPackage())
//                && IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(describe.getDefineType())
                && !Objects.equals(Boolean.TRUE, arg.isTool())) {
            objectDataList.stream()
                    .map(ObjectDataExt::of)
                    .forEach(objectDataExt -> {
                        List<TeamMemberInfoPoJo> relevantTeamFromObjectData = objectDataExt.getRelevantTeamFromObjectData();
                        addRelevantTeam(objectDataExt, relevantTeamFromObjectData);
                    });
        }
        stopWatch.lap("TeamMember");

        MetaDataActionService.CreateAttributes createAttributes = MetaDataActionService.CreateAttributes.builder()
                .calculateDefaultValue(arg.getCalculateDefaultValue())
                .build();

        //通过接口新建的和手动新建的区分开
        List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(objectDataList, requestContext.getUser(), createAttributes);
        stopWatch.lap("bulkSaveObjectData");
        serviceFacade.log(requestContext.getUser(), EventType.ADD, ActionType.Add, describe, dataList);
        stopWatch.lap("recordLog");
        stopWatch.logSlow(5000);

        //触发工作流
        if (arg.isTriggerWorkFlow() && !ObjectDescribeExt.isSFAObject(arg.getDescribeApiName())) {
            batchStartCreateWorkFlow(requestContext, dataList);
        }

        return BulkCreateData.Result.builder()
                .describeApiName(describe.getApiName())
                .data(dataList.stream().map(ObjectDataDocument::of).collect(Collectors.toList()))
                .build();
    }

    private void addRelevantTeam(ObjectDataExt objectDataExt, List<TeamMemberInfoPoJo> relevantTeamFromObjectData) {
        if (CollectionUtils.empty(relevantTeamFromObjectData)) {
            Optional<String> ownerId = objectDataExt.getOwnerId();
            ownerId.ifPresent(a -> {
                TeamMember teamMember = new TeamMember(a, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
                objectDataExt.addTeamMembers(Lists.newArrayList(teamMember));
            });
        }
    }

    @SuppressWarnings("unchecked")
    public UpdateData.Result updateObjectData(UpdateData.Arg arg, RequestContext requestContext) {
        IObjectData objectData = new ObjectData();
        UpdateData.Result result = new UpdateData.Result();
        //check privilege
        checkPrivilegeForOpenAPI(requestContext, arg.getDescribeAPIName());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescribeAPIName());

        objectData.fromJsonString(arg.getDataJson());
        objectData.setId(arg.getDataId());
        objectData.setDescribeId(describe.getId());
        objectData.setDescribeApiName(arg.getDescribeAPIName());
        objectData.setTenantId(requestContext.getTenantId());

        //移除不支持编辑的字段
        ObjectDataExt.of(objectData).removeFieldsNotSupportEdit(describe);
        //校验币种是否存在
        ObjectDataExt.of(objectData).checkCurrencyOption(describe);

        IActionContext context = ActionContextExt.of(requestContext.getUser(), requestContext)
                .setTriggerFlow(arg.isTriggerFlow())
                .setIsTool(arg.isTool())
                .setIsSpecifyTime(arg.isSpecifyTime())
                .setNotValidate(arg.isNotValidate())
                .setSkipImmutableFieldValidate(Boolean.TRUE.equals(arg.getSkipImmutableFieldValidate()))
                .setActionType("updateDataByRest")
                .set("returnFullData", true)
                .getContext();

        IObjectData dbObjectData = serviceFacade.findObjectDataIgnoreAll(requestContext.getUser(), arg.getDataId(),
                arg.getDescribeAPIName());
        // 校验数据的版本号是不是最新的
        DataConflictsResult dataConflictsResult = checkDataVersion(requestContext.getUser(), describe, objectData, dbObjectData, arg.isProcessDataConflicts());
        if (Objects.nonNull(dataConflictsResult)) {
            result.setDataConflictsResult(dataConflictsResult);
            result.setVersionCheckBlocked(true);
            return result;
        }

        //校验数据权限
        if (Boolean.TRUE.equals(arg.isApplyDataPrivilegeCheck())) {
            serviceFacade.doDataPrivilegeCheck(requestContext.getUser(), Lists.newArrayList(dbObjectData),
                    describe, ObjectAction.UPDATE.getActionCode());
        }

        //校验验证规则
        if (Boolean.TRUE.equals(arg.isApplyValidationRule())) {
            infraServiceFacade.doValidate(requestContext.getUser(), IRule.UPDATE, describe, Lists.newArrayList(objectData));
        }

        Map<String, Object> updatedFieldMap = Maps.newHashMap();
        IObjectData updated = objectData;

        if (arg.isUseSnapshotForApproval()) {
            RequestUtil.setModifyLogHidden();
            BizInfo bizInfo = BizInfo.builder()
                    .biz(ApprovalFlow.getCode())
                    .otherBizId(RequestUtil.getOtherBizId())
                    .bizId(RequestUtil.getBizId())
                    .build();
            DataSnapshotResult snapshotResult = dataSnapshotLogicService.createSnapshotWithData(requestContext.getUser(),
                    objectData, dbObjectData, describe, bizInfo);
            //返回快照供审批流展示详情使用
            Map<String, Object> mergedSnapshot = snapshotResult.getMergedMasterSnapshot();
            result.setSnapshot(mergedSnapshot);
            //将快照合并到dbData中供修改记录使用
            updatedFieldMap.putAll(snapshotResult.getDiffMap());
            ObjectDataExt.of(dbObjectData).putAll(snapshotResult.getSnapshot().getMasterSnapshot());
        } else {
            //编辑前计算
            serviceFacade.calculateForBatchEditData(User.systemUser(context.getEnterpriseId()), Lists.newArrayList(objectData), Lists.newArrayList(dbObjectData), describe);
            Map<String, Object> map = Maps.newHashMap();
            ((ObjectData) objectData).getContainerDocument().forEach((x, y) -> map.put(String.valueOf(x), y));
            //diff数据记修改记录
            updatedFieldMap = ObjectDataExt.of(dbObjectData).diff(objectData, describe);
            if (Boolean.TRUE.equals(arg.isIncrementalUpdate())) {
                updated = incrementalUpdate(objectData, map, context, requestContext.getUser());
            } else {
                updated = serviceFacade.updateObjectData(requestContext.getUser(), objectData, context);
            }
        }

        result.setObjectDataDocument(Objects.isNull(updated) ? ObjectDataDocument.of(objectData) : ObjectDataDocument.of(updated));
        if (arg.isIncludeDescribe()) {
            result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        }
        //记录修改日志
        if (!arg.isSkipModifyLog() && Objects.nonNull(updated) && CollectionUtils.notEmpty(updatedFieldMap)) {
            recordLog(updated, requestContext, describe, updatedFieldMap, dbObjectData);
        }
        return result;
    }

    private DataConflictsResult checkDataVersion(User user, IObjectDescribe objectDescribe, IObjectData objectData, IObjectData dbObjectData, boolean processDataConflicts) {
        if (processDataConflicts) {
            return processDataConflicts(user, objectDescribe, dbObjectData, objectData);
        }
        ObjectDataExt.checkDataVersion(objectData, dbObjectData);
        return null;
    }

    private DataConflictsResult processDataConflicts(User user, IObjectDescribe objectDescribe, IObjectData dbObjectData, IObjectData objectData) {
        if (!ObjectDataExt.checkIfDataVersionConflict(objectData, dbObjectData)) {
            return null;
        }
        return serviceFacade.processDataConflicts(user, objectDescribe, dbObjectData, objectData);
    }

    private IObjectData incrementalUpdate(IObjectData objectData, Map<String, Object> map, IActionContext context, User user) {
        List<IObjectData> result;
        if (AppFrameworkConfig.isUpdateIgnoreOtherGrayApiName(objectData.getDescribeApiName())) {
            result = serviceFacade.batchUpdateByFields(context, Lists.newArrayList(objectData), Lists.newArrayList(map.keySet()));
        } else {
            result = serviceFacade.bulkUpdateWithMap(user, Lists.newArrayList(objectData), map, context);
        }
        return CollectionUtils.empty(result) ? null : result.get(0);
    }

    private void recordLog(IObjectData data, RequestContext requestContext, IObjectDescribe describe, Map<String, Object> updatedFieldMap, IObjectData dbObjectData) {
        serviceFacade.log(requestContext.getUser(), EventType.MODIFY, ActionType.Modify, describe, data, updatedFieldMap, dbObjectData, requestContext.getBizId());
    }

    @SuppressWarnings("unchecked")
    public DeletedById.Result invalidByDataId(DeletedById.Arg arg, RequestContext requestContext) {
        DeletedById.Result result = new DeletedById.Result();
        //检查权限
        checkPrivilegeForOpenAPI(requestContext, arg.getDescribeAPIName());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescribeAPIName());
        IObjectData objectData;
        if (!ObjectDescribeExt.isSFAObject(arg.getDescribeAPIName())) {
            objectData = serviceFacade.findObjectDataIgnoreFormula(requestContext.getUser(), arg.getDataId(),
                    describe.getApiName());
        } else {
            objectData = new ObjectData();
            objectData.setId(arg.getDataId());
            objectData.setTenantId(requestContext.getTenantId());
            objectData.setDescribeApiName(arg.getDescribeAPIName());
            objectData.setDescribeId(describe.getId());
        }

        IActionContext context = ActionContextExt.of(requestContext.getUser(), requestContext).getContext();
        //最终操作成功的列表,用于写日志。
        List<IObjectData> resultListToLog = Lists.newArrayList();
        IObjectData deleted = serviceFacade.invalid(objectData, requestContext.getUser(), context);
        resultListToLog.add(deleted);
        //处理从对象
        List<IObjectDescribe> detailObjectDescribeList = handleDetailDataInvalid(requestContext, describe, objectData, resultListToLog);
        Map<String, IObjectDescribe> describeMap = detailObjectDescribeList.stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        describeMap.put(describe.getApiName(), describe);
        serviceFacade.masterDetailLog(requestContext.getUser(), EventType.DELETE, ActionType.Invalid, describeMap, resultListToLog);
        result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        result.setObjectDataDocument(ObjectDataDocument.of(deleted));
        return result;
    }

    private List<IObjectDescribe> handleDetailDataInvalid(RequestContext requestContext, IObjectDescribe masterDescribe,
                                                          IObjectData masterData, List<IObjectData> resultListToLog) {
        if (ObjectDescribeExt.isSFAObject(masterDescribe.getApiName())) {
            return Lists.newArrayList();
        }
        List<IObjectDescribe> detailDescribeList = serviceFacade.findDetailDescribes(requestContext.getTenantId(),
                masterDescribe.getApiName());
        if (CollectionUtils.empty(detailDescribeList)) {
            return Lists.newArrayList();
        }

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        for (IObjectDescribe detail : detailDescribeList) {
            task.submit(() -> {
                List<IObjectData> detailObjectDataList = serviceFacade.findDetailObjectDataList(detail, masterData,
                        requestContext.getUser());
                serviceFacade.bulkInvalid(detailObjectDataList, requestContext.getUser());
                resultListToLog.addAll(detailObjectDataList);
            });
        }

        try {
            task.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("Error in run handleDetailData,", e);
        }
        return detailDescribeList;
    }

    public FindNameByIds.Result findNameByIds(FindNameByIds.Arg arg, RequestContext requestContext) {
        FindNameByIds.Result result = new FindNameByIds.Result();
        IActionContext context = ActionContextExt.of(requestContext.getUser(), requestContext)
                .getContext();
        List<INameCache> names = serviceFacade.findRecordName(context, arg.getDescribeAPIName(), arg.getIdList());
        List<Map<String, Object>> dataList = names.stream()
                .map(x -> buildNameMap(requestContext.getUser(), x, arg.getDescribeAPIName(), arg.getNamingConvention()))
                .collect(Collectors.toList());
        FindNameByIds.ResultData dataResult = new FindNameByIds.ResultData();
        dataResult.setData(dataList);
        result.setQueryResult(dataResult);
        return result;
    }

    /**
     * 构建包含不同命名策略的名称映射表
     *
     * @param user             当前用户上下文，用于租户隔离查询
     * @param nameCache        名称缓存数据源，包含原始名称及扩展名称
     * @param describeApiName  需要查询的对象描述API名称
     * @param namingConvention 命名策略标识，决定名称的展示优先级
     * @return 包含最终展示名称和扩展名称的映射表，结构示例：
     * {
     * "_id": "123",
     * "name": "优选展示名称",
     * "displayName": "扩展显示名",
     * "languageName": "多语言名称"
     * }
     * @逻辑说明： 1. 当使用 ui_optimized 模式时：
     * - 优先使用displayName（需对象描述支持显示名）
     * - 次优先使用languageName（当displayName不可用时）
     * - 最后回退到原始name字段
     * 2. 默认模式保留所有名称字段，供调用方自行处理
     */
    private Map<String, Object> buildNameMap(User user, INameCache nameCache, String describeApiName, String namingConvention) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("_id", nameCache.getId());
        if (FindNameByIds.Arg.UI_OPTIMIZED.equals(namingConvention)) {
            IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), describeApiName);
            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
            if (objectDescribeExt.isSupportDisplayName()) {
                String displayName = nameCache.getDisplayName();
                map.put("name", StringUtils.isBlank(displayName) ? "" : displayName);
                return map;
            }
            map.put("name", nameCache.getName());
            String languageName = nameCache.getLanguageName();
            if (StringUtils.isNotBlank(languageName)) {
                map.put("name", languageName);
            }
            return map;
        }
        map.put("name", nameCache.getName());
        map.put("displayName", nameCache.getDisplayName());
        map.put("languageName", nameCache.getLanguageName());
        return map;
    }

    public FindByQuery.Result findBySearchQuery(FindByQuery.Arg arg, RequestContext context) {
        // 日志监控
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#findBySearchQuery");
        FindByQuery.Result result = new FindByQuery.Result();
        checkPrivilegeForOpenAPI(context, arg.getDescribeAPIName());
        stopWatch.lap("checkPrivilegeForOpenAPI");

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeAPIName());
        stopWatch.lap("findObject");
        if (arg.getIncludeDescribe()) {
            result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        }
        IActionContext actionContext = ActionContextExt.of(context.getUser(), context).setSkipRelevantTeam(RequestUtil.skipRelevantTeam()).getContext();

        QueryResult<IObjectData> queryResult;
        if (!ObjectDescribeExt.isSFAObject(arg.getDescribeAPIName())) {
            //自定义对象，转换成使用searchTemplateQuery
            SearchTemplateQuery searchTemplateQuery = convertSearchQueryToSearchTemplateQuery(arg.getQuery());
            queryResult = serviceFacade.findBySearchQuery(actionContext, arg.getDescribeAPIName(), searchTemplateQuery);
        } else {
            queryResult = serviceFacade.findBySearchQuery(context.getUser(), arg.getDescribeAPIName(),
                    arg.getQuery());
        }
        stopWatch.lap("findBySearchQuery");
        List<IObjectData> dataList = queryResult.getData();
        if (Objects.nonNull(dataList)) {
            if (!ObjectDescribeExt.isSFAObject(arg.getDescribeAPIName())) {
                //根据ObjectDescribe和DataProjection中的201_111_061字段集合交集做字段的过滤
                List<String> includeFields = getFieldsByDescribeAndDataProjection(describe,
                        arg.getQuery().getDataProjection());
                dataList = serviceFacade.getDataProjectionDataList(dataList, includeFields);
                stopWatch.lap("getDataProjectionDataList");
            }
            //转换时间格式
            dataList.forEach(x -> ((ObjectData) x).covertDateTime());
        }
        FindByQuery.QueryResultInfo info = new FindByQuery.QueryResultInfo();
        info.setTotalNumber(queryResult.getTotalNumber());
        info.setData(ObjectDataDocument.ofList(dataList));
        result.setQueryResult(info);
        stopWatch.logSlow(3000);
        return result;
    }

    public FindBySearchTemplateQuery.Result findBySearchTemplateQuery(FindBySearchTemplateQuery.Arg arg, RequestContext context) {
        // 日志监控
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#findBySearchTemplateQuery");

        checkPrivilegeForOpenAPI(context, arg.getDescribeAPIName());
        stopWatch.lap("checkPrivilegeForOpenAPI");

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        FindBySearchTemplateQuery.Result result = findObject(arg, context);
        stopWatch.lap("findObject");

        handleSearchQuery(arg.getDescribeAPIName(), arg.getSearchTemplateQuery(), context);
        stopWatch.lap("handleSearchQuery");

        IActionContext actionContext = ActionContextExt.of(context.getUser(), context)
                .setSkipRelevantTeam(RequestUtil.skipRelevantTeam())
                .setKeepAllMultiLangValue(BooleanUtils.isTrue(arg.getKeepAllMultiLangValue()))
                .getContext();
        if (BooleanUtils.isTrue(arg.getEsSearchSkipRecentUpdateCheck())) {
            ActionContextExt.of(actionContext).esSearchSkipRecentUpdateCheck();
        }
        if (BooleanUtils.isTrue(arg.getIgnoreBaseVisibleRange())) {
            ActionContextExt.of(actionContext).setIgnoreBaseVisibleRange(arg.getIgnoreBaseVisibleRange());
        }
        QueryResult<IObjectData> queryResult;
        List<IObjectData> dataList;
        if (BooleanUtils.isTrue(arg.getSelectSpecialFields()) && !CollectionUtils.empty(arg.getFieldList())) {
            queryResult = serviceFacade.findBySearchTemplateQueryWithFields(actionContext, arg.getDescribeAPIName(), arg.getSearchTemplateQuery(), arg.getFieldList());
            dataList = queryResult.getData();
        } else {
            queryResult = serviceFacade.findBySearchQuery(actionContext, arg.getDescribeAPIName(), arg.getSearchTemplateQuery());
            dataList = filterFieldsInDataList(queryResult.getData(), arg.getFieldList());
        }
        FindBySearchTemplateQuery.QueryResultInfo info = new FindBySearchTemplateQuery.QueryResultInfo();
        info.setTotalNumber(queryResult.getTotalNumber());
        info.setData(ObjectDataDocument.ofList(dataList));
        result.setQueryResult(info);
        stopWatch.lap("findBySearchQuery");

        stopWatch.logSlow(3000);

        return result;
    }

    private void handleSearchQuery(String describeAPIName, SearchTemplateQuery searchTemplateQuery, RequestContext context) {
        if (searchTemplateQuery.getLimit() <= 0 || searchTemplateQuery.getLimit() > AppFrameworkConfig.getMaxQueryLimit()) {
            log.warn("query limit:{} exceed max value:{}", searchTemplateQuery.getLimit(), AppFrameworkConfig.getMaxQueryLimit());
            searchTemplateQuery.setLimit(AppFrameworkConfig.getMaxQueryLimit());
        }

        User user = context.getUser();
        if (user.isOutUser()) {
            OutDataPrivilege outDataPrivilege = dataPrivilegeService.getOutDataPrivilege(user, context.getAppId(), describeAPIName);
            ISearchTemplate template = customSceneService.findDefaultByDescribeApiName(describeAPIName, user);
            boolean isSubCascadeConfig = crmService.getIsSubCascadeConfig(context.getTenantId());
            DataRightsParameter dataRightsParameter = SearchTemplateExt.of(template).getDataRightsParameter(isSubCascadeConfig);
            dataRightsParameter.setLinkAppDataAuthRange(outDataPrivilege.getValue());

            searchTemplateQuery.setPermissionType(2);
            searchTemplateQuery.setDataRightsParameter(dataRightsParameter);
            return;
        }

        if (!SearchTemplateQueryExt.of(searchTemplateQuery).isAllScene()) {
            return;
        }
        // 管理员「全部」场景不走数据权限
        if (user.isSupperAdmin() || (searchTemplateQuery.getPermissionType() != 0 && serviceFacade.isAdmin(user))) {
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setDataRightsParameter(null);
        }
    }

    private List<IObjectData> filterFieldsInDataList(List<IObjectData> dataList, List<String> fieldList) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(fieldList)) {
            return dataList;
        }
        return serviceFacade.getDataProjectionDataList(dataList, fieldList);
    }

    private FindBySearchTemplateQuery.Result findObject(FindBySearchTemplateQuery.Arg arg, RequestContext context) {
        FindBySearchTemplateQuery.Result result = new FindBySearchTemplateQuery.Result();
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeAPIName());
        if (arg.getIncludeDescribe()) {
            result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        }
        return result;
    }

    /**
     * 简单查询接口，不走数据权限，不实时计算计算字段、统计字段和默认值
     *
     * @param arg
     * @param context
     * @return
     */
    public FindByIdWithFields.Result findByIdWithFields(FindByIdWithFields.Arg arg, RequestContext context) {
        IActionContext actionContext = ActionContextExt.of(context.getUser(), context).setSkipRelevantTeam(RequestUtil.skipRelevantTeam()).getContext();
        List<IObjectData> objectDataList = serviceFacade.findSimpleDataByIds(context.getTenantId(), arg.getDescribeApiName(),
                Lists.newArrayList(arg.getId()), arg.getFieldList(), actionContext);
        if (CollectionUtils.empty(objectDataList)) {
            log.warn("findByIdWithFields, data not found. tenantId=>{}, apiName=>{}, id=>{}", context.getTenantId(),
                    arg.getDescribeApiName(), arg.getId());
            return FindByIdWithFields.Result.builder().build();
        }
        return FindByIdWithFields.Result.builder().data(ObjectDataDocument.of(objectDataList.get(0))).build();
    }

    public FindDataByIds.Result findByIds(FindDataByIds.Arg arg, RequestContext requestContext) {
        FindDataByIds.Result result = new FindDataByIds.Result();
        checkPrivilegeForOpenAPI(requestContext, arg.getDescribeAPIName());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        List<String> idList = Lists.newArrayList(arg.getIdList());
        IActionContext context = ActionContextExt.of(requestContext.getUser(), requestContext).setSkipRelevantTeam(RequestUtil.skipRelevantTeam())
                .getContext();
        //传了补充字段信息标记的告诉元数据不要处理引用字段
        if (arg.isFillInfo() || arg.isFillExtendField()) {
            ActionContextExt.of(context).disableDeepQuote();
        }
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(context, idList, arg.getDescribeAPIName());
        // 是否补充额外信息
        if (arg.isFillInfo() || arg.isFillExtendField()) {
            fillExtraInfo(requestContext, objectDataList, arg.isFillInfo(), true, true,
                    true, true);
        }
        result.setDataList(ObjectDataDocument.ofList(objectDataList));
        return result;
    }

    private void fillExtraInfo(RequestContext requestContext, List<IObjectData> objectDataList, boolean isFillInfo,
                               boolean fillQuoteFieldValue, boolean fillLookupName, boolean fillCountryAreaLabel,
                               boolean fillDeptUser) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        String objectDescribeApiName = objectDataList.get(0).getDescribeApiName();
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), objectDescribeApiName);
        fillFieldInfo(requestContext, describe, objectDataList, fillQuoteFieldValue, fillLookupName, fillDeptUser);
        if (fillCountryAreaLabel) {
            fillCountryAreaLabel(objectDataList, describe, requestContext.getUser());
        }
        if (isFillInfo) {
            convertFieldForView(requestContext.getUser(), describe.getFieldDescribes(), objectDataList);
        }
    }

    private void fillCountryAreaLabel(List<IObjectData> objectDataList, IObjectDescribe describe, User user) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        serviceFacade.fillCountryAreaLabel(describe, objectDataList, user);
    }

    private void fillFieldInfo(RequestContext context, IObjectDescribe describe, List<IObjectData> objectDataList,
                               boolean fillQuoteFieldValue, boolean fillLookupName, boolean fillDeptUser) {
        if (!fillQuoteFieldValue && !fillLookupName && !fillDeptUser) {
            return;
        }
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        List<IObjectData> dataList = ObjectDataExt.synchronize(objectDataList);
        if (fillQuoteFieldValue) {
            parallelTask.submit(() -> {
                Boolean oldValue = RequestUtil.setRecordCalculateLog(false);
                //处理引用字段
                infraServiceFacade.fillQuoteFieldValue(context.getUser(), dataList, describe, null, false);
                RequestUtil.setRecordCalculateLog(oldValue);
            });
        }
        if (fillLookupName) {
            parallelTask.submit(() -> {
                //添加lookup字段的主属性__r
                serviceFacade.fillObjectDataWithRefObject(describe, dataList, context.getUser(), null);

            });
        }
        if (fillDeptUser) {
            parallelTask.submit(() -> {
                serviceFacade.fillUserInfo(describe, dataList, context.getUser());
                serviceFacade.fillDepartmentInfo(describe, dataList, context.getUser());
            });
        }
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill info of listController, ei:{}, object:{}", context.getTenantId(),
                    describe.getApiName(), e);
        }
    }

    private void convertFieldForView(User user, List<IFieldDescribe> fieldDescribes, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        fieldDescribes.stream()
                .filter(this::needConvertFieldType)
                .forEach(field -> {
                    String fieldRenderType = ObjectDescribeExt.getFieldRenderType(field);
                    DataConvertContext dataConvertContext;
                    if (AppFrameworkConfig.isUseMultiRegionByFieldType(fieldRenderType)) {
                        dataConvertContext = DataConvertContext.of(user, MultiRegionContextHolder.getUserRegion());
                    } else {
                        dataConvertContext = DataConvertContext.of(user);
                    }
                    FieldDataConverter converter = fieldDataConverterManager.getFieldDataConverter(fieldRenderType);
                    dataList.forEach(data -> data.set(field.getApiName(), converter.convertFieldData(data, field, dataConvertContext)));
                });
    }

    private boolean needConvertFieldType(IFieldDescribe fieldDescribe) {
        String fieldRenderType = ObjectDescribeExt.getFieldRenderType(fieldDescribe);
        return JsonFieldHandler.timeJsonField.contains(fieldRenderType)
                || JsonFieldHandler.optionJsonField.contains(fieldRenderType)
                || IFieldType.COUNTRY.equals(fieldRenderType)
                || IFieldType.PROVINCE.equals(fieldRenderType)
                || IFieldType.CITY.equals(fieldRenderType)
                || IFieldType.DISTRICT.equals(fieldRenderType);
    }

    public RecoverData.Result recover(RecoverData.Arg arg, RequestContext requestContext) {
        RecoverData.Result result = new RecoverData.Result();
        IObjectData objectData = new ObjectData();
        checkPrivilegeForOpenAPI(requestContext, arg.getDescribeAPIName());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescribeAPIName());

        objectData.setId(arg.getDataId());
        objectData.setTenantId(requestContext.getTenantId());
        objectData.setDescribeId(describe.getId());
        objectData.setDescribeApiName(describe.getApiName());

        IActionContext context = ActionContextExt.of(requestContext.getUser(), requestContext)
                .getContext();
        //最终操作成功的列表,用于写日志。
        List<IObjectData> resultListToLog = Lists.newArrayList();
        IObjectData deleted = serviceFacade.recover(objectData, context, requestContext.getUser());
        resultListToLog.add(deleted);
        //处理相关从对象的关系
        List<IObjectDescribe> detailDescribeList = handleDetailDataRecover(requestContext, describe, objectData, resultListToLog);
        Map<String, IObjectDescribe> describeMap = detailDescribeList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        describeMap.put(describe.getApiName(), describe);
        serviceFacade.masterDetailLog(requestContext.getUser(), EventType.MODIFY, ActionType.Recovery, describeMap, resultListToLog);
        result.setObjectDataDocument(ObjectDataDocument.of(deleted));
        result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        return result;
    }

    private List<IObjectDescribe> handleDetailDataRecover(RequestContext requestContext, IObjectDescribe masterDescribe,
                                                          IObjectData masterData, List<IObjectData> resultListToLog) {
        if (ObjectDescribeExt.isSFAObject(masterDescribe.getApiName())) {
            return Lists.newArrayList();
        }
        List<IObjectDescribe> detailDescribeList = serviceFacade.findDetailDescribes(requestContext.getTenantId(),
                masterDescribe.getApiName());
        if (CollectionUtils.empty(detailDescribeList)) {
            return Lists.newArrayList();
        }
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        for (IObjectDescribe detail : detailDescribeList) {
            task.submit(() -> {
                List<IObjectData> detailDataList = serviceFacade.findDetailIncludeInvalidObjectDataListIgnoreFormula(
                        detail, masterData, requestContext.getUser());
                detailDataList = detailDataList.stream().filter(d -> d.isDeleted()).collect(Collectors.toList());
                serviceFacade.bulkRecover(detailDataList, requestContext.getUser());
                resultListToLog.addAll(detailDataList);
            });
        }

        try {
            task.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("Error in run handleDetailData,", e);
        }
        return detailDescribeList;
    }

    public FindRelatedList.Result findRelatedList(FindRelatedList.Arg arg, RequestContext requestContext) {
        FindRelatedList.Result result = new FindRelatedList.Result();
        IRelatedListQuery searchQuery = arg.getQuery();
        checkPrivilegeForOpenAPI(requestContext, searchQuery.getTenantId());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IRelatedListQuery.QueryResult<IObjectData> queryResult = serviceFacade.findRelatedObjectData(searchQuery,
                requestContext.getUser());

        List<IObjectDescribe> describeList = null;
        if (arg.getIncludeDescribe() && null != queryResult && !CollectionUtils.empty(queryResult.getResultList())) {
            describeList = Lists.newArrayList();
            List<IRelatedListQuery.QueryResult.RelatedObjectResult<IObjectData>> resultList =
                    queryResult.getResultList();
            for (IRelatedListQuery.QueryResult.RelatedObjectResult<IObjectData> re : resultList) {
                IObjectDescribe objectDescribe = serviceFacade.findObject(requestContext.getTenantId(), re.getChildApiName());
                if (null != objectDescribe) {
                    describeList.add(objectDescribe);
                }
            }
        }

        result.setRelateQueryList(queryResult, describeList);
        return result;
    }

    public FindById.Result findDataById(FindById.Arg arg, RequestContext context) {
        FindById.Result result = new FindById.Result();
        checkPrivilegeForOpenAPI(context, arg.getDescAPIName());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }

        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getDescAPIName());
        IActionContext actionContext = ActionContextExt.of(context.getUser(), context)
                .setSkipRelevantTeam(RequestUtil.skipRelevantTeam())
                .setKeepAllMultiLangValue(arg.isKeepAllMultiLangValue())
                .getContext();
        if ("-10000".equals(actionContext.getUserId())) {
            actionContext.setPrivilegeCheck(false);
        }

        IObjectData found = null;
        if (arg.isIncludeDeleted()) {
            found = serviceFacade.findObjectDataIgnoreStatus(context.getUser(), arg.getDataId(), arg.getDescAPIName());
        } else {
            if (arg.isIncludeInvalid()) {
                found = serviceFacade.getObjectDataIncludeDeleted(actionContext, arg.getDataId(), arg.getDescAPIName());
            } else {
                found = serviceFacade.findObjectData(actionContext, arg.getDataId(), describe);
            }
        }
        if (arg.isCheckPrivilege()) {
            serviceFacade.doDataPrivilegeCheck(context.getUser(), Lists.newArrayList(found), describe,
                    ObjectAction.VIEW_DETAIL.getActionCode());
        }

        // 是否补充额外信息
        if (arg.isFillExtendField()) {
            fillExtraInfo(context, Lists.newArrayList(found), false, false,
                    true, true, true);
        } else if (arg.isIncludeLookup()) {
            fillExtraInfo(context, Lists.newArrayList(found), false, false,
                    arg.isIncludeLookup(), false, false);
        }


        if (arg.isIncludeDescribe()) {
            result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        }
        result.setObjectDataDocument(ObjectDataDocument.of(found));
        return result;
    }

    public DeletedById.Result delete(DeletedById.Arg arg, RequestContext requestContext) {
        DeletedById.Result result = new DeletedById.Result();
        checkPrivilegeForOpenAPI(requestContext, arg.getDescribeAPIName());

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescribeAPIName());

        IObjectData objectData = new ObjectData();
        objectData.setId(arg.getDataId());
        objectData.setTenantId(requestContext.getTenantId());
        objectData.setDescribeApiName(arg.getDescribeAPIName());
        objectData.setDescribeId(describe.getId());

        List<IObjectData> deletedList = serviceFacade.bulkDelete(Lists.newArrayList(objectData), requestContext.getUser());
        serviceFacade.log(requestContext.getUser(), EventType.DELETE, ActionType.Delete, describe, deletedList);
        //处理从对象数据
        handleDetailDataDelete(requestContext, describe, objectData);
        if (!CollectionUtils.empty(deletedList)) {
            result.setObjectDataDocument(ObjectDataDocument.of(deletedList.get(0)));
        }
        if (arg.isIncludeDescribe()) {
            result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        }
        return result;
    }

    public BulkDeleted.Result bulkDelete(List<String> idList, String describeApiName, boolean direct) {
        RequestContext context = RequestContextManager.getContext();
        BulkDeleted.Result result = BulkDeleted.Result.builder().build();

        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), describeApiName);

        List<IObjectData> dataList = Lists.newArrayList();
        for (String id : idList) {
            IObjectData data = new ObjectData();
            data.setId(id);
            data.setTenantId(context.getTenantId());
            data.setDescribeApiName(describeApiName);
            data.setDescribeId(describe.getId());
            dataList.add(data);
        }
        List<IObjectData> deletedList = null;
        if (direct) {
            deletedList = serviceFacade.bulkDeleteDirect(dataList, context.getUser());
        } else {
            deletedList = serviceFacade.bulkDelete(dataList, context.getUser());
        }
        serviceFacade.log(context.getUser(), EventType.DELETE, ActionType.Delete, describe, deletedList);
        result.setObjectDataDocuments(ObjectDataDocument.ofList(deletedList));
        if (CollectionUtils.empty(deletedList)) {
            return result;
        }
        // 处理从对象数据
        handleDetailDataListDelete(context, describe, deletedList);
        return result;
    }

    private void handleDetailDataListDelete(RequestContext context,
                                            IObjectDescribe describe,
                                            List<IObjectData> deletedList) {
        List<IObjectDescribe> detailDescribeList = serviceFacade.findDetailDescribes(context.getTenantId(),
                describe.getApiName());
        if (CollectionUtils.empty(detailDescribeList)) {
            return;
        }
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        for (IObjectDescribe detail : detailDescribeList) {
            task.submit(() -> {
                SearchTemplateQuery searchTemplate = createSearchTemplateToFindDetailDataList(detail,
                        deletedList, describe.getApiName());
                QueryResult<IObjectData> queryResult =
                        serviceFacade.findBySearchQuery(context.getUser(), detail.getApiName(), searchTemplate);
                if (Objects.nonNull(queryResult) && !CollectionUtils.empty(queryResult.getData())) {
                    List<IObjectData> dataList = queryResult.getData();
                    serviceFacade.bulkDeleteDirect(dataList, context.getUser());
                    serviceFacade.log(context.getUser(), EventType.DELETE, ActionType.Delete, detail, dataList);
                }
            });
        }
        task.run();
    }

    /**
     * 构造searchQuery，以批量查询从对象数据
     *
     * @param detail        从对象描述
     * @param deletedList   删除成功的主对象数据列表
     * @param masterApiName 主对象的api name
     * @return SearchTemplateQuery实例
     */
    private SearchTemplateQuery createSearchTemplateToFindDetailDataList(IObjectDescribe detail,
                                                                         List<IObjectData> deletedList,
                                                                         String masterApiName) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(detail);
        String masterFieldApiName = describeExt.getMasterDetailFieldName(masterApiName).orElse(null);

        List<String> masterIdList = deletedList.stream().map(IObjectData::getId).collect(Collectors.toList());

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10000);
        query.setOffset(0);

        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldValues(Arrays.asList(detail.getApiName()));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(masterFieldApiName);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.IN);
        filter.setIndexName(IObjectData.ID);
        filter.setFieldValues(masterIdList);
        filters.add(filter);

        query.setFilters(filters);
        return query;
    }

    private void handleDetailDataDelete(RequestContext requestContext, IObjectDescribe masterDescribe,
                                        IObjectData masterData) {
        if (ObjectDescribeExt.isSFAObject(masterDescribe.getApiName())) {
            return;
        }

        List<IObjectDescribe> detailDescribeList = serviceFacade.findDetailDescribes(requestContext.getTenantId(),
                masterDescribe.getApiName());
        if (CollectionUtils.empty(detailDescribeList)) {
            return;
        }

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        for (IObjectDescribe detail : detailDescribeList) {
            task.submit(() -> {
                List<IObjectData> detailDataList = serviceFacade.findDetailIncludeInvalidObjectDataListIgnoreFormula(
                        detail, masterData, requestContext.getUser());
                detailDataList = detailDataList.stream().filter(d -> d.isDeleted()).collect(Collectors.toList());
                serviceFacade.bulkDelete(detailDataList, requestContext.getUser());
                serviceFacade.log(requestContext.getUser(), EventType.DELETE, ActionType.Delete, detail, detailDataList);
            });
        }
        try {
            task.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("Error in run handleDetailData,", e);
        }
    }

    /**
     * 根据ObjectDescribe和DataProjection中的字段集合交集做字段的过滤
     *
     * @param objectDescribe 对象描述
     * @param dataProjection 返回字段
     * @return 返回字段列表
     */
    private List<String> getFieldsByDescribeAndDataProjection(IObjectDescribe objectDescribe,
                                                              IDataProjection dataProjection) {
        List<String> describeFieldList = new ArrayList<>();
        Iterator var3 = objectDescribe.getFieldDescribes().iterator();

        while (var3.hasNext()) {
            IFieldDescribe fieldDescribe = (IFieldDescribe) var3.next();
            if (fieldDescribe.isActive()) {
                describeFieldList.add(fieldDescribe.getApiName());
            }
        }

        if (dataProjection != null && dataProjection.getIncludeFields() != null && !dataProjection.getIncludeFields().isEmpty()) {
            List<String> projectionIncludeFields = dataProjection.getIncludeFields();
            describeFieldList.retainAll(projectionIncludeFields);
        }

        return describeFieldList;
    }

    /**
     * 将ISearchQuery转换为SearchTemplateQuery
     *
     * @param query ISearchQuery
     * @return SearchTemplateQuery
     */
    private SearchTemplateQuery convertSearchQueryToSearchTemplateQuery(ISearchQuery query) {
        IFilter notDeleteFilter = new Filter();
        notDeleteFilter.setFieldName(IObjectDescribe.IS_DELETED);
        notDeleteFilter.setOperator(Operator.IN);
        notDeleteFilter.setFieldValues(Lists.newArrayList("0", "1"));

        List<IFilter> allFilters = Lists.newArrayList(notDeleteFilter);
        List<IConditions> allConditions = query.getConditions();
        if (!CollectionUtils.empty(allConditions)) {
            for (IConditions conds : allConditions) {
                switch (conds.getType()) {
                    case IConditions.TERM_CONDITION:
                    case IConditions.FILTER_CONDITION:
                        List<IFilter> eqFilters = convertCondition((AbstractConditions) conds, Operator.EQ);
                        allFilters.addAll(eqFilters);
                        break;
                    case IConditions.MATCH_CONDITION:
                        List<IFilter> likeFilters = convertCondition((AbstractConditions) conds, Operator.LIKE);
                        allFilters.addAll(likeFilters);
                        break;
                    case IConditions.CONTAINS_CONDITION:
                        //为流程团队提供对于查询数组方法的condition
                        List<IFilter> containsFilters = convertCondition((AbstractConditions) conds, Operator.CONTAINS);
                        allFilters.addAll(containsFilters);
                        break;
                    default:
                        break;
                }
            }
        }

        List<RangeConditions> rangeConditions = query.getRangeConditions();
        if (!CollectionUtils.empty(rangeConditions)) {
            for (RangeConditions cond : rangeConditions) {
                List<IFilter> rangeFilters = converRangeCondition(cond);
                allFilters.addAll(rangeFilters);
            }
        }

        List<Order> orders = query.getOrders();
        List<OrderBy> orderBys = Lists.newArrayList();
        if (!CollectionUtils.empty(orders)) {
            for (Order order : orders) {
                OrderBy orderBy = new OrderBy();
                orderBy.setFieldName(order.getField());
                orderBy.setIsAsc(order.getAscending());
                orderBys.add(orderBy);
            }
        }

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilters(allFilters);
        searchTemplateQuery.setLimit(query.getLimit());
        searchTemplateQuery.setOffset(query.getOffset());
        searchTemplateQuery.setOrders(orderBys);
        return searchTemplateQuery;
    }

    private List<IFilter> converRangeCondition(RangeConditions cond) {
        List<IFilter> list = Lists.newArrayList();
        String fieldName = cond.getFieldName();
        Object from = cond.getFrom();
        if (Objects.nonNull(from)) {
            IFilter fromFilter = new Filter();
            fromFilter.setFieldName(fieldName);
            fromFilter.setOperator(cond.isIncludeLower() ? Operator.GTE : Operator.GT);
            fromFilter.setFieldValues(Lists.newArrayList(String.valueOf(from)));
            list.add(fromFilter);
        }

        Object to = cond.getTo();
        if (Objects.nonNull(to)) {
            IFilter toFilter = new Filter();
            toFilter.setFieldName(fieldName);
            toFilter.setOperator(cond.isIncludeUpper() ? Operator.LTE : Operator.LT);
            toFilter.setFieldValues(Lists.newArrayList(String.valueOf(to)));
            list.add(toFilter);
        }
        return list;
    }

    private List<IFilter> convertCondition(AbstractConditions conds, Operator operator) {
        List<IFilter> list = Lists.newArrayList();
        HashMap<String, String> map = conds.getConditions();

        if (CollectionUtils.empty(map)) {
            return list;
        }

        Set<Map.Entry<String, String>> entries = map.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            IFilter filter = new Filter();
            filter.setFieldName(entry.getKey());
            filter.setOperator(operator);
            filter.setFieldValues(Lists.newArrayList(entry.getValue()));
            list.add(filter);
        }

        return list;
    }

    private void startCreateApprovalAndWorkFlow(RequestContext context, IObjectData created) {
        //尝试触发审批流
        Map<String, Object> dataMap = ObjectDataExt.of(created).toMap();
        ApprovalFlowStartResult approvalResult = serviceFacade.startApprovalFlow(context.getUser(), created.getDescribeApiName(), created.getId(),
                ApprovalFlowTriggerType.CREATE, dataMap, dataMap, null);

        //更新生命状态（触发成功->审核中，未匹配审批流->正常）
        if (ObjectDataExt.of(created).createApprovalStartCallback(approvalResult)) {
            serviceFacade.batchUpdateByFields(context.getUser(), Lists.newArrayList(created), Lists.newArrayList(ObjectLifeStatus.LIFE_STATUS_API_NAME));
        }

        //未匹配审批流才需要触发工作流
        if (ApprovalFlowStartResult.APPROVAL_NOT_EXIST.equals(approvalResult)) {
            startCreateWorkFlow(context, created);
        }
    }

    private void startCreateWorkFlow(RequestContext context, IObjectData created) {
        //只有normal或in_change状态的数据才触发工作流
        if (!ObjectDataExt.of(created).isNormal() && !ObjectDataExt.of(created).isInChange()) {
            return;
        }
        infraServiceFacade.startWorkFlow(created.getId(), created.getDescribeApiName(), WorkflowProducer.TRIGGER_START,
                context.getUser(), Maps.newHashMap(), context.getEventId());
    }

    private void batchStartCreateWorkFlow(RequestContext context, List<IObjectData> dataList) {
        dataList.stream().forEach(data -> startCreateWorkFlow(context, data));
    }

    private void checkPrivilegeForOpenAPI(RequestContext context, String descAPIName) {
        if (OPENAPI_PEER_NAME.equals(context.getPeerName())
                && !ObjectDescribeExt.isSFAObject(descAPIName)
                && !serviceFacade.isAdmin(context.getUser())) {
            log.warn("DescribeAPIName {} do not have privilege.", descAPIName);
            throw new ValidateException("Bad Request");
        }
    }

    public BatchUpdateData.Result batchUpdate(BatchUpdateData.Arg arg, RequestContext context) {
        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());

        BatchUpdateData.Result result = new BatchUpdateData.Result();
        List<IObjectData> objectDataList = Lists.newArrayList();
        //记录每条数据的变更字段
        Map<String, Set<String>> dataMap = Maps.newHashMap();
        if (!Objects.isNull(arg.getDataListJson())) {
            JSONArray dataJsonArray = JSONArray.parseArray(arg.getDataListJson());
            for (int i = 0; i < dataJsonArray.size(); i++) {
                IObjectData objectData = new ObjectData();
                JSONObject json = dataJsonArray.getJSONObject(i);
                // 只有_id字段的data，不加入更新清单
                if (Objects.nonNull(json.get(ObjectData.ID)) && json.size() == 1) {
                    continue;
                }
                objectData.fromJsonString(json.toJSONString());
                objectData.setDescribeApiName(arg.getDescribeApiName());
                objectData.setTenantId(context.getTenantId());

                //移除不支持编辑的字段
                ObjectDataExt.of(objectData).removeFieldsNotSupportEdit(objectDescribe);

                objectDataList.add(objectData);

                dataMap.put(objectData.getId(), json.keySet().stream().filter(x -> !"_id".equals(x))
                        .collect(Collectors.toSet()));
            }
        }
        //如果是达芬奇规则触发的，发修改记录
        Map<String, IObjectData> idDataMap = objectDataList.stream().collect(
                Collectors.toMap(IObjectData::getId, d -> d, (k1, k2) -> k1));
        List<IObjectData> dbObjectData = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(), Lists.newArrayList(idDataMap.keySet()), arg.getDescribeApiName());
        //编辑数据触发计算
        serviceFacade.calculateForBatchEditData(User.systemUser(context.getTenantId()), objectDataList, dbObjectData, objectDescribe);
        //需要更新的字段
        List<String> updateFields = dataMap.entrySet().stream()
                .flatMap(e -> e.getValue().stream()).distinct().collect(Collectors.toList());
        // 批量更新指定字段，如果数据没有传该字段，就不更新
        RequestContextManager.getContext().setAttribute("absent_field_not_change", Boolean.TRUE);
        IActionContext actionContext = ActionContextExt.of(context.getUser(), context)
                .setActionType("batchUpdateByRest")
                .setSkipImmutableFieldValidate(context.skipImmutableFieldValidate())
                .getContext();
        List<IObjectData> objectDatas = serviceFacade.batchUpdateByFields(actionContext, objectDataList, updateFields);
        if (Objects.nonNull(arg.getRuleName())) {
            dataMap.forEach((dataId, fieldList) -> {
                StringBuilder builder = new StringBuilder();
                fieldList.forEach(f -> {
                    if (Objects.nonNull(objectDescribe.getFieldDescribe(f))) {
                        builder.append(I18N.text(I18NKey.DATA_CHANGED_BY_SCORE_RULE_LOG, arg.getRuleName(), objectDescribe.getFieldDescribe(f).getLabel()));
//                        builder.append(objectDescribe.getFieldDescribe(f).getLabel());
                        builder.append(",");
                    }
                });
                if (builder.length() > 0) {
                    builder.deleteCharAt(builder.length() - 1);
                    ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                    parallelTask.submit(() -> {
                        Map<String, IObjectData> objectDataMap = objectDatas.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
                        Map<String, Map<String, Object>> updateFieldMap = Maps.newHashMap();
                        dbObjectData.forEach(x -> {
                            if (Objects.isNull(objectDataMap.get(x.getId()))) {
                                return;
                            }
                            updateFieldMap.put(x.getId(), ObjectDataExt.of(x).diff(objectDataMap.get(x.getId()), objectDescribe));
                        });

                        if (Objects.isNull(objectDataMap.get(dataId))) {
                            return;
                        }
                        serviceFacade.logWithCustomMessage(context.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, Lists.newArrayList(objectDataMap.get(dataId)),
                                updateFieldMap, dbObjectData, "da_vinci", arg.getRuleName(), builder.toString());
                    });
                    parallelTask.run();
                }
            });
        }

        result.setResult("OK");
        return result;
    }

    public CalculateCountValue.Result calculateCountValue(RequestContext context, CalculateCountValue.Arg arg) {
        CountFieldDescribe countFieldDescribe = arg.toCountFieldDescribe();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(countFieldDescribe.getWheres());
        // 指定筛选的数据源
        queryExt.setSearchSource(arg.getSearchSource());
        Object countValue = serviceFacade.getCountValue(context.getTenantId(), arg.toCountFieldDescribe(), queryExt.getQuery());
        return new CalculateCountValue.Result(countValue, true);
    }

    /**
     * 根据主对象数据ID查询所有从对象的数据ID
     *
     * @param descAPIName 主对象描述的api name
     * @param dataId      主对象数据id
     * @return 所有从对象数据
     */
    public FindDetailDataList.Result findDetailDataListByMasterId(String descAPIName, String dataId) {
        RequestContext context = RequestContextManager.getContext();
        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), descAPIName);
        IObjectData data = serviceFacade.findObjectData(context.getUser(), dataId, descAPIName);
        // 查询所有从对象
        List<IObjectDescribe> detailDescribeList = serviceFacade.findDetailDescribes(context.getTenantId(),
                describe.getApiName());
        Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        for (IObjectDescribe detail : detailDescribeList) {     // 最多5个从对象
            task.submit(() -> {
                List<IObjectData> dataList = serviceFacade.findDetailObjectDataList(detail, data, context.getUser());
                if (!CollectionUtils.empty(dataList)) {
                    dataMap.put(detail.getApiName(), dataList);
                }
            });
        }
        try {
            task.await(12, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.warn("find detail data list time out!", e);
//            log.warn("find detail data list time out!, tenantId:{}, userId:{}, masterDescribe:{}", context.getTenantId(),
//                    context.getUser().getUserId(), descAPIName, e);
        }
        FindDetailDataList.Result result = FindDetailDataList.Result.builder()
                .masterData(ObjectDataDocument.of(data))
                .detailData(ObjectDataDocument.ofMap(dataMap))
                .build();
        return result;
    }

    public BatchIncrementUpdate.Result batchIncrementUpdate(BatchIncrementUpdate.Arg arg, RequestContext context) {
        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, context.getTenantId())) {
            context.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getApiName());
        List<ObjectDataDocument> dataDocuments = JSON.parseArray(arg.getDataListJson(), ObjectDataDocument.class);
        // 从上下文中补充企业 id 和对象 ApiName
        List<IObjectData> dataList = ObjectDataDocument.ofDataList(dataDocuments).stream()
                .peek(data -> {
                    data.setDescribeApiName(arg.getApiName());
                    data.setTenantId(context.getTenantId());
                    //移除不支持编辑的字段
                    ObjectDataExt.of(data).removeFieldsNotSupportEdit(describe);
                }).collect(Collectors.toList());

        serviceFacade.calculateForBatchEditData(User.systemUser(context.getTenantId()), dataList, null, describe);

        List<String> validFieldApiNames = dataDocuments.stream()
                .flatMap(dataDocument -> dataDocument.keySet().stream())
                .distinct().collect(Collectors.toList());

        if (UdobjGrayConfig.isAllow("batchUpdateWithFieldsReturnDiffData", context.getTenantId())) {
            UpdateResult updateResult = serviceFacade.batchUpdateWithFieldsReturnDiffData(context.getUser(), dataList, validFieldApiNames, false,
                    MetaDataActionService.UpdateAttributes.builder().actionType("batchIncrementUpdateByRest").build());
            if (Objects.nonNull(updateResult)) {
                ParallelUtils.createBackgroundTask()
                        .submit(() ->
                                serviceFacade.bulkRecordEditLog(context.getUser(), EventType.MODIFY, ActionType.Modify, describe,
                                        updateResult.getResultDataList(), updateResult.getBeforeUpdateDataList()))
                        .run();
                return BatchIncrementUpdate.Result.of(updateResult.getResultDataList());
            }
            return BatchIncrementUpdate.Result.of(Lists.newArrayList());
        } else {
            IActionContext actionContext = ActionContextExt.of(context.getUser(), context)
                    .setActionType("batchIncrementUpdateByRest")
                    .setSkipImmutableFieldValidate(context.skipImmutableFieldValidate())
                    .getContext();
            List<IObjectData> objectDataList = serviceFacade.batchUpdateByFields(actionContext, dataList, validFieldApiNames);
            return BatchIncrementUpdate.Result.of(objectDataList);
        }
    }

    public FindSimpleDataByIds.Result findSimpleDataByIds(FindSimpleDataByIds.Arg arg, RequestContext context) {
        IActionContext actionContext = ActionContextExt.of(context.getUser(), context).setSkipRelevantTeam(RequestUtil.skipRelevantTeam()).getContext();
        List<IObjectData> objectDataList = serviceFacade.findSimpleDataByIds(context.getTenantId(), arg.getDescribeApiName(),
                arg.getIds(), arg.getFieldList(), actionContext);
        if (CollectionUtils.empty(objectDataList)) {
            log.warn("findSimpleDataByIds, data not found. tenantId=>{}, apiName=>{}, ids=>{}", context.getTenantId(),
                    arg.getDescribeApiName(), arg.getIds());
            return FindSimpleDataByIds.Result.builder().build();
        }
        return FindSimpleDataByIds.Result.builder().dataList(ObjectDataDocument.ofList(objectDataList)).build();
    }
}
