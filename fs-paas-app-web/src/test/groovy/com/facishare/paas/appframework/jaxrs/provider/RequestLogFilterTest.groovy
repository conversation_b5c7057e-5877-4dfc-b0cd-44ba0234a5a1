package com.facishare.paas.appframework.jaxrs.provider


import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

import javax.ws.rs.container.ContainerRequestContext
import javax.ws.rs.core.MultivaluedHashMap
import javax.ws.rs.core.UriInfo
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException

class RequestLogFilterTest extends Specification {
    def requestLogFilter = new RequestLogFilter()
    def containerRequestContext = Mock(ContainerRequestContext)
    def uriInfo = Mock(UriInfo)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        containerRequestContext.getUriInfo() >> uriInfo
        uriInfo.getPath() >> "/test/path"
        uriInfo.getQueryParameters() >> new MultivaluedHashMap<>()
    }

    @Unroll
    def "test validateBodySize with contentLength=#contentLength"() {
        given:
        containerRequestContext.getHeaderString("Content-Length") >> contentLength
        AppFrameworkConfig.maxBodySize = 1024L

        when:
        def thrown = null
        try {
            requestLogFilter.filter(containerRequestContext)
        } catch (Exception e) {
            thrown = e
        }

        then:
        if (shouldThrowException) {
            thrown instanceof ValidateException || thrown instanceof NumberFormatException
        } else {
            thrown == null
        }

        where:
        contentLength | shouldThrowException
        null         | false
        "500"        | false
        "1025"       | true
        "0"          | false
        "-1"         | false
        "invalid"    | true  // 会抛出NumberFormatException
    }

    def "test copy with null streams"() {
        when:
        Whitebox.invokeMethod(requestLogFilter, "copy", 
            inputStream as InputStream, 
            outputStream as OutputStream)

        then:
        thrown(ValidateException)

        where:
        inputStream              | outputStream
        null                    | new ByteArrayOutputStream()
        new ByteArrayInputStream(new byte[0]) | null
        null                    | null
    }

    def "test copy with normal data"() {
        given:
        def input = new ByteArrayInputStream("test data".getBytes())
        def output = new ByteArrayOutputStream()
        AppFrameworkConfig.maxBodySize = 1024L

        when:
        def count = Whitebox.invokeMethod(requestLogFilter, "copy", 
            input as InputStream, 
            output as OutputStream)

        then:
        count == 9
        output.toString() == "test data"
    }

    def "test copy with large data exceeding max size"() {
        given:
        def largeData = "x" * 1025
        def input = new ByteArrayInputStream(largeData.getBytes())
        def output = new ByteArrayOutputStream()
        AppFrameworkConfig.maxBodySize = 1024L

        when:
        Whitebox.invokeMethod(requestLogFilter, "copy", 
            input as InputStream, 
            output as OutputStream)

        then:
        thrown(ValidateException)
    }

    def "test filter with entity"() {
        given:
        def testData = "test data"
        def input = new ByteArrayInputStream(testData.getBytes())
        def headers = new javax.ws.rs.core.MultivaluedHashMap<String, String>()
        containerRequestContext.hasEntity() >> true
        containerRequestContext.getEntityStream() >> input
        containerRequestContext.getMethod() >> "POST"
        containerRequestContext.getHeaders() >> headers
        AppFrameworkConfig.maxBodySize = 1024L

        when:
        requestLogFilter.filter(containerRequestContext)

        then:
        1 * containerRequestContext.setEntityStream(_)
        noExceptionThrown()
    }

    def "should properly handle resources with try-with-resources"() {
        given:
        def filter = new RequestLogFilter()
        def context = Mock(ContainerRequestContext)
        def mockInputStream = new CloseTrackingInputStream("test content".bytes)
        def headers = new MultivaluedHashMap<String, String>()
        def mockUriInfo = Mock(UriInfo)
        
        when:
        context.hasEntity() >> true
        context.getEntityStream() >> mockInputStream
        context.getHeaders() >> headers
        context.getMethod() >> "POST"
        context.getUriInfo() >> mockUriInfo
        mockUriInfo.getPath() >> "/test/path"
        mockUriInfo.getQueryParameters() >> new MultivaluedHashMap<>()
        filter.filter(context)
        
        then:
        1 * context.setEntityStream(_)
        mockInputStream.closed // 验证流被关闭
        noExceptionThrown()
    }
    
    def "should properly handle exceptions in try-with-resources"() {
        given:
        def filter = new RequestLogFilter()
        def context = Mock(ContainerRequestContext)
        def mockInputStream = new CloseTrackingInputStream(null) {
            @Override
            int read(byte[] b) throws IOException {
                throw new IOException("Simulated read error")
            }
        }
        
        when:
        context.hasEntity() >> true
        context.getEntityStream() >> mockInputStream
        filter.filter(context)
        
        then:
        thrown(IOException)
        mockInputStream.closed // 验证即使发生异常，流也被关闭
    }
    
    // 用于跟踪流是否被关闭的测试辅助类
    class CloseTrackingInputStream extends ByteArrayInputStream {
        boolean closed = false
        
        CloseTrackingInputStream(byte[] buf) {
            super(buf ?: new byte[0])
        }
        
        @Override
        void close() {
            closed = true
            super.close()
        }
    }
} 