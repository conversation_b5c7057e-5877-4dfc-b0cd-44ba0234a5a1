package com.facishare.paas.appframework.rest.service

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.MetaDataFindService
import com.facishare.paas.appframework.metadata.MetaDataService
import com.facishare.paas.appframework.rest.dto.data.AggregateQuery
import com.facishare.paas.metadata.api.describe.Count
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class ObjectDataRestV3ServiceTest extends Specification {

    ObjectDataRestV3Service objectDataRestV3Service
    MetaDataFindService metaDataFindService
    MetaDataService metaDataService
    DescribeLogicService describeLogicService
    FsGrayReleaseBiz fsGrayReleaseBiz

    def setup() {
        metaDataFindService = Mock(MetaDataFindService)
        metaDataService = Mock(MetaDataService)
        describeLogicService = Mock(DescribeLogicService)
        fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)

        objectDataRestV3Service = new ObjectDataRestV3Service(
                metaDataFindService: metaDataFindService,
                metaDataService: metaDataService,
                describeLogicService: describeLogicService
        )

        // 设置请求上下文
        def user = User.builder().tenantId("12345").userId("test_user").build()
        def requestContext = RequestContext.builder().user(user).tenantId("12345").build()
        RequestContextManager.setContext(requestContext)
        
        // 设置灰度配置
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证聚合查询字段类型限制功能，包括不同聚合函数类型、字段类型和灰度配置下的行为
     */
    @Unroll
    def "aggregateQueryTest_字段类型限制场景 - #testCase"() {
        given: "准备测试数据"
        def arg = new AggregateQuery.Arg(
                describeApiName: "TestObject",
                searchQueryInfo: createSearchQueryInfo(aggFunction, aggField)
        )

        def objectDescribe = Mock(IObjectDescribe)
        def fieldDescribe = createFieldDescribe(fieldType)
        Whitebox.setInternalState(AppFrameworkConfig, 'numberFieldTypes', ['number', 'currency', 'percentile'] as Set)

        and: "设置Mock行为"
        describeLogicService.findObjectWithoutCopy(_, _) >> objectDescribe
        objectDescribe.getFieldDescribe(aggField) >> fieldDescribe
        metaDataFindService.aggregateFindBySearchQuery(_, _, _) >> []
        fsGrayReleaseBiz.isAllow(_, _) >> isGrayEnabled

        when: "调用聚合查询方法"
        def result = null
        def exception = null
        try {
            result = objectDataRestV3Service.aggregateQuery(arg)
        } catch (Exception e) {
            exception = e
        }

        then: "验证结果"
        if (shouldThrowException) {
            assert exception != null
            assert exception instanceof ValidateException
            if (expectedErrorMessage) {
                assert exception.message == expectedErrorMessage
            }
        } else {
            assert exception == null
            assert result != null
        }

        where:
        testCase                      | aggFunction     | aggField    | fieldType        | isGrayEnabled | shouldThrowException | expectedErrorMessage
        "count场景不受限制"            | Count.TYPE_COUNT | null        | null             | true          | false                | null
        "非count场景-灰度开启-数字字段" | "sum"           | "amount"    | "number"         | true          | false                | null
        "非count场景-灰度开启-非数字字段" | "sum"         | "name"      | "text"           | true          | true                 | "aggregate.field.type.error"
        "非count场景-灰度关闭-非数字字段" | "sum"         | "name"      | "text"           | false         | false                | null
        "非count场景-字段不存在"       | "sum"           | "notExist"  | null             | true          | false                | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证聚合查询参数校验功能，包括必填参数校验和聚合函数类型校验
     */
    @Unroll
    def "aggregateQueryErrorTest_参数校验场景 - #testCase"() {
        given: "准备测试数据"
        def arg = new AggregateQuery.Arg(
                describeApiName: describeApiName,
                searchQueryInfo: searchQueryInfo
        )

        when: "调用聚合查询方法"
        def exception = null
        try {
            objectDataRestV3Service.aggregateQuery(arg)
        } catch (Exception e) {
            exception = e
        }

        then: "验证异常结果"
        exception != null
        exception instanceof ValidateException

        where:
        testCase                | describeApiName | searchQueryInfo
        "对象API名为空"          | ""              | "{\"groupByParameter\":{\"aggFunctions\":[{\"aggFunction\":\"count\"}]}}"
        "查询条件为空"           | "TestObject"    | ""
        "查询条件格式错误"       | "TestObject"    | "{\"invalidFormat\":true}"
    }

    private String createSearchQueryInfo(String aggFunction, String aggField) {
        // 创建一个简单的JSON字符串来模拟SearchTemplateQuery
        def aggFunctionJson = aggField ? "{\"aggFunction\":\"$aggFunction\",\"aggField\":\"$aggField\"}" : "{\"aggFunction\":\"$aggFunction\"}"
        return "{\"groupByParameter\":{\"aggFunctions\":[$aggFunctionJson]}}"
    }

    private IFieldDescribe createFieldDescribe(String fieldType) {
        if (fieldType == null) {
            return null
        }

        def fieldDescribe = Mock(IFieldDescribe)
        fieldDescribe.getType() >> fieldType
        return fieldDescribe
    }
}
