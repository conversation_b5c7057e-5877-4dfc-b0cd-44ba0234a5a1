<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.5.5-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-paas-app-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--RestEasy基本包-->
        <!--<dependency>-->
        <!--<groupId>org.jboss.resteasy</groupId>-->
        <!--<artifactId>resteasy-jaxrs</artifactId>-->
        <!--<version>${resteasy.version}</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-servlet-initializer</artifactId>
        </dependency>
        <!--序列化和反序列化-->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jackson2-provider</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.fge</groupId>
                    <artifactId>jackson-coreutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--RestEasy整合BeanValidation-->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-validator-provider</artifactId>
            <version>${resteasy.version}</version>
        </dependency>
        <!--RestEasy整合Spring-->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-spring</artifactId>
        </dependency>
    </dependencies>
</project>