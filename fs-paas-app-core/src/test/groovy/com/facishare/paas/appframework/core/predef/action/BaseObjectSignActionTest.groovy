import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO
import com.facishare.paas.appframework.core.exception.SignInException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.BaseObjectSignAction
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

class BaseObjectSignActionTest extends Specification {

    @Subject
    BaseObjectSignAction baseObjectSignAction = new BaseObjectSignAction() {
        @Override
        protected List<String> process(SignInFieldDescribe signInFieldDescribe, IObjectData stored, IObjectData data) {
            return []
        }

        @Override
        protected List<String> getFuncPrivilegeCodes() {
            return null
        }
    }

    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacadeImpl)

    def setup() {
        SpringBeanHolder springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        baseObjectSignAction.infraServiceFacade = infraServiceFacade
        def requestContext = RequestContext.builder().user(User.systemUser('74255'))
                .tenantId('74255')
                .peerName('peerName')
                .lang(Lang.defaultLang())
                .build()
        ActionContext actionContext = new ActionContext(requestContext, 'AccountObj', 'SignIn')
        baseObjectSignAction.actionContext = actionContext
    }

    def setupSpec() {
        // Mock I18N 相关配置
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    @Unroll
    def "测试 compensateAddress 方法 with location: #location"() {
        given: "设置测试数据和Mock"
        if (expectedAddress) {
            def geoResult = new GeoAddressDTO.GeoResult(address: expectedAddress)
            infraServiceFacade.getAddressByGeo(_, _) >> geoResult
        }

        when: "调用compensateAddress方法"
        def result
        try {
            result = baseObjectSignAction.compensateAddress(location)
        } catch (SignInException e) {
            result = "exception"
        }

        then: "验证结果"
        result == expectedResult

        where: "测试不同的场景"
        location                            | expectedAddress | expectedResult
        null                                | null            | "exception"  // 空位置
        ""                                  | null            | "exception"  // 空字符串
        "116.397128"                        | null            | "exception"  // 格式错误
        "116.397128#%\$"                    | null            | "exception"  // 缺少纬度
        "#%\$39.916527"                     | null            | "#%\$39.916527"  // 缺少经度
        "invalid#%\$invalid"                | null            | "invalid#%\$invalid"  // 无效坐标
        "116.397128#%\$39.916527"           | "北京市"        | "116.397128#%\$39.916527#%\$北京市"  // 正常坐标
        "116.397128#%\$39.916527#%\$"       | "上海市"        | "116.397128#%\$39.916527#%\$上海市"  // 空地址
        "116.397128#%\$39.916527#%\$深圳市" | null            | "116.397128#%\$39.916527#%\$深圳市"  // 已有地址
        "NaN#%\$39.916527"                  | null            | "exception"  // 无效经度
        "116.397128#%\$NaN"                 | null            | "exception"  // 无效纬度
        "#%\$#%\$--"                        | null            | "#%\$#%\$--" // 深研特殊场景：联影等大客户
    }
} 