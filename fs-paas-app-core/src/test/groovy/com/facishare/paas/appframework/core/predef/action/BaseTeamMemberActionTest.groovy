package com.facishare.paas.appframework.core.predef.action


import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.metadata.TeamMember
import com.facishare.paas.metadata.api.TeamRoleInfo
import org.powermock.reflect.internal.WhiteboxImpl
import spock.lang.Specification

class BaseTeamMemberActionTest extends Specification {
    BaseTeamMemberAction baseTeamMemberAction = Mock(BaseTeamMemberAction)

    def "test"() {
        given:
        Map<String, Set<String>> map = new HashMap<>()
        Set<String> set = new HashSet<>()
        set.add("1")
        set.add("2")
        map.put("x", set)
        map.put("y", new HashSet<String>())
        when:
        def role = BaseTeamMemberAction.getTeamMemberRoleKey(I18NKey.ADD_ROLE, 2)
        then:
        print(role)
    }

    def "test getTeamMemberRole and getI18NTeamMemberRole"() {
        given: "a team member role map, a data id and a list of team role infos"
        List<TeamRoleInfo> teamRoleInfos = []
        if (buildTeamRoleInfos) {
            TeamMember.Role.values().each {
                def teamRole = TeamRoleInfo.builder()
                        .roleName((String) WhiteboxImpl.getInternalState(it, "defaultLabel"))
                        .roleType(it.getValue())
                        .roleNameTransKey((String) WhiteboxImpl.getInternalState(it, "labelKey"))
                        .build()
                teamRoleInfos << teamRole
            }
        }
        when: "getTeamMemberRole or role I18Nkey is called"
        def roles = WhiteboxImpl.invokeMethod(baseTeamMemberAction, method, teamMemberRoleMap, dataId, teamRoleInfos)
        then:
        result == roles
        where:
        buildTeamRoleInfos | method                  | teamMemberRoleMap                   | dataId || result
        true               | "getI18NTeamMemberRole" | ['id': ["1", "2", "3", "4"] as Set] | 'id'   || ['#I18N#paas.udobj.constant.owner', '#I18N#paas.udobj.constant.follower', '#I18N#paas.udobj.constant.service_staff', '#I18N#paas.udobj.constant.normal_staff']
        true               | "getTeamMemberRole"     | ['id': ["1", "2", "3", "4"] as Set] | 'id'   || "负责人、联合跟进人、售后服务人员、普通成员"
        true               | "getI18NTeamMemberRole" | ['id': ["3", "4"] as Set]           | 'id'   || ['#I18N#paas.udobj.constant.service_staff', '#I18N#paas.udobj.constant.normal_staff']
        true               | "getTeamMemberRole"     | ['id': ["1", "4"] as Set]           | 'id'   || "负责人、普通成员"
        false              | "getTeamMemberRole"     | ['id': ["3", "2"] as Set]           | 'id'   || "售后人员、联合跟进人"
        false              | "getI18NTeamMemberRole" | ['id': ["3", "2"] as Set]           | 'id'   || ['#I18N#paas.udobj.constant.service_staff', '#I18N#paas.udobj.constant.follower']
    }
}
