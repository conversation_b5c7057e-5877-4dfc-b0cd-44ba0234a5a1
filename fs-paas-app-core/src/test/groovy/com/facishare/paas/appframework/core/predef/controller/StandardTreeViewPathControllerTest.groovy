package com.facishare.paas.appframework.core.predef.controller

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.ControllerContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.treeview.TreeViewService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.release.GrayRule
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification

class StandardTreeViewPathControllerTest extends Specification {
    StandardTreeViewPathController standardTreeViewPathController;
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    TreeViewService treeViewService = Mock(TreeViewService)

    @Shared
    String describeJson = '''{"tenant_id":"74255","store_table_name":"fmcg_channel_name","description":"","index_version":1,"is_deleted":false,"define_type":"package","release_version":"6.4","package":"CRM","is_active":true,"last_modified_time":1657004992451,"create_time":1656402836418,"is_support_tree_view":true,"last_modified_by":"1000","display_name":"渠道","created_by":"-10000","version":10,"is_open_display_name":false,"icon_index":14,"api_name":"ChannelObj","icon_path":"","is_udef":true,"short_name":"wqk","_id":"62bab3940a56450001cf01a1","fields":{"tenant_id":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836407,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"62bab3940a56450001cf0184","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new"},"channel_state":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836408,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":true,"label":"渠道状态","type":"true_or_false","is_required":false,"api_name":"channel_state","options":[{"label":"启用","value":true},{"label":"禁用","value":false}],"define_type":"package","_id":"62bab3940a56450001cf0185","is_index_field":false,"is_single":false,"index_name":"b_1","status":"new","help_text":""},"lock_user":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836409,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"62bab3940a56450001cf0186","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new"},"tree_view":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402997501,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"树形视图层级","label":"树形视图层级","type":"tree_path","is_need_convert":false,"is_required":false,"api_name":"tree_view","define_type":"package","_id":"62bab4350059070001d7c95f","is_index_field":false,"is_single":false,"index_name":"s_5","status":"released","help_text":""},"extend_obj_data_id":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836410,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":true,"description":"连接通表的记录ID,扩展字段用","label":"扩展字段在mt_data中的记录ID","type":"text","is_required":false,"api_name":"extend_obj_data_id","define_type":"system","_id":"62bab3940a56450001cf0187","is_index_field":false,"is_single":false,"index_name":"t_3","status":"released","max_length":64},"is_deleted":{"describe_api_name":"ChannelObj","is_index":false,"create_time":1656402836418,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836412,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","label":"作废前生命状态","type":"text","is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"62bab3940a56450001cf0189","is_index_field":false,"label_r":"作废前生命状态","is_single":false,"index_name":"t_4","status":"new","max_length":256},"object_describe_api_name":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836418,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released"},"owner_department":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1656402836415,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"62bab3940a56450001cf018c","is_index_field":false,"status":"new","help_text":""},"owner":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836417,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"62bab3940a56450001cf018d","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"channel_level":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":0,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1656402836419,"is_encrypted":false,"step_value":1,"display_style":"input","length":14,"default_value":"","label":"渠道层级","api_name":"channel_level","_id":"62bab3940a56450001cf018f","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"channel_description":{"describe_api_name":"ChannelObj","is_index":true,"default_is_expression":false,"is_active":true,"create_time":1656402836418,"min_length":0,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","default_value":"","label":"渠道描述","type":"long_text","default_to_zero":false,"is_required":false,"api_name":"channel_description","define_type":"package","_id":"62bab3940a56450001cf018e","is_index_field":false,"is_single":false,"index_name":"t_1","status":"new","help_text":"","max_length":2000},"last_modified_time":{"describe_api_name":"ChannelObj","is_index":true,"create_time":1656402836418,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836422,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"62bab3940a56450001cf0192","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_2","status":"new","help_text":""},"create_time":{"describe_api_name":"ChannelObj","is_index":true,"create_time":1656402836418,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836424,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"62bab3940a56450001cf0194","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_3","status":"new","help_text":""},"superior_channel":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"package","input_mode":"","is_single":false,"index_name":"s_4","is_index":true,"is_active":true,"create_time":1656402836425,"is_encrypted":false,"tree_view_field":"tree_view","target_api_name":"ChannelObj","label":"上级渠道1","target_related_list_name":"target_related_list_ChannelObj_ChannelObj__c","target_related_list_label":"渠道","action_on_target_delete":"set_null","related_wheres":[],"api_name":"superior_channel","_id":"62bab46f0059070001d7c961","is_index_field":true,"status":"new","help_text":""},"last_modified_by":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836418,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released"},"out_tenant_id":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836418,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released"},"version":{"describe_api_name":"ChannelObj","is_index":false,"create_time":1656402836418,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"record_type":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836430,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"62bab3940a56450001cf019a","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"ChannelObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1656402836431,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"62bab3940a56450001cf019b","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"channel_number":{"describe_api_name":"ChannelObj","is_index":true,"default_is_expression":false,"is_active":true,"create_time":1656402836432,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","default_value":"","label":"渠道编号","type":"text","default_to_zero":false,"is_required":false,"api_name":"channel_number","define_type":"package","_id":"62bab3940a56450001cf019c","input_mode":"","is_index_field":false,"is_single":false,"index_name":"t_2","status":"new","help_text":"","max_length":100},"data_own_department":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836433,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"62bab3940a56450001cf019d","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"new","help_text":""},"name":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":true,"description":"name","type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1656402836653,"is_encrypted":false,"default_value":"","label":"渠道","api_name":"name","_id":"62bab3940a56450001cf019e","is_index_field":false,"status":"new","help_text":""},"order_by":{"describe_api_name":"ChannelObj","is_index":false,"create_time":1656402836418,"length":8,"description":"order_by","is_unique":false,"label":"order_by","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"order_by","define_type":"system","index_name":"l_by","round_mode":4,"status":"released"},"_id":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"actions":{}}'''

    void setup() {
        RequestContext requestContext = RequestContext.builder()
                .user(User.systemUser("74255"))
                .build()
        ControllerContext controllerContext = new ControllerContext(requestContext, "ChannelObj", "TreeViewPath")
        IObjectDescribe objectDescribe = new ObjectDescribe(JSON.parseObject(describeJson))
        standardTreeViewPathController = new StandardTreeViewPathController("objectDescribeExt": ObjectDescribeExt.of(objectDescribe),
                "serviceFacade": serviceFacade, "controllerContext": controllerContext, "treeViewService": treeViewService)
        Whitebox.setInternalState(AppFrameworkConfig, "treeViewSupportObject", ['ChannelObj': new GrayRule('white:*')])
    }

    def "DoService"() {
        given:
        StandardTreeViewPathController.Arg arg = new StandardTreeViewPathController.Arg()
        arg.setDataIds(["62bd2dfe582ebb0001d96e1a"])
        List<IObjectData> objectDataList = []
        def data1 = Spy(ObjectData)
        data1.set("superior_channel", "62babbc720a7fa10fcd257c1")
        data1.set("_id", "62bab9ac20a7fa10fcd25733")
        data1.set("name", "笔记本电脑")
        data1.set("display_name", "笔记本电脑display")
        data1.set("tree_view", "62bab6eeb4d84300013daa37.62bab8b420a7fa10fcd255ee.62babbc720a7fa10fcd257c1")
        objectDataList << data1
        List<IObjectData> parentObjectDataList = []
        def data2 = Spy(ObjectData)
        data2.set("superior_channel", "629f1a7b5fd9a70001930f37")
        data2.set("_id", "62bab8b420a7fa10fcd255ee")
        data2.set("name", testName)
        data2.set("display_name", "数码产品display")
        def data3 = Spy(ObjectData)
        data3.set("name", "相关渠道")
        data3.set("display_name", "相关渠道display")
        data3.set("_id", "629f1a7b5fd9a70001930f37")
        def data4 = Spy(ObjectData)
        data4.set("name", "平板电脑")
        data4.set("display_name", "平板电脑display")
        data4.set("superior_channel", "62bab8b420a7fa10fcd255ee")
        data4.set("_id", "62babbc720a7fa10fcd257c1")

        parentObjectDataList << data2
        parentObjectDataList << data3
        parentObjectDataList << data4


        when:
        IObjectDescribe objectDescribe = new ObjectDescribe(JSON.parseObject(describeJson))
        objectDescribe.set("is_open_display_name", displayNameSwitch)
        Whitebox.setInternalState(standardTreeViewPathController, "objectDescribeExt", ObjectDescribeExt.of(objectDescribe))

        standardTreeViewPathController.setArg(arg)
        2 * treeViewService.findTreeViewObjDataListByIds(_, _, _, _) >> objectDataList >> parentObjectDataList
        def service = standardTreeViewPathController.doService(arg)
        then:
        service.treePathList.get(0).wholePath == treeViewWholePath
        where:
        displayNameSwitch | testName   || treeViewWholePath
        false             | "数码产品" || "相关渠道/数码产品/平板电脑/笔记本电脑"
        false             | null       || "相关渠道//平板电脑/笔记本电脑"
        true              | "数码产品" || "相关渠道display/数码产品display/平板电脑display/笔记本电脑display"

    }
}
