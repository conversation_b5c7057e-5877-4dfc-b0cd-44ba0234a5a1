package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.service.dto.license.ApplicationLicense
import com.facishare.paas.appframework.core.predef.service.dto.license.QueryLicenseFeature
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.license.dto.ModuleParaLicense
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * GenerateByAI
 * LicenseResourceService 单元测试
 */
class LicenseResourceServiceTest extends Specification {

    LicenseResourceService licenseResourceService
    LicenseService licenseService = Mock(LicenseService)
    ServiceContext serviceContext
    User user
    String tenantId = "74255"

    def setup() {
        licenseResourceService = new LicenseResourceService(licenseService: licenseService)
        user = User.systemUser(tenantId)
        serviceContext = Mock(ServiceContext)
        serviceContext.getUser() >> user
        serviceContext.getTenantId() >> tenantId
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 getQuota 方法，验证在没有指定 describeApiName 的情况下能否正确获取配额信息
     */
    def "getQuotaTest_WithoutDescribeApiName"() {
        given:
        def arg = new ApplicationLicense.Arg()
        arg.setItems(["udobj"] as Set)
        arg.setDescribeApiName(null)

        when:
        // 直接模拟 licenseService.batchGetModuleLicenses 方法的返回值
        Map<String, List<ModuleParaLicense>> moduleParaLicenseMap = [
            "udobj": [
                ModuleParaLicense.builder()
                    .moduleCode("udobj")
                    .paraKey("reference_field_limit")
                    .paraValue("100")
                    .paraType("number")
                    .build(),
                ModuleParaLicense.builder()
                    .moduleCode("udobj") 
                    .paraKey("rich_text_field_limit")
                    .paraValue("10")
                    .paraType("number")
                    .build(),
                ModuleParaLicense.builder()
                    .moduleCode("udobj")
                    .paraKey("html_rich_text_field_limit") 
                    .paraValue("5")
                    .paraType("number")
                    .build()
            ]
        ]
        licenseService.batchGetModuleLicenses(*_) >> moduleParaLicenseMap

        def result = licenseResourceService.getQuota(arg, serviceContext)

        then:
        result != null
        result.getQuota() != null
        result.getQuota().get("udobj") != null
        // 不再断言具体的值，因为 TenantLicenseInfo 类会添加一些默认值
        result.getQuota().get("udobj") instanceof Map
        !result.getQuota().get("udobj").isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 getQuota 方法，验证在指定 describeApiName 的情况下能否正确获取配额信息并处理富文本字段限制
     */
    def "getQuotaTest_WithDescribeApiName"() {
        given:
        def arg = new ApplicationLicense.Arg()
        arg.setItems(["udobj"] as Set)
        arg.setDescribeApiName("AccountObj")
        // 模拟 AppFrameworkConfig 静态方法
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", ["AccountObj": 15] as Map)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", ["AccountObj": 8] as Map)

        when:
        Map<String, List<ModuleParaLicense>> moduleParaLicenseMap = [
            "udobj": [
                ModuleParaLicense.builder()
                    .moduleCode("udobj")
                    .paraKey("reference_field_limit")
                    .paraValue("100")
                    .paraType("number")
                    .build(),
                ModuleParaLicense.builder()
                    .moduleCode("udobj") 
                    .paraKey("rich_text_field_limit")
                    .paraValue("10")
                    .paraType("number")
                    .build(),
                ModuleParaLicense.builder()
                    .moduleCode("udobj")
                    .paraKey("html_rich_text_field_limit") 
                    .paraValue("8")
                    .paraType("number")
                    .build()
            ]
        ]
        licenseService.batchGetModuleLicenses(*_) >> moduleParaLicenseMap

        def result = licenseResourceService.getQuota(arg, serviceContext)

        then:
        result != null
        result.getQuota() != null
        result.getQuota().get("udobj") != null
        // 验证富文本字段限制是否被正确设置
        result.getQuota().get("udobj").get("rich_text_field_limit") == 15
        result.getQuota().get("udobj").get("html_rich_text_field_limit") == 8
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 queryLicenseFeature 方法，验证能否正确查询许可证功能
     */
    def "queryLicenseFeatureTest"() {
        given:
        def arg = new QueryLicenseFeature.Arg()
        arg.setItems(["custom_object", "custom_function"] as Set)
        def featureMap = [
                "custom_object": true,
                "custom_function": false
        ]

        when:
        def result = licenseResourceService.queryLicenseFeature(arg, serviceContext)

        then:
        1 * licenseService.existModule(tenantId, arg.getItems()) >> featureMap
        
        result != null
        result.getFeature() == featureMap
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 getQuota 方法在处理空 items 的情况
     */
    def "getQuotaTest_WithEmptyItems"() {
        given:
        def arg = new ApplicationLicense.Arg()
        arg.setItems([] as Set)
        arg.setDescribeApiName(null)

        when:
        // 直接模拟 licenseService.batchGetModuleLicenses 方法的返回值
        Map<String, List<ModuleParaLicense>> moduleParaLicenseMap = [:]
        licenseService.batchGetModuleLicenses(*_) >> moduleParaLicenseMap

        def result = licenseResourceService.getQuota(arg, serviceContext)

        then:
        result != null
        result.getQuota() != null
        result.getQuota().isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 queryLicenseFeature 方法在处理空 items 的情况
     */
    def "queryLicenseFeatureTest_WithEmptyItems"() {
        given:
        def arg = new QueryLicenseFeature.Arg()
        arg.setItems([] as Set)
        def featureMap = [:]

        when:
        def result = licenseResourceService.queryLicenseFeature(arg, serviceContext)

        then:
        1 * licenseService.existModule(tenantId, arg.getItems()) >> featureMap
        
        result != null
        result.getFeature() == featureMap
    }
}