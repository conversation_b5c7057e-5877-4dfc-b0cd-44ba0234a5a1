package com.facishare.paas.appframework.core.predef.controller

import com.facishare.paas.appframework.metadata.layout.component.GeneralComponentInfo
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt
import com.facishare.wechat.proxy.common.result.NewBaseResult
import org.apache.commons.validator.Arg
import org.mockito.Mock
import spock.lang.Specification
import org.powermock.reflect.Whitebox
class BaseListHeaderControllerTest extends Specification {
//    ListComponentExt listComponentExt = Mock(ListComponentExt)
//    def baseListHeaderController = new BaseListHeaderController<Arg>() {}
//    def arg = Mock(Arg)
//    GeneralComponentInfo generalComponentInfo = Mock(GeneralComponentInfo)


    @Mock
    ListComponentExt listComponentExt

    @Mock
    Arg arg

    @Mock
    GeneralComponentInfo generalComponentInfo

    def baseListHeaderController = new BaseListHeaderController<Arg>() {}


    def setup() {
        baseListHeaderController.listComponentExt = listComponentExt
        baseListHeaderController.arg = arg
    }


    def "test getGeneralInfoHideType with listComponentExt and arg null"() {
        given:
        baseListHeaderController.listComponentExt = null
        baseListHeaderController.arg = null

        when:
        def result = Whitebox.invokeMethod(baseListHeaderController, "getGeneralInfoHideType")

        then:
        result == []
    }

    def "test getGeneralInfoHideType with generalComponentInfo absent"() {
        given:
        arg.getListType() >> "enable_selected_layout"
        listComponentExt.getGeneralInfoByPageType("enable_selected_layout") >> Optional.empty()

        when:
        def result = Whitebox.invokeMethod(baseListHeaderController, "getGeneralInfoHideType")

        then:
        result == []
    }

}


