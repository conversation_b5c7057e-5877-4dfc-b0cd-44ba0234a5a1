package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.service.PhoneNumberService
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import spock.lang.Specification

class ObjectPhoneNumberServiceTest extends Specification {
    ObjectPhoneNumberService objectPhoneNumberService = new ObjectPhoneNumberService(
            "phoneNumberService": Mock(PhoneNumberService.class)
    )

    RequestContext requestContext = RequestContext.builder()
            .tenantId("testTenantId")
            .clientIp('testClientIp')
            .user(Optional.of(new User("tenantId", "userId")))
            .build()

    ServiceContext serviceContext = new ServiceContext(requestContext, "serviceName", "serviceMethod")


    def "test queryVerificationCode"() {
        expect:
        1 == 1
        /*
        given:
        QueryVerificationCode.Arg arg = QueryVerificationCode.Arg.builder()
                .phone("123456")
                .captchaCode("captchaCode")
                .captchaId("captchaId")
                .build()
        when:
        def queryResult = objectPhoneNumberService.queryVerificationCode(arg, serviceContext)
        assert queryResult.errorCode == errorCode

        then:
        1 * objectPhoneNumberService.phoneNumberService.queryVerificationCode(_, _, _, _, _, _) >> sendCodeResultEnum

        where:
        errorCode                                 || sendCodeResultEnum
        QueryVerificationCode.Result.SUCCESS      || "SUCCESS"
        QueryVerificationCode.Result.NEED_CAPTCHA || "NEED_IMAGE_CODE"
        QueryVerificationCode.Result.NEED_CAPTCHA || "IMAGE_CODE_ERROR"
        QueryVerificationCode.Result.ERROR        || "IP_LIMITED"
        QueryVerificationCode.Result.ERROR        || "SMS_SEND_ERROR"
        QueryVerificationCode.Result.ERROR        || "SMS_TOO_OFTEN"
        QueryVerificationCode.Result.ERROR        || "AREACODE_NOT_SUPPORT"
        QueryVerificationCode.Result.ERROR        || "Business_NOT_SUPPORT"
        QueryVerificationCode.Result.ERROR        || "other_Result"
        */
    }

    def "test checkVerificationCode"() {
        expect:
        1 == 1
        /*
        given:
        CheckVerificationCode.Arg arg = CheckVerificationCode.Arg.builder()
                .phone("123456")
                .smsCode("abcdef")
                .build()
        when:
        def result = objectPhoneNumberService.checkVerificationCode(arg, serviceContext)
        assert result.success == status

        then:
        1 * objectPhoneNumberService.phoneNumberService.checkVerificationCode(_, _, _) >> codeEnum

        where:
        status || codeEnum
        true   || "SUCCESS"
        false  || "SMS_CODE_ERROR"
        false  || "VERIFY_CODE_EXCEED_LIMIT"
        false  || "otherResult"
        */
    }


    def "test refreshCaptcha"() {
        expect:
        1 == 1
        /*
        when:
        objectPhoneNumberService.refreshCaptcha()
        then:
        1 * objectPhoneNumberService.phoneNumberService.refreshCaptcha() >> GetImageCode.Result.builder().build()
        noExceptionThrown()
        */
    }


    def "test checkSmsStatus"() {
        expect:
        1 == 1
        /*
        when:
        objectPhoneNumberService.checkSmsStatus(serviceContext)
        then:
        1 * objectPhoneNumberService.phoneNumberService.checkSmsStatus(_) >> true
        noExceptionThrown()
        */
    }


}
