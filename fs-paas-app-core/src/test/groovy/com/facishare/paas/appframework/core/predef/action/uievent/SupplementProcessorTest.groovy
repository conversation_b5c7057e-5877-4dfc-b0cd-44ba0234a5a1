package com.facishare.paas.appframework.core.predef.action.uievent

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction
import com.facishare.paas.metadata.api.IUIEvent
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification

class SupplementProcessorTest extends Specification {
    SupplementProcessor supplementProcessor

    RequestContext requestContext = Mock(RequestContext)
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    @Shared
    String describeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1709885122466,"create_time":1631173218514,"description":"","last_modified_by":"1000","display_name":"fj-UI事件主","created_by":"1000","version":1005,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_qep6N__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"hGD","_id":"6139ba6225421000015b28f9","fields":{"lock_rule":{"type":"lock_rule","define_type":"package","is_index":false,"is_active":true,"default_value":"default_lock_rule","label":"锁定规则","api_name":"lock_rule"},"field_bcI2r__c":{"type":"object_reference","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_name":"id","field_values":["Budget_account_filtter__c"]}]}],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"查找科目","target_api_name":"TPMBudgetBusinessSubjectObj","target_related_list_name":"target_related_list_Gtsch__c","api_name":"field_bcI2r__c"},"field_1iqS4__c":{"is_index":true,"is_active":true,"group_type":"date_time_range","label":"日期范围-1","type":"group","api_name":"field_1iqS4__c","define_type":"custom","fields":{"end_time_field":"field_T631g__c","start_time_field":"field_kM81X__c"}},"date_range__c":{"is_index":false,"is_active":true,"group_type":"date_time_range","label":"日期范围","type":"group","api_name":"date_range__c","define_type":"custom","fields":{"end_time_field":"end_time__c","start_time_field":"start_time__c"}},"mc_exchange_rate":{"type":"number","decimal_places":6,"define_type":"package","max_length":16,"is_index":true,"is_active":true,"length":10,"default_value":"","label":"汇率","api_name":"mc_exchange_rate","is_show_mask":false,"round_mode":4},"field_Z24o4__c":{"return_type":"number","type":"count","decimal_places":2,"wheres":[],"define_type":"custom","is_index":true,"is_active":true,"label":"钱合计","api_name":"field_Z24o4__c","is_show_mask":false,"round_mode":4},"field_date55k1__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"开始时间55","api_name":"field_date55k1__c","date_format":"yyyy-MM-dd"},"field_chrIH__c":{"type":"location","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"label":"签退地址","api_name":"field_chrIH__c"},"field_zt7pl__c":{"type":"currency","decimal_places":2,"used_in":"component","define_type":"custom","max_length":14,"is_index":true,"is_active":true,"length":12,"default_value":"","label":"收款金额","api_name":"field_zt7pl__c","is_show_mask":false,"round_mode":4},"field_j4x49__c":{"type":"location","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"label":"定位-无乡镇","api_name":"field_j4x49__c"},"field_5aPLJ__c":{"type":"select_one","used_in":"component","options":[{"value":"complete","label":"已完成"},{"value":"incomplete","label":"未完成"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"incomplete","label":"拜访状态","api_name":"field_5aPLJ__c"},"life_status_before_invalid":{"type":"text","define_type":"package","input_mode":"","max_length":256,"is_index":false,"is_active":true,"default_value":"","label":"作废前生命状态","api_name":"life_status_before_invalid"},"field_uQkxD__c":{"type":"html_rich_text","define_type":"custom","max_length":131072,"is_index":false,"is_active":true,"default_value":"","label":"总结","api_name":"field_uQkxD__c"},"field_O96X1__c":{"type":"date_time","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"日期时间","api_name":"field_O96X1__c","date_format":"yyyy-MM-dd HH:mm"},"field_EgkrH__c":{"type":"location","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"label":"定位","api_name":"field_EgkrH__c"},"field_0cIkb__c":{"type":"rich_text","define_type":"custom","max_length":50000,"is_index":true,"is_active":true,"default_value":"","label":"协同富文本","api_name":"field_0cIkb__c"},"field_hekam__c":{"is_index":false,"is_active":true,"quote_field_type":"text","label":"引用详细地址","type":"quote","quote_field":"field_0us5a__c__r.field_9v21c__c","api_name":"field_hekam__c","define_type":"custom","is_show_mask":false},"field_cvb2S__c":{"type":"currency","decimal_places":12,"define_type":"custom","max_length":14,"is_index":true,"is_active":true,"length":2,"default_value":"$field_Z24o4__c$","label":"金额","api_name":"field_cvb2S__c","is_show_mask":false,"round_mode":4},"field_Ef1Sz__c":{"type":"select_many","options":[{"label":"更新主从","font_color":"#2a304d","value":"option1"},{"label":"新增从1","font_color":"#2a304d","value":"1527SA41R"},{"label":"新增从2","font_color":"#2a304d","value":"BWC802kid"},{"label":"删除从1","font_color":"#2a304d","value":"U8VY2pcjy"},{"label":"删除从2","font_color":"#2a304d","value":"dZ4aYk02S"},{"label":"单独编辑从1","font_color":"#2a304d","value":"6vEq3d12c"},{"not_usable":true,"label":"其他","font_color":"#2a304d","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":["option1"],"label":"函数操作","api_name":"field_Ef1Sz__c"},"field_p1Re8__c":{"return_type":"number","type":"count","decimal_places":0,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"IN","field_name":"field_5ir20__c","field_values":["2"]}]}],"define_type":"custom","is_index":true,"is_active":true,"label":"统计fj-UI事件从1=法师","api_name":"field_p1Re8__c","is_show_mask":false,"round_mode":4},"field_85dau__c":{"type":"image","define_type":"custom","is_index":true,"is_active":true,"watermark":[{"type":"variable","value":"current_user"},{"type":"variable","value":"current_time"},{"type":"variable","value":"current_address"}],"label":"图片","is_watermark":false,"api_name":"field_85dau__c"},"field_9I230__c":{"type":"select_one","options":[{"not_usable":false,"label":"数学-14","value":"58B8PKOHY"},{"not_usable":false,"label":"英语","value":"722TQyaS1"},{"not_usable":true,"label":"语文I","value":"631hxe27Z"},{"not_usable":true,"label":"物理-50","value":"1217tuHE1"},{"not_usable":false,"label":"化学123","value":"Kc5612mTu"},{"not_usable":false,"label":"生物","value":"KsT8300ms"},{"not_usable":false,"label":"政治I","value":"km7t2K5FN"},{"not_usable":false,"label":"历史","value":"2NL5s2kKb"},{"not_usable":false,"label":"地理","value":"V951l1km3"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"'km7t2K5FN'","label":"单选科目","api_name":"field_9I230__c"},"field_q4b93__c":{"is_index":false,"is_active":true,"quote_field_type":"select_one","label":"引用手机","type":"quote","quote_field":"field_0us5a__c__r.field_epQal__c","api_name":"field_q4b93__c","define_type":"custom","is_show_mask":false},"version":{"is_index":false,"length":8,"label":"version","type":"number","decimal_places":0,"api_name":"version","define_type":"system","round_mode":4},"field_7Vbk3__c":{"return_type":"number","type":"count","decimal_places":0,"wheres":[],"define_type":"custom","is_index":true,"is_active":true,"label":"统计字段","api_name":"field_7Vbk3__c","is_show_mask":false,"round_mode":4},"field_3w4DI__c":{"type":"object_reference","wheres":[],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"查找关联","target_api_name":"OrderPaymentObj","target_related_list_name":"target_related_list_5812Q__c","api_name":"field_3w4DI__c"},"field_11Fsw__c":{"type":"text","define_type":"custom","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"表单组件中的单行文本","api_name":"field_11Fsw__c","is_show_mask":false},"field_rUwSP__c":{"type":"object_reference_many","wheres":[],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"fj-主新建无函数","target_api_name":"object_p0I5m__c","target_related_list_name":"target_related_list_Fwcjj__c","api_name":"field_rUwSP__c"},"field_g90wV__c":{"type":"department","wheres":[],"optional_type":"department","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"部门1","api_name":"field_g90wV__c"},"field_6bDGT__c":{"return_type":"number","type":"count","decimal_places":0,"wheres":[],"define_type":"custom","is_index":true,"is_active":true,"label":"统计fj-UI事件从1","api_name":"field_6bDGT__c","is_show_mask":false,"round_mode":4},"field_426G7__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"收款时间","api_name":"field_426G7__c","date_format":"yyyy-MM-dd HH:mm:ss"},"field_yb9iB__c":{"type":"select_one","used_in":"component","options":[{"value":"incomplete","label":"未收款"},{"value":"complete","label":"已收款"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"收款状态","api_name":"field_yb9iB__c"},"tenant_id":{"is_index":false,"is_active":true,"label":"tenant_id","type":"text","api_name":"tenant_id","define_type":"system","max_length":200},"field_1Hq4P__c":{"is_index":false,"is_active":true,"group_type":"payment","label":"支付(收款)组件1","type":"group","api_name":"field_1Hq4P__c","define_type":"custom","fields":{"pay_status_field":"field_yb9iB__c","pay_type_field":"field_js17w__c","pay_time_field":"field_426G7__c","pay_amount_field":"field_zt7pl__c"}},"field_y2k46__c":{"type":"text","define_type":"custom","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"非必填文本","api_name":"field_y2k46__c","is_show_mask":false},"field_2p5Ck__c":{"type":"employee","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_name":"id","field_values":["filter_channel__c"]}]}],"define_type":"custom","is_index":true,"is_active":false,"default_value":"","label":"人员","api_name":"field_2p5Ck__c"},"field_P1maQ__c":{"type":"select_many","options":[{"not_usable":false,"label":"必修I","value":"TAnQ3YS1h"},{"not_usable":false,"label":"必修II2222","value":"0m7Fp4Y7t"},{"not_usable":false,"label":"必修III","value":"vTvr4wXOd"},{"not_usable":false,"label":"必修IV","value":"6y37Kk5N1"},{"not_usable":false,"label":"必修V","value":"assu3f7ri"},{"not_usable":false,"label":"选修3-1","value":"5C2qXod43"},{"not_usable":false,"label":"选修3-2","value":"nhz8q9rm1"},{"not_usable":false,"label":"选修3-3","value":"pQ1ZptAxC"},{"not_usable":true,"label":"选修3-4","value":"8by4gL22c"},{"not_usable":false,"label":"选修3-5","value":"0N25Ks9ot"},{"not_usable":false,"label":"必修VIII","value":"vb71n110Y"}],"define_type":"custom","cascade_parent_api_name":"field_9I230__c","is_index":true,"is_active":true,"default_value":"[\\"test\\", \\"test1\\"]","label":"多选科目","api_name":"field_P1maQ__c"},"field_0us5a__c":{"type":"object_reference","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_name":"id","field_values":["filter__c"]}]}],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"关联fj-主新建","target_api_name":"object_p0I5m__c","target_related_list_name":"target_related_list_329P1__c","api_name":"field_0us5a__c"},"data_own_organization":{"type":"department","wheres":[],"optional_type":"department","define_type":"package","is_index":true,"is_active":true,"default_value":"","label":"归属组织","api_name":"data_own_organization"},"field_2qK2y__c":{"type":"select_one","options":[{"value":"option1","label":"示例选项"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"单选-cesss","api_name":"field_2qK2y__c"},"field_8rXV3__c":{"type":"select_one","used_in":"component","options":[{"value":"complete","label":"已完成"},{"value":"incomplete","label":"未完成"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"incomplete","label":"签退状态","api_name":"field_8rXV3__c"},"field_KbWc4__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"开始时间-01","api_name":"field_KbWc4__c","date_format":"yyyy-MM-dd HH:mm"},"field_V02Ig__c":{"type":"number","decimal_places":2,"define_type":"custom","max_length":14,"is_index":true,"is_active":true,"length":12,"default_value":"$field_RC2lj__c$+$field_m5m7a__c$","label":"数字默认b+c","api_name":"field_V02Ig__c","is_show_mask":false,"round_mode":4},"field_js17w__c":{"type":"text","used_in":"component","define_type":"custom","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"收款方式","api_name":"field_js17w__c","is_show_mask":false},"field_M134b__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"签到时间","api_name":"field_M134b__c","date_format":"yyyy-MM-dd HH:mm"},"origin_source":{"is_index":false,"is_active":true,"label":"数据来源","type":"select_one","api_name":"origin_source","options":[{"value":"0","label":"数据同步"}],"define_type":"system"},"field_RF8w9__c":{"type":"time","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"时间","api_name":"field_RF8w9__c","date_format":"HH:mm"},"start_time__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"开始时间时间","api_name":"start_time__c","date_format":"yyyy-MM-dd HH:mm"},"import_dep__c":{"type":"department","wheres":[],"optional_type":"department","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"部门","api_name":"import_dep__c"},"field_j2727__c":{"type":"object_reference_many","wheres":[],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"查找关联(多选)-渠道","target_api_name":"ChannelObj","target_related_list_name":"target_related_list_HGFcy__c","api_name":"field_j2727__c"},"field_date55k__c":{"is_index":true,"is_active":true,"group_type":"date_time_range","label":"日期范围55","type":"group","api_name":"field_date55k__c","define_type":"custom","fields":{"end_time_field":"field_date55k2__c","start_time_field":"field_date55k1__c"}},"field_le23l__c":{"type":"select_many","options":[{"label":"唐山","font_color":"#2a304d","value":"KtuG2wfOk"},{"label":"石家庄","font_color":"#2a304d","value":"20lzvA0zI"},{"label":"秦皇岛","font_color":"#2a304d","value":"option1"},{"label":"其他","font_color":"#2a304d","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"$field_0us5a__c__r.field_9p43F__c._value$","label":"必输多选","api_name":"field_le23l__c"},"mc_functional_currency":{"is_index":false,"is_active":true,"label":"本位币","type":"select_one","api_name":"mc_functional_currency","options":[{"not_usable":false,"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package"},"field_2m6mJ__c":{"type":"select_one","options":[{"value":"xvOK4a72a","label":"橘子子"},{"value":"option1","label":"苹果子"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"单选子","api_name":"field_2m6mJ__c"},"field_6c3EG__c":{"type":"text","used_in":"component","define_type":"custom","input_mode":"","max_length":300,"is_index":true,"is_active":true,"default_value":"","label":"详细地址-无乡镇","api_name":"field_6c3EG__c","is_show_mask":false},"field_jt9F4__c":{"type":"text","define_type":"custom","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"必填文本","api_name":"field_jt9F4__c","is_show_mask":false},"last_modified_time":{"is_index":true,"label":"最后修改时间","type":"date_time","api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss"},"life_status":{"type":"select_one","options":[{"label":"未生效","font_color":"#2a304d","value":"ineffective"},{"label":"审核中","font_color":"#2a304d","value":"under_review"},{"label":"正常","font_color":"#2a304d","value":"normal"},{"label":"变更中","font_color":"#2a304d","value":"in_change"},{"label":"作废","font_color":"#2a304d","value":"invalid"}],"define_type":"package","is_index":true,"is_active":true,"default_value":"normal","label":"生命状态","api_name":"life_status"},"field_w5j83__c":{"type":"object_reference","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_name":"id","field_values":["func_Lt0vS__c"]}]}],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"whatlist查找关联","target_api_name":"object_zl_whatlist__c","target_related_list_name":"target_related_list_wWeeu__c","api_name":"field_w5j83__c"},"out_tenant_id":{"is_index":false,"is_active":true,"label":"外部企业","type":"text","api_name":"out_tenant_id","define_type":"system","max_length":200},"field_pcsz7__c":{"type":"file_attachment","define_type":"custom","is_index":true,"is_active":true,"label":"附件2","api_name":"field_pcsz7__c"},"field_Sec0Z__c":{"is_index":false,"is_active":true,"quote_field_type":"object_reference","label":"引用关联渠道","type":"quote","quote_field":"field_0us5a__c__r.field_U3vPb__c","api_name":"field_Sec0Z__c","define_type":"custom","is_show_mask":false},"country__c":{"type":"country","used_in":"component","options":[],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"国家","api_name":"country__c"},"field_3bzRz__c":{"type":"object_reference_many","wheres":[],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"查找关联(多选)","target_api_name":"object_31Eox__c","target_related_list_name":"target_related_list_ooc0j__c","api_name":"field_3bzRz__c"},"order_by":{"is_index":false,"length":8,"label":"order_by","type":"number","decimal_places":0,"api_name":"order_by","define_type":"system","round_mode":4},"field_N89H7__c":{"type":"select_one","options":[{"value":"8jd1jA611","label":"桃子"},{"label":"橙子","font_color":"#2a304d","value":"85ogUr7w1"},{"label":"苹果","font_color":"#2a304d","value":"mBUsdvaC1"},{"value":"zj6iym0Ea","label":"芒果"},{"label":"香蕉","font_color":"#2a304d","value":"option1"},{"label":"其他","font_color":"#2a304d","value":"other"}],"define_type":"custom","is_used_by_stage":true,"is_index":true,"is_active":true,"default_value":"","label":"种植水果单选","api_name":"field_N89H7__c"},"field_m5m7a__c":{"type":"number","decimal_places":2,"define_type":"custom","max_length":14,"is_index":true,"is_active":true,"length":12,"default_value":"","label":"数字b","api_name":"field_m5m7a__c","is_show_mask":false,"round_mode":4},"field_cjbdf__c":{"is_index":false,"is_active":true,"quote_field_type":"select_many","label":"引用爱好","type":"quote","quote_field":"field_0us5a__c__r.field_ki9p0__c","api_name":"field_cjbdf__c","define_type":"custom","is_show_mask":false},"field_kM81X__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"开始时间-1","api_name":"field_kM81X__c","date_format":"yyyy-MM-dd HH:mm"},"field_T631g__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"结束时间-1","api_name":"field_T631g__c","date_format":"yyyy-MM-dd HH:mm"},"field_19l9I__c":{"type":"object_reference","wheres":[],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"测试关闭显示字段对象","target_api_name":"object_ls211__c","target_related_list_name":"target_related_list_qKta1__c","api_name":"field_19l9I__c"},"field_OVvd2__c":{"type":"big_file_attachment","define_type":"custom","is_index":true,"is_active":true,"label":"大附件","api_name":"field_OVvd2__c"},"field_kj5ob__c":{"is_index":true,"is_active":true,"group_type":"date_time_range","label":"日期范围-01","type":"group","api_name":"field_kj5ob__c","define_type":"custom","fields":{"end_time_field":"field_pAMh8__c","start_time_field":"field_KbWc4__c"}},"field_2z74y__c":{"type":"object_reference","wheres":[],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"关联客户","target_api_name":"AccountObj","target_related_list_name":"target_related_list_oac0I__c","api_name":"field_2z74y__c"},"field_1JnD1__c":{"type":"date","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"日期1","api_name":"field_1JnD1__c","date_format":"yyyy-MM-dd"},"field_333w2__c":{"quote_field_type":"select_one","type":"quote","quote_field":"field_0us5a__c__r.field_0qdI8__c","define_type":"custom","is_index":true,"is_active":true,"label":"引用最爱吃的蔬菜","api_name":"field_333w2__c","is_show_mask":false},"field_91nN5__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"开始时间2","api_name":"field_91nN5__c","date_format":"yyyy-MM-dd HH:mm"},"owner_department":{"type":"text","define_type":"package","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"负责人主属部门","api_name":"owner_department","is_show_mask":false},"field_67Gan__c":{"type":"select_many","options":[{"label":"中国","font_color":"#2a304d","value":"r761zJTNW"},{"label":"越南","font_color":"#2a304d","value":"Fnl2C2p8c"},{"label":"老挝","font_color":"#2a304d","value":"option1"},{"label":"其他","font_color":"#2a304d","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"[[], \\"Fnl2C2p8c\\"]","label":"多选","api_name":"field_67Gan__c"},"field_yU2dm__c":{"type":"city","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"field_zY4fP__c","is_index":true,"is_active":true,"default_value":"","label":"市-无乡镇","api_name":"field_yU2dm__c"},"field_8j35M__c":{"is_index":true,"is_active":true,"group_type":"date_time_range","label":"日期范围3","type":"group","api_name":"field_8j35M__c","define_type":"custom","fields":{"end_time_field":"field_LniJO__c","start_time_field":"field_1VZ2s__c"}},"field_EsVim__c":{"type":"select_one","used_in":"component","options":[{"value":"complete","label":"已完成"},{"value":"incomplete","label":"未完成"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"incomplete","label":"签到状态","api_name":"field_EsVim__c"},"district__c":{"type":"district","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"city__c","is_index":true,"is_active":true,"label":"区","api_name":"district__c"},"lock_status":{"type":"select_one","options":[{"label":"未锁定","font_color":"#2a304d","value":"0"},{"label":"锁定","font_color":"#2a304d","value":"1"}],"define_type":"package","is_index":true,"is_active":true,"default_value":"0","label":"锁定状态","api_name":"lock_status"},"package":{"is_index":false,"is_active":true,"label":"package","type":"text","api_name":"package","define_type":"system","max_length":200},"field_7ukz5__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"结束时间2","api_name":"field_7ukz5__c","date_format":"yyyy-MM-dd HH:mm"},"create_time":{"is_index":true,"label":"创建时间","type":"date_time","api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss"},"field_9y2wU__c":{"type":"object_reference","wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"N","field_name":"field_C2atD__c","field_values":["true"]}]}],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"关联渠道","target_api_name":"ChannelObj","target_related_list_name":"target_related_list_m16zz__c","api_name":"field_9y2wU__c"},"field_4u04f__c":{"type":"select_many","options":[{"value":"3fU210hlL","label":"小于100"},{"value":"25zTq7z06","label":"大于100小于500"},{"value":"option1","label":"大于500"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":[],"label":"企业人数","api_name":"field_4u04f__c"},"province__c":{"type":"province","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"country__c","is_index":true,"is_active":true,"default_value":"","label":"省","api_name":"province__c"},"field_gEebm__c":{"return_type":"date_time","type":"count","decimal_places":0,"wheres":[],"define_type":"custom","is_index":true,"is_active":true,"label":"统计日期字段","api_name":"field_gEebm__c","is_show_mask":false,"round_mode":4},"created_by":{"is_index":true,"is_active":true,"label":"创建人","type":"employee","api_name":"created_by","define_type":"system"},"relevant_team":{"is_index":true,"is_active":true,"label":"相关团队","type":"embedded_object_list","api_name":"relevant_team","define_type":"package"},"field_RhIw8__c":{"is_index":true,"is_active":true,"group_type":"date_time_range","label":"日期范围2","type":"group","api_name":"field_RhIw8__c","define_type":"custom","fields":{"end_time_field":"field_7ukz5__c","start_time_field":"field_91nN5__c"}},"data_own_department":{"type":"department","wheres":[],"optional_type":"department","define_type":"package","is_index":true,"is_active":true,"default_value":"","label":"归属部门","api_name":"data_own_department"},"field_aorbk__c":{"type":"select_one","options":[{"value":"C3mG8ou09","label":"测试英雄"},{"value":"35frh44C1","label":"妖姬"},{"value":"1FX8kBvb1","label":"奶妈"},{"value":"2om18ACSv","label":"机器人"},{"value":"26e1wzry1","label":"女警"},{"value":"A3fx10XQ4","label":"阿木木"},{"value":"d6md415kI","label":"死歌"},{"value":"gDvjyMSxq","label":"大虫子"},{"value":"60Uwb52e9","label":"奥巴马"},{"value":"14w6q0dsb","label":"劫"},{"value":"8lOQS7ynx","label":"诺手"},{"value":"Aw8L4Hzfw","label":"盲僧"},{"value":"d1Pk2jFWa","label":"奇亚娜"},{"value":"option1","label":"莫甘娜"},{"value":"other","label":"其他"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"英雄","api_name":"field_aorbk__c"},"field_n4D12__c":{"is_index":false,"is_active":true,"quote_field_type":"text","label":"单行文本引用","type":"quote","quote_field":"field_0us5a__c__r.field_t0891__c","api_name":"field_n4D12__c","define_type":"custom","is_show_mask":false},"field_5I5Ru__c":{"type":"object_reference","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_name":"id","field_values":["filter__c"]}]}],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"关联fj-测试负责人","target_api_name":"object_L612u__c","target_related_list_name":"target_related_list_z3RFA__c","api_name":"field_5I5Ru__c"},"field_9xG4P__c":{"type":"long_text","define_type":"custom","max_length":2000,"is_index":true,"is_active":true,"default_value":"$field_y2k46__c$","label":"多行文本","api_name":"field_9xG4P__c"},"name":{"type":"text","define_type":"system","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"主属性","api_name":"name","is_show_mask":false},"_id":{"is_index":false,"is_active":true,"label":"_id","type":"text","api_name":"_id","define_type":"system","max_length":200},"field_1uR3x__c":{"type":"select_one","options":[{"value":"8W9bG226K","label":"5"},{"value":"ae40LeKU2","label":"4"},{"value":"E4fa88jz2","label":"3"},{"value":"w32iV0hc2","label":"2"},{"value":"option1","label":"1"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"排位所在位置","api_name":"field_1uR3x__c"},"field_6lubk__c":{"is_index":false,"is_active":true,"quote_field_type":"district","label":"引用区","type":"quote","quote_field":"field_0us5a__c__r.field_IN0Jz__c","api_name":"field_6lubk__c","define_type":"custom","is_show_mask":false},"sign_in_info__c":{"is_index":false,"is_active":true,"label":"签到信息","type":"embedded_object_list","used_in":"component","api_name":"sign_in_info__c","define_type":"custom"},"field_YzEm3__c":{"type":"text","define_type":"custom","input_mode":"","max_length":100,"is_index":true,"is_active":true,"default_value":"","label":"地区定位赋值UI事件触发字段","api_name":"field_YzEm3__c","is_show_mask":false},"field_pAMh8__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"结束时间-01","api_name":"field_pAMh8__c","date_format":"yyyy-MM-dd HH:mm"},"field_1VZ2s__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"开始时间34","api_name":"field_1VZ2s__c","date_format":"yyyy-MM-dd HH:mm"},"town__c":{"type":"town","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"district__c","is_index":false,"is_active":true,"label":"乡镇","api_name":"town__c"},"lock_user":{"type":"employee","wheres":[],"define_type":"package","is_index":false,"is_active":true,"label":"加锁人","api_name":"lock_user"},"field_1k25V__c":{"type":"auto_number","define_type":"custom","is_index":true,"is_active":true,"default_value":"01","label":"编号V1","api_name":"field_1k25V__c"},"partner_id":{"type":"object_reference","wheres":[],"define_type":"package","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"合作伙伴","target_api_name":"PartnerObj","target_related_list_name":"partner_object_qep6n__c_list","api_name":"partner_id"},"field_3S7d1__c":{"is_index":false,"is_active":true,"group_type":"area","label":"地区定位-无乡镇","type":"group","api_name":"field_3S7d1__c","define_type":"custom","fields":{"area_country":"field_8C1cg__c","area_location":"field_j4x49__c","area_detail_address":"field_6c3EG__c","area_city":"field_yU2dm__c","area_province":"field_zY4fP__c","area_district":"field_8tr12__c"}},"is_deleted":{"is_index":false,"default_value":false,"label":"is_deleted","type":"true_or_false","api_name":"is_deleted","options":[],"define_type":"system"},"field_5LUfm__c":{"type":"object_reference","wheres":[],"define_type":"custom","input_mode":"","is_index":true,"is_active":false,"default_value":"","label":"查找关联-自己","target_api_name":"object_qep6N__c","target_related_list_name":"target_related_list_lq1w2__c","api_name":"field_5LUfm__c"},"field_Jl9X1__c":{"type":"date","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"日期","api_name":"field_Jl9X1__c","date_format":"yyyy-MM"},"object_describe_api_name":{"is_index":false,"is_active":true,"label":"object_describe_api_name","type":"text","api_name":"object_describe_api_name","define_type":"system","max_length":200},"out_owner":{"is_index":true,"is_active":true,"label":"外部负责人","type":"employee","api_name":"out_owner","define_type":"system"},"field_8tr12__c":{"type":"district","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"field_yU2dm__c","is_index":true,"is_active":true,"label":"区-无乡镇","api_name":"field_8tr12__c"},"out_resources":{"type":"select_one","options":[{"value":"partner","label":"代理通"}],"define_type":"package","is_index":false,"is_active":true,"default_value":"","label":"外部来源","api_name":"out_resources"},"field_16Yl4__c":{"type":"select_one","options":[{"value":"vbQKTQ169","label":"辅助"},{"value":"U1Ms5aKM4","label":"打野"},{"value":"276tebcDS","label":"射手"},{"value":"C3mG8ou09","label":"中路"},{"value":"option1","label":"上路"},{"value":"other","label":"其他"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":"IF(EQUALS($field_aorbk__c._value$, \\"other\\"), $field_aorbk__c._value$, \\"C3mG8ou09\\")","label":"位置选择","api_name":"field_16Yl4__c"},"field_uVre4__c":{"type":"department_many","wheres":[],"optional_type":"department","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"部门(多选)","api_name":"field_uVre4__c"},"field_695k5__c":{"return_type":"number","type":"formula","decimal_places":2,"define_type":"custom","is_index":true,"is_active":true,"label":"计算a=b+c","api_name":"field_695k5__c","is_show_mask":false},"owner":{"type":"employee","wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"ISN","field_name":"employee_create_time","field_values":[]}]}],"define_type":"package","is_index":true,"is_active":true,"default_value":"","label":"负责人","api_name":"owner"},"address__c":{"type":"text","used_in":"component","define_type":"custom","input_mode":"","max_length":300,"is_index":true,"is_active":true,"default_value":"","label":"详细地址","api_name":"address__c","is_show_mask":false},"field_L3B64__c":{"type":"number","decimal_places":1,"used_in":"component","define_type":"custom","max_length":16,"is_index":true,"is_active":true,"length":15,"default_value":"","label":"间隔时长","api_name":"field_L3B64__c","is_show_mask":false,"round_mode":4},"field_BzE8k__c":{"type":"object_reference","wheres":[],"define_type":"custom","input_mode":"","is_index":true,"is_active":true,"default_value":"","label":"查找关联1","target_api_name":"object_l835k__c","target_related_list_name":"target_related_list_g46F2__c","api_name":"field_BzE8k__c"},"city__c":{"type":"city","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"province__c","is_index":true,"is_active":true,"default_value":"","label":"市","api_name":"city__c"},"field_bM277__c":{"group_type":"sign_in","type":"group","define_type":"custom","is_index":false,"is_active":true,"label":"签到组件","api_name":"field_bM277__c","fields":{"sign_in_location_field":"field_sp3sb__c","visit_status_field":"field_5aPLJ__c","sign_in_status_field":"field_EsVim__c","sign_in_time_field":"field_M134b__c","sign_out_status_field":"field_8rXV3__c","sign_out_location_field":"field_chrIH__c","interval_field":"field_L3B64__c","sign_in_info_list_field":"sign_in_info__c","sign_out_time_field":"field_mk2Y5__c"}},"field_0uowd__c":{"is_index":false,"is_active":true,"quote_field_type":"country","label":"引用国家","type":"quote","quote_field":"field_0us5a__c__r.field_ob8e1__c","api_name":"field_0uowd__c","define_type":"custom","is_show_mask":false},"last_modified_by":{"is_index":true,"is_active":true,"label":"最后修改人","type":"employee","api_name":"last_modified_by","define_type":"system"},"field_twIIy__c":{"type":"select_many","options":[{"value":"6D2eb2Wa5","label":"普通客户"},{"value":"bjS0e03em","label":"vip客户"},{"value":"option1","label":"重要客户"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":[],"label":"客户级别","api_name":"field_twIIy__c"},"mc_currency":{"type":"select_one","options":[{"not_usable":false,"label":"BYR - 白俄罗斯卢布","value":"BYR"},{"not_usable":false,"label":"AED - UAE Dirham","value":"AED"},{"not_usable":false,"label":"BIF - 布隆迪法郎","value":"BIF"},{"not_usable":false,"label":"BRL - 巴西币","value":"BRL"},{"not_usable":false,"label":"BOB - 玻利维亚的玻利维亚诺","value":"BOB"},{"not_usable":false,"label":"BYN - 白俄罗斯卢布","value":"BYN"},{"not_usable":false,"label":"BZD - 伯利兹元","value":"BZD"},{"not_usable":false,"label":"BOV - 玻利维亚姆夫多尔","value":"BOV"},{"not_usable":false,"label":"BRB - 巴西克鲁塞罗（旧）","value":"BRB"},{"not_usable":false,"label":"BSD - 巴哈马元","value":"BSD"},{"not_usable":false,"label":"自定义QWE","value":"qwe"},{"not_usable":false,"label":"自定义zas","value":"zas"},{"not_usable":false,"label":"翻译ASD","value":"asd"},{"not_usable":false,"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"not_usable":false,"label":"ada名称","value":"ada"},{"not_usable":false,"label":"BTN - 不丹卢比","value":"BTN"},{"not_usable":false,"label":"BGN - Bulgarian Lev","value":"BGN"},{"not_usable":false,"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":false,"label":"BND - Brunei Dollar","value":"BND"},{"not_usable":false,"label":"BMD - 百慕大元","value":"BMD"},{"not_usable":false,"label":"AOA - 安哥拉宽扎","value":"AOA"},{"not_usable":false,"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"not_usable":false,"label":"ARS - 阿根廷比索","value":"ARS"},{"not_usable":false,"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"not_usable":false,"label":"AUD - Australian Dollar","value":"AUD"},{"not_usable":false,"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"not_usable":false,"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"not_usable":false,"label":"USD - U.S. Dollar","value":"USD"},{"not_usable":false,"label":"BAM - 自由兑换马克","value":"BAM"},{"not_usable":false,"label":"BBD - 巴巴多斯元","value":"BBD"},{"not_usable":false,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":false,"label":"BWP - 博茨瓦纳普拉","value":"BWP"},{"not_usable":false,"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"not_usable":false,"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","is_index":true,"is_active":true,"default_value":"","label":"币种","api_name":"mc_currency"},"field_oo82r__c":{"is_index":false,"is_active":true,"group_type":"area","label":"地区定位","type":"group","api_name":"field_oo82r__c","define_type":"custom","fields":{"area_country":"country__c","area_location":"field_EgkrH__c","area_town":"town__c","area_detail_address":"address__c","area_city":"city__c","area_province":"province__c","area_district":"district__c"}},"field_sp3sb__c":{"type":"location","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"label":"签到地址","api_name":"field_sp3sb__c"},"record_type":{"is_index":true,"is_active":true,"label":"业务类型","type":"record_type","api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型","value":"default__c"},{"is_active":true,"label":"树形视图验证","value":"record_4Oreg__c","api_name":"record_4Oreg__c"},{"description":"你好2","is_active":true,"label":"UI事件业务类型","value":"record_fHdQx__c","api_name":"record_fHdQx__c"},{"is_active":true,"label":"地位字段","value":"record_7ExSf__c","api_name":"record_7ExSf__c"},{"is_active":true,"label":"相关对象一起","api_name":"record_34zTM__c"},{"is_active":false,"label":"业务类型-ceshi1","api_name":"record_i5g5M__c"}],"define_type":"package"},"field_date55k2__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"结束时间55","api_name":"field_date55k2__c","date_format":"yyyy-MM-dd"},"end_time__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"结束时间时间","api_name":"end_time__c","date_format":"yyyy-MM-dd HH:mm"},"field_l193L__c":{"type":"signature","define_type":"custom","is_index":true,"is_active":true,"label":"签名字段","is_watermark":false,"api_name":"field_l193L__c"},"field_7pw11__c":{"type":"date","define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"发钱日期","api_name":"field_7pw11__c","date_format":"yyyy-MM-dd"},"field_LniJO__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"结束时间3","api_name":"field_LniJO__c","date_format":"yyyy-MM-dd HH:mm"},"field_RC2lj__c":{"type":"number","decimal_places":2,"define_type":"custom","max_length":14,"is_index":true,"is_active":true,"length":12,"default_value":"","label":"数字c","api_name":"field_RC2lj__c","is_show_mask":false,"round_mode":4},"field_zY4fP__c":{"type":"province","used_in":"component","options":[],"define_type":"custom","cascade_parent_api_name":"field_8C1cg__c","is_index":true,"is_active":true,"default_value":"","label":"省-无乡镇","api_name":"field_zY4fP__c"},"field_mk2Y5__c":{"type":"date_time","used_in":"component","define_type":"custom","is_index":true,"is_active":true,"not_use_multitime_zone":false,"default_value":"","label":"签退时间","api_name":"field_mk2Y5__c","date_format":"yyyy-MM-dd HH:mm"},"field_3xo1m__c":{"return_type":"number","type":"formula","decimal_places":2,"define_type":"custom","is_index":true,"is_active":true,"label":"计算字段-使用单选","api_name":"field_3xo1m__c","is_show_mask":false},"field_8C1cg__c":{"type":"country","used_in":"component","options":[],"define_type":"custom","is_index":true,"is_active":true,"default_value":"","label":"国家-无乡镇","api_name":"field_8C1cg__c"},"mc_exchange_rate_version":{"is_index":false,"is_active":true,"label":"汇率版本","type":"text","api_name":"mc_exchange_rate_version","define_type":"package","max_length":256},"field_e3z2j__c":{"type":"select_many","options":[{"value":"option1","label":"示例选项"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_index":true,"is_active":true,"default_value":[],"label":"多选111","api_name":"field_e3z2j__c"},"field_2Cn77__c":{"is_index":true,"is_active":true,"default_value":true,"label":"关闭字段","type":"true_or_false","api_name":"field_2Cn77__c","options":[{"label":"是","font_color":"#2a304d","value":true},{"label":"否","font_color":"#2a304d","value":false}],"define_type":"custom"},"external_person__c":{"describe_api_name":"object_qep6N__c","is_index":true,"is_active":true,"create_time":1726819872276,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"外部人员111111","type":"out_employee","field_num":13,"is_required":false,"enable_clone":true,"api_name":"external_person__c","define_type":"custom","_id":"66ed2e207f533a00070659c8","is_single":false,"is_index_field":false,"index_name":"a_0","help_text":"","status":"new"}},"release_version":"6.4","actions":{}}'''
    String detailDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1710228037229,"create_time":*************,"description":"","last_modified_by":"1000","display_name":"fj-UI事件从1","created_by":"1000","version":85,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_7wQM3__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"tgE","_id":"6139ba7725421000015b2927","fields":{"lock_rule":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1631173239483,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定规则","is_unique":false,"rules":[],"default_value":"default_lock_rule","label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"6139ba7725421000015b291f","is_single":false,"label_r":"锁定规则","is_index_field":false,"index_name":"s_3","status":"new"},"field_sZG2e__c":{"describe_api_name":"object_7wQM3__c","is_index":true,"file_amount_limit":1,"is_active":true,"create_time":1681293375000,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"file_source":["local","net"],"label":"附件","type":"file_attachment","field_num":27,"file_size_limit":104857600,"is_required":false,"api_name":"field_sZG2e__c","define_type":"custom","_id":"6436803f14f429000164ee80","is_single":false,"is_index_field":false,"index_name":"a_6","support_file_types":[],"help_text":"单个文件不得超过100M","status":"new"},"field_I26FO__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"d_5","max_length":14,"is_index":true,"is_active":true,"create_time":1703668092145,"is_encrypted":false,"display_style":"input","step_value":1,"length":12,"default_value":"","label":"数字c","field_num":32,"api_name":"field_I26FO__c","_id":"658be97ccfca9f000164a50a","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new","description":""},"field_9lt4t__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":true,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"d_2","max_length":14,"is_index":true,"is_active":true,"create_time":1631179647005,"is_encrypted":false,"display_style":"input","step_value":1,"length":12,"default_value":"","label":"数字-字段级必填","field_num":12,"api_name":"field_9lt4t__c","_id":"6139d37f7e27bd00013edf4b","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"field_44L92__c":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":1641543890074,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"区","type":"district","field_num":22,"used_in":"component","is_required":false,"api_name":"field_44L92__c","options":[],"define_type":"custom","_id":"61d7f8d256f5f90001141ccd","is_single":false,"cascade_parent_api_name":"field_zcszw__c","is_index_field":false,"index_name":"s_10","config":{},"help_text":"","status":"new"},"field_25trk__c":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1703668264889,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"text","remove_mask_roles":{},"is_unique":false,"label":"引用fj-函数主属性","type":"quote","quote_field":"field_HB77d__c__r.name","is_required":false,"api_name":"field_25trk__c","define_type":"custom","_id":"658bea29cfca9f000164cfe4","is_single":false,"is_index_field":false,"index_name":"t_0","is_show_mask":false,"help_text":"","status":"new","description":""},"field_lBgii__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"department","is_required":false,"wheres":[],"enable_clone":true,"define_type":"custom","is_single":true,"index_name":"a_4","is_index":true,"is_active":true,"create_time":1646376056567,"is_encrypted":false,"default_value":"","label":"从1部门","field_num":26,"api_name":"field_lBgii__c","_id":"6221b47827604000017d2e16","is_index_field":false,"help_text":"","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"label_r":"汇率","index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1631173239529,"is_encrypted":false,"display_style":"input","step_value":1,"length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"6139ba7725421000015b2924","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"life_status_before_invalid":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1631173239484,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","field_num":7,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"6139ba7725421000015b2922","is_single":false,"label_r":"作废前生命状态","is_index_field":false,"index_name":"t_1","max_length":256,"status":"new"},"field_3tn59__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"d_4","max_length":14,"is_index":true,"is_active":true,"create_time":1703668086887,"is_encrypted":false,"display_style":"input","step_value":1,"length":12,"default_value":"","label":"数字b","field_num":31,"api_name":"field_3tn59__c","_id":"658be977cfca9f000164a4ed","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"owner_department":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":true,"label_r":"负责人主属部门","index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1631173239519,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"6139ba7725421000015b2916","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_q3hl2__c":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1676432637120,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"object_reference","remove_mask_roles":{},"description":"","is_unique":false,"label":"引用主-科目","type":"quote","quote_field":"field_i3yKp__c__r.field_bcI2r__c","is_required":false,"api_name":"field_q3hl2__c","define_type":"custom","_id":"63ec54fd0d892f000106748d","is_single":false,"is_index_field":false,"index_name":"s_16","is_show_mask":false,"help_text":"","status":"new"},"field_UNme1__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[],"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_9","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"default_value":"","label":"查找关联-客户","target_api_name":"AccountObj","target_related_list_name":"target_related_list_mMbp2__c","field_num":17,"target_related_list_label":"fj-UI事件从1","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_UNme1__c","_id":"61cbd22f0bcf8e00012257d4","is_index_field":true,"help_text":"","status":"new"},"field_HB77d__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"field_881RX__c","field_values":["$field_i3yKp__c__r.field_0us5a__c$"]}]}],"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_1","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"default_value":"","label":"关联fj-UI事件函数引用关系测试","target_api_name":"object_lrMBl__c","target_related_list_name":"target_related_list_kryCM__c","field_num":14,"target_related_list_label":"fj-UI事件函数引用关系测试","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_HB77d__c","_id":"61c2d86c84ed2900017b0673","is_index_field":true,"help_text":"","status":"new"},"field_Z1dKZ__c":{"expression_type":"js","return_type":"number","describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_0","is_index":true,"is_active":true,"expression":"$field_3tn59__c$+$field_I26FO__c$","create_time":1703068635517,"is_encrypted":false,"label":"计算a=b+c","field_num":30,"api_name":"field_Z1dKZ__c","_id":"6582c3db1a3e9f000182895d","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_G0M97__c":{"expression_type":"js","return_type":"number","describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_6","is_index":true,"is_active":true,"expression":"$field_i3yKp__c__r.field_6bDGT__c$","create_time":1703686382111,"is_encrypted":false,"label":"计算=主统计","field_num":33,"api_name":"field_G0M97__c","_id":"658c30f0fe55820001b32ba9","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new","description":""},"lock_status":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"锁定状态","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","is_single":false,"label_r":"锁定状态","index_name":"s_4","is_index":true,"is_active":true,"create_time":1631173239483,"is_encrypted":false,"default_value":"0","label":"锁定状态","field_num":2,"is_need_convert":false,"api_name":"lock_status","_id":"6139ba7725421000015b2920","is_index_field":false,"config":{},"help_text":"","status":"new"},"package":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"package","label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","max_length":200,"status":"released"},"create_time":{"describe_api_name":"object_7wQM3__c","is_index":true,"create_time":*************,"is_unique":false,"description":"create_time","label":"创建时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"field_G11fj__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_7","max_length":100,"is_index":true,"is_active":true,"create_time":1686308888757,"is_encrypted":false,"default_value":"","label":"测试","field_num":28,"api_name":"field_G11fj__c","_id":"648308184de8ad0001c7e9d4","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_81aev__c":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":1641543890074,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"default_value":"","label":"国家","type":"country","field_num":19,"used_in":"component","is_required":false,"api_name":"field_81aev__c","options":[],"define_type":"custom","_id":"61d7f8d256f5f90001141cca","is_single":false,"is_index_field":false,"index_name":"s_12","config":{},"help_text":"","status":"new"},"version":{"describe_api_name":"object_7wQM3__c","is_index":false,"create_time":*************,"length":8,"is_unique":false,"description":"version","label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"created_by":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"relevant_team":{"describe_api_name":"object_7wQM3__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1631173239527,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"6139ba7725421000015b291e","is_single":false,"label_r":"相关团队","is_index_field":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"data_own_department":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"department","is_required":false,"wheres":[],"define_type":"package","is_single":true,"label_r":"归属部门","index_name":"data_owner_dept_id","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"default_value":"","label":"归属部门","is_need_convert":false,"api_name":"data_own_department","_id":"6267ce7dcbf6d40001ece5a8","is_index_field":false,"help_text":"","status":"released"},"field_1Hicx__c":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1641543889948,"is_encrypted":false,"is_support_town":true,"auto_adapt_places":false,"description":"","is_unique":false,"group_type":"area","label":"地区定位","type":"group","is_required":false,"api_name":"field_1Hicx__c","is_support_village":true,"define_type":"custom","_id":"61d7f8d256f5f90001141cd0","is_single":false,"is_index_field":false,"fields":{"area_country":"field_81aev__c","area_location":"field_0e1vr__c","area_town":"field_8k655__c","area_detail_address":"field_j6lw2__c","area_city":"field_zcszw__c","area_village":"field_6fq2o__c","area_province":"field_B3MNe__c","area_district":"field_44L92__c"},"index_name":"s_13","help_text":"","status":"new"},"field_c5L2n__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"enable_clone":true,"options":[{"label":"乐芙兰","value":"1"},{"label":"盲僧","value":"2"},{"label":"盖伦","value":"3"},{"label":"奥巴马","value":"4"},{"label":"娜美","value":"5"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_single":false,"index_name":"s_8","is_index":true,"is_active":true,"create_time":1640163915958,"is_encrypted":false,"default_value":"","label":"使用英雄单选","field_num":15,"api_name":"field_c5L2n__c","_id":"61c2ea4c84ed2900017b480a","is_index_field":false,"config":{},"help_text":"","status":"new"},"field_G6oh5__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"description":"","is_unique":false,"type":"currency","decimal_places":10,"default_to_zero":true,"is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"d_8","max_length":20,"is_index":true,"is_active":true,"create_time":1703830905247,"is_encrypted":false,"length":10,"default_value":"","label":"净价","currency_unit":"￥","field_num":35,"api_name":"field_G6oh5__c","_id":"65c08c53cd2bf700018c4202","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new"},"name":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","is_single":false,"label_r":"主属性","index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1631173239628,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"6139ba7725421000015b2914","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_B3MNe__c":{"describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"description":"","is_unique":false,"type":"province","used_in":"component","is_required":false,"options":[],"define_type":"custom","is_single":false,"cascade_parent_api_name":"field_81aev__c","index_name":"s_14","is_index":true,"is_active":true,"create_time":1641543890074,"is_encrypted":false,"default_value":"","label":"省","field_num":20,"api_name":"field_B3MNe__c","_id":"61d7f8d256f5f90001141ccb","is_index_field":false,"config":{},"help_text":"","status":"new"},"_id":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"field_WY2r1__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference_many","is_required":false,"wheres":[],"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"a_3","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"default_value":"","label":"查找关联(多选)-客户","target_api_name":"AccountObj","target_related_list_name":"target_related_list_96Nly__c","field_num":18,"target_related_list_label":"fj-UI事件从12","action_on_target_delete":"set_null","api_name":"field_WY2r1__c","_id":"61cbd23e0bcf8e0001225892","is_index_field":false,"help_text":"","status":"new"},"tenant_id":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"tenant_id","label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"data_own_organization":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"department","is_required":false,"wheres":[],"define_type":"package","is_single":true,"label_r":"归属组织","index_name":"a_5","is_index":true,"is_active":true,"create_time":********05938,"is_encrypted":false,"default_value":"","label":"归属组织","is_need_convert":false,"api_name":"data_own_organization","_id":"6230427204a720000162e67d","is_index_field":false,"help_text":"","status":"released"},"field_a6lwl__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_3","max_length":100,"is_index":true,"is_active":true,"create_time":1631179007736,"is_encrypted":false,"default_value":"","label":"备注","field_num":11,"api_name":"field_a6lwl__c","_id":"6139d0ff7e27bd00013ede0f","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"origin_source":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":*************,"is_unique":false,"label":"数据来源","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"origin_source","options":[{"label":"数据同步","value":"0"}],"define_type":"system","is_extend":false,"index_name":"s_os","config":{"display":0},"status":"released","description":""},"lock_user":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1631173239484,"is_encrypted":false,"auto_adapt_places":false,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"6139ba7725421000015b2921","is_single":true,"label_r":"加锁人","is_index_field":false,"index_name":"a_1","status":"new"},"field_j6lw2__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"used_in":"component","is_required":false,"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_5","max_length":300,"is_index":true,"is_active":true,"create_time":1641543890074,"is_encrypted":false,"default_value":"","label":"详细地址","field_num":23,"api_name":"field_j6lw2__c","_id":"61d7f8d256f5f90001141cce","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_xQ1T5__c":{"return_type":"number","describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"object_w4o31__c","is_required":false,"wheres":[],"define_type":"custom","is_single":false,"index_name":"d_3","field_api_name":"field_9yT34__c","is_index":true,"default_result":"d_null","is_active":true,"create_time":1692344551406,"is_encrypted":false,"count_type":"sum","count_field_api_name":"field_vn3b1__c","label":"统计A数字","field_num":29,"count_to_zero":false,"api_name":"field_xQ1T5__c","count_field_type":"number","_id":"64df20e7ab6755000190a0d0","is_index_field":false,"is_show_mask":false,"round_mode":4,"help_text":"","status":"new","description":""},"field_S7Bod__c":{"expression_type":"js","return_type":"number","describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_7","is_index":true,"is_active":true,"expression":"$field_HB77d__c__r.field_5cA3a__c$","create_time":1703686574769,"is_encrypted":false,"label":"计算=lookup统计","field_num":34,"api_name":"field_S7Bod__c","_id":"658c31affe55820001b32dd7","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new","description":""},"is_deleted":{"describe_api_name":"object_7wQM3__c","is_index":false,"create_time":*************,"is_unique":false,"description":"is_deleted","default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","options":[],"define_type":"system","index_name":"is_del","status":"released"},"object_describe_api_name":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"object_describe_api_name","label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","max_length":200,"status":"released"},"out_owner":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"index_name":"o_owner","config":{"display":1},"status":"released","description":""},"mc_functional_currency":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1631173239530,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"not_usable":false,"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"6139ba7725421000015b2925","is_single":false,"label_r":"本位币","is_index_field":false,"index_name":"s_6","config":{},"help_text":"","status":"new"},"field_i3yKp__c":{"describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"description":"","is_unique":false,"type":"master_detail","is_required":true,"define_type":"custom","is_single":false,"index_name":"s_2","is_index":true,"is_active":true,"create_time":1631173499307,"is_encrypted":false,"label":"主从关系","target_api_name":"object_qep6N__c","show_detail_button":false,"target_related_list_name":"target_related_list_nxV8W__c","field_num":10,"target_related_list_label":"fj-UI事件从1","api_name":"field_i3yKp__c","is_create_when_master_create":true,"_id":"6139bb7b25421000015b2a2d","is_index_field":true,"is_required_when_master_create":false,"help_text":"","status":"new"},"owner":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"employee","is_required":false,"wheres":[],"define_type":"package","is_single":true,"label_r":"负责人","index_name":"owner","is_index":true,"is_active":true,"create_time":1631173239518,"is_encrypted":false,"default_value":"","label":"负责人","is_need_convert":false,"api_name":"owner","_id":"6139ba7725421000015b2915","is_index_field":false,"help_text":"","status":"new"},"field_5ir20__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_many","default_to_zero":false,"is_required":false,"enable_clone":true,"options":[{"label":"法师","value":"1"},{"label":"打野","value":"2"},{"label":"上单","value":"3"},{"label":"adc","value":"4"},{"label":"辅助","value":"5"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","is_single":false,"index_name":"a_2","is_index":true,"is_active":true,"create_time":1640164031198,"is_encrypted":false,"default_value":[],"label":"喜欢位置多选","field_num":16,"api_name":"field_5ir20__c","_id":"61c2eabf84ed2900017b4877","is_index_field":false,"config":{},"help_text":"","status":"new"},"last_modified_time":{"describe_api_name":"object_7wQM3__c","is_index":true,"create_time":*************,"is_unique":false,"description":"last_modified_time","label":"最后修改时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"field_zcszw__c":{"describe_api_name":"object_7wQM3__c","auto_adapt_places":false,"description":"","is_unique":false,"type":"city","used_in":"component","is_required":false,"options":[],"define_type":"custom","is_single":false,"cascade_parent_api_name":"field_B3MNe__c","index_name":"s_11","is_index":true,"is_active":true,"create_time":1641543890074,"is_encrypted":false,"default_value":"","label":"市","field_num":21,"api_name":"field_zcszw__c","_id":"61d7f8d256f5f90001141ccc","is_index_field":false,"config":{},"help_text":"","status":"new"},"life_status":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","is_single":false,"label_r":"生命状态","index_name":"s_5","is_index":true,"is_active":true,"create_time":1631173239526,"is_encrypted":false,"default_value":"normal","label":"生命状态","field_num":3,"is_need_convert":false,"api_name":"life_status","_id":"6139ba7725421000015b291d","is_index_field":false,"config":{},"help_text":"","status":"new"},"last_modified_by":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"field_8k655__c":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1641544851670,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"乡镇","type":"town","field_num":25,"used_in":"component","is_required":false,"api_name":"field_8k655__c","options":[],"define_type":"custom","_id":"61d7fc9311a6fe0001c7d073","is_single":false,"cascade_parent_api_name":"field_44L92__c","is_index_field":false,"index_name":"s_15","config":{},"help_text":"","status":"new"},"field_6fq2o__c":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":1710228037025,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"村","type":"village","field_num":36,"used_in":"component","is_required":false,"api_name":"field_6fq2o__c","options":[],"define_type":"custom","_id":"65f002456fe47f00017ac3e3","is_single":false,"cascade_parent_api_name":"field_8k655__c","is_index_field":false,"index_name":"s_0","config":{},"help_text":"","status":"new","description":""},"out_tenant_id":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":*************,"pattern":"","is_unique":false,"description":"out_tenant_id","label":"外部企业","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","index_name":"o_ei","config":{"display":0},"max_length":200,"status":"released"},"mc_currency":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"select_one","default_to_zero":false,"is_required":false,"options":[{"not_usable":false,"label":"BYR - 白俄罗斯卢布","value":"BYR"},{"not_usable":false,"label":"AED - UAE Dirham","value":"AED"},{"not_usable":false,"label":"BIF - 布隆迪法郎","value":"BIF"},{"not_usable":false,"label":"BRL - 巴西币","value":"BRL"},{"not_usable":false,"label":"BOB - 玻利维亚的玻利维亚诺","value":"BOB"},{"not_usable":false,"label":"BYN - 白俄罗斯卢布","value":"BYN"},{"not_usable":false,"label":"BZD - 伯利兹元","value":"BZD"},{"not_usable":false,"label":"BOV - 玻利维亚姆夫多尔","value":"BOV"},{"not_usable":false,"label":"BRB - 巴西克鲁塞罗（旧）","value":"BRB"},{"not_usable":false,"label":"BSD - 巴哈马元","value":"BSD"},{"not_usable":false,"label":"自定义QWE","value":"qwe"},{"not_usable":false,"label":"自定义zas","value":"zas"},{"not_usable":false,"label":"翻译ASD","value":"asd"},{"not_usable":false,"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"not_usable":false,"label":"ada名称","value":"ada"},{"not_usable":false,"label":"BTN - 不丹卢比","value":"BTN"},{"not_usable":false,"label":"BGN - Bulgarian Lev","value":"BGN"},{"not_usable":false,"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":false,"label":"BND - Brunei Dollar","value":"BND"},{"not_usable":false,"label":"BMD - 百慕大元","value":"BMD"},{"not_usable":false,"label":"AOA - 安哥拉宽扎","value":"AOA"},{"not_usable":false,"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"not_usable":false,"label":"ARS - 阿根廷比索","value":"ARS"},{"not_usable":false,"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"not_usable":false,"label":"AUD - Australian Dollar","value":"AUD"},{"not_usable":false,"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"not_usable":false,"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"not_usable":false,"label":"USD - U.S. Dollar","value":"USD"},{"not_usable":false,"label":"BAM - 自由兑换马克","value":"BAM"},{"not_usable":false,"label":"BBD - 巴巴多斯元","value":"BBD"},{"not_usable":false,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":false,"label":"BWP - 博茨瓦纳普拉","value":"BWP"},{"not_usable":false,"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"not_usable":false,"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","is_single":false,"label_r":"币种","index_name":"s_7","is_index":true,"is_active":true,"create_time":1631173239528,"is_encrypted":false,"default_value":"","label":"币种","field_num":5,"api_name":"mc_currency","_id":"6139ba7725421000015b2923","is_index_field":false,"config":{},"help_text":"","status":"new"},"record_type":{"describe_api_name":"object_7wQM3__c","is_index":true,"is_active":true,"create_time":1631173239525,"is_encrypted":false,"auto_adapt_places":false,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":true,"api_name":"record_GtCOw__c","label":"UI事件从1-类型1"}],"define_type":"package","_id":"6139ba7725421000015b291c","is_single":false,"label_r":"业务类型","is_index_field":false,"index_name":"r_type","config":{},"help_text":"","status":"released"},"field_zp4yB__c":{"describe_api_name":"object_7wQM3__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","remove_mask_roles":{},"description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"enable_clone":true,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1631180049062,"is_encrypted":false,"default_value":"","label":"被修改-条件","field_num":13,"api_name":"field_zp4yB__c","_id":"6139d5117e27bd00013edfdf","is_index_field":false,"is_show_mask":false,"help_text":"","status":"new"},"field_0e1vr__c":{"describe_api_name":"object_7wQM3__c","auto_location":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"location","used_in":"component","is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"t_6","is_index":true,"is_active":true,"create_time":1641543890074,"is_encrypted":false,"label":"定位","is_geo_index":false,"field_num":24,"api_name":"field_0e1vr__c","range_limit":false,"_id":"61d7f8d256f5f90001141ccf","radius_range":100,"is_index_field":false,"help_text":"","status":"new"},"mc_exchange_rate_version":{"describe_api_name":"object_7wQM3__c","is_index":false,"is_active":true,"create_time":1631173239531,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"label":"汇率版本","type":"text","field_num":8,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"6139ba7725421000015b2926","is_single":false,"label_r":"汇率版本","is_index_field":false,"index_name":"t_2","max_length":256,"help_text":"","status":"new"}},"release_version":"6.4","actions":{}}'''
    @Shared
    String dataJson = '''{"lock_rule":null,"discount":"100.0000","account_id__r":"jejej","order_time":*************,"receivable_amount":"100.00","logistics_status":"1","ship_to_id":null,"order_status":"7","ship_to_add":null,"extend_obj_data_id":null,"created_by__r":{"picAddr":null,"description":null,"dept":null,"supervisorId":null,"title":null,"modifyTime":null,"post":"","createTime":null,"phone":null,"name":"admin01","nickname":null,"tenantId":null,"id":"1001","position":null,"email":"","status":null},"life_status_before_invalid":null,"order_amount":"100.00","owner_department_id":"1000","price_book_id__relation_ids":"5d4157fca5083d7cdb50f4b4","owner_department":"研发部","signature_attachment":null,"plan_payment_amount":null,"lock_status":"0","package":"CRM","data_own_department__r":{"deptName":"研发部","leaderName":null,"leaderUserId":null,"deptId":"1000","parentId":"999999","status":0},"create_time":*************,"resource":"0","submit_time":*************,"new_opportunity_id":null,"quote_id":null,"payment_amount":"0.00","created_by":["1001"],"version":"5","delivery_comment":null,"relevant_team":[{"teamMemberEmployee":["1001"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2"}],"confirmed_receive_date":null,"delivery_date":null,"data_own_department":["1000"],"object_describe_id":"5b0689ca9e787b86896a1a24","name":"20191017-000003","bill_money_to_confirm":"0","_id":"5da7dce52dc22b000137a982","payment_money_to_confirm":"0","data_own_department__l":[{"parentId":"999999","deptId":"1000","deptName":"研发部","status":0}],"tenant_id":"78586","remark":null,"invoice_amount":"0.00","lock_user":null,"is_deleted":false,"receipt_type":null,"returned_goods_amount":"0.00","object_describe_api_name":"SalesOrderObj","owner__l":[{"id":"1001","name":"admin01","email":"","post":""}],"refund_amount":"0.00","out_owner":null,"relevant_team__r":"admin01","owner__r":{"picAddr":null,"description":null,"dept":null,"supervisorId":null,"title":null,"modifyTime":null,"post":"","createTime":null,"phone":null,"name":"admin01","nickname":null,"tenantId":null,"id":"1001","position":null,"email":"","status":null},"product_amount":"100.00","owner":["1001"],"last_modified_time":1571282150477,"life_status":"normal","is_user_define_work_flow":null,"ship_to_tel":null,"last_modified_by__l":[{"id":"-10000","name":"系统"}],"created_by__l":[{"id":"1001","name":"admin01","email":"","post":""}],"last_modified_by":["-10000"],"out_tenant_id":null,"record_type":"default__c","last_modified_by__r":{"picAddr":null,"description":null,"dept":null,"supervisorId":null,"title":null,"modifyTime":null,"post":null,"createTime":null,"phone":null,"name":"系统","nickname":null,"tenantId":null,"id":"-10000","position":null,"email":null,"status":null},"account_id":"5d5b8cd47b8a5e0001349aff","account_id__relation_ids":"5d5b8cd47b8a5e0001349aff","order_by":null,"commision_info":null,"confirmed_delivery_date":null}'''
    StandardTriggerEventAction action = Mock(StandardTriggerEventAction)

    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
    IUIEvent event = Mock(IUIEvent)
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    void setup() {
        action.serviceFacade = serviceFacade
        action.getObjectDescribe() >>  new ObjectDescribeDocument(JSON.parseObject(describeJson)).toObjectDescribe()
        action.getDetailDescribe() >> ['object_7wQM3__c': new ObjectDescribeDocument(JSON.parseObject(detailDescribeJson)).toObjectDescribe()]
        supplementProcessor = new SupplementProcessor(action)
        supplementProcessor.requestContext = requestContext
        supplementProcessor.serviceFacade = serviceFacade
        supplementProcessor.infraServiceFacade = infraServiceFacade
        supplementProcessor.event = event
        supplementProcessor.objectDescribe = new ObjectDescribeDocument(JSON.parseObject(describeJson)).toObjectDescribe()

    }

    def 'test invoke'() {
        given:
        def objectData = new ObjectDataDocument(JSON.parseObject(dataJson)).toObjectData()
        def processRequest = UIEventProcess.ProcessRequest.builder()
                .masterData(objectData)
                .masterWithOnlyChangedFields(new ObjectDataDocument(["field__c": "value","field_0us5a__c":"id22","field_333w2__c":"test","import_dep__c":["1000"]
                ,"field_8C1cg__c": "248","owner":["1000"],"external_person__c":["200088"]]).toObjectData())
                .doCalculate(true)
                .detailDataMap(['detail__c':[]])
                .detailWithOnlyChangedFields([:])
                .build()
        when:
        supplementProcessor.invoke(processRequest, Mock(ProcessorContext))
        then:
        noExceptionThrown()
        supplementProcessor.masterEmployee.contains("owner")
        supplementProcessor.masterEmployee.contains("external_person__c")
    }
}