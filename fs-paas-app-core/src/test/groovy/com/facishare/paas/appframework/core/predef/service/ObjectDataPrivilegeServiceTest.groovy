package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.metadata.TeamMember
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class ObjectDataPrivilegeServiceTest extends Specification {

    ObjectDataPrivilegeService objectDataPrivilegeService

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        objectDataPrivilegeService = new ObjectDataPrivilegeService()
    }

    def "测试getOutTenantIdsFromTeamMembers方法 - 输入为null"() {
        when:
        def result = objectDataPrivilegeService.getOutTenantIdsFromTeamMembers(null)

        then:
        result == []
    }

    def "测试getOutTenantIdsFromTeamMembers方法 - 输入为空列表"() {
        given:
        def teamMembers = []

        when:
        def result = objectDataPrivilegeService.getOutTenantIdsFromTeamMembers(teamMembers)

        then:
        result == []
    }

    def "测试getOutTenantIdsFromTeamMembers方法 - 没有外部团队成员"() {
        given:
        def teamMembers = [
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.EMPLOYEE.getValue(), outTenantId: null),
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.DEPARTMENT.getValue(), outTenantId: null)
        ]

        when:
        def result = Whitebox.invokeMethod(objectDataPrivilegeService, "getOutTenantIdsFromTeamMembers", teamMembers)

        then:
        result == []
    }

    def "测试getOutTenantIdsFromTeamMembers方法 - 有外部团队成员"() {
        given:
        def teamMembers = [
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.EMPLOYEE.getValue(), outTenantId: "123", sourceType: "2"),
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.EMPLOYEE.getValue(), outTenantId: "1234"),
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.INTERCONNECT_DEPARTMENT.getValue(), outTenantId: "456", sourceType: "2"),
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.DEPARTMENT.getValue(), outTenantId: null),
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.EMPLOYEE.getValue(), outTenantId: "123", sourceType: "2"), // 重复ID
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.EMPLOYEE.getValue(), outTenantId: "abc", sourceType: "2") // 非数字ID
        ]

        when:
        def result = objectDataPrivilegeService.getOutTenantIdsFromTeamMembers(teamMembers)

        then:
        result.size() == 2
        result.containsAll(["123", "456"])
    }

    def "测试getOutTenantIdsFromTeamMembers方法 - 包含null成员"() {
        given:
        def teamMembers = [
                null,
                new TeamMemberInfoPoJo(teamMemberType: TeamMember.MemberType.EMPLOYEE.getValue(), outTenantId: "123", sourceType: "2")
        ]

        when:
        def result = objectDataPrivilegeService.getOutTenantIdsFromTeamMembers(teamMembers)

        then:
        result == ["123"]
    }
} 