package com.facishare.paas.appframework.core.model

import spock.lang.Specification

/**
 * Created by li<PERSON><PERSON><PERSON> on 2018/2/6.
 */
class SerializerManagerImplTest extends Specification {

    def "test serializer"() {
        given:
        SerializerManager serializerManager = new SerializerManagerImpl()

        JSONSerializer serializer = serializerManager.getSerializer(RequestContext.ContentType.FULL_JSON)

        when:

        TestBigDecimal testBigDecimal = new TestBigDecimal()
        testBigDecimal.setABigDecimal(BigDecimal.valueOf(12.9999999999999))
        testBigDecimal.setADouble(9.999)

        def json = serializer.encode(testBigDecimal)

        then:
        println json

        def kkk = serializer.decode(Map.class,json)

        println json


    }


}
