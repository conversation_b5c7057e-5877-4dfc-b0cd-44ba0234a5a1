package com.facishare.paas.appframework.core.predef.controller

import com.facishare.paas.appframework.core.model.ControllerContext
import com.facishare.paas.appframework.core.model.InfraServiceFacade
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.metadata.bizfield.BizFieldLogicService
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import spock.lang.Specification

//@PowerMockRunnerDelegate(Sputnik.class)
//@SuppressStaticInitializationFor(["example.com.AbbreviationProvinceUtil"])
class StandardPluginListControllerTest extends Specification {
    def objectApiName = 'object_6zPsk__c'
    def allRecord = ['1', '2', '3']

    BizFieldLogicService bizFieldLogicService = Mock(BizFieldLogicService)

    ServiceFacade serviceFacade = Mock(ServiceFacade)

    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)

    def arg = new StandardPluginListController.Arg(actionCode: 'Add', agentType: 'web')
    StandardPluginListController controller

    void setup() {
        def controllerContext = Mock(ControllerContext)
        controllerContext.getTenantId() >> '74255'
        controllerContext.getObjectApiName() >> objectApiName
        controllerContext.getModelName() >> 'PluginList'
        //new ControllerContext()     //(tenantId: '74255', objectApiName: objectApiName, method: )
        //controllerContext.tenantId = ''
        //Whitebox.setInternalState(AppFrameworkConfig, "removeDuplicatedValueGrayEi", [] as Set)
        controller = new StandardPluginListController()
        controller.controllerContext = controllerContext
        controller.arg = arg
        controller.bizFieldLogicService = bizFieldLogicService
        controller.serviceFacade = serviceFacade
        controller.infraServiceFacade = infraServiceFacade
        def describe = new ObjectDescribe(
                api_name: objectApiName,
                fields: [record_type: [
                        describe_api_name: objectApiName,
                        type             : 'record_type',
                        options          : [
                                [
                                        "api_name": "1",
                                ],
                                [
                                        "api_name": "2",
                                ],
                                [
                                        "api_name": "3",
                                ]
                        ]
                ]
                ]
        )
        //describe.getFieldDescribe(*_) >> any()
        serviceFacade.findObjectWithoutCopy(*_) >> describe
        serviceFacade.findDetailDescribesCreateWithMaster(*_) >> []
        //infraServiceFacade.findPluginInstances(*-_) >> []
        bizFieldLogicService.getAllBizFields(*_) >> null

        //DomainPluginDefinition definition = new DomainPluginDefinition(
        //        apiName: 'pluginApiName',
        //        type: DomainPluginType.Domain,
        //        resources: [
        //                "":
        //
        //        ]
        //)


        //DomainPluginDefinitionHolder插桩
        //DomainPluginDefinitionHolder mockDefinition = GroovySpy(DomainPluginDefinitionHolder.class, global: true)
        //mockDefinition.getResources(*_) >> any()
        //mockDefinition.getPluginDefinition(*_) >> definition
    }

    def "DoServiceTest"() {
        expect:
        1 == 1
        /*
        given:
            infraServiceFacade.findPluginInstances(*_)  >> [new DomainPluginInstance(
                    pluginApiName: 'multi_level_order_sfa',
                    refObjectApiName: objectApiName,
                    excludeRecordType: excludeRecordType,
                    recordTypeList: recordTypeList)
            ]

        expect:
            controller.doService(arg).domains.get(0).getRecordTypeList().equals(resultRecordType)

        where:
            excludeRecordType   |   recordTypeList  ||   resultRecordType
            false               |   ['1', '2']      ||   ['1', '2']
            true                |   ['1', '2']      ||   ['3']
            true                |   []              ||   null
        */
    }
}