package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectMappingService
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Language
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class StandardConvertSaveActionTest extends Specification {
    StandardConvertSaveAction standardConvertSaveAction
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    ActionContext actionContext = Mock(ActionContext)
    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> [JSON.parseObject("{\"code\":\"zh-CN\",\"name\":\"简体中文\",\"displayName\":\"简体中文\",\"default\":true}", Language.class)]
    }
    def setup() {
        infraServiceFacade.getSpringBeanHolder() >> new SpringBeanHolder()
        standardConvertSaveAction = new StandardConvertSaveAction('serviceFacade': serviceFacade, 'actionContext': actionContext,
                'infraServiceFacade': infraServiceFacade)
    }

    def 'test doAct'() {
        given:
        def arg = new AbstractConvertAction.Arg()
        arg.setSourceIds(['123'])
        arg.setRuleApiName('rule__c')
        arg.setSourceApiName('source__c')
        arg.setSpecifiedDetails(true)
        def sourceDetail = new AbstractConvertAction.SourceDetail()
        sourceDetail.setMasterId("123")
        sourceDetail.setDetails(["source__c": ["d123"]])
        arg.setSpecifiedSourceDetails([sourceDetail])
        and:
        def describe = new ObjectDescribe()
        describe.setApiName('source__c')
        Whitebox.setInternalState(standardConvertSaveAction, 'sourceObjectDescribe', describe)
        Whitebox.setInternalState(standardConvertSaveAction, 'arg', arg)
        and:
        def mtConvertRule = new MtConvertRule()
        mtConvertRule.setApiName('master_rule__c')
        mtConvertRule.setSourceObjectDescribeApiName('master__c')
        mtConvertRule.setSceneType(1)
        mtConvertRule.setStrategy(1)
        def detailRule = new MtConvertRule()
        detailRule.setApiName('detail_rule__c')
        detailRule.setSourceObjectDescribeApiName('detail__c')
        List<MtConvertRule> rules = [mtConvertRule, detailRule]
        Whitebox.setInternalState(standardConvertSaveAction, 'convertRuleList', rules)
        Whitebox.setInternalState(standardConvertSaveAction, 'masterConvertRule', mtConvertRule)
        def master = new ObjectDescribe()
        master.setApiName('master__c')
        def detail = new ObjectDescribe()
        detail.setApiName('detail__c')
        def detailFieldDescribe = new MasterDetailFieldDescribe()
        detailFieldDescribe.setApiName('master_detail__c')
        detailFieldDescribe.setIsCreateWhenMasterCreate(true)
        detail.addFieldDescribe(detailFieldDescribe)
        Whitebox.setInternalState(standardConvertSaveAction, 'sourceDetailDescribes', [detail])
        when:
        actionContext.getUser() >> User.systemUser('74255')
        and:
        def mappingDataResult = ObjectMappingService.MappingDataResult.builder().build()
        infraServiceFacade.mappingDataByRuleConfig(*_) >> {
            mappingDataResult.setObjectData(new ObjectData([api_name: 'target__c']))
            return mappingDataResult
        }
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('master__c')
        infraServiceFacade.findConvertRuleByApiName(*_) >> [ruleInfo]
        serviceFacade.findObjectsWithoutCopy(*_) >> ['master__c': new ObjectDescribe()]
        serviceFacade.buildDetailSearchTemplateQuery(*_) >> new SearchTemplateQuery()
        serviceFacade.findObjectDataByIdsExcludeInvalid(*_) >> {
            [new ObjectData([api_name: 'detail__c'])]
        }
        def build = BaseObjectSaveAction.Result.builder().build()
        build.setObjectData(new ObjectDataDocument([api_name: 'master__c', "_id": "123"]))
        serviceFacade.triggerAction(*_) >> build
        Whitebox.setInternalState(AppFrameworkConfig, 'splitOrderMaxNumLimit', ['74255': 20])
        def data = new ObjectData([api_name: 'master__c', "_id": "123"])
        def detail1 = new ObjectData([api_name: 'detail__c', "_id": "d123"])
        Whitebox.setInternalState(standardConvertSaveAction, 'sourceObjectDataList', [data, detail1])
        def result = standardConvertSaveAction.doAct(arg)
        then:
        noExceptionThrown()
//        1 * serviceFacade.removeMaskFieldValue(*_)
        result.getMasterSlaveDataList().size() == 1
    }

    def 'test getFuncPrivilegeCodes'() {
        given:
        actionContext.getObjectApiName() >> objectApiName
        def mtConvertRule = new MtConvertRule()
        mtConvertRule.setApiName('master_rule__c')
        mtConvertRule.setSourceObjectDescribeApiName('master__c')
        mtConvertRule.setTargetObjectDescribeApiName(convertTargetApiName)
        mtConvertRule.setSceneType(1)
        mtConvertRule.setStrategy(1)
        Whitebox.setInternalState(standardConvertSaveAction, 'masterConvertRule', mtConvertRule)
        when:
        def codes = standardConvertSaveAction.getFuncPrivilegeCodes()
        then:
        result == codes
        where:
        objectApiName | convertTargetApiName || result
        'source__c'   | 'source__c'          || ['ReferenceCreate', 'Add']
        'source__c'   | 'target__c'          || ['Transform', 'Add']
    }
}
