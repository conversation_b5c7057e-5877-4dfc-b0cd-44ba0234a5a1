package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.util.ObjectAction
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.service.dto.log.BatchQueryObjectActions
import com.facishare.paas.appframework.core.predef.service.dto.log.GetLogModuleGroup
import com.facishare.paas.appframework.core.predef.service.dto.log.GetTenantLogInterval
import com.facishare.paas.appframework.log.dto.LogAnalysis
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Created by fengjy in 2019/11/1 15:05
 */

class ObjectLogServiceTest extends Specification {

    ObjectLogService objectLogService
    String tenantId = "74255"
    ServiceContext context

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(User.systemUser(tenantId))
                .build()
        RequestContextManager.setContext(requestContext)
        objectLogService = new ObjectLogService()
        context = Mock(ServiceContext)
    }

    def "test getLogModuleGroup"() {
        given:
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogModuleGroup")
        GetLogModuleGroup.Arg arg = new GetLogModuleGroup.Arg()
        when:
        objectLogService.getLogModuleGroup(arg, context)
        then:
        1 == 1
    }

    def "test updateLogAnalysis"() {
        setup()
        given:
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "updateLogAnalysis")
        List<LogAnalysis.OperationLog> operationLogList = Lists.newArrayList()
        LogAnalysis.OperationLog operationLog = new LogAnalysis.OperationLog()
        operationLog.setDescribeApiName("AccountObj")
        operationLog.setOperation(Lists.newArrayList("1", "2"))
        operationLogList.add(operationLog)
        LogAnalysis.Arg arg = new LogAnalysis.Arg()
        arg.setLoginLog(true)
        arg.setOperationLog(true)
        arg.setOperationLogArgs(operationLogList)
        when:
        objectLogService.updateLogAnalysis(arg, context)
        then:
        1 == 1
    }

    def "test findLogAnalysis"() {
        setup()
        given:
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findLogAnalysis")
        LogAnalysis.Arg arg = new LogAnalysis.Arg()
        when:
        objectLogService.findLogAnalysis(arg, context)
        then:
        1 == 1
    }

    def "test findOperationLogRelationship"() {
        setup()
        given:
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findOperationLogRelationship")
        LogAnalysis.Arg arg = new LogAnalysis.Arg()
        when:
        objectLogService.findOperationLogRelationship(arg, context)
        then:
        1 == 1
    }

    def "test getTenantLogInterval"() {
        setup()
        given:
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getTenantLogInterval")
        GetTenantLogInterval.Arg arg = new GetTenantLogInterval.Arg()
        arg.setLogType("crm_log")
        when:
        objectLogService.getTenantLogInterval(arg, context)
        then:
        1 == 1
    }

    @Unroll
    def "test batchQueryObjectActions with #scenario"() {
        given: "准备测试参数"
        def arg = new BatchQueryObjectActions.Arg(actionCodes: actionCodes)

        when: "调用方法"
        def result = objectLogService.batchQueryObjectActions(arg, context)

        then: "验证结果"
        result.actionInfos.size() == expectedSize
        if (expectedSize > 0) {
            verifyAll(result.actionInfos[0]) {
                actionCode == expectedActionCode
                actionLabelKey == expectedActionLabelKey
                defaultActionLabel == expectedDefaultActionLabel
                buttonApiName == expectedButtonApiName
                actionLabel == expectedActionLabel
            }
        }

        where: "测试数据"
        scenario         | actionCodes       | expectedSize | expectedActionCode | expectedActionLabelKey     | expectedDefaultActionLabel | expectedButtonApiName   | expectedActionLabel
        "空列表"         | []                | 0            | null               | null                       | null                       | null                    | null
        "null列表"       | null              | 0            | null               | null                       | null                       | null                    | null
        "未知action"     | ["UnknownAction"] | 0            | null               | null                       | null                       | null                    | null
        "单个有效action" | ["Delete"]        | 1            | "Delete"           | "paas.udobj.action.delete" | "删除"                     | "Delete_button_default" | "删除"
        "多个action"     | ["Delete", "Add"] | 2            | "Delete"           | "paas.udobj.action.delete" | "删除"                     | "Delete_button_default" | "删除"
        "特殊字符action" | ["Delete__c"]     | 0            | null               | null                       | null                       | null                    | null
        "空字符串action" | [""]              | 0            | null               | null                       | null                       | null                    | null
        "中文action"     | ["删除"]          | 0            | null               | null                       | null                       | null                    | null
    }

    @Unroll
    def "test batchQueryObjectActions with multiple valid actions"() {
        given: "准备多个有效action codes"
        def arg = new BatchQueryObjectActions.Arg(actionCodes: ["Delete", "Add", "Edit"])

        when: "调用方法"
        def result = objectLogService.batchQueryObjectActions(arg, context)

        then: "验证结果列表大小"
        result.actionInfos.size() == 3

        and: "验证每个action info的内容"
        result.actionInfos.each { actionInfo ->
            def expectedAction = ObjectAction.of(actionInfo.actionCode)
            verifyAll(actionInfo) {
                actionCode == expectedAction.actionCode
                actionLabelKey == expectedAction.i18NKey
                defaultActionLabel == expectedAction.defaultActionLabel
                buttonApiName == expectedAction.buttonApiName
                actionLabel == expectedAction.actionLabel
            }
        }
    }
}
