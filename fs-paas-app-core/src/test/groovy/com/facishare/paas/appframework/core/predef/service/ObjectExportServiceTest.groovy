package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.service.dto.export.ExportDTO
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.DescribeLogicService
import spock.lang.Specification

class ObjectExportServiceTest extends Specification {

    LogService logService
    ObjectExportService objectExportService
    DescribeLogicService describeLogicService

    def tenant_id = '78057'
    ServiceContext context

    def setup() {
        RequestContext requestContext = RequestContext.builder().tenantId(tenant_id).user(User.systemUser(tenant_id)).build()
        RequestContextManager.setContext(requestContext)
        context = ContextManager.buildServiceContext("exportVerify", "")

        logService = Mock()
        describeLogicService = Mock()

        objectExportService = new ObjectExportService(
                logService: logService,
                describeLogicService: describeLogicService
        )
    }


    def "test exportApiVerify"() {
        given:
        def arg = ExportDTO.Arg.newInstance()
        arg.setSearchQuery("{\"operationTimeFrom\":1726122066611,\"operationTimeTo\":1726125666611,\"module\":\"UserDefineFunc\"}")
        arg.setExportBizType("auditLog")
        arg.setPageSize(0)
        when:
        def result = objectExportService.exportVerify(arg, context)
        then:
        println("exportApiVerify" + result)
    }

}
