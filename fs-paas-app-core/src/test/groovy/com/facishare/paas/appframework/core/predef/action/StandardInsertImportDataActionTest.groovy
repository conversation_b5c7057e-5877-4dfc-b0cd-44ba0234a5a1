package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.*
import com.fxiaoke.api.IdGenerator
import com.google.common.collect.HashBiMap
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification

class StandardInsertImportDataActionTest extends Specification {
    @Shared
    def describeJson = '''{"fields":{"field_m0KDi__c":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"description":"","is_unique":false,"type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1568702145131,"length":12,"default_value":"","label":"数字","field_num":7,"api_name":"field_m0KDi__c","_id":"5d807ec1a5083d69323353b4","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"help_text":"","status":"new"},"field_o10qI__c":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"is_index":true,"is_active":true,"create_time":1573699472718,"is_unique":false,"default_value":"","label":"日期","time_zone":"GMT+8","type":"date","field_num":19,"default_to_zero":false,"is_required":false,"api_name":"field_o10qI__c","define_type":"custom","date_format":"yyyy-MM-dd","_id":"5dccbf90a5083d9644cd13e4","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"l_4","help_text":"","status":"new"},"life_status":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304572,"description":"","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective","config":{"edit":0,"enable":0,"remove":0}},{"label":"审核中","value":"under_review","config":{"edit":0,"enable":0,"remove":0}},{"label":"正常","value":"normal","config":{"edit":0,"enable":0,"remove":0}},{"label":"变更中","value":"in_change","config":{"edit":0,"enable":0,"remove":0}},{"label":"作废","value":"invalid","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a07b8a5083dd185da53ac","is_index_field":false,"is_single":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"is_readonly":0,"is_required":0,"api_name":0,"options":0,"is_unique":0,"default_value":0,"label":0,"help_text":0}},"index_name":"s_3","help_text":"","status":"new"},"life_status_before_invalid":{"describe_api_name":"object_z87LW__c","is_index":false,"is_active":true,"create_time":1571192856477,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5da68018a5083d686f162409","is_index_field":false,"is_single":false,"index_name":"t_1","max_length":256,"status":"new"},"lock_rule":{"describe_api_name":"object_z87LW__c","is_index":false,"is_active":true,"create_time":1571192856477,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5da68018a5083d686f162406","is_index_field":false,"is_single":false,"index_name":"s_1","status":"new"},"lock_status":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304559,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0","config":{"edit":0,"enable":0,"remove":0}},{"label":"锁定","value":"1","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a07b8a5083dd185da53ab","is_index_field":false,"is_single":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"is_readonly":0,"is_required":0,"api_name":0,"options":0,"is_unique":0,"default_value":0,"label":0,"help_text":0}},"index_name":"s_2","help_text":"","status":"new"},"lock_user":{"describe_api_name":"object_z87LW__c","is_index":false,"is_active":true,"create_time":1571192856477,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","field_num":5,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5da68018a5083d686f162407","is_index_field":false,"is_single":true,"index_name":"a_1","status":"new"},"name":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"pattern":"","description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1566181304604,"default_value":"","label":"主属性","api_name":"name","_id":"5d5a07b8a5083dd185da53ae","is_index_field":false,"config":{"add":0,"edit":1,"enable":0,"display":1,"remove":0,"attrs":{"api_name":1,"is_unique":1,"label":1,"type":1,"help_text":1}},"help_text":"","status":"new"},"owner":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304569,"description":"","is_unique":false,"label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d5a07b8a5083dd185da53a9","is_index_field":false,"is_single":true,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"index_name":"owner","help_text":"","status":"new"},"owner_department":{"describe_api_name":"object_z87LW__c","default_is_expression":false,"pattern":"","description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1566181304570,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"5d5a07b8a5083dd185da53af","is_index_field":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"help_text":"","status":"new"},"record_type":{"describe_api_name":"object_z87LW__c","is_index":true,"is_active":true,"create_time":1566181304571,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型","config":{"edit":1,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a07b8a5083dd185da53b1","is_index_field":false,"is_single":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"index_name":"r_type","help_text":"","status":"released"},"relevant_team":{"describe_api_name":"object_z87LW__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1571192856589,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5da68018a5083d686f162408","is_index_field":false,"is_single":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":1566181304573},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":1566181304573},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":1566181304573},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":1566181304573,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":1566181304573,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":1566181304573},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":1566181304573},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":1566181304573},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":1566181304573},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":1566181304573},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":1566181304573},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":1566181304573,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":1566181304573},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":1566181304573},"order_by":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"order_by","api_name":"order_by","description":"order_by","status":"released","index_name":"o_by","create_time":1566181304573}},"actions":{},"index_version":1,"_id":"5d5a07b8a5083dd185da53a8","tenant_id":"78057","is_udef":true,"api_name":"object_z87LW__c","created_by":"1000","last_modified_by":"1000","display_name":"yuan","package":"CRM","is_active":true,"icon_path":"A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png","version":36,"release_version":"6.4","define_type":"custom","is_deleted":false,"config":{"button":{"add":1},"layout":{"add":1,"assign":1},"layout_rule":{"add":1},"edit":1,"cascade":{"add":1},"rule":{"add":1},"fields":{"add":1},"record_type":{"add":1,"assign":1}},"last_modified_time":1574059100620,"create_time":1566181304573,"icon_index":0,"description":""}'''
    @Shared
    def tenantId = "78057"
    @Shared
    def userId = "1000"
    @Shared
    def apiName = "object_z87LW__c"
    @Shared
    def actionCode = "InsertImport"
    @Shared
    def outTenantId = "200074255"
    @Shared
    def outUserId = "100018916"
    @Shared
    def user = User.builder().tenantId(tenantId).userId(userId).outTenantId(outTenantId).outUserId(outUserId).build()
    @Shared
    def requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
    @Shared
    def actionContext = new ActionContext(requestContext, apiName, actionCode)
    @Shared
    def sourceDataList = createDataDocumentList()
    @Shared
    def dataJson1 = '''{"tenant_id":"78057","data_own_organization":["1012"],"mc_exchange_rate":"1.000000","is_deleted":false,"object_describe_api_name":"object_2nD0p__c","owner_department_id":"1019","owner_department":"tz-小关财务","relevant_team__r":"admin01_obj0509","mc_functional_currency":"CNY","field_YdCL4__c":"主不报错1全额卫栖梧","owner":["1000"],"package":"CRM","last_modified_time":1705562957410,"create_time":1705043836088,"last_modified_by":["1000"],"mc_currency":"CNY","version":"2","created_by":["1000"],"record_type":"default__c","relevant_team":[{"teamMemberEmployee":["1000"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"","sourceType":"","teamMemberType":"0","teamMemberDeptCascade":"0"}],"name":"1","mc_exchange_rate_version":"1607586176212"}'''
    @Shared
    def dataJson2 = '''{"tenant_id":"78057","data_own_organization":["1012"],"mc_exchange_rate":"1.000000","is_deleted":false,"object_describe_api_name":"object_2nD0p__c","owner_department_id":"1019","owner_department":"tz-小关财务","relevant_team__r":"admin01_obj0509","mc_functional_currency":"CNY","field_YdCL4__c":"主不报错1全额卫栖梧","owner":["1000"],"package":"CRM","last_modified_time":1705562957410,"create_time":1705043836088,"last_modified_by":["1000"],"mc_currency":"CNY","version":"2","created_by":["1000"],"record_type":"default__c","relevant_team":[{"teamMemberEmployee":["1000"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"","sourceType":"","teamMemberType":"0","teamMemberDeptCascade":"0"}],"name":"2","mc_exchange_rate_version":"1607586176212"}'''
    @Shared
    def dataJson3 = '''{"tenant_id":"78057","data_own_organization":["1012"],"mc_exchange_rate":"1.000000","is_deleted":false,"object_describe_api_name":"object_2nD0p__c","owner_department_id":"1019","owner_department":"tz-小关财务","relevant_team__r":"admin01_obj0509","mc_functional_currency":"CNY","field_YdCL4__c":"主不报错1全额卫栖梧","owner":["1000"],"package":"CRM","last_modified_time":1705562957410,"create_time":1705043836088,"last_modified_by":["1000"],"mc_currency":"CNY","version":"2","created_by":["1000"],"record_type":"default__c","relevant_team":[{"teamMemberEmployee":["1000"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"","sourceType":"","teamMemberType":"0","teamMemberDeptCascade":"0"}],"name":"3","mc_exchange_rate_version":"1607586176212"}'''

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    SpringBeanHolder springBeanHolder
    IObjectDescribe objectDescribe
    IUniqueRule uniqueRule
    OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO
    ImportLogMessage.ImportMessageBuilder importLogMessageBuilder

    def setup() {
        objectDescribe = createObjectDescribe()
        serviceFacade = Mock(ServiceFacade)
        infraServiceFacade = Mock(InfraServiceFacade)
        springBeanHolder = Mock(SpringBeanHolder)
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        uniqueRule = Mock(IUniqueRule)
        optionalFeaturesSwitchDTO = Mock(OptionalFeaturesSwitchDTO)
        importLogMessageBuilder = Mock(ImportLogMessage.ImportMessageBuilder)
    }


    def "test before"() {
        given:
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                tenantId: tenantId,
                userId: userId,
                apiName: apiName,
                importType: 0,
                matchingType: matchType,
                isEmptyValueToUpdate: false,
                rows: sourceDataList)
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg)
        when:
        objectDescribe.isActive() >> true
        serviceFacade.findObject(_, _) >> objectDescribe
        infraServiceFacade.findBySwitchCache(_, _, _, _, _) >> Optional.of(uniqueRule)
        infraServiceFacade.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled() >> true
        infraServiceFacade.findEnableTeamRoleInfosWithoutOwner(_, _) >> []
        serviceFacade.getEnableDuplicateSearchRuleList(_, IDuplicatedSearch.Type.NEW, _, false, DuplicateSearchOrderByType.ORDER_BY_SORT) >> []
        serviceFacade.findDuplicatedSearchByApiNameAndType(_, _, IDuplicatedSearch.Type.NEW, false) >> []
        infraServiceFacade.generateTeamMemberField(actionContext.getUser(), objectDescribe.getApiName()) >> []
        standardInsertImportDataAction.before(arg)
        then:
        noExceptionThrown()
        where:
        matchType << [1, 2]
    }

    def "test doAct"() {
        given:
        List<BaseImportDataAction.ImportData> importDataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData();
        importData.setData(new ObjectData())
        importData.setRowNo(1)
        importDataList.add(importData)
        List<BaseImportAction.ImportError> importErrorList = Lists.newArrayList()
        BaseImportAction.ImportError importError = new BaseImportAction.ImportError()
        importErrorList.add(importError)
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                tenantId: tenantId,
                userId: userId,
                apiName: apiName,
                importType: 0,
                matchingType: matchType,
                isEmptyValueToUpdate: false,
                rows: sourceDataList)
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                importLogMessageBuilder: importLogMessageBuilder,
                dataList: importDataList,
                allErrorList: importErrorList,
                arg: arg)
        when:
        objectDescribe.isActive() >> true
        serviceFacade.findObject(_, _) >> objectDescribe
        infraServiceFacade.findBySwitchCache(_, _, _, _, _) >> Optional.of(uniqueRule)
        infraServiceFacade.findOptionalFeaturesSwitch(_, _) >> optionalFeaturesSwitchDTO
        optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled() >> true
        infraServiceFacade.findEnableTeamRoleInfosWithoutOwner(_, _) >> []
        serviceFacade.getEnableDuplicateSearchRuleList(_, IDuplicatedSearch.Type.NEW, _, false, DuplicateSearchOrderByType.ORDER_BY_SORT) >> []
        serviceFacade.findDuplicatedSearchByApiNameAndType(_, _, IDuplicatedSearch.Type.NEW, false) >> []
        serviceFacade.fillDataOwnDeptAndOrgByOutUser(_, _, _) >> fillData
        infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe) >> ObjectDescribeExt.of(objectDescribe).getFieldDescribes()
        standardInsertImportDataAction.doAct(arg)
        then:
        noExceptionThrown()
        where:
        matchType | fillData
        1         | true
        1         | false
        2         | true
        2         | false
    }


    def "test after"() {
        given:
        List<IObjectData> actualList = Lists.newArrayList();
        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        actualList.add(data)
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isApprovalFlowEnabled: true,
                isWorkFlowEnabled: true)
        StandardInsertImportDataAction standardInsertImportDataAction = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                actualList: actualList,
                arg: arg)
        BaseImportAction.Result result = new BaseImportAction.Result()
        when:
        standardInsertImportDataAction.after(arg, result)
        then:
        noExceptionThrown()
    }

    def "test customDefaultValue"() {
        given:
        List<IFieldDescribe> fieldDescribeList = []
        SignInFieldDescribe signInFieldDescribe = new SignInFieldDescribe()
        signInFieldDescribe.setApiName("field_sign__c")
        fieldDescribeList.add(signInFieldDescribe)
        PaymentFieldDescribe paymentFieldDescribe = new PaymentFieldDescribe()
        paymentFieldDescribe.setApiName("field_pay__c")
        fieldDescribeList.add(paymentFieldDescribe)
        objectDescribe.setFieldDescribes(fieldDescribeList)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isApprovalFlowEnabled: isApprovalFlowEnabled,
                isWorkFlowEnabled: isWorkFlowEnabled)
        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg)
        List<IObjectData> dataList = createDataList()
        when:
        action.customDefaultValue(dataList)
        then:
        noExceptionThrown()
        where:
        isApprovalFlowEnabled | isWorkFlowEnabled
        true                  | true
        false                 | false

    }


    def "test fillOutResource"() {
        given:
        def actionContext = new ActionContext(RequestContext.builder().tenantId(tenantId).user(user).appId(appId).build(), apiName, actionCode)
        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe))
        List<IObjectData> dataList = createDataList()
        when:
        Whitebox.setInternalState(AppIdMapping, "appIdMapping", HashBiMap.create(["prm": "prm", "fxt": "fxt"]))
        action.fillOutResource(dataList)
        then:
        noExceptionThrown()
        where:
        appId | _
        "prm" | _
        "fxt" | _
    }

    def "test importData"() {
        given:
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isWorkFlowEnabled: true)
        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg)
        List<IObjectData> dataList = createDataList()
        when:
        RequestContextManager.setContext(requestContext)
        action.importData(dataList)
        then:
        noExceptionThrown()
    }


    def "test fillDataOutOwnDeptAndOrganization"() {
        given:
        List<BaseImportDataAction.ImportData> dataList = createImportDataList()
        List<BaseImportAction.ImportError> allErrorList = Lists.newArrayList()
        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                isWorkFlowEnabled: true)
        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                serviceFacade: serviceFacade,
                objectDescribe: objectDescribe,
                infraServiceFacade: infraServiceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                dataList: dataList,
                allErrorList: allErrorList,
                arg: arg)
        when:
        action.fillDataOutOwnDeptAndOrganization()
        then:
        noExceptionThrown()
    }

    def "test customValidate"() {
        given:
        IObjectDescribe objectDescribe = Spy(ObjectDescribe)
        objectDescribe.setApiName(apiName)

        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList()
        IFieldDescribe currencyField = new SelectOneFieldDescribe()
        currencyField.setApiName("mc_currency")
        fieldDescribeList.add(currencyField)

        IFieldDescribe exchangeField = new NumberFieldDescribe()
        exchangeField.setApiName("mc_exchange_rate")
        fieldDescribeList.add(exchangeField)

        objectDescribe.setFieldDescribes(fieldDescribeList)


        List<BaseImportDataAction.ImportData> dataList = Lists.newArrayList()
        BaseImportDataAction.ImportData importData = new BaseImportDataAction.ImportData()
        importData.setRowNo(1)
        IObjectData data = new ObjectData()
        data.setId(IdGenerator.get())
        data.set("mc_currency", "CNY")
        data.set("mc_exchange_rate", 1.0)
        data.setOwner(Lists.newArrayList("1000"))
        importData.setData(data)
        dataList.add(importData)

        BaseImportAction.Arg arg = new BaseImportAction.Arg(
                matchingType: matchingType,
                supportFieldMapping: supportFieldMapping,
                isEmptyValueToUpdate: isEmptyValueToUpdate
        )

        StandardInsertImportDataAction action = new StandardInsertImportDataAction(
                actionContext: actionContext,
                objectDescribe: objectDescribe,
                serviceFacade: serviceFacade,
                objectDescribeExt: ObjectDescribeExt.of(objectDescribe),
                arg: arg,
        )

        when:
        action.customValidate(dataList)
        then:
        noExceptionThrown()
        where:
        matchingType | supportFieldMapping | isEmptyValueToUpdate
        1            | true                | true
        1            | false               | false
        2            | true                | true
        2            | false               | false
    }

    def createImportDataList() {
        BaseImportDataAction.ImportData data1 = new BaseImportDataAction.ImportData()
        data1.setRowNo(2)
        data1.setData(createObjectData(dataJson1))

        BaseImportDataAction.ImportData data2 = new BaseImportDataAction.ImportData()
        data2.setRowNo(2)
        data2.setData(createObjectData(dataJson2))

        BaseImportDataAction.ImportData data3 = new BaseImportDataAction.ImportData()
        data3.setRowNo(2)
        data3.setData(createObjectData(dataJson3))

        List<BaseImportDataAction.ImportData> result = Lists.newArrayList()
        result.add(data1)
        result.add(data2)
        result.add(data3)
        return result

    }

    def createDataList() {
        List<IObjectData> dataList = Lists.newArrayList()
        dataList.add(createObjectData(dataJson1))
        dataList.add(createObjectData(dataJson2))
        dataList.add(createObjectData(dataJson3))
        return dataList
    }

    def createDataDocumentList() {
        List<ObjectDataDocument> result = Lists.newArrayList()
        for (int i = 2; i < 5; i++) {
            ObjectDataDocument document = ObjectDataDocument.of(
                    ["rowNo"       : i, "负责人（必填）": "admin01", "业务类型（必填）": "预设业务类型", "日期": "2019-10-16 00:00:00",
                     "主属性（必填）": "导入" + i, "数字": 100, "归属部门": ""])
            result.add(document)
        }
        return result
    }


    def createObjectDescribe() {
        IObjectDescribe describe = new ObjectDescribe(JSON.parseObject(describeJson, Map.class))
        return describe
    }

    def createObjectData(String json) {
        IObjectData data = new ObjectData(JSON.parseObject(json, Map.class))
        return data
    }
}
