package com.facishare.paas.appframework.core.predef.controller

import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.core.model.ControllerContext
import com.facishare.paas.appframework.core.predef.listener.prm.PrmNewDetailControllerListener
import com.google.common.collect.HashBiMap
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class StandardWebDetailControllerTest extends Specification{

    def setup() {
    }


    def "getControllerListenerClassList returns correct list when AppId is PRM"() {
        given:
        def controller = new StandardWebDetailController()
        ControllerContext controllerContext = Mock()
        Whitebox.setInternalState(controller, "controllerContext", controllerContext)
        AppIdMapping.appIdMapping = HashBiMap.create()
        AppIdMapping.appIdMapping.put("prm","1234")

        when:
        controllerContext.getAppId() >> "1234"
        def result = controller.getControllerListenerClassList()

        then:
        result.size() == 1
        result[0] == PrmNewDetailControllerListener
    }

}
