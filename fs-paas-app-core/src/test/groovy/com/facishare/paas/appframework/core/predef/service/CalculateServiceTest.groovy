package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.graph.ValueGraph
import com.facishare.paas.appframework.common.graph.ValueGraphBuilder
import com.facishare.paas.appframework.common.service.dto.QueryJob
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.common.util.UdobjGrayUtil
import com.facishare.paas.appframework.core.BaseTest
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.uievent.UIEventProcess
import com.facishare.paas.appframework.core.predef.facade.CalculateServiceFacade
import com.facishare.paas.appframework.core.predef.service.calculate.CalculateWithUIActionCallbackContainer
import com.facishare.paas.appframework.core.predef.service.dto.calculate.*
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.metadata.*
import com.facishare.paas.appframework.metadata.cache.RedissonService
import com.facishare.paas.appframework.metadata.dto.CheckFieldsForCalc
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.appframework.metadata.expression.CalculateDataContext
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService
import com.facishare.paas.appframework.metadata.expression.SimpleExpression
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService
import com.facishare.paas.appframework.metadata.relation.*
import com.facishare.paas.expression.exception.ExpressionCompileException
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IRecordTypeOption
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.RecordTypeOption
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import com.fxiaoke.release.GrayRule
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox

/**
 * Created by zhouwr on 2019/9/23
 */
class CalculateServiceTest extends BaseTest {

    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    ExpressionCalculateLogicService expressionCalculateLogicService = Mock(ExpressionCalculateLogicService)
    MetaDataService metaDataService = Mock(MetaDataService)
    QuoteValueService quoteValueService = Mock(QuoteValueService)
    FsGrayReleaseBiz fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
    FieldRelationGraphService fieldRelationGraphService = Mock(FieldRelationGraphService)
    FieldRelationCalculateService fieldRelationCalculateService = Mock(FieldRelationCalculateService)
    RedissonService redissonService = Mock(RedissonService)
    JobScheduleService jobScheduleService = Mock(JobScheduleService)
    RecordTypeLogicService recordTypeLogicService = Mock(RecordTypeLogicService)
    MaskFieldLogicService maskFieldLogicService = Mock(MaskFieldLogicService)
    CalculateServiceFacade calculateServiceFacade = Mock(CalculateServiceFacade)
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)

    CalculateService calculateService = new CalculateService()

    ServiceContext context

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        context = new ServiceContext(requestContext, "calculate", null)

        calculateService.describeLogicService = describeLogicService
        calculateService.expressionCalculateLogicService = expressionCalculateLogicService
        calculateService.metaDataService = metaDataService
        calculateService.fieldRelationGraphService = fieldRelationGraphService
        calculateService.fieldRelationCalculateService = fieldRelationCalculateService
        calculateService.redissonService = redissonService
        calculateService.quoteValueService = quoteValueService
        calculateService.jobScheduleService = jobScheduleService
        calculateService.recordTypeLogicService = recordTypeLogicService
        calculateService.maskFieldLogicService = maskFieldLogicService
        calculateService.calculateServiceFacade = calculateServiceFacade
        calculateService.serviceFacade = serviceFacade
        calculateService.infraServiceFacade = infraServiceFacade

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
    }

    def "test expressionCheck"() {
        given:
        ExpressionCheck.Arg arg = new ExpressionCheck.Arg(json_data: "{}")

        when:
        ExpressionCheck.Result result = calculateService.expressionCheck(arg, context)

        then:
        1 * expressionCalculateLogicService.compileCheck(_, _)
        result.code == 0
    }

    def "test expressionCheck exception-errorReminder-true"() {
        given:
        ExpressionCheck.Arg arg = new ExpressionCheck.Arg(json_data: "{}", errorReminder: true)

        when:
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.EXPRESSION_CHECK_DETAIL_REMIND_GRAY, tenantId) >> true
        expressionCalculateLogicService.compileCheck(_, _) >> { throw new ExpressionCompileException("-error") }
        ExpressionCheck.Result result = calculateService.expressionCheck(arg, context)

        then:
        result.code == 400
        result.value == "-error"
    }

    def "test expressionCheck exception"() {
        given:
        ExpressionCheck.Arg arg = new ExpressionCheck.Arg(json_data: "{}")

        when:
        expressionCalculateLogicService.compileCheck(_, _) >> { throw compileException }
        calculateService.expressionCheck(arg, context)

        then:
        thrown(expectedException)

        where:
        compileException                                        || expectedException
        new ObjectDefNotFoundError("object not found")          || ValidateException
        new ExpressionCompileException("expression is invalid") || MetaDataBusinessException
        new StackOverflowError()                                || ValidateException
    }

    def "test expressionDebug"() {
        given:
        ExpressionDebug.Arg arg = new ExpressionDebug.Arg()

        when:
        expressionCalculateLogicService.expressionDebug(_, _, _) >> debugResult
        ExpressionDebug.Result result = calculateService.expressionDebug(arg, context)

        then:
        result.success == expectedSuccess
        result.result == expectedResult
        result.errorMessage == expectedErrorMessage

        where:
        debugResult    | expectedSuccess | expectedResult | expectedErrorMessage
        'debugResult1' | true            | 'debugResult1' | null
    }

    def "test expressionDebug exception"() {
        given:
        ExpressionDebug.Arg arg = new ExpressionDebug.Arg()

        when:
        expressionCalculateLogicService.expressionDebug(_, _, _) >> { throw new RuntimeException("error") }
        ExpressionDebug.Result result = calculateService.expressionDebug(arg, context)

        then:
        !result.success
        result.result == null
        result.errorMessage == "error"
    }

    def "test expressionCalculate"() {
        given:
        ExpressionCalculate.Arg arg = new ExpressionCalculate.Arg(objectDescribeApiName: "testApiName", object_data: "{}")

        when:
        ExpressionCalculate.Result result = calculateService.expressionCalculate(arg, context)

        then:
        result != null
    }

    def "test expressionCalculate exception"() {
        given:
        ExpressionCalculate.Arg arg = new ExpressionCalculate.Arg(objectDescribeApiName: objectApiName, object_data: "{}")

        when:
        calculateService.expressionCalculate(arg, context)

        then:
        thrown(expectedException)

        where:
        objectApiName || expectedException
        null          || ValidateException
        ""            || ValidateException
    }

    def "test batchExpressionCalculate"() {
        given:
        BatchExpressionCalculate.Arg arg = new BatchExpressionCalculate.Arg(objectDescribeApiName: "testApiName", object_data: "{}")

        when:
        BatchExpressionCalculate.Result result = calculateService.batchExpressionCalculate(arg, context)

        then:
        result != null
    }

    def "test batchExpressionCalculate exception"() {
        given:
        BatchExpressionCalculate.Arg arg = new BatchExpressionCalculate.Arg(objectDescribeApiName: objectApiName, object_data: "{}")

        when:
        calculateService.batchExpressionCalculate(arg, context)

        then:
        thrown(expectedException)

        where:
        objectApiName || expectedException
        null          || ValidateException
        ""            || ValidateException
    }

    def "test batchDataExpressionCalculate"() {
        given:
        BatchDataExpressionCalculate.Arg arg = new BatchDataExpressionCalculate.Arg(calculate_data_list: calculate_data_list)

        when:
        BatchDataExpressionCalculate.Result result = calculateService.batchDataExpressionCalculate(arg, context)

        then:
        result != null
        result.value_list == value_list

        where:
        calculate_data_list                                                                 || value_list
        '''[{\"objectDescribeApiName\":\"testApiName\",\"objectDataList\":{}}]'''           || null
        '''[{\"objectDescribeApiName\":\"testApiName\",\"objectDataList\":{\"id1\":{}}}]''' || ["testApiName": ["id1": [:]]]
    }

    def "test calculateBatch with empty fields"() {
        given:
        CalculateBatchData.Arg arg = new CalculateBatchData.Arg(calculateFieldApiNames: calculateFieldApiNames, calculateFields: calculateFields)

        when:
        CalculateBatchData.Result result = calculateService.calculateBatch(arg, context)

        then:
        result != null
        result.calculateResult == calculateResult

        where:
        calculateFieldApiNames | calculateFields || calculateResult
        null                   | null            || [:]
        null                   | []              || [:]
        []                     | null            || [:]
        []                     | []              || [:]
    }

    def "test calculateBatch with calculateFields"() {
        given:
        CalculateBatchData.Arg arg = new CalculateBatchData.Arg(objectApiName: "testApiName", dataMap: ["id1": ObjectDataDocument.of([:])],
                modifiedDataIndexList: ["id1"], calculateFields: [CalculateRelation.RelateField.of("field1", 1, "F")])
        IObjectDescribe objectDescribe = new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["api_name": "field1", "type": "formula"]]])
        Map<String, IObjectDescribe> describeMap = ["testApiName": objectDescribe]
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build()
        when:
        describeLogicService.findObject(_, _) >> objectDescribe
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> FieldRelationGraph.of(graph, describeMap)
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> { arg.getDataMap().values().forEach { it.put("field1", "value1") } }
        CalculateBatchData.Result result = calculateService.calculateBatch(arg, context)

        then:
        result != null
        result.getCalculateResult().size() == 1
        result.getCalculateResult().get("id1").get("field1") == "value1"
    }

    def "test calculateBatch with calculateFieldNames"() {
        given:
        CalculateBatchData.Arg arg = new CalculateBatchData.Arg(objectApiName: "testApiName", dataMap: ["id1": ObjectDataDocument.of([:])],
                modifiedDataIndexList: ["id1"], calculateFieldApiNames: ["field1"])
        IObjectDescribe objectDescribe = new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["api_name": "field1", "type": "formula"]]])
        Map<String, IObjectDescribe> describeMap = ["testApiName": objectDescribe]
        ValueGraph<FieldNode, RelateEdge> graph = ValueGraphBuilder.directed().build()
        Map<String, List<CalculateRelation.RelateField>> calculateFields = ["testApiName": [CalculateRelation.RelateField.of("field1", 1, "F")]]

        when:
        describeLogicService.findObject(_, _) >> objectDescribe
        fieldRelationCalculateService.computeCalculateFields(_, _) >> CalculateFields.of(FieldRelationGraph.of(graph, describeMap), calculateFields)
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> { arg.getDataMap().values().forEach { it.put("field1", "value1") } }
        CalculateBatchData.Result result = calculateService.calculateBatch(arg, context)

        then:
        result != null
        result.getCalculateResult().size() == 1
        result.getCalculateResult().get("id1").get("field1") == "value1"
    }

    def "test batchCalculate with null arg"() {
        given:
        BatchCalculate.Arg arg = null
        when:
        calculateService.batchCalculate(arg, context)
        then:
        thrown(ValidateException)
    }

    def "test batchCalculate with empty params"() {
        given:
        BatchCalculate.Arg arg = new BatchCalculate.Arg(calculateFieldApiNames: calculateFieldApiNames, calculateFields: calculateFields)
        when:
        BatchCalculate.Result result = calculateService.batchCalculate(arg, context)
        then:
        result != null
        result.getCalculateResult() == expectedResult
        where:
        calculateFieldApiNames | calculateFields || expectedResult
        null                   | null            || [:]
        [:]                    | [:]             || [:]
        [:]                    | null            || [:]
        null                   | [:]             || [:]
    }

    def "test batchCalculate lock failed with seriesId"() {
        given:
        UdobjGrayUtil.GrayConfig grayConfig = JacksonUtils.fromJson('''{"functions":[{"functionName":"lock_by_series_id_in_calculate_and_ui_event_and_add_edit_request","blackRule":{"ei":"white:*"}}]}''', UdobjGrayUtil.GrayConfig.class)
        Whitebox.setInternalState(UdobjGrayUtil, "GRAY_CONFIG", grayConfig)
        BatchCalculate.Arg arg = new BatchCalculate.Arg(seriesId: "seriesId1", masterObjectApiName: "testApiName")
        when:
        redissonService.tryLockWithErrorMsg(_, _, _, _, _, _) >> { throw new ValidateException("error1") }
        calculateService.batchCalculate(arg, context)
        then:
        thrown(ValidateException)
    }

    def "test batchCalculate lock success with seriesId"() {
        given:
        UdobjGrayUtil.GrayConfig grayConfig = JacksonUtils.fromJson('''{"functions":[{"functionName":"lock_by_series_id_in_calculate_and_ui_event_and_add_edit_request","blackRule":{"ei":"white:*"}}]}''', UdobjGrayUtil.GrayConfig.class)
        Whitebox.setInternalState(UdobjGrayUtil, "GRAY_CONFIG", grayConfig)
        BatchCalculate.Arg arg = new BatchCalculate.Arg(seriesId: "seriesId1", masterObjectApiName: "testApiName", masterData: ObjectDataDocument.of([:]))
        when:
        BatchCalculate.Result result = calculateService.batchCalculate(arg, context)
        then:
        result != null
        result.calculateResult == [:]
    }

    def "test batchCalculate without seriesId"() {
        given:
        def masterApiName = "testApiName"
        BatchCalculate.Arg arg = new BatchCalculate.Arg(masterObjectApiName: masterApiName, masterData: ObjectDataDocument.of(masterData),
                detailDataMap: detailDataMap, modifiedObjectApiName: modifiedObjectApiName,
                modifiedDataIndexList: modifiedDataIndexList, calculateFields: calculateFields)
        IObjectDescribe masterDescribe = new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["api_name": "field1", "type": "text"], "field2": ["api_name": "field2", "type": "formula", "expression": "\$field1\$", "return_type": "text"], "field3": ["api_name": "field3", "type": "formula", "expression": "\$field2\$", "return_type": "text"]]])
        IObjectDescribe detailDescribe = new ObjectDescribe(["api_name": "detail1", "fields": ["md_field": ["api_name": "md_field", "type": "master_detail", "target_api_name": "testApiName"], "field1": ["api_name": "field1", "type": "text"], "field2": ["api_name": "field2", "type": "formula", "expression": "\$field1\$", "return_type": "text"], "field3": ["api_name": "field3", "type": "formula", "expression": "\$field2\$", "return_type": "text"]]])
        Map<String, IObjectDescribe> describeMap = ["testApiName": masterDescribe, "detail1": detailDescribe]
        FieldRelationGraph graph = FieldRelationGraphBuilder.builder()
                .graphLayers([FieldRelationGraphBuilder.GraphLayer.of(Lists.newArrayList(describeMap.values()))])
                .build()
                .getGraph()
        when:
        describeLogicService.findObjects(_, _) >> {
            return describeMap
        }
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> {
            return [detailDescribe]
        }
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            return graph
        }
        metaDataService.batchCalculateBySortFieldsWithDetailData(_, _, _, _, _) >> { args ->
            IObjectData masterData = args[1]
            Map<String, List<CalculateObjectData>> detailCalculateDataMap = args[2]
            def masterResult = ((Map) expectedResult.getOrDefault(masterApiName, Collections.emptyMap())).getOrDefault("0", Collections.emptyMap())
            ObjectDataExt.of(masterData).putAll(masterResult)
            detailCalculateDataMap.forEach { apiName, detailCalculateData ->
                detailCalculateData.forEach { it ->
                    def detailResult = ((Map) expectedResult.getOrDefault(apiName, Collections.emptyMap())).getOrDefault(it.getIndex(), Collections.emptyMap())
                    ObjectDataExt.of(it.getObjectData()).putAll(detailResult)
                }
            }
        }
        BatchCalculate.Result result = calculateService.batchCalculate(arg, context)
        then:
        result != null
        result.calculateResult == expectedResult

        where:
        masterData           | detailDataMap                                                            | modifiedObjectApiName | modifiedDataIndexList | calculateFields                                                                                                                                                                                                                                || expectedResult
        ["field1": "value1"] | ["detail1": ["d1_idx1": ObjectDataDocument.of(["field1": "d1_value1"])]] | "detail1"             | ["d1_idx1"]           | ["testApiName": [CalculateRelation.RelateField.of("field2", 1, "F"), CalculateRelation.RelateField.of("field3", 2, "F")], "detail1": [CalculateRelation.RelateField.of("field2", 1, "F"), CalculateRelation.RelateField.of("field3", 2, "F")]] || ["testApiName": ["0": ObjectDataDocument.of(["field2": "value1", "field3": "value1"])],
                                                                                                                                                                                                                                                                                                                                                                                                             "detail1"    : ["d1_idx1": ObjectDataDocument.of(["field2": "d1_value1", "field3": "d1_value1"])]]
    }

    def "test calculateWithExpression with empty objectApiName"() {
        given:
        CalculateWithExpression.Arg arg = new CalculateWithExpression.Arg(objectApiName: objectApiName)
        when:
        calculateService.calculateWithExpression(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName || expectedException
        null          || ValidateException
        ""            || ValidateException
    }

    def "test calculateWithExpression with empty expressionList"() {
        given:
        CalculateWithExpression.Arg arg = new CalculateWithExpression.Arg(objectApiName: "testApiName", expressionList: expressionList)
        when:
        CalculateWithExpression.Result result = calculateService.calculateWithExpression(arg, context)
        then:
        result != null
        result.calculateValues == calculateValues
        where:
        expressionList || calculateValues
        null           || [:]
        []             || [:]
    }

    def "test calculateWithExpression normal"() {
        given:
        CalculateWithExpression.Arg arg = new CalculateWithExpression.Arg(objectApiName: "testApiName", expressionList: [new SimpleExpression(id: "exp1")])
        when:
        describeLogicService.findObject(_, _) >> { new ObjectDescribe(["api_name": "testApiName"]) }
        expressionCalculateLogicService.calculateWithExpression(_, _, _, _) >> { ["exp1": "value1"] }
        CalculateWithExpression.Result result = calculateService.calculateWithExpression(arg, context)
        then:
        result != null
        result.calculateValues == ["exp1": "value1"]
    }

    def "test bulkDataCalculateWithExpression with empty objectApiName"() {
        given:
        BulkDataCalculateWithExpression.Arg arg = new BulkDataCalculateWithExpression.Arg(objectApiName: objectApiName)
        when:
        calculateService.bulkDataCalculateWithExpression(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName || expectedException
        null          || ValidateException
        ""            || ValidateException
    }

    def "test bulkDataCalculateWithExpression with empty expressionList"() {
        given:
        BulkDataCalculateWithExpression.Arg arg = new BulkDataCalculateWithExpression.Arg(objectApiName: "testApiName", expressionList: expressionList)
        when:
        BulkDataCalculateWithExpression.Result result = calculateService.bulkDataCalculateWithExpression(arg, context)
        then:
        result != null
        result.calcResult == calculateValues
        where:
        expressionList || calculateValues
        null           || [:]
        []             || [:]
    }

    def "test bulkDataCalculateWithExpression normal"() {
        given:
        BulkDataCalculateWithExpression.Arg arg = new BulkDataCalculateWithExpression.Arg(objectApiName: "testApiName",
                dataCtx: [new CalculateDataContext(dataId: "data1")],
                expressionList: [new SimpleExpression(id: "exp1")])
        when:
        expressionCalculateLogicService.bulkDataCalculateWithExpression(_, _, _) >> { ["data1": ["exp1": "value1"]] }
        BulkDataCalculateWithExpression.Result result = calculateService.bulkDataCalculateWithExpression(arg, context)
        then:
        result != null
        result.calcResult == ["data1": ["exp1": "value1"]]
    }

    def "test batchCalculateCountFields"() {
        given:
        BatchCalculateCountFields.Arg arg = new BatchCalculateCountFields.Arg(objectApiName: "testApiName",
                objectDataId: "data1",
                countFieldList: [new BatchCalculateCountFields.CountField(apiName: "field1")])
        when:
        metaDataService.calculateCountField(_, _, _, _) >> { ["field1": "value1"] }
        BatchCalculateCountFields.Result result = calculateService.batchCalculateCountFields(arg, context)
        then:
        result != null
        result.countFieldValues == ObjectDataDocument.of(["field1": "value1"])
    }

    def "test calculateAndUpdateFormulaFields with invalid param"() {
        given:
        CalculateAndUpdateFormulaFields.Arg arg = new CalculateAndUpdateFormulaFields.Arg(objectApiName: objectApiName,
                dataIds: dataIds, fieldApiNames: fieldApiNames)
        when:
        calculateService.calculateAndUpdateFormulaFields(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName | dataIds   | fieldApiNames || expectedException
        null          | null      | null          || ValidateException
        ""            | null      | null          || ValidateException
        "testApiName" | null      | null          || ValidateException
        "testApiName" | []        | null          || ValidateException
        "testApiName" | ["data1"] | null          || ValidateException
        "testApiName" | ["data1"] | []            || ValidateException
    }

    def "test calculateAndUpdateFormulaFields"() {
        given:
        CalculateAndUpdateFormulaFields.Arg arg = new CalculateAndUpdateFormulaFields.Arg(objectApiName: "testApiName",
                dataIds: ["data1"], fieldApiNames: ["field1"])
        RequestContext requestContext1 = RequestContext.builder()
                .tenantId(tenantId)
                .user(User.systemUser(tenantId))
                .requestSource(requestSource)
                .build()
        RequestContextManager.setContext(requestContext1)
        ServiceContext context1 = new ServiceContext(requestContext1, "calculate", "calculateAndUpdateFormulaFields");
        when:
        metaDataService.calculateAndUpdateFormulaFields(_, _, _, _, _) >> { ["data1": ["field1": "value1"]] }
        CalculateAndUpdateFormulaFields.Result result = calculateService.calculateAndUpdateFormulaFields(arg, context1)
        then:
        result != null
        result.result == expectedResult
        where:
        requestSource                     || expectedResult
        RequestContext.RequestSource.CEP  || null
        RequestContext.RequestSource.REST || ["data1": ["field1": "value1"]]
    }

    def "test calculateCountOrFormulaField with invalid param"() {
        given:
        CalculateCountOrFormulaField.Arg arg = new CalculateCountOrFormulaField.Arg(objectApiName: objectApiName,
                dataId: dataId, fieldApiName: fieldApiName)
        when:
        calculateService.calculateCountOrFormulaField(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName | dataId    | fieldApiName || expectedException
        null          | null      | null         || ValidateException
        ""            | null      | null         || ValidateException
        "testApiName" | null      | null         || ValidateException
        "testApiName" | ""        | null         || ValidateException
        "testApiName" | ["data1"] | null         || ValidateException
        "testApiName" | ["data1"] | ""           || ValidateException
    }

    def "test calculateCountOrFormulaField"() {
        given:
        CalculateCountOrFormulaField.Arg arg = new CalculateCountOrFormulaField.Arg(objectApiName: "testApiName",
                dataId: "data1", fieldApiName: "field1")
        when:
        describeLogicService.findObject(tenantId, "testApiName") >> { new ObjectDescribe(["api_name": "testApiName", "fields": ["field1": ["api_name": "field1", "type": fieldType]]]) }
        metaDataService.findObjectData(_, _, _) >> { new ObjectData() }
        quoteValueService.fillQuoteFieldValue(_, _, _, _, _, _, _, _) >> { args ->
            IObjectData data = (IObjectData) ((List) args[1]).get(0)
            data.set("field1", "value1")
            data.set("field1__v", "value1__v")
            data.set("field1__r", "value1__r")
        }
        metaDataService.calculateCountField(_, _, _, _) >> { args ->
            IObjectData data = (IObjectData) args[0]
            data.set("field1", "value1")
        }
        expressionCalculateLogicService.bulkCalculate(_, _, _) >> { args ->
            IObjectData data = (IObjectData) ((List) args[1]).get(0)
            data.set("field1", "value1")
        }
        CalculateCountOrFormulaField.Result result = calculateService.calculateCountOrFormulaField(arg, context)
        then:
        result != null
        result.result == expectedResult
        where:
        fieldType || expectedResult
        "quote"   || ObjectDataDocument.of(["field1": "value1", "field1__v": "value1__v", "field1__r": "value1__r"])
        "count"   || ObjectDataDocument.of(["field1": "value1"])
        "formula" || ObjectDataDocument.of(["field1": "value1"])
    }

    def "test submitCalculateJob with invalid param"() {
        given:
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg(calculateJobList: calculateJobList)
        when:
        calculateService.submitCalculateJob(arg, context)
        then:
        thrown(expectedException)
        where:
        calculateJobList                                                                          || expectedException
        null                                                                                      || ValidateException
        []                                                                                        || ValidateException
        [new SubmitCalculateJob.CalculateJob()]                                                   || ValidateException
        [new SubmitCalculateJob.CalculateJob(objectApiName: "testApiName")]                       || ValidateException
        [new SubmitCalculateJob.CalculateJob(objectApiName: "testApiName", fieldApiNameList: [])] || ValidateException
    }

    def "test submitCalculateJob"() {
        given:
        SubmitCalculateJob.Arg arg = new SubmitCalculateJob.Arg(manual: manual, lastModifiedTime: lastModifiedTime, calculateJobList: calculateJobList)
        IObjectDescribe describe1 = new ObjectDescribe(["api_name": "testApiName", "last_modified_time": 9, "fields": ["field1": ["api_name": "field1", "type": "formula", "is_index": true], "field2": ["api_name": "field2", "type": "count", "is_index": true], "field3": ["api_name": "field3", "type": "quote", "is_index": true]]])
        IObjectDescribe describe2 = new ObjectDescribe(["api_name": "testApiName1", "is_active": false])
        IObjectDescribe describe3 = new ObjectDescribe(["api_name": "testApiName2", "last_modified_time": 6, "fields": ["field1": ["api_name": "field1", "type": "formula", "is_index": true], "field2": ["api_name": "field2", "type": "count", "is_index": true], "field3": ["api_name": "field3", "type": "quote", "is_index": true]]])
        Map<String, IObjectDescribe> describeMap = ImmutableMap.of(describe1.getApiName(), describe1, describe2.getApiName(), describe2, describe3.getApiName(), describe3)
        when:
        describeLogicService.findObjects(_, _) >> { args ->
            List<String> apiNames = (List<String>) args[1]
            Map<String, IObjectDescribe> resultMap = [:]
            apiNames.forEach { resultMap.put(it, describeMap.get(it)) }
            return resultMap
        }
        describeLogicService.findObject(_, _) >> { args ->
            String apiName = (String) args[1]
            return describeMap.get(apiName)
        }
        describeLogicService.findDescribeListWithFields(_) >> {
            Lists.newArrayList(describeMap.values())
        }
        SubmitCalculateJob.Result result = calculateService.submitCalculateJob(arg, context)
        then:
        result != null
        result.calculateFieldMap == expectedResult
        where:
        manual | lastModifiedTime | calculateJobList                                                                                  || expectedResult
        true   | null             | null                                                                                              || ["testApiName": ["field1", "field2", "field3"], "testApiName2": ["field1", "field2", "field3"]]
        true   | 8                | null                                                                                              || ["testApiName": ["field1", "field2", "field3"]]
        true   | null             | [new SubmitCalculateJob.CalculateJob(objectApiName: "testApiName")]                               || ["testApiName": ["field1", "field2", "field3"]]
        false  | null             | [new SubmitCalculateJob.CalculateJob(objectApiName: "testApiName", fieldApiNameList: ["field1"])] || ["testApiName": ["field1"]]
    }

    def "test checkCalculateFields with invalid param"() {
        given:
        CheckCalculateFields.Arg arg = new CheckCalculateFields.Arg(objectApiName: objectApiName, fieldList: fieldList)
        when:
        calculateService.checkCalculateFields(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName | fieldList                                   || expectedException
        null          | null                                        || ValidateException
        ""            | null                                        || ValidateException
        null          | []                                          || ValidateException
        ""            | []                                          || ValidateException
        null          | [["api_name": "field1", "type": "formula"]] || ValidateException
        ""            | [["api_name": "field1", "type": "formula"]] || ValidateException
        "testApiName" | null                                        || ValidateException
        "testApiName" | []                                          || ValidateException
    }

    def "test checkCalculateFields"() {
        given:
        CheckCalculateFields.Arg arg = new CheckCalculateFields.Arg(objectApiName: objectApiName, fieldList: fieldList)
        when:
        CheckCalculateFields.Result result = calculateService.checkCalculateFields(arg, context)
        then:
        result != null
        result.success == expectedResult
        where:
        objectApiName | fieldList                                   || expectedResult
        "testApiName" | [["api_name": "field1", "type": "formula"]] || true
    }

    def "test batchCalculateOfRecordType"() {
        given:
        BatchCalculateOfRecordType.Arg arg = new BatchCalculateOfRecordType.Arg(masterObjectApiName: masterObjectApiName,
                recordType: recordType, masterData: ObjectDataDocument.of(masterData), detailDataMap: ObjectDataDocument.ofMap(detailDataMap),
                originalObjectApiName: originalObjectApiName, buttonApiName: buttonApiName, ruleApiName: ruleApiName)
        when:
        recordTypeLogicService.findValidRecordTypeListMap(_, _) >> {
            return ["detail1": [new RecordTypeOption(["api_name": "default__c"])]]
        }
        recordTypeLogicService.filterUnMatchRecordTypes(_, _, _, _) >> { args ->
            return (Map<String, List<IRecordTypeOption>>) args[1]
        }
        describeLogicService.findObjects(_, _) >> { args ->
            Collection<String> objectApiNames = (Collection<String>) args[1]
            Map<String, IObjectDescribe> describeMap = [:]
            objectApiNames.forEach { describeMap.put(it, new ObjectDescribe(["api_name": it])) }
            return describeMap
        }
        BatchCalculateOfRecordType.Result result = calculateService.batchCalculateOfRecordType(arg, context)
        then:
        result != null
        result.objectData == ObjectDataDocument.of(expectedResultObjectData)
        result.detail == ObjectDataDocument.ofMap(expectedResultDetail)
        where:
        masterObjectApiName | recordType   | masterData           | detailDataMap                                                                                                                                                    | originalObjectApiName | buttonApiName | ruleApiName || expectedResultObjectData                          || expectedResultDetail
        null                | null         | ["field1": "value1"] | ["detail1": [new ObjectData(["field1": "value1"])]]                                                                                                              | null                  | null          | null        || ["field1": "value1", "record_type": null]         || ["detail1": [new ObjectData(["field1": "value1"])]]
        null                | ""           | ["field1": "value1"] | ["detail1": [new ObjectData(["field1": "value1"])]]                                                                                                              | null                  | null          | null        || ["field1": "value1", "record_type": ""]           || ["detail1": [new ObjectData(["field1": "value1"])]]
        null                | "default__c" | ["field1": "value1"] | [:]                                                                                                                                                              | null                  | null          | null        || ["field1": "value1", "record_type": "default__c"] || [:]
        null                | "default__c" | ["field1": "value1"] | ["detail1": [new ObjectData(["field1": "value1", "record_type": "default__c"])]]                                                                                 | null                  | null          | null        || ["field1": "value1", "record_type": "default__c"] || ["detail1": [new ObjectData(["field1": "value1", "record_type": "default__c"])]]
        "testApiName"       | "default__c" | ["field1": "value1"] | ["detail1": [new ObjectData(["field1": "value1", "record_type": "default__c"])], "detail2": [new ObjectData(["field1": "value1", "record_type": "default__c"])]] | null                  | null          | null        || ["field1": "value1", "record_type": "default__c"] || ["detail1": [new ObjectData(["field1": "value1", "record_type": "default__c"])], "detail2": []]
        "testApiName"       | "default__c" | ["field1": "value1"] | ["detail1": [new ObjectData(["field1": "value1", "record_type": "default__c"])], "detail2": [new ObjectData(["field1": "value1", "record_type": "default__c"])]] | null                  | null          | "rule1"     || ["field1": "value1", "record_type": "default__c"] || ["detail1": [new ObjectData(["field1": "value1", "record_type": "default__c"])], "detail2": []]
    }

    def "test calculateWithDrafts"() {
        given:
        CalculateWithDrafts.Arg arg = new CalculateWithDrafts.Arg(objectData: objectData, detailDataMap: detailDataMap,
                skipCalculateFields: skipCalculateFields, skipCalculateDVField: skipCalculateDVField, maskFieldApiNames: maskFieldApiNames)
        when:
        Whitebox.setInternalState(AppFrameworkConfig, "maskFieldEncryptGray", maskFieldEncryptGray)
        describeLogicService.findObject(_, _) >> { args ->
            return new ObjectDescribe(["api_name": (String) args[1]])
        }
        describeLogicService.findObjects(_, _) >> { args ->
            Collection<String> objectApiNames = (Collection<String>) args[1]
            Map<String, IObjectDescribe> describeMap = [:]
            objectApiNames.forEach { describeMap.put(it, new ObjectDescribe(["api_name": it])) }
            return describeMap
        }
        CalculateWithDrafts.Result result = calculateService.calculateWithDrafts(arg, context)
        then:
        result != null
        result.objectData == ObjectDataDocument.of(expectedResultObjectData)
        result.detailDataMap == ObjectDataDocument.ofMap(expectedResultDetail)
        where:
        maskFieldEncryptGray                     | objectData                                  | detailDataMap | skipCalculateFields | skipCalculateDVField | maskFieldApiNames || expectedResultObjectData                    || expectedResultDetail
        [:]                                      | ["object_describe_api_name": "testApiName"] | [:]           | [:]                 | false                | [:]               || ["object_describe_api_name": "testApiName"] || [:]
        ["testApiName": new GrayRule("white:")]  | ["object_describe_api_name": "testApiName"] | [:]           | [:]                 | false                | [:]               || ["object_describe_api_name": "testApiName"] || [:]
        ["testApiName": new GrayRule("white:*")] | ["object_describe_api_name": "testApiName"] | [:]           | [:]                 | false                | [:]               || ["object_describe_api_name": "testApiName"] || [:]
    }

    def "test calculateWithEditInfo with null arg"() {
        given:
        CalculateWithEditInfo.Arg arg = null
        when:
        calculateService.calculateWithEditInfo(arg, context)
        then:
        thrown(ValidateException)
    }

    def "test calculateWithEditInfo"() {
        given:
        CalculateWithEditInfo.Arg arg = new CalculateWithEditInfo.Arg(describeApiName: describeApiName, beforeDataInfo: beforeDataInfo,
                afterDataInfo: afterDataInfo)
        when:
        describeLogicService.findObject(_, _) >> { args ->
            return new ObjectDescribe(["api_name": (String) args[1]])
        }
        calculateServiceFacade.calculateWithUIActionCallback(_, _, _) >> { args ->
            IObjectData masterData = ((ObjectDataDocument) args[1]).toObjectData()
            masterData.setDescribeApiName(((CalculateWithUIActionCallbackContainer) args[0]).objectDescribe.getApiName())
            return UIEventProcess.ProcessRequest.builder()
                    .masterData(masterData)
                    .detailWithOnlyChangedFields(ObjectDataDocument.ofDataMap((Map<String, List<ObjectDataDocument>>) args[2]))
                    .build()
        }
        CalculateWithEditInfo.Result result = calculateService.calculateWithEditInfo(arg, context)
        then:
        result != null
        result.data == expectedResult
        where:
        describeApiName | beforeDataInfo                                                             | afterDataInfo                                                              || expectedResult
        "testApiName"   | new CalculateWithEditInfo.DataInfo(masterData: ObjectDataDocument.of([:])) | new CalculateWithEditInfo.DataInfo(masterData: ObjectDataDocument.of([:])) || ["testApiName": ObjectDataDocument.of(["object_describe_api_name": "testApiName"])]
    }

    def "test checkFields"() {
        given:
        CheckFieldsForCalc.Arg arg = new CheckFieldsForCalc.Arg(fieldDescribes: fieldDescribes)
        when:
        calculateService.checkFields(arg, context)
        then:
        1 * serviceFacade.checkFieldsForCalc(_, _, _, _, _)
        noExceptionThrown()
        where:
        fieldDescribes << [["{\"api_name\":\"field1\",\"type\":\"formula\"}"],
                           ["{\"api_name\":\"field2\",\"type\":\"count\"}"],
                           ["{\"api_name\":\"field1\",\"type\":\"formula\"}", "{\"api_name\":\"field2\",\"type\":\"count\"}"]]

    }

    def "test calculateAndUpdateByOriginalData with invalid param"() {
        given:
        CalculateAndUpdateByOriginalData.Arg arg = new CalculateAndUpdateByOriginalData.Arg(objectApiName: objectApiName, dataId: dataId)
        when:
        calculateService.calculateAndUpdateByOriginalData(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName | dataId || expectedException
        null          | null   || ValidateException
        ""            | null   || ValidateException
        null          | "id1"  || ValidateException
        ""            | "id1"  || ValidateException
        "testApiName" | null   || ValidateException
        "testApiName" | ""     || ValidateException
    }

    def "test calculateAndUpdateByOriginalData"() {
        given:
        CalculateAndUpdateByOriginalData.Arg arg = new CalculateAndUpdateByOriginalData.Arg(objectApiName: objectApiName, dataId: dataId)
        when:
        CalculateAndUpdateByOriginalData.Result result = calculateService.calculateAndUpdateByOriginalData(arg, context)
        then:
        1 * metaDataService.calculateAndUpdateByOriginalData(_, _, _, _)
        result == expectedResult
        where:
        objectApiName | dataId || expectedResult
        "testApiName" | "id1"  || new CalculateAndUpdateByOriginalData.Result()
    }

    def "test findCalculateFieldsWithUnCompletedJob with invalid param"() {
        given:
        FindCalculateFieldsWithUnCompletedJob.Arg arg = new FindCalculateFieldsWithUnCompletedJob.Arg(objectApiName: objectApiName)
        when:
        calculateService.findCalculateFieldsWithUnCompletedJob(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName || expectedException
        null          || ValidateException
        ""            || ValidateException
    }

    def "test findCalculateFieldsWithUnCompletedJob when gray is not enabled"() {
        given:
        FindCalculateFieldsWithUnCompletedJob.Arg arg = new FindCalculateFieldsWithUnCompletedJob.Arg(objectApiName: "testApiName")
        when:
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_CALCULATION_PROGRESS, tenantId) >> false
        FindCalculateFieldsWithUnCompletedJob.Result result = calculateService.findCalculateFieldsWithUnCompletedJob(arg, context)
        then:
        result != null
        !result.supportCalculationProgress
    }

    def "test findCalculateFieldsWithUnCompletedJob when gray is enabled"() {
        given:
        FindCalculateFieldsWithUnCompletedJob.Arg arg = new FindCalculateFieldsWithUnCompletedJob.Arg(objectApiName: "testApiName")
        IObjectDescribe objectDescribe = new ObjectDescribe([
                "api_name": "testApiName",
                "fields"  : [
                        "field1": ["api_name": "field1", "type": "formula"],
                        "field2": ["api_name": "field2", "type": "count"]
                ]
        ])
        when:
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_CALCULATION_PROGRESS, tenantId) >> true
        describeLogicService.findObjectWithoutCopy(_, _) >> objectDescribe
        jobScheduleService.queryJobInfo(_, _) >> {
            QueryJob.ResultInfo resultInfo = new QueryJob.ResultInfo()
            resultInfo.setJobInfoList([new QueryJob.JobInfo(id: "job1", status: 150, jobParam: "[\"field1\"]", executeDataNum: 100),
                                       new QueryJob.JobInfo(id: "job2", status: 100, jobParam: "[\"field2\"]", predictDataNum: 100)])
            return resultInfo
        }
        FindCalculateFieldsWithUnCompletedJob.Result result = calculateService.findCalculateFieldsWithUnCompletedJob(arg, context)
        then:
        result != null
        result.supportCalculationProgress
        result.calculateFields.size() == 2
        result.calculateFields[0].apiName.equals("field1")
        result.calculateFields[1].apiName.equals("field2")
    }

    def "test findCalculationJobInfo with invalid param"() {
        given:
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg(objectApiName: objectApiName, fieldApiName: fieldApiName, jobId: jobId)
        when:
        calculateService.findCalculationJobInfo(arg, context)
        then:
        thrown(expectedException)
        where:
        objectApiName | fieldApiName | jobId || expectedException
        null          | null         | null  || ValidateException
        ""            | null         | null  || ValidateException
        "testApiName" | null         | null  || ValidateException
        "testApiName" | ""           | null  || ValidateException
    }

    def "test findCalculationJobInfo by jobId"() {
        given:
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg(jobId: "job1")
        when:
        jobScheduleService.queryJobInfo(_, _) >> {
            QueryJob.ResultInfo resultInfo = new QueryJob.ResultInfo()
            resultInfo.setJobInfoList([new QueryJob.JobInfo(id: "job1", status: 150, jobParam: "[\"field1\"]", executeDataNum: 100)])
            return resultInfo
        }
        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, context)
        then:
        result != null
        result.jobInfo != null
        result.jobInfo.jobId == "job1"
        result.jobInfo.jobStatusCode == 150
    }

    def "test findCalculationJobInfo by field"() {
        given:
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg(objectApiName: "testApiName", fieldApiName: "field1")
        IObjectDescribe objectDescribe = new ObjectDescribe([
                "api_name": "testApiName",
                "fields"  : [
                        "field1": ["api_name": "field1", "type": "formula", "is_index": true]
                ]
        ])
        when:
        describeLogicService.findObjectWithoutCopy(_, _) >> objectDescribe
        jobScheduleService.queryJobInfo(_, _) >> {
            QueryJob.ResultInfo resultInfo = new QueryJob.ResultInfo()
            resultInfo.setJobInfoList([new QueryJob.JobInfo(id: "job1", status: 200, jobParam: "[\"field1\"]", executeDataNum: 100)])
            return resultInfo
        }
        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, context)
        then:
        result != null
        result.jobInfo != null
        result.jobInfo.jobId == "job1"
        result.jobInfo.jobStatusCode == 200
    }

    def "test findCalculationJobInfo when field not found"() {
        given:
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg(objectApiName: "testApiName", fieldApiName: "field1")
        IObjectDescribe objectDescribe = new ObjectDescribe([
                "api_name": "testApiName",
                "fields"  : [
                        "field2": ["api_name": "field2", "type": "count"]
                ]
        ])
        when:
        describeLogicService.findObjectWithoutCopy(_, _) >> objectDescribe
        jobScheduleService.queryJobInfo(_, _) >> {
            QueryJob.ResultInfo resultInfo = new QueryJob.ResultInfo()
            resultInfo.setJobInfoList([new QueryJob.JobInfo(id: "job1", status: 200, jobParam: "[\"field1\"]", executeDataNum: 100)])
            return resultInfo
        }
        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, context)
        then:
        result != null
        result.jobInfo == null
    }

    def "test findCalculationJobInfo when job not found"() {
        given:
        FindCalculationJobInfo.Arg arg = new FindCalculationJobInfo.Arg(objectApiName: "testApiName", fieldApiName: "field1")
        IObjectDescribe objectDescribe = new ObjectDescribe([
                "api_name": "testApiName",
                "fields"  : [
                        "field1": ["api_name": "field2", "type": "formula", "is_index": true]
                ]
        ])
        when:
        describeLogicService.findObjectWithoutCopy(_, _) >> objectDescribe
        jobScheduleService.queryJobInfo(_, _) >> {
            QueryJob.ResultInfo resultInfo = new QueryJob.ResultInfo()
            resultInfo.setJobInfoList([])
            return resultInfo
        }
        FindCalculationJobInfo.Result result = calculateService.findCalculationJobInfo(arg, context)
        then:
        result != null
        result.jobInfo == null
    }

    def "test batchCalculate with detail delete data"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.metaClass.actByDeleteDetail = { -> true }
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> true }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with copy detail and excluded fields"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def excludedFields = [(detailApiName): ["0": [new CalculateRelation.RelateField(fieldName: "field2")]]]

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.excludedDetailCalculateFields = excludedFields
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with mask fields"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1"    : [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ],
                        "mask_field": [
                                "api_name": "mask_field",
                                "type"    : "text",
                                "is_mask" : true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName, mask_field: "123456")
        arg.maskFieldApiNames = [(masterApiName): ["mask_field"]]
        arg.calculateFields = [(masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")]]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> []
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        maskFieldLogicService.decodeMaskFieldEncryptValue(_, _, _) >> {}
        maskFieldLogicService.fillMaskFieldValue(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with optimize mode"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.calculateFields = [(masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")]]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> true }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> []
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with invalid describe"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": false,
                "fields"   : [:]  // 添加空的fields以避免NPE
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.calculateFields = [(masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")]]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> []
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> { throw new ValidateException("对象已停用") }

        calculateService.batchCalculate(arg, context)

        then:
        def e = thrown(ValidateException)
        e.message == "对象已停用"
    }

    def "test batchCalculate with detail data"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with optimize mode and less calculate fields"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"
        def lookupApiName = "lookup1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name" : "field1",
                                "type"     : "formula",
                                "is_active": true
                        ],
                        "field2": [
                                "api_name" : "field2",
                                "type"     : "formula",
                                "is_active": true
                        ]
                ]
        ])

        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name" : "field1",
                                "type"     : "formula",
                                "is_active": true
                        ],
                        "field2": [
                                "api_name" : "field2",
                                "type"     : "formula",
                                "is_active": true
                        ],
                        "field3": [
                                "api_name"       : "field3",
                                "type"           : "object_reference",
                                "is_active"      : true,
                                "target_api_name": lookupApiName
                        ]
                ]
        ])

        def lookupDescribe = new ObjectDescribe([
                "api_name" : lookupApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"                   : "field1",
                                "type"                       : "count",
                                "is_active"                  : true,
                                "sub_object_describe_apiname": detailApiName,
                                "field_api_name"             : "field3",
                                "count_type"                 : "count"
                        ]
                ]
        ])

        // 只计算field1字段
        def argCalculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (lookupApiName): [new CalculateRelation.RelateField(fieldName: "field1")]
        ]
        def computeCalculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1"), new CalculateRelation.RelateField(fieldName: "field2")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field1"), new CalculateRelation.RelateField(fieldName: "field2")],
                (lookupApiName): [new CalculateRelation.RelateField(fieldName: "field1")]
        ]

        def masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        def detailData = new ObjectDataDocument(id: "d1", apiName: detailApiName)

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = masterData
        arg.detailDataMap = [(detailApiName): ["d1": detailData]]
        arg.calculateFields = argCalculateFields
        arg.modifiedObjectApiName = detailApiName
        arg.modifiedDataIndexList = ["d1"]
        arg.modifiedType = BatchCalculate.ADD
        arg.oldMasterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.oldDetailDataMap = [(detailApiName): ["d1": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]

        def graph = FieldRelationGraphBuilder.builder()
                .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe, lookupDescribe])])
                .build()
                .getGraph()

        when:
        UdobjGrayUtil.GrayConfig grayConfig = JacksonUtils.fromJson('''{"functions":[{"functionName":"optimizeDetailObjectCalculateInBatchCalculate","blackRule":{"ei":"white:*"}}]}''', UdobjGrayUtil.GrayConfig.class)
        Whitebox.setInternalState(UdobjGrayUtil, "GRAY_CONFIG", grayConfig)
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> graph
        fieldRelationCalculateService.computeCalculateFieldsForEditData(_) >> { EditCalculateParam param ->
            def calculateFields = CalculateFields.of(graph, computeCalculateFields)
            calculateFields.setCalculateDataMap([
                    (detailApiName): [CalculateObjectData.of("d1", detailData.toObjectData(),
                            Sets.newHashSet(computeCalculateFields[detailApiName]), [:])]
            ])
            return calculateFields
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> { User user,
                                                                    IObjectData masterDataArg,
                                                                    Map<String, List<IObjectData>> detailDataMap,
                                                                    CalculateFields calculateFields ->
            // 验证计算字段
            assert calculateFields.calculateFieldMap[masterApiName].size() == 1
            assert calculateFields.calculateFieldMap[masterApiName].get(0).fieldName == "field1"
            assert calculateFields.calculateFieldMap[detailApiName].size() == 1
            assert calculateFields.calculateFieldMap[detailApiName].get(0).fieldName == "field1"
            assert calculateFields.calculateDataMap[detailApiName].size() == 1
            assert calculateFields.calculateDataMap[detailApiName].get(0).calculateFields.size() == 1
            assert calculateFields.calculateDataMap[detailApiName].get(0).calculateFields.toList().get(0).fieldName == "field1"

            // 直接修改主对象数据
            masterDataArg.set("field1", "masterValue1")

            // 直接修改从对象数据
            if (detailDataMap && detailDataMap[detailApiName]) {
                detailDataMap[detailApiName].each { detailDataArg ->
                    detailDataArg.set("field1", "detailValue1")
                }
            }
        }

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
        result.calculateResult[masterApiName]["0"] != null
        result.calculateResult[masterApiName]["0"].field1 == "masterValue1"
        result.calculateResult[detailApiName]["d1"].field1 == "detailValue1"
    }

    def "test batchCalculate with calculateFieldApiNames"() {
        given:
        def masterApiName = "testApiName"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ],
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.calculateFieldApiNames = [(masterApiName): ["field1"]]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }
        arg.metaClass.actByDeleteDetail = { -> false }
        arg.metaClass.actByCopyDetail = { -> false }
        arg.metaClass.skipConcurrentCheck = { -> true }

        def expectedCalculateFields = [(masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")]]
        def graph = FieldRelationGraphBuilder.builder()
                .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe])])
                .build()
                .getGraph()

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> []
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> graph
        fieldRelationCalculateService.convertToCalculateFields(_, _, _) >> CalculateFields.of(graph, expectedCalculateFields)
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> { args ->
            def actualCalculateFields = args[3]
            assert actualCalculateFields.size() == 1
            assert actualCalculateFields[masterApiName][0].fieldName == "field1"
        }

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with modified data"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.modifiedObjectApiName = detailApiName
        arg.modifiedDataIndexList = ["0"]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with empty detail data and actByDeleteDetail true"() {
        given:
        def masterApiName = "testApiName"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.calculateFields = [(masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")]]
        arg.metaClass.actByDeleteDetail = { -> true }
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> []
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with empty detail data and actByDeleteDetail false"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.calculateFields = [(masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")]]
        arg.metaClass.actByDeleteDetail = { -> false }
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with excludedDetailCalculateFields"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.excludedDetailCalculateFields = [(detailApiName): ["0": [new CalculateRelation.RelateField(fieldName: "field2")]]]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFieldsWithDetailData(_, _, _, _, _) >> {}

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with applyOptimize true"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> true }
        arg.metaClass.masterModifyData = { -> true }
        arg.metaClass.detailAddDataMap = { Map detailCalculateDataMap -> [:] }
        arg.metaClass.detailDeleteDataMap = { -> [:] }
        arg.metaClass.detailModifyDataMap = { -> [:] }
        arg.metaClass.oldMasterData = { -> new ObjectDataDocument(id: "1", apiName: masterApiName) }
        arg.metaClass.oldDetailDataMap = { ->
            [(detailApiName): [new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        fieldRelationCalculateService.computeCalculateFieldsForEditData(_) >> { EditCalculateParam param ->
            def calculateFields = CalculateFields.of(
                    fieldRelationGraphService.buildReverseDependencyGraph([], false, true, true),
                    arg.calculateFields
            )
            calculateFields.setCalculateDataMap([(detailApiName): [CalculateObjectData.of("0", new ObjectDataDocument(id: "d1", apiName: detailApiName), true)]])
            return calculateFields
        }
        metaDataService.mergeWithDbData(_, _, _) >> {}
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> { User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, Map<String, List<CalculateRelation.RelateField>> calculateFields ->
            // 验证计算字段
            assert calculateFields[masterApiName].size() == 1
            assert calculateFields[masterApiName][0].fieldName == "field1"

            // 直接修改主对象数据
            masterData.put("field1", "masterValue1")

            // 直接修改从对象数据
            if (detailDataMap && detailDataMap[detailApiName]) {
                detailDataMap[detailApiName].each { detailData ->
                    detailData.put("field1", "detailValue1")
                }
            }
        }

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    def "test batchCalculate with actByCopyDetail true"() {
        given:
        def masterApiName = "testApiName"
        def detailApiName = "detail1"

        def masterDescribe = new ObjectDescribe([
                "api_name" : masterApiName,
                "is_active": true,
                "fields"   : [
                        "field1": [
                                "api_name"          : "field1",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])
        def detailDescribe = new ObjectDescribe([
                "api_name" : detailApiName,
                "is_active": true,
                "fields"   : [
                        "field2": [
                                "api_name"          : "field2",
                                "type"              : "formula",
                                "is_active"         : true,
                                "is_calculate_field": true
                        ]
                ]
        ])

        def arg = new BatchCalculate.Arg()
        arg.masterObjectApiName = masterApiName
        arg.masterData = new ObjectDataDocument(id: "1", apiName: masterApiName)
        arg.detailDataMap = [(detailApiName): ["0": new ObjectDataDocument(id: "d1", apiName: detailApiName)]]
        arg.calculateFields = [
                (masterApiName): [new CalculateRelation.RelateField(fieldName: "field1")],
                (detailApiName): [new CalculateRelation.RelateField(fieldName: "field2")]
        ]
        arg.metaClass.applyOptimize = { String tenantId, Set<String> detailApiNames -> true }
        arg.metaClass.actByCopyDetail = { -> true }
        arg.metaClass.getModifiedObjectApiName = { -> detailApiName }
        arg.metaClass.needCalculateAllData = { String apiName, String fieldName, Map lookupOrMasterCalculateFields, FieldRelationGraph graph -> false }

        when:
        describeLogicService.findObjects(_, _) >> [(masterApiName): masterDescribe, (detailApiName): detailDescribe]
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        fieldRelationGraphService.buildReverseDependencyGraph(_, _, _, _) >> {
            def graph = FieldRelationGraphBuilder.builder()
                    .graphLayers([FieldRelationGraphBuilder.GraphLayer.of([masterDescribe, detailDescribe])])
                    .build()
                    .getGraph()
            return graph
        }
        fieldRelationCalculateService.computeCalculateFieldsForEditData(_) >> { EditCalculateParam param ->
            def calculateFields = CalculateFields.of(
                    fieldRelationGraphService.buildReverseDependencyGraph([], false, true, true),
                    arg.calculateFields
            )
            calculateFields.setCalculateDataMap([(detailApiName): [CalculateObjectData.of("0", new ObjectDataDocument(id: "d1", apiName: detailApiName), true)]])
            return calculateFields
        }
        metaDataService.batchCalculateBySortFields(_, _, _, _) >> { user, masterData, detailDataMap, calculateFields ->
            // 设置主对象的计算字段值
            masterData.put("field1", "masterValue1")
            // 设置从对象的计算字段值
            detailDataMap[detailApiName]?.each { detailData ->
                detailData.put("field1", "detailValue1")
            }
        }

        def result = calculateService.batchCalculate(arg, context)

        then:
        result != null
        result.calculateResult != null
    }

    /**
     * 测试内容描述：测试当描述映射中存在空值时，doBatchCalculate方法应该抛出ValidateException异常
     */
    def "doBatchCalculateTest_ThrowsExceptionWhenDescribeMapContainsNullValue"() {
        given:
        def tenantId = "74255"
        def masterObjApiName = "AccountObj"
        def detailObjApiName = "DetailObj"
        def masterDescribe = new ObjectDescribe()
        masterDescribe.setApiName(masterObjApiName)

        def arg = new BatchCalculate.Arg()
        arg.setMasterObjectApiName(masterObjApiName)
        arg.setCalculateFieldApiNames(["AccountObj": ["name"], "DetailObj": ["field1"]])
        arg.setMasterData(new ObjectDataDocument())

        // 设置 describeLogicService.findObjects 返回包含空值的描述映射
        def describeMap = [
                (masterObjApiName): masterDescribe,
                (detailObjApiName): null
        ]
        describeLogicService.findObjects(_ as String, _ as Set) >> describeMap

        // 设置 detailDescribes
        describeLogicService.findDetailDescribesCreateWithMaster(_ as String, _ as String) >> []

        when:
        calculateService.doBatchCalculate(arg, context)

        then:
        thrown(ValidateException)
    }

}