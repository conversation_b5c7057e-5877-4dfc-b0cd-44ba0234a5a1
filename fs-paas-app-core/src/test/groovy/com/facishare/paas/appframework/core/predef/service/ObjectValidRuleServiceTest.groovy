package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.service.dto.validateRule.*
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.LayoutRuleLogicService
import com.facishare.paas.appframework.metadata.RecordTypeLogicService
import com.facishare.paas.appframework.metadata.ValidateRuleServiceImpl
import com.facishare.paas.appframework.metadata.dto.RuleResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.Rule
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class ObjectValidRuleServiceTest extends Specification {

    ObjectValidRuleService objectValidRuleService
    ValidateRuleServiceImpl validateRuleService = Mock(ValidateRuleServiceImpl)
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    LogService logService = Mock(LogService)
    LayoutRuleLogicService layoutRuleLogicService = Mock(LayoutRuleLogicService)
    RecordTypeLogicService recordTypeLogicService = Mock(RecordTypeLogicService)
    static final String TENANT_ID = "74255"

    def setupSpec() {
        // 设置国际化相关的mock
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        objectValidRuleService = new ObjectValidRuleService(
                validateRuleService: validateRuleService,
                describeLogicService: describeLogicService,
                logService: logService,
                layoutRuleLogicService: layoutRuleLogicService,
                recordTypeLogicService: recordTypeLogicService
        )
    }

    def cleanup() {
        RequestContextManager.removeContext()
    }

    private ServiceContext createServiceContext(String tenantId = TENANT_ID, String userId = "1000") {
        def user = new User(tenantId, userId)
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        return new ServiceContext(requestContext, "cacheValidate", "cacheValidate")
    }

    def "测试缓存验证 - 当对象描述版本不匹配时应返回失败"() {
        given: "准备测试数据"
        def requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def arg = new CacheValidate.Arg(
                objectApiName: "testObject",
                describeVersion: 1L,
                detailCacheValid: [
                        new CacheValidate.DetailObjCacheValid(
                                objectApiName: "detailObject",
                                describeVersion: 1L
                        )
                ]
        )
        def describe = Mock(IObjectDescribe) {
            getApiName() >> "testObject"
            getVersion() >> 2L
        }

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证交互和结果"
        1 * describeLogicService.findObjectsWithoutCopy(TENANT_ID, _) >> ["testObject": describe]
        !result.success

        where: "测试场景"
        testCase               | describeVersion | objectVersion
        "对象版本高于缓存版本" | 1L              | 2L
    }

    def "测试缓存验证 - 当布局规则验证失败时应返回失败"() {
        given: "准备测试数据"
        def requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def arg = new CacheValidate.Arg(
                objectApiName: "testObject",
                describeVersion: 2L,
                detailCacheValid: []
        )
        def describe = Mock(IObjectDescribe) {
            getApiName() >> "testObject"
            getVersion() >> 2L
        }

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证交互和结果"
        1 * describeLogicService.findObjectsWithoutCopy(TENANT_ID, _) >> ["testObject": describe]
        1 * layoutRuleLogicService.layoutRuleValidate(requestContext.user, _) >> false
        !result.success

        where: "测试场景"
        testCase           | layoutRuleValid
        "布局规则验证失败" | false
    }

    def "测试缓存验证 - 当所有验证都通过时应返回成功"() {
        given: "准备测试数据"
        def requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def arg = new CacheValidate.Arg(
                objectApiName: "testObject",
                describeVersion: 2L,
                detailCacheValid: []
        )
        def describe = Mock(IObjectDescribe) {
            getApiName() >> "testObject"
            getVersion() >> 2L
        }

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证交互和结果"
        1 * describeLogicService.findObjectsWithoutCopy(TENANT_ID, _) >> ["testObject": describe]
        1 * layoutRuleLogicService.layoutRuleValidate(requestContext.user, _) >> true
        result.success

        where: "测试场景"
        testCase         | describeVersion | layoutRuleValid
        "所有验证都通过" | 2L              | true
    }

    @Unroll
    def "测试缓存验证 - 边界条件测试"() {
        given: "准备测试数据"
        def requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def arg = new CacheValidate.Arg(
                objectApiName: objectApiName,
                describeVersion: describeVersion,
                detailCacheValid: detailCacheValid
        )

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证交互和结果"
        noExceptionThrown()
        interactions * describeLogicService.findObjectsWithoutCopy(TENANT_ID, _) >> objectDescribes
        if (expectedLayoutValidation) {
            1 * layoutRuleLogicService.layoutRuleValidate(requestContext.user, _) >> layoutRuleValid
        }
        result.success == expectedSuccess

        where: "测试场景"
        testCase               | objectApiName | describeVersion | detailCacheValid | objectDescribes                                | layoutRuleValid | expectedLayoutValidation | expectedSuccess | interactions
        "空对象名称"           | ""            | 1L              | []               | [:]                                            | true            | false                    | false           | 0
        "null对象名称"         | null          | 1L              | []               | [:]                                            | true            | false                    | false           | 0
        "空详细缓存验证列表"   | "testObject"  | 1L              | []               | ["testObject": mockDescribe("testObject", 1L)] | true            | true                     | true            | 1
        "null详细缓存验证列表" | "testObject"  | 1L              | null             | ["testObject": mockDescribe("testObject", 1L)] | true            | true                     | true            | 1
        "对象不存在"           | "testObject"  | 1L              | []               | [:]                                            | true            | false                    | false           | 1
        "版本号为负数"         | "testObject"  | -1L             | []               | ["testObject": mockDescribe("testObject", 1L)] | true            | false                    | false           | 1
        "版本号为0"            | "testObject"  | 0L              | []               | ["testObject": mockDescribe("testObject", 1L)] | true            | false                    | false           | 1
    }

    private IObjectDescribe mockDescribe(String apiName, Long version) {
        Mock(IObjectDescribe) {
            getApiName() >> apiName
            getVersion() >> version
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则的正常场景，验证规则创建成功并记录日志
     */
    @Unroll
    def "createTest_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"
        def jsonData = """
            {
                "api_name": "${ruleApiName}",
                "describe_api_name": "${describeApiName}",
                "name": "Test Rule",
                "active": true,
                "rule_type": "VALIDATE",
                "error_level": "ERROR"
            }
        """

        def arg = new CreateRule.Arg(json_data: jsonData)
        def context = createServiceContext(tenantId, userId)

        def rule = new Rule()
        rule.setApiName(ruleApiName)
        rule.setDescribeApiName(describeApiName)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)

        when: "调用创建方法"
        validateRuleService.create(_) >> ruleResult
        logService.log(_, _, _, _, _) >> null

        then: "验证结果"
        def result = objectValidRuleService.create(arg, context)
        result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则失败的场景，验证规则创建失败时的处理
     */
    @Unroll
    def "createTest_Failure"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"
        def jsonData = """
            {
                "api_name": "${ruleApiName}",
                "describe_api_name": "${describeApiName}",
                "name": "Test Rule",
                "active": true,
                "rule_type": "VALIDATE",
                "error_level": "ERROR"
            }
        """

        def arg = new CreateRule.Arg(json_data: jsonData)
        def context = createServiceContext(tenantId, userId)

        def rule = new Rule()
        rule.setApiName(ruleApiName)
        rule.setDescribeApiName(describeApiName)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(false)

        when: "调用创建方法"
        validateRuleService.create(_) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.create(arg, context)
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新验证规则的正常场景，验证规则更新成功并记录日志
     */
    @Unroll
    def "updateTest_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"
        def jsonData = """
            {
                "api_name": "${ruleApiName}",
                "describe_api_name": "${describeApiName}",
                "name": "Updated Test Rule",
                "active": true,
                "rule_type": "VALIDATE",
                "error_level": "ERROR"
            }
        """

        def arg = new UpdateRule.Arg(json_data: jsonData)
        def context = createServiceContext(tenantId, userId)

        def rule = new Rule()
        rule.setApiName(ruleApiName)
        rule.setDescribeApiName(describeApiName)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)

        when: "调用更新方法"
        validateRuleService.update(_, _) >> ruleResult
        logService.log(_, _, _, _, _) >> null

        then: "验证结果"
        def result = objectValidRuleService.update(arg, context)
        result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新验证规则失败的场景，验证规则更新失败时的处理
     */
    @Unroll
    def "updateTest_Failure"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"
        def jsonData = """
            {
                "api_name": "${ruleApiName}",
                "describe_api_name": "${describeApiName}",
                "name": "Updated Test Rule",
                "active": true,
                "rule_type": "VALIDATE",
                "error_level": "ERROR"
            }
        """

        def arg = new UpdateRule.Arg(json_data: jsonData)
        def context = createServiceContext(tenantId, userId)

        def rule = new Rule()
        rule.setApiName(ruleApiName)
        rule.setDescribeApiName(describeApiName)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(false)

        when: "调用更新方法"
        validateRuleService.update(_, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.update(arg, context)
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则的正常场景，验证规则删除成功并记录日志
     */
    @Unroll
    def "deleteTest_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new DeleteRule.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)

        when: "调用删除方法"
        validateRuleService.delete(_, _, _) >> ruleResult
        logService.log(_, _, _, _, _) >> null

        then: "验证结果"
        def result = objectValidRuleService.delete(arg, context)
        result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则失败的场景，验证规则删除失败时的处理
     */
    @Unroll
    def "deleteTest_Failure"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new DeleteRule.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(false)

        when: "调用删除方法"
        validateRuleService.delete(_, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.delete(arg, context)
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询验证规则列表的正常场景，成功返回规则列表
     */
    @Unroll
    def "findRuleListTest_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleName = "test_rule"

        def arg = new FindValidRuleList.Arg(
                describeApiName: describeApiName,
                ruleName: ruleName
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)
        ruleResult.setRuleList([new Rule()])

        when: "调用查询方法"
        validateRuleService.findRuleList(_, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.findRuleList(arg, context)
        result.success
        result.ruleList.size() == 1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询验证规则列表失败的场景
     */
    @Unroll
    def "findRuleListTest_Failure"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleName = "test_rule"

        def arg = new FindValidRuleList.Arg(
                describeApiName: describeApiName,
                ruleName: ruleName
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(false)

        when: "调用查询方法"
        validateRuleService.findRuleList(_, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.findRuleList(arg, context)
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询单个验证规则详情的正常场景，成功返回规则信息
     */
    @Unroll
    def "findRuleInfoTest_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new FindRuleInfo.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)
        ruleResult.setRule([name: "Test Rule"])

        when: "调用查询方法"
        validateRuleService.findRuleInfo(_, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.findRuleInfo(arg, context)
        result.success
        result.rule.name == "Test Rule"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询单个验证规则详情失败的场景
     */
    @Unroll
    def "findRuleInfoTest_Failure"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new FindRuleInfo.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(false)

        when: "调用查询方法"
        validateRuleService.findRuleInfo(_, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.findRuleInfo(arg, context)
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用验证规则的场景，验证规则成功启用并记录日志
     */
    @Unroll
    def "isActiveTest_Enable_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new ActiveRule.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName,
                isActive: true
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)

        when: "调用启用方法"
        validateRuleService.isActive(_, _, _, _) >> ruleResult
        logService.log(_, _, _, _, _) >> null

        then: "验证结果"
        def result = objectValidRuleService.isActive(arg, context)
        result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用验证规则的场景，验证规则成功禁用并记录日志
     */
    @Unroll
    def "isActiveTest_Disable_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new ActiveRule.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName,
                isActive: false
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)

        when: "调用禁用方法"
        validateRuleService.isActive(_, _, _, _) >> ruleResult
        logService.log(_, _, _, _, _) >> null

        then: "验证结果"
        def result = objectValidRuleService.isActive(arg, context)
        result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用/禁用验证规则失败的场景
     */
    @Unroll
    def "isActiveTest_Failure"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        def ruleApiName = "test_rule"

        def arg = new ActiveRule.Arg(
                describeApiName: describeApiName,
                ruleApiName: ruleApiName,
                isActive: isActive
        )
        def context = createServiceContext(tenantId, userId)

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(false)

        when: "调用方法"
        validateRuleService.isActive(_, _, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.isActive(arg, context)
        !result.success

        where:
        isActive << [true, false]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则的正常场景，规则验证通过并返回相应消息
     */
    @Unroll
    def "validateRuleTest_Success"() {
        given: "准备测试数据"
        def objectData = new ObjectData([
                "id"                      : "1",
                "name"                    : "test",
                "object_describe_api_name": "AccountObj"
        ])
        def objectDataDocument = ObjectDataDocument.of(objectData)
        def arg = new ValidateRule.Arg()
        arg.setObjectData(objectDataDocument)
        arg.setObjectApiName("AccountObj")
        arg.setDetails([:])
        arg.setOption("test_option")

        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)
        ruleResult.setSave(false)
        ruleResult.setEnableBlocking(true)
        ruleResult.setBlockMessages(["test message"])
        ruleResult.setNonBlockMessages([])

        when: "调用验证方法"
        describeLogicService.findObjects(_, _) >> ["AccountObj": objectDescribe]
        validateRuleService.validateRule(_, _, _, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.validateRule(arg, createServiceContext())
        !result.success
        result.message == "test message"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则参数为空的异常场景
     */
    @Unroll
    def "validateRuleTest_EmptyParam"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def context = createServiceContext(tenantId)

        when: "调用验证方法"
        objectValidRuleService.validateRule(null, context)

        then: "验证异常"
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则对象数据为空的异常场景
     */
    @Unroll
    def "validateRuleTest_EmptyObjectData"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def arg = new ValidateRule.Arg(objectApiName: "AccountObj")
        def context = createServiceContext(tenantId)

        when: "调用验证方法"
        objectValidRuleService.validateRule(arg, context)

        then: "验证异常"
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则不匹配的场景
     */
    @Unroll
    def "validateRuleTest_NoMatch"() {
        given: "准备测试数据"
        def objectData = new ObjectData([
                "id"                      : "1",
                "name"                    : "test",
                "object_describe_api_name": "AccountObj"
        ])
        def objectDataDocument = ObjectDataDocument.of(objectData)
        def arg = new ValidateRule.Arg()
        arg.setObjectData(objectDataDocument)
        arg.setObjectApiName("AccountObj")
        arg.setDetails([:])
        arg.setOption("test_option")

        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")

        def ruleResult = new RuleResult()
        ruleResult.setSuccess(true)
        ruleResult.setSave(true)
        ruleResult.setEnableBlocking(false)
        ruleResult.setBlockMessages([])
        ruleResult.setNonBlockMessages([])

        when: "调用验证方法"
        describeLogicService.findObjects(_, _) >> ["AccountObj": objectDescribe]
        validateRuleService.validateRule(_, _, _, _, _) >> ruleResult

        then: "验证结果"
        def result = objectValidRuleService.validateRule(arg, createServiceContext())
        !result.success
        result.message == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试缓存验证的正常场景，所有版本都匹配且布局规则验证通过
     */
    @Unroll
    def "cacheValidateTest_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def describeApiName = "AccountObj"
        def detailApiName = "ContactObj"
        def layoutApiName = "DefaultLayout"
        def version = 1
        def lastModifiedTime = 1000L

        def arg = new CacheValidate.Arg(
                objectApiName: describeApiName,
                describeVersion: version,
                layoutApiName: layoutApiName,
                layoutLastModifiedTime: lastModifiedTime,
                layoutRule: [],
                detailCacheValid: [
                        new CacheValidate.DetailObjCacheValid(
                                objectApiName: detailApiName,
                                describeVersion: version,
                                detailLayoutList: [
                                        new CacheValidate.DetailLayoutRelatedInfo(
                                                layoutApiName: layoutApiName,
                                                layoutLastModifiedTime: lastModifiedTime,
                                                layoutRule: []
                                        )
                                ]
                        )
                ]
        )
        def context = createServiceContext(tenantId)

        def masterDescribe = new ObjectDescribe()
        masterDescribe.setApiName(describeApiName)
        masterDescribe.setVersion(version)

        def detailDescribe = new ObjectDescribe()
        detailDescribe.setApiName(detailApiName)
        detailDescribe.setVersion(version)

        and: "Mock依赖服务的行为"
        describeLogicService.findObjectsWithoutCopy(tenantId, _ as Set) >> [
                (describeApiName): masterDescribe,
                (detailApiName)  : detailDescribe
        ]
        layoutRuleLogicService.layoutRuleValidate(context.user, _ as List) >> true

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证结果"
        result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试缓存验证参数为空的场景
     */
    @Unroll
    def "cacheValidateTest_EmptyParam"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def context = createServiceContext(tenantId)

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(null, context)

        then: "验证结果"
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试缓存验证时对象描述版本不匹配的场景
     */
    @Unroll
    def "cacheValidateTest_VersionMismatch"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def describeApiName = "AccountObj"
        def version = 1

        def arg = new CacheValidate.Arg(
                objectApiName: describeApiName,
                describeVersion: version,
                layoutApiName: "DefaultLayout",
                layoutLastModifiedTime: 1000L,
                layoutRule: []
        )
        def context = createServiceContext(tenantId)

        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeApiName)
        objectDescribe.setVersion(version + 1) // 版本号更高

        and: "Mock依赖服务的行为"
        describeLogicService.findObjectsWithoutCopy(tenantId, _ as Set) >> [(describeApiName): objectDescribe]

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证结果"
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试缓存验证时布局规则验证失败的场景
     */
    @Unroll
    def "cacheValidateTest_LayoutRuleValidateFail"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def describeApiName = "AccountObj"
        def version = 1

        def arg = new CacheValidate.Arg(
                objectApiName: describeApiName,
                describeVersion: version,
                layoutApiName: "DefaultLayout",
                layoutLastModifiedTime: 1000L,
                layoutRule: []
        )
        def context = createServiceContext(tenantId)

        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeApiName)
        objectDescribe.setVersion(version)

        and: "Mock依赖服务的行为"
        describeLogicService.findObjectsWithoutCopy(tenantId, _ as Set) >> [(describeApiName): objectDescribe]
        layoutRuleLogicService.layoutRuleValidate(context.user, _ as List) >> false

        when: "调用缓存验证方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证结果"
        !result.success
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证cacheValidate方法中调用recordTypeLogicService.validateLayoutAndRecordType的逻辑
     */
    @Unroll
    def "cacheValidateTest_RecordTypeLogicService验证#testCase"() {
        given: "准备测试数据和环境"
        def requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def arg = new CacheValidate.Arg(
                objectApiName: "testObject",
                describeVersion: 2L,
                layoutType: layoutType,
                recordType: recordType,
                layoutRule: [
                        new LayoutRuleDocument(
                                ruleApiName: "rule1",
                                lastModifiedTime: 123456L
                        )
                ],
                layoutApiName: "testLayout",
                layoutLastModifiedTime: 123456L,
                layoutVersion: 1L
        )

        def describe = Mock(IObjectDescribe) {
            getApiName() >> "testObject"
            getVersion() >> 2L
        }

        def objectDescribes = ["testObject": describe]

        when: "调用cacheValidate方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证交互和结果"
        1 * describeLogicService.findObjectsWithoutCopy(TENANT_ID, _) >> objectDescribes
        1 * layoutRuleLogicService.layoutRuleValidate(requestContext.user, _) >> true
        count * recordTypeLogicService.validateLayoutAndRecordType(requestContext.user, objectDescribes, _) >> recordTypeValid
        result.success == expectedSuccess

        where: "测试场景"
        testCase                     | layoutType | recordType | count | recordTypeValid | expectedSuccess
        "记录类型和布局验证成功"     | "Edit"     | "default"  | 1     | true            | true
        "记录类型和布局验证失败"     | "Edit"     | "default"  | 1     | false           | false
        "未提供布局类型和记录类型"   | ""         | ""         | 0     | false           | true
        "提供布局类型但记录类型为空" | "Edit"     | ""         | 0     | false           | true
        "提供记录类型但布局类型为空" | ""         | "default"  | 0     | false           | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：验证cacheValidate方法中当布局类型和记录类型都存在，但记录类型验证失败的情况
     */
    @Unroll
    def "cacheValidateTest_当布局类型和记录类型都存在但验证失败时应返回失败"() {
        given: "准备测试数据"
        def requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(User.systemUser(TENANT_ID))
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        def context = new ServiceContext(requestContext, "cacheValidate", "cacheValidate")

        def arg = new CacheValidate.Arg(
                objectApiName: "testObject",
                describeVersion: 2L,
                layoutType: "Edit",
                recordType: "default",
                layoutRule: [
                        new LayoutRuleDocument(
                                ruleApiName: "rule1",
                                lastModifiedTime: 123456L
                        )
                ],
                layoutApiName: "testLayout",
                layoutLastModifiedTime: 123456L,
                layoutVersion: 1L,
                detailCacheValid: [
                        new CacheValidate.DetailObjCacheValid(
                                objectApiName: "detailObject",
                                describeVersion: 2L,
                                detailLayoutList: [
                                        new CacheValidate.DetailLayoutRelatedInfo(
                                                layoutApiName: "detailLayout",
                                                layoutLastModifiedTime: 123456L,
                                                recordType: "detailDefault",
                                                layoutRule: [
                                                        new LayoutRuleDocument(
                                                                ruleApiName: "detailRule1",
                                                                lastModifiedTime: 123456L
                                                        )
                                                ]
                                        )
                                ]
                        )
                ]
        )

        def masterDescribe = Mock(IObjectDescribe) {
            getApiName() >> "testObject"
            getVersion() >> 2L
        }

        def detailDescribe = Mock(IObjectDescribe) {
            getApiName() >> "detailObject"
            getVersion() >> 2L
        }

        def objectDescribes = [
                "testObject"  : masterDescribe,
                "detailObject": detailDescribe
        ]

        when: "调用cacheValidate方法"
        def result = objectValidRuleService.cacheValidate(arg, context)

        then: "验证交互和结果"
        1 * describeLogicService.findObjectsWithoutCopy(TENANT_ID, _) >> objectDescribes
        1 * layoutRuleLogicService.layoutRuleValidate(requestContext.user, _) >> true
        1 * recordTypeLogicService.validateLayoutAndRecordType(requestContext.user, objectDescribes, _) >> false
        !result.success
    }
} 