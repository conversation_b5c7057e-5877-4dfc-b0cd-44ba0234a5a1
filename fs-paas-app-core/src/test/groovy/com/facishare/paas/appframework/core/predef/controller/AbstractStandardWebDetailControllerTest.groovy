package com.facishare.paas.appframework.core.predef.controller


import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.RelatedObjectDescribeStructure
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot
import com.facishare.paas.appframework.metadata.layout.PageType
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.ui.layout.ILayout
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class AbstractStandardWebDetailControllerTest extends Specification {
    ControllerContext controllerContext = Mock()
    ServiceFacade serviceFacade = Mock(ServiceFacade)
    InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
    SpringBeanHolder springBeanHolder = Mock(SpringBeanHolder)
    StandardWebDetailController detailController


    def setup() {
        infraServiceFacade.getSpringBeanHolder() >> springBeanHolder
        AppFrameworkConfig.unSupportButtonInternalObject = ["AccountObj"]
        detailController = new StandardWebDetailController(
                "serviceFacade": serviceFacade,
                "controllerContext": controllerContext,
                "infraServiceFacade": infraServiceFacade
        )
        def requestContext = RequestContext.builder()
                .tenantId("74255")
                .user(new User("74255", "1000"))
                .build()
        RequestContextManager.setContext(requestContext)
    }

    def "webDetailLayout getLayout"() {
        given:

//        def arg = new AbstractStandardDetailController.Arg()
        Whitebox.setInternalState(detailController, "arg", arg)
        Whitebox.setInternalState(detailController, "data", new ObjectData())
        Whitebox.setInternalState(detailController, "describe", new ObjectDescribe())
        Whitebox.setInternalState(detailController, "relatedObjects", [])
        Whitebox.setInternalState(detailController, "detailObjects", [])
        Whitebox.setInternalState(detailController, "relatedObjects", [])
        controllerContext.getUser() >> new User("74255", "1000")

        ILayout layout = new Layout()
        when:
        controllerContext.getObjectApiName() >> describeApiName
        serviceFacade.getLayoutWithComponents(_ as User, _ as String, _ as IObjectDescribe, _ as IObjectData, _ as List<RelatedObjectDescribeStructure>, _ as List<RelatedObjectDescribeStructure>, _ as Collection<String>, _ as PageType) >> layout

        serviceFacade.getLayoutWithComponents(_ as User, _ as String, _ as IObjectDescribe, _ as IObjectData, _ as List<RelatedObjectDescribeStructure>, _ as List<RelatedObjectDescribeStructure>, _ as Collection<String>, _ as PageType, _ as boolean, _ as boolean) >> layout;

        infraServiceFacade.isEnableQixinGroup(_ as User, _ as String) >> true
        infraServiceFacade.isHiddenQiXinEntrance(_ as User) >> true
        def res = detailController.getLayout()
        then:
//        res == result
        noExceptionThrown()

        where:
        arg                                        | describeApiName | isEnableQixinGrup || result
        new AbstractStandardDetailController.Arg() | "AccountObj"    | true              || new Layout()
        buildAbstractStandardDetailController()    | ""              | false             || new Layout()
        buildAbstractStandardDetailController()    | "AccountObj"    | false             || new Layout()
    }

    private AbstractStandardDetailController.Arg buildAbstractStandardDetailController() {
        def arg = new AbstractStandardDetailController.Arg()
        arg.setLayoutApiName("Account__c")
        return arg
    }


    def "webDetailLayout getLayout 2"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        arg.setLayoutApiName("layoutApiName")
        Whitebox.setInternalState(detailController, "arg", arg)
        when:
        controllerContext.getObjectApiName() >> "AccountObj"
        serviceFacade.getLayoutByApiNameWithComponents(_ as User, _ as String, _ as IObjectDescribe, _ as IObjectData, _ as List<RelatedObjectDescribeStructure>, _ as List<RelatedObjectDescribeStructure>, _ as boolean) >> new Layout()
        def res = detailController.getLayout()
        then:
        noExceptionThrown()
    }

    def "before"() {
        given:
//        Whitebox.setInternalState(detailController, "describe", new ObjectDescribe())
        def arg = new AbstractStandardDetailController.Arg()
        def info = new RequestContext.BizInfo()
        info.setOtherBizId("CRM")
        arg.setBizInfo(info)
        arg.setMergeSnapshot(true)
        arg.setMasterDataId("2132323123213213214")
        arg.setIncludeLayout(true)
        Whitebox.setInternalState(detailController, "arg", arg)


        IObjectDescribe describe = new ObjectDescribe()
        IFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe()
        masterDetailFieldDescribe.setApiName("field_ssda__c")
        masterDetailFieldDescribe.setDescribeApiName("AccountObj")
        masterDetailFieldDescribe.setTargetApiName("SaleOrderObj")
//        masterDetailFieldDescribe.setIsCreateWhenMasterCreate(true)
        describe.setFieldDescribes([masterDetailFieldDescribe])

        when:
        serviceFacade.findRelatedFields(_, _) >> []
        serviceFacade.findObjectData(_, _, _) >> new ObjectData()
        controllerContext.getUser() >> new User("74255", "1000")
        serviceFacade.filterDescribesWithActionCode(_, _, _) >> [describe]
        serviceFacade.findObjectIncludeMultiField(_, _) >> describe
        serviceFacade.findObjectWithoutCopy(_, _) >> describe
        controllerContext.getMethodName() >> "webDetail"
        infraServiceFacade.findAndMergeObjectDataWithOriginalData(_, _, _,) >> { throw new ObjectDataNotFoundException("") }
        serviceFacade.findRelatedDescribes(_, _) >> [new ObjectDescribe()]
        infraServiceFacade.findAndMergeSnapshot(_, _, _, _, _) >> new ObjectDataSnapshot()
        detailController.before(arg)
        then:
        noExceptionThrown()

    }


    def "doServiceTest"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        def info = new RequestContext.BizInfo()
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("AccountObj")
        info.setOtherBizId("CRM")
        arg.setBizInfo(info)
        arg.setCalculateFormula(true)
        arg.setMergeSnapshot(true)
        arg.setIncludeLayout(true)
        Whitebox.setInternalState(detailController, "arg", arg)
        Whitebox.setInternalState(detailController, "describe", describe)
        Whitebox.setInternalState(detailController, "data", ObjectDataExt.of([:]))
        when:
        controllerContext.getTenantId() >> "74255"
        controllerContext.getUser() >> User.systemUser("74255")
        serviceFacade.getLayoutWithComponents(_, _, _, _, _, _, _, _) >> new Layout()
        detailController.doService(arg)
        then:
        noExceptionThrown()
    }

    def "buildSimpleDescribe test"() {
        given:
        def arg = new AbstractStandardDetailController.Arg()
        def info = new RequestContext.BizInfo()
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("AccountObj")
        info.setOtherBizId("CRM")
        arg.setBizInfo(info)
        arg.setCalculateFormula(true)
        arg.setMergeSnapshot(true)
        arg.setIncludeLayout(true)
        Whitebox.setInternalState(detailController, "arg", arg)
        Whitebox.setInternalState(detailController, "describe", describe)
        Whitebox.setInternalState(detailController, "data", ObjectDataExt.of([:]))

        when:
        detailController
        then:
        noExceptionThrown()
    }

    def "test buildAPLPluginArg"() {
        when:
        def arg = detailController.buildAPLPluginArg("test")
        then:
        noExceptionThrown()
    }


    def "test findRelatedObjects"() {
        given:
         IFieldDescribe fieldDescribe = new TextFieldDescribe()
        when:
        def arg = new AbstractStandardDetailController.Arg()
        Whitebox.setInternalState(detailController,"arg",arg)
        serviceFacade.findRelatedFields(_, _) >> [fieldDescribe]
        detailController.findRelatedObjects()
        then:
        noExceptionThrown()

    }

}
