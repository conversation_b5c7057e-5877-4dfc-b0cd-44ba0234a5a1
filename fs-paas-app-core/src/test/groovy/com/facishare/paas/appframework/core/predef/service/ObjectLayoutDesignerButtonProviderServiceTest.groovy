package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.LayoutButtonExt
import com.facishare.paas.appframework.metadata.button.GetLayoutDesignerButton
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonManager
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonProvider
import com.facishare.paas.metadata.impl.ui.layout.Button
import com.facishare.paas.metadata.ui.layout.IButton
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * ObjectLayoutDesignerButtonProviderService的单元测试
 */
class ObjectLayoutDesignerButtonProviderServiceTest extends Specification {

    ObjectLayoutDesignerButtonProviderService service
    LayoutDesignerButtonManager layoutDesignerButtonManager = Mock(LayoutDesignerButtonManager)

    def setupSpec() {
        // 设置国际化相关的mock
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        service = new ObjectLayoutDesignerButtonProviderService(
                layoutDesignerButtonManager: layoutDesignerButtonManager
        )
    }

    def cleanup() {
        RequestContextManager.removeContext()
    }

    private ServiceContext createServiceContext(String tenantId = "74255", String userId = "1000") {
        def user = new User(tenantId, userId)
        def requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build()
        RequestContextManager.setContext(requestContext)
        return new ServiceContext(requestContext, "getButtons", "LayoutDesignerButtonProvider")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法的正常场景，获取设计器按钮成功
     */
    @Unroll
    def "getLayoutDesignerButton_Success"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        
        // 创建按钮数据
        def buttonData = [
            "api_name": "edit",
            "name": "edit",
            "label": "编辑",
            "order": 10
        ]
        
        def arg = new GetLayoutDesignerButton.Arg(
            describeApiName: describeApiName,
            buttonList: [buttonData]
        )
        def context = createServiceContext(tenantId, userId)
        
        // 模拟按钮提供者
        def buttonProvider = Mock(LayoutDesignerButtonProvider)
        
        when: "调用获取按钮方法"
        layoutDesignerButtonManager.getLocalProvider(describeApiName) >> buttonProvider
        buttonProvider.getButtons(_, _) >> { args ->
            // 模拟按钮处理，返回相同的按钮列表
            return args[0]
        }
        
        def result = service.getLayoutDesignerButton(arg, context)
        
        then: "验证结果"
        result != null
        result.result != null
        result.result.size() == 1
        result.result[0].api_name == "edit"
        result.result[0].name == "edit"
        result.result[0].label == "编辑"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法当按钮列表为空时的场景
     */
    @Unroll
    def "getLayoutDesignerButton_EmptyButtonList"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        
        def arg = new GetLayoutDesignerButton.Arg(
            describeApiName: describeApiName,
            buttonList: []
        )
        def context = createServiceContext(tenantId, userId)
        
        def buttonProvider = Mock(LayoutDesignerButtonProvider)
        
        when: "调用获取按钮方法"
        layoutDesignerButtonManager.getLocalProvider(describeApiName) >> buttonProvider
        buttonProvider.getButtons(_, _) >> { args ->
            return []
        }
        
        def result = service.getLayoutDesignerButton(arg, context)
        
        then: "验证结果"
        result != null
        result.result != null
        result.result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法当按钮列表为null时的场景
     */
    @Unroll
    def "getLayoutDesignerButton_NullButtonList"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        
        def arg = new GetLayoutDesignerButton.Arg(
            describeApiName: describeApiName,
            buttonList: null
        )
        def context = createServiceContext(tenantId, userId)
        
        def buttonProvider = Mock(LayoutDesignerButtonProvider)
        
        when: "调用获取按钮方法"
        layoutDesignerButtonManager.getLocalProvider(describeApiName) >> buttonProvider
        buttonProvider.getButtons(_, _) >> { args ->
            return []
        }
        
        def result = service.getLayoutDesignerButton(arg, context)
        
        then: "验证结果"
        result != null
        result.result != null
        result.result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法当按钮提供者修改了按钮属性的场景
     */
    @Unroll
    def "getLayoutDesignerButton_ButtonPropertiesModified"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        
        def buttonData = [
            "api_name": "edit",
            "name": "edit",
            "label": "编辑",
            "order": 10
        ]
        
        def arg = new GetLayoutDesignerButton.Arg(
            describeApiName: describeApiName,
            buttonList: [buttonData]
        )
        def context = createServiceContext(tenantId, userId)
        
        def buttonProvider = Mock(LayoutDesignerButtonProvider)
        
        when: "调用获取按钮方法"
        layoutDesignerButtonManager.getLocalProvider(describeApiName) >> buttonProvider
        buttonProvider.getButtons(_, _) >> { args ->
            List<IButton> buttons = args[0]
            // 修改按钮属性
            buttons.each { button ->
                button.set("label", "修改后的编辑")
                button.set("disabled", true)
            }
            return buttons
        }
        
        def result = service.getLayoutDesignerButton(arg, context)
        
        then: "验证结果"
        result != null
        result.result != null
        result.result.size() == 1
        result.result[0].api_name == "edit"
        result.result[0].name == "edit"
        result.result[0].label == "修改后的编辑"
        result.result[0].disabled == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法当按钮提供者过滤了某些按钮的场景
     */
    @Unroll
    def "getLayoutDesignerButton_ButtonFiltered"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        
        def buttonData1 = [
            "api_name": "edit",
            "name": "edit",
            "label": "编辑",
            "order": 10
        ]
        
        def buttonData2 = [
            "api_name": "delete",
            "name": "delete",
            "label": "删除",
            "order": 20
        ]
        
        def arg = new GetLayoutDesignerButton.Arg(
            describeApiName: describeApiName,
            buttonList: [buttonData1, buttonData2]
        )
        def context = createServiceContext(tenantId, userId)
        
        def buttonProvider = Mock(LayoutDesignerButtonProvider)
        
        when: "调用获取按钮方法"
        layoutDesignerButtonManager.getLocalProvider(describeApiName) >> buttonProvider
        buttonProvider.getButtons(_, _) >> { args ->
            List<IButton> buttons = args[0]
            // 只保留edit按钮
            return buttons.findAll { button -> button.getName() == "edit" }
        }
        
        def result = service.getLayoutDesignerButton(arg, context)
        
        then: "验证结果"
        result != null
        result.result != null
        result.result.size() == 1
        result.result[0].name == "edit"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLayoutDesignerButton方法当按钮提供者添加了新按钮的场景
     */
    @Unroll
    def "getLayoutDesignerButton_ButtonAdded"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def userId = "1000"
        def describeApiName = "AccountObj"
        
        def buttonData = [
            "api_name": "edit",
            "name": "edit",
            "label": "编辑",
            "order": 10
        ]
        
        def arg = new GetLayoutDesignerButton.Arg(
            describeApiName: describeApiName,
            buttonList: [buttonData]
        )
        def context = createServiceContext(tenantId, userId)
        
        def buttonProvider = Mock(LayoutDesignerButtonProvider)
        
        when: "调用获取按钮方法"
        layoutDesignerButtonManager.getLocalProvider(describeApiName) >> buttonProvider
        buttonProvider.getButtons(_, _) >> { args ->
            List<IButton> buttons = args[0]
            // 添加一个新按钮
            def newButton = new Button([
                "api_name": "custom",
                "name": "custom",
                "label": "自定义按钮",
                "order": 30
            ])
            buttons.add(newButton)
            return buttons
        }
        
        def result = service.getLayoutDesignerButton(arg, context)
        
        then: "验证结果"
        result != null
        result.result != null
        result.result.size() == 2
        result.result.any { it.name == "edit" }
        result.result.any { it.name == "custom" }
    }
} 