package com.facishare.paas.appframework.metadata

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class ButtonUsePageTypeTest extends Specification {

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
        i18nServiceImpl.get(*_) >> "test"
    }

    def "test ButtonUsePageType initialization"() {
        when:
        def detailPage = ButtonUsePageType.Detail
        def createPage = ButtonUsePageType.Create
        def editPage = ButtonUsePageType.Edit
        def dataListPage = ButtonUsePageType.DataList
        def relatedListPage = ButtonUsePageType.RelatedList

        then:
        detailPage.getId() == "detail"
        createPage.getId() == "create"
        editPage.getId() == "edit"
        dataListPage.getId() == "list"
        relatedListPage.getId() == "related_list"
        noExceptionThrown()
    }

    def "test ButtonUsePageType of method"() {
        when:
        def page = ButtonUsePageType.of(pageType)

        then:
        page == expectedPage
        noExceptionThrown()

        where:
        pageType         | expectedPage
        "detail"         | ButtonUsePageType.Detail
        "create"         | ButtonUsePageType.Create
        "edit"          | ButtonUsePageType.Edit
        "list"          | ButtonUsePageType.DataList
        "related_list"   | ButtonUsePageType.RelatedList
        "list_batch"     | ButtonUsePageType.ListBatch
        "list_normal"    | ButtonUsePageType.ListNormal
        "list_component" | ButtonUsePageType.ListComponent
        "recycle_bin"    | ButtonUsePageType.RecycleBin
    }
} 