package com.facishare;

import org.junit.Test;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/3/4 2:54 下午
 */
public class PatternTest {
    public static final Pattern SPECIAL_CHAR_REGX = Pattern.compile("[\\u0000-\\u001F\\u007F-\\u00A0]");

    @Test
    public void test() {
        String str1 = "LAW&TRUST INTERNATIONAL";
        System.out.println(Arrays.toString(str1.getBytes()));
        String str2 = "LAW&TRUST INTERNATIONAL";
        System.out.println(Arrays.toString(str2.getBytes()));


        String[] s = " ".chars().mapToObj(Integer::toHexString).toArray(String[]::new);
        System.out.println(Arrays.toString(s));


        Matcher m = SPECIAL_CHAR_REGX.matcher("  ");
        System.out.println(m.replaceAll(""));
    }
}
