package com.facishare.paas.appframework.core.model;

import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.coordination.FeedService;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.StageThrusterService;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ActionFilterResult;
import com.facishare.paas.appframework.payment.PaymentService;
import com.facishare.paas.appframework.privilege.FieldPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;


/**
 * 服务门面接口
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
public interface ServiceFacade extends MetaDataService, FunctionPrivilegeService, LogService, ApprovalFlowService,
        CustomSceneService, LicenseService, RecordTypeLogicService, UserRoleInfoService, OrgService, FeedService,
        PaymentService, LayoutLogicService, DescribeLogicService, ButtonLogicService, CustomButtonService,
        FieldPrivilegeService, DuplicatedSearchService, StageThrusterService, UniqueRuleLogicService,
        ObjectDataFlowService, DuplicatedSearchDataService,
        MetaDataGlobalService, ConfigService, DefObjLifeStatusService {

    FunctionLogicService getFunctionLogicService();

    MultiCurrencyLogicService getMultiCurrencyLogicService();

    ExportTaskHookService getExportTaskHookService();

    <T> void registerService(Class<T> clazz, T service);

    <T> T getService(Class<T> clazz);

    void checkActionByLockStatusAndLifeStatus(IObjectData objectData, ObjectAction actionEnum, User user, String apiName, boolean isCascadeDealDetailObj);

    void checkActionByLockStatusAndLifeStatus(List<IObjectData> objectDataList, ObjectAction actionEnum, User user, String apiName, boolean isDetailObjCheckMode);

    ActionFilterResult validateActionByLockStatusAndLifeStatus(List<IObjectData> objectDataList,
                                                               ObjectAction actionEnum, User user, String apiName, boolean isDetailObjCheckMode);

    <A, T> T triggerAction(ActionContext actionContext, A a, Class<T> resultType);

    <A, T> T triggerRemoteAction(ActionContext actionContext, A a, Class<T> resultType);

    <A, T> T triggerController(ControllerContext controllerContext, A a, Class<T> resultType);

    String getEAByEI(String ei);

    void getNPathsWithoutPermission(String ea, String userId, List<V5FileInfo> files);

    <T> T getBean(Class<T> clazz);

    List<FileStoreService.PathPair> saveImageFromTempFilesAndNames(String tenantId, String userId, List<FileStoreService.PathOriginNames> pathOriginNames, String fileExt);
}
