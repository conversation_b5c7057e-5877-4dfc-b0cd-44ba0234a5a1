package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.google.common.base.Strings;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务处理器的执行器，用于执行指定的业务处理器
 * Created by zhouwr on 2023/1/6.
 */
@Slf4j
@Builder
public class HandlerExecutor {
    private HandlerManager handlerManager;
    private String objectApiName;
    private String interfaceCode;
    private String handlerType;
    private String industryCode;
    private List<SimpleHandlerDescribe> handlerDescribeList;
    private HandlerContext handlerContext;
    private HandlerFunctions.BuildArgFunction buildArgFunction;
    private HandlerFunctions.ProcessResultFunction processResultFunction;
    private Map<String, HandlerFunctions.ExecuteFunction> executeFunctions;

    public void execute() {
        //过滤不需要执行的业务处理器
        handlerDescribeList = filterHandler();
        if (CollectionUtils.empty(handlerDescribeList)) {
            log.debug("no handler to execute,type:{}", handlerType);
            return;
        }
        long startTime = System.currentTimeMillis();
        StopWatch totalSW = StopWatch.create("execute_handler_" + handlerType);
        try {
            handlerDescribeList.forEach(handlerDescribe -> {
                long stepStartTime = System.currentTimeMillis();
                String handlerApiName = handlerDescribe.getApiName();
                StopWatch stopWatch = StopWatch.create("execute_" + handlerApiName);
                Handler handler;
                Handler.Arg arg = null;
                try {
                    //构造业务处理器的执行参数
                    arg = buildArgFunction.apply(handlerDescribe);
                    stopWatch.lap("buildArg");
                    //成功触发了审批流，跳过continue_after_approval_success不等于true的Handler
                    if (skipHandler(handlerDescribe, arg)) {
                        log.warn("skip handler:{}", handlerDescribe.getApiName());
                        return;
                    }
                    //虚拟Handler直接使用ExecuteFunction执行
                    if (CollectionUtils.notEmpty(executeFunctions) && executeFunctions.containsKey(handlerApiName)) {
                        executeFunctions.get(handlerApiName).apply();
                        log.debug("execute handler success:{},cost:{}", handlerApiName, System.currentTimeMillis() - stepStartTime);
                        stopWatch.lap("execute");
                        totalSW.lap(handlerApiName);
                        return;
                    }
                    //查找业务处理器实现类
                    handler = handlerManager.getHandler(handlerDescribe);
                    stopWatch.lap("getHandler");
                    if (Objects.isNull(handler)) {
                        log.warn("Handler not found:{}", handlerDescribe.getApiName());
                        return;
                    }
                    //执行业务处理器
                    Handler.Result result = handler.handle(handlerContext, arg);
                    stopWatch.lap("execute");
                    //处理业务处理器返回结果
                    if (Objects.nonNull(processResultFunction)) {
                        processResultFunction.apply(handlerContext, arg, result);
                        stopWatch.lap("processResult");
                    }
                    log.debug("execute handler success:{},cost:{}", handlerApiName, System.currentTimeMillis() - stepStartTime);
                    totalSW.lap(handlerApiName);
                } catch (Exception e) {
                    if (e instanceof AppBusinessException) {
                        log.warn("execute handler failed,handlerApiName:{},arg:{} ", handlerApiName, JacksonUtils.toJson(arg), e);
                    } else {
                        log.error("execute handler error,handlerApiName:{},arg:{} ", handlerApiName, JacksonUtils.toJson(arg), e);
                    }
                    //finally类型的Handler不抛出异常
                    if (!Objects.equals(HandlerType.FINALLY.getCode(), handlerDescribe.getHandlerType())) {
                        throw e;
                    }
                } finally {
                    stopWatch.logSlow(100);
                }
            });
            log.info("execute handler end,code:{},type:{},num:{},cost:{}", interfaceCode, handlerType,
                    handlerDescribeList.size(), System.currentTimeMillis() - startTime);
        } finally {
            totalSW.logSlow(300);
        }
    }

    private List<SimpleHandlerDescribe> filterHandler() {
        if (CollectionUtils.empty(handlerDescribeList)) {
            return Collections.emptyList();
        }
        return handlerDescribeList.stream().filter(x -> {
            if (!Objects.equals(interfaceCode, x.getInterfaceCode())) {
                return false;
            }
            if (!Objects.equals(handlerType, x.getHandlerType())) {
                return false;
            }
            //如果接口传了industryCode，则返回没有配置industryCode或配置的industryCode和传进来的industryCode相同的Handler,
            //否则只能返回没有配置industryCode的Handler
            if (!Strings.isNullOrEmpty(industryCode)) {
                return Strings.isNullOrEmpty(x.getIndustryCode()) || Objects.equals(industryCode, x.getIndustryCode());
            } else {
                return Strings.isNullOrEmpty(x.getIndustryCode());
            }
        }).collect(Collectors.toList());
    }

    private boolean skipHandler(SimpleHandlerDescribe handlerDescribe, Handler.Arg arg) {
        if (Objects.isNull(arg)) {
            return true;
        }
        if (HandlerType.FINALLY.getCode().equals(handlerDescribe.getHandlerType())) {
            return false;
        }
        if (handlerDescribe.isContinueAfterApprovalSuccess()) {
            return false;
        }
        if (arg instanceof ActionHandler.Arg) {
            ActionHandler.Arg actionHandlerArg = (ActionHandler.Arg) arg;
            return actionHandlerArg.isTriggerApprovalFlowSuccess();
        }
        return false;
    }
}
