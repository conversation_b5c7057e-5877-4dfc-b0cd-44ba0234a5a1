package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.function.plugin.FunctionPluginConfig;
import com.facishare.paas.appframework.function.plugin.FunctionPluginService;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhaooju on 2022/7/1
 */
@Component
@PluginProvider(apiName = "APLControllerPlugin")
public class APLControllerPluginImpl implements APLControllerPlugin {
    @Autowired
    private FunctionPluginService functionPluginService;

    @Override
    public Result before(PluginContext context, Arg arg) {
        FunctionPluginService.FunctionPluginContext<Result> functionPluginContext = FunctionPluginService.FunctionPluginContext.<Result>builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(arg, context))
                .resultProcess(jsonString -> buildResult(arg, jsonString))
                .build();
        String functionApiName = getFunctionApiName(context);
        return functionPluginService.executeFuncMethod(functionPluginContext, functionApiName, BEFORE);
    }

    @Override
    public Result after(PluginContext context, Arg arg) {
        FunctionPluginService.FunctionPluginContext<Result> functionPluginContext = FunctionPluginService.FunctionPluginContext.<Result>builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(arg, context))
                .resultProcess(jsonString -> buildResult(arg, jsonString))
                .build();
        String functionApiName = getFunctionApiName(context);
        return functionPluginService.executeFuncMethod(functionPluginContext, functionApiName, AFTER);
    }

    private String getFunctionApiName(PluginContext context) {
        FunctionPluginConfig instance = FunctionPluginConfig.getInstance();
        return instance.getFunctionApiName(context.getTenantId(), context.getObjectApiName(), context.getModuleCode(), context.getModuleType());
    }

    private List<String> buildArg(Arg arg, PluginContext context) {
        List<String> result = Lists.newArrayList();
        arg.setCode(context.getModuleCode());
        arg.setType(context.getModuleType());
        String json = JacksonUtils.toJson(arg);
        if (!Strings.isNullOrEmpty(json)) {
            result.add(json);
        }
        return result;
    }

    private Result buildResult(Arg arg, String jsonString) {
        JsonNode jsonNode = JacksonUtils.readTree(jsonString);
        if (Objects.isNull(jsonNode)) {
            return null;
        }
        Object controllerArg = getObjectFromNode(jsonNode, "controllerArg", arg.argType());
        Object controllerResult = getObjectFromNode(jsonNode, "controllerResult", arg.resultType());
        Boolean supportAfter = getObjectFromNode(jsonNode, "supportAfter", Boolean.class);
        return new Result(controllerArg, controllerResult, supportAfter);
    }

    private <T> T getObjectFromNode(JsonNode jsonNode, String fieldName, Class<T> type) {
        if (Objects.isNull(type)) {
            return null;
        }
        JsonNode controllerArgNode = jsonNode.get(fieldName);
        return JacksonUtils.convertValue(controllerArgNode, type);
    }


}
