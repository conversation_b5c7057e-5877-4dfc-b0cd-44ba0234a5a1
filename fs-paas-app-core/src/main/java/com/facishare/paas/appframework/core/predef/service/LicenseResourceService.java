package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.license.ApplicationLicense;
import com.facishare.paas.appframework.core.predef.service.dto.license.QueryLicenseFeature;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.facishare.paas.appframework.license.util.ModulePara.HTML_RICH_TEXT_FIELD_LIMIT;
import static com.facishare.paas.appframework.license.util.ModulePara.RICH_TEXT_FIELD_LIMIT;

/**
 * <AUTHOR>
 * @date 2019/1/9 3:55 PM
 * 各个业务模块许可证服务
 */
@ServiceModule("license")
@Component
@Slf4j
public class LicenseResourceService {
    @Autowired
    private LicenseService licenseService;

    @ServiceMethod("get")
    public ApplicationLicense.Result getQuota(ApplicationLicense.Arg arg, ServiceContext serviceContext) {
        log.info("get license quota, user:{}, app:{}", serviceContext.getUser(), arg.getItems());
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(serviceContext.getUser())
                .build()
                .init(arg.getItems());
        Map<String, Map<String, Integer>> quotaMap = tenantLicenseInfo.getQuotaMap();
        if (StringUtils.isNotEmpty(arg.getDescribeApiName())) {
            Map<String, Integer> udobjQuotaMap = CollectionUtils.nullToEmpty(quotaMap.get(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
            String richTextFieldLimitParaKey = RICH_TEXT_FIELD_LIMIT.getParaKey();
            udobjQuotaMap.put(richTextFieldLimitParaKey, AppFrameworkConfig.getRichTextFieldMaxCount(arg.getDescribeApiName(), udobjQuotaMap.get(richTextFieldLimitParaKey)));
            String htmlRichTextFieldLimitParaKey = HTML_RICH_TEXT_FIELD_LIMIT.getParaKey();
            udobjQuotaMap.put(htmlRichTextFieldLimitParaKey, AppFrameworkConfig.getHtmlRichTextFieldMaxCount(arg.getDescribeApiName(), udobjQuotaMap.get(htmlRichTextFieldLimitParaKey)));

        }
        return ApplicationLicense.Result.builder()
                .quota(quotaMap)
                .build();
    }

    @ServiceMethod("query_feature")
    public QueryLicenseFeature.Result queryLicenseFeature(QueryLicenseFeature.Arg arg, ServiceContext serviceContext) {
        log.info("queryLicenseFeature, user:{}, app:{}", serviceContext.getUser(), arg.getItems());
        Map<String, Boolean> result = licenseService.existModule(serviceContext.getTenantId(), arg.getItems());
        return QueryLicenseFeature.Result.builder()
                .feature(result)
                .build();
    }
}
