package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptInfo;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerFunctions;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.predef.controller.BaseListController.Result;
import com.facishare.paas.appframework.core.predef.handler.list.BaseListHandler;
import com.facishare.paas.appframework.core.predef.service.dto.tag.FindAllTagByBulkDataId;
import com.facishare.paas.appframework.core.util.RecordTypeUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.TableComponentRender;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.IViewComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.NewTableComponentExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.publicobject.dto.ConnectedEnterpriseDTO;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.describe.AreaFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.support.JsonFieldHandler;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.LIST_SELECT_PARTLY_GRAY_EI;

/**
 * Created by zhouwr on 2017/10/17
 */
public abstract class BaseListController<A> extends PreDefineController<A, Result> {

    protected final int MAX_QUERY_LIMIT = AppFrameworkConfig.getMaxQueryLimit();

    protected ObjectDescribeExt objectDescribe;
    private Map<String, Integer> describeVersionMap;
    private ButtonInfo buttonInfo;
    protected SearchTemplateQuery query;
    protected QueryResult<IObjectData> queryResult;
    protected List<ILayout> listLayouts;
    protected ILayout layout;
    private List<IUdefButton> udefButtonList;

    private List<FindAllTagByBulkDataId.DataTag> dataTagInfo;
    //    private Map<String, String> summaryData;
    private final Map<String, UserInfo> userInfoMap = Maps.newHashMap();
    private final Map<String, DeptInfo> deptInfoMap = Maps.newHashMap();
    private final Map<String, ConnectedEnterpriseDTO.Helper> dataVisibilityRangeMap = Maps.newHashMap();

    protected ISearchTemplate defaultTemplate;

    protected String sceneApiName;
    protected int limit;
    protected int offset;
    protected List<String> idSort;

    private DescribeExtra describeExtra;

    private ObjectDescribeDocument objectDescribeExt;
    private MaskFieldLogicService maskFieldLogicService;

    protected AtomicBoolean skipFindDataByFilter = new AtomicBoolean(false);
    protected Boolean showTag;

    protected Map<String, String> recordTypeMapping;

    protected abstract String getSearchQueryInfo();

    protected abstract String getSearchTemplateId();

    protected abstract String getSearchTemplateApiName();

    protected abstract String getSearchTemplateType();

    protected ISearchTemplate getDefaultTemplate() {
        return null;
    }

    protected boolean serializeEmpty() {
        return true;
    }

    protected boolean extractExtendInfo() {
        return false;
    }

    protected Map<String, Object> getRelatedListComponent() {
        return null;
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    protected void init() {
        doGetObjectDescribe();
        //非灰度企业才直接修改describe，否则通过describeExt下发
        if (!AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), objectDescribe.getApiName())) {
            //前端适配业务类型字段，在option中补充value字段
            RecordTypeUtil.extendRecordTypeFieldWithValue(objectDescribe);
        }
        describeVersionMap = getDescribeVersionMap();
        stopWatch.lap("findObject");

        if (needButtonInfo()) {
            udefButtonList = serviceFacade.findCustomButtonByUsePage(objectDescribe.getApiName(), getUsePageType(),
                    controllerContext.getUser());
            stopWatch.lap("findCustomButton");
        }
        defaultTemplate = getDefaultTemplate();
        stopWatch.lap("getDefaultTemplate");
    }

    protected void checkQueryLimit(SearchTemplateQuery query) {
        if (query.getLimit() <= 0 || query.getLimit() > MAX_QUERY_LIMIT) {
            log.warn("query limit:{} exceed max value:{}", query.getLimit(), MAX_QUERY_LIMIT);
            query.setLimit(MAX_QUERY_LIMIT);
        }
    }

    protected void checkQueryLimit(Query query) {
        if (query.getLimit() <= 0 || query.getLimit() > MAX_QUERY_LIMIT) {
            log.warn("query limit:{} exceed max value:{}", query.getLimit(), MAX_QUERY_LIMIT);
            query.setLimit(MAX_QUERY_LIMIT);
        }
    }

    protected abstract Map<String, Integer> getDescribeVersionMap();

    protected abstract String getUsePageType();

    protected boolean isIgnoreSceneFilter() {
        return false;
    }

    protected boolean isGetDataOnly() {
        return false;
    }

    protected boolean isIncludeInvalidData() {
        return false;
    }

    protected Boolean getFindExplicitTotalNum() {
        return null;
    }

    protected boolean isIgnoreSceneRecordType() {
        return false;
    }

    protected Boolean getNeedReturnCountNum() {
        return null;
    }

    protected boolean isRelatedPage() {
        return false;
    }

    @Override
    protected void doInit() {
        init();
        if (!serializeEmpty()) {
            RequestUtil.setSerializeEmptyFalse();
        }
        initContextCache();
        initLayoutContext();
        initRequestContext();
        stopWatch.lap("initLayoutContext");
    }

    @Override
    protected void before(A arg) {
        super.before(arg);
        validate();
    }

    protected void validate() {
        if (StringUtils.isEmpty(getSearchQueryInfo())) {
            throw new ValidateException("searchQueryInfo is empty");
        }
    }

    protected void initRequestContext() {
        RequestContext context = RequestContextManager.getContext();
        context.setApiResource(controllerContext.getMethodName());
    }

    protected void initLayoutContext() {

    }

    @Override
    protected Result doService(A arg) {
        doGetSearchTemplateQuery();
        doFindData();
        doFindOtherInfo();
        return doBuildResult();
    }

    private Result doBuildResult() {
        return buildResult(listLayouts, query, queryResult);
    }

    private void doFindOtherInfo() {
        // mobile,H5,小程序请求,必须要查询摘要布局
        if (needLayout(query) || RequestUtil.isMobileOrH5Request()) {
            listLayouts = findMobileLayouts();
            stopWatch.lap("findMobileLayouts");

            layout = findLayout();
            stopWatch.lap("findLayout");

            if (needRecordTypeMapping()) {
                recordTypeMapping = findRecordTypeMapping();
            }
        }

        if (needButtonInfo()) {
            buttonInfo = getButtonInfo(queryResult);
            stopWatch.lap("getButtonInfo");
        }

        if (needTagInfo()) {
            dataTagInfo = queryTagInfo(queryResult);
            stopWatch.lap("queryTagInfo");
        }

        if (needDescribe(query)) {
            objectDescribeExt = buildDescribeExt(query);
            stopWatch.lap("buildDescribeExt");
        }
    }

    private void doFindData() {
        beforeQueryData(query);
        stopWatch.lap("beforeQueryData");

        queryResult = getQueryResult(query);
    }

    private void doGetSearchTemplateQuery() {
        query = getSearchTemplateQuery();
        stopWatch.lap("buildSearchTemplateQuery");
    }

    private List<FindAllTagByBulkDataId.DataTag> queryTagInfo(QueryResult<IObjectData> queryResult) {
        Set<String> dataIds = queryResult.getData().stream().map(IObjectData::getId).collect(Collectors.toSet());
        List<DataAndSubTag> list = infraServiceFacade.findAllTagByBulkDataId(objectDescribe.getApiName(), dataIds, controllerContext.getUser());
        return FindAllTagByBulkDataId.DataTag.fromDataAndSubTagList(list);
    }

    protected boolean needTagInfo() {
        if ((!RequestUtil.isMobileDeviceRequest() && BooleanUtils.isTrue(showTag)) || cardComponentHasTag()) {
            return true;
        }
        if (layout == null) {
            return false;
        }
        return LayoutExt.of(layout).needTagInfo();
    }

    private boolean cardComponentHasTag() {
        if (!getListComponent().isPresent()) {
            return false;
        }
        ListComponentExt listComponentExt = getListComponent().get();
        Optional<IViewComponentInfo> viewInfo = listComponentExt.getViewInfoByName(IViewComponentInfo.CARD_VIEW);
        if (!viewInfo.isPresent()) {
            return false;
        }
        IViewComponentInfo cardView = viewInfo.get();
        Map cardLayout = cardView.get("card_layout", Map.class);
        if (Objects.isNull(cardLayout)) {
            return false;
        }
        NewTableComponentExt newTableComponentExt = new NewTableComponentExt(cardLayout);
        return newTableComponentExt.isShowTag();
    }

    private void initContextCache() {
        ContextCacheUtil.openContextCache();
        //-10000系统用户不需要初始化
        if (!controllerContext.getUser().isSupperAdmin()) {
            //功能权限
            serviceFacade.queryAndCacheFuncPrivilege(controllerContext.getUser(), controllerContext.getObjectApiName(), getObjectActionCodeMap());
            stopWatch.lap("queryAndCacheFuncPrivilege");
        }
    }

    //业务方自定义的需要校验功能权限的code
    protected Map<String, Set<String>> getCustomObjectActionCodeMap() {
        return Maps.newHashMap();
    }

    //需要初始化的功能权限列表
    private Map<String, Set<String>> getObjectActionCodeMap() {
        Map<String, Set<String>> codeMap = Maps.newHashMap();

        //通用按钮
        Set<String> actionCodes = Sets.newHashSet(CollectionUtils.nullToEmpty(getFuncPrivilegeCodes()));
        ComponentActions listActions = getComponentActions();
        actionCodes.addAll(listActions.getActionCodes());

        //自定义按钮
        if (CollectionUtils.notEmpty(udefButtonList)) {
            List<String> buttonActions = udefButtonList.stream().map(x -> {
                ButtonExt buttonExt = ButtonExt.of(x);
                String actionCode = buttonExt.toButton().getAction();
                buttonExt.setAction(actionCode);
                return actionCode;
            }).collect(Collectors.toList());
            actionCodes.addAll(buttonActions);
        }

        codeMap.put(objectDescribe.getApiName(), actionCodes);

        //业务方自定义的需要校验功能权限的code
        Map<String, Set<String>> customObjectActionCodeMap = getCustomObjectActionCodeMap();
        if (CollectionUtils.notEmpty(customObjectActionCodeMap)) {
            customObjectActionCodeMap.forEach((k, v) -> {
                codeMap.putIfAbsent(k, Sets.newHashSet());
                codeMap.get(k).addAll(v);
            });
        }

        return codeMap;
    }

    private ComponentActions getComponentActions() {
        if (isRelatedPage()) {
            return ComponentActions.RELATED_OBJECT;
        }
        return ComponentActions.LIST_PAGE_HEADER;
    }

    private SearchTemplateQuery getSearchTemplateQuery() {
        SearchTemplateQuery query;
        if (isSupportOrFilter()) {
            query = (SearchTemplateQuery) defineQuery().toSearchTemplateQuery();
            validateQuery(query);
        } else {
            query = buildSearchTemplateQuery();
        }
        SearchTemplateQuery searchTemplateQuery = customSearchTemplate(query);
        SearchTemplateQueryExt.of(searchTemplateQuery).validateWheresAndFilters(controllerContext.getUser(), objectDescribe);
        if (AppFrameworkConfig.objectMultiLangGray(controllerContext.getTenantId(), objectDescribe.getApiName())
                && AppFrameworkConfig.isSupportListQueryByLangObj(objectDescribe.getApiName())) {
            SearchTemplateQueryExt.of(searchTemplateQuery).setSearchLang(I18N.getContext().getLanguage());
        }
        if (isRecentVisitSceneReOrder(searchTemplateQuery)) {
            limit = searchTemplateQuery.getLimit();
            offset = searchTemplateQuery.getOffset();
            searchTemplateQuery.setLimit(100);
            searchTemplateQuery.setOffset(0);
        }
        return searchTemplateQuery;
    }

    private boolean isSupportOrFilter() {
        String searchQueryInfo = getSearchQueryInfo();
        if (notSupportOrFilterWhenHasPattern(searchQueryInfo)) {
            return false;
        }
        return noNeedCheckObjectOfCustomObjectListFiltersOrAppLicense(controllerContext.getObjectApiName())
                || serviceFacade.isSupportOrFilter(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    private boolean notSupportOrFilterWhenHasPattern(String searchQueryInfo) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_SUPPORT_OR_FILTER_WHEN_HAS_PATTERN_GRAY, controllerContext.getTenantId())
                && StringUtils.isNotBlank(searchQueryInfo)
                && StringUtils.isNotBlank(SearchTemplateQuery.fromJsonString(searchQueryInfo).getPattern());
    }

    /**
     * 默认支持筛选支持或,不需要校验license的对象
     *
     * @param objectApiName
     * @return
     */
    protected boolean noNeedCheckObjectOfCustomObjectListFiltersOrAppLicense(String objectApiName) {
        return false;
    }

    /**
     * 业务自定义 SearchTemplateQuery
     *
     * @param searchQuery
     * @return
     */
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        return searchQuery;
    }

    private void validateQuery(SearchTemplateQuery query) {
        if (!SearchTemplateQueryExt.validateQueryPattern(query)) {
            log.warn("defineQuery fail, ei:{}, objectApiName:{}, SearchTemplateQuery:{}",
                    controllerContext.getTenantId(), controllerContext.getObjectApiName(), query.toJsonString());
        }
    }

    protected void fillCountryAreaLabel() {
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return;
        }
        serviceFacade.fillCountryAreaLabel(objectDescribe, queryResult.getData(), controllerContext.getUser());
    }

    protected ButtonInfo getButtonInfo(QueryResult<IObjectData> queryResult) {
        StopWatch sw = StopWatch.create("getButtonInfo");
        try {
            Map<String, List<String>> buttonMap = Maps.newHashMap();

            Set<String> readonlyFields = serviceFacade.getReadonlyFields(controllerContext.getUser(), objectDescribe.getApiName());
            sw.lap("getReadonlyFields");
            Map<String, List<IUdefButton>> iButtonMap = serviceFacade.filterListButton(udefButtonList, objectDescribe,
                    queryResult.getData(), controllerContext.getUser(), getUsePageType());
            sw.lap("filterListButton");
            Set<IUdefButton> buttonSet = handleButtons(buttonMap, iButtonMap);
            sw.lap("handleButtons");
            Map<String, IObjectFieldExtra> fieldExtraMap = getStringIObjectFieldExtraMap();
            sw.lap("findDescribeExtra");
            buttonSet.stream().map(ButtonExt::of)
                    .forEach(buttonExt -> buttonExt.handleButtonParam(controllerContext.getUser(), null, objectDescribe, fieldExtraMap, readonlyFields));
            sw.lap("handleButtonParam");
            return ButtonInfo.builder()
                    .buttons(ButtonDocument.ofList(buttonSet))
                    .buttonMap(buttonMap)
                    .build();
        } finally {
            sw.logSlow(100);
        }
    }

    private Map<String, IObjectFieldExtra> getStringIObjectFieldExtraMap() {
        return Optional.ofNullable(getDescribeExtra())
                .map(it -> it.getDescribeExtra(objectDescribe.getApiName()))
                .orElseGet(Maps::newHashMap);
    }

    private Set<IUdefButton> handleButtons(Map<String, List<String>> buttonMap, Map<String, List<IUdefButton>> iButtonMap) {
        ListComponentExt listComponentExt = null;
        if (CollectionUtils.notEmpty(getRelatedListComponent()) && getRelatedListComponent().containsKey(ListComponentExt.SCENE_INFO)) {
            listComponentExt = ListComponentExt.of(getRelatedListComponent())
                    .fillSceneInfoPageType(IComponentInfo.PAGE_TYPE_RELATED)
                    .fillButtonInfoPageType(IComponentInfo.PAGE_TYPE_RELATED);
        } else if (isGrayListLayout()) {
            listComponentExt = getListComponent().orElse(null);
        }

        Set<IUdefButton> buttonSet = Sets.newHashSet();
        String pageType = isRelatedPage() ? IComponentInfo.PAGE_TYPE_RELATED : IComponentInfo.PAGE_TYPE_LIST;
        for (Map.Entry<String, List<IUdefButton>> entry : iButtonMap.entrySet()) {
            // 根据 layout 中的配置，来过滤排序列表页单条按钮
            List<IUdefButton> buttonList = entry.getValue();
            if (listComponentExt != null) {
                buttonList = listComponentExt.filterUdefButtonsByUsePage(entry.getValue(), ButtonUsePageType.DataList, pageType);
            }
            buttonMap.put(entry.getKey(), buttonList.stream().map(IUdefButton::getApiName).distinct().collect(Collectors.toList()));
            buttonSet.addAll(buttonList);
        }
        // 循环中会修改按钮，导致按钮的hashcode变化，set中可能会有重复数据。这里先临时处理一下去重
        return Sets.newHashSet(buttonSet);
    }

    protected final boolean isGrayListLayout() {
        return AppFrameworkConfig.isGrayListLayout(controllerContext.getTenantId(), objectDescribe.getApiName());
    }

    protected Optional<ListComponentExt> getListComponent() {
        return Optional.empty();
    }

    protected LayoutAgentType getLayoutAgentType() {
        return RequestUtil.isMobileRequest() ? LayoutAgentType.MOBILE : LayoutAgentType.WEB;
    }

    protected ObjectDescribeExt findObject() {
        return findObject(controllerContext.getObjectApiName());
    }

    protected ObjectDescribeExt findObject(String apiName) {
        IObjectDescribe objectDescribe;
        //灰度企业不拷贝对象描述
        if (AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            objectDescribe = serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        } else {
            objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), apiName);
        }
        return ObjectDescribeExt.of(objectDescribe);
    }

    protected ILayout findLayout() {
        if (CollectionUtils.notEmpty(listLayouts)) {
            return listLayouts.get(0);
        }
        return null;
    }

    protected Map<String, String> findRecordTypeMapping() {
        return null;
    }


    protected List<ILayout> findMobileLayouts() {
        return findMobileLayouts(objectDescribe);
    }

    protected List<ILayout> findMobileLayouts(ObjectDescribeExt describeExt) {
        List<ILayout> listLayouts = serviceFacade.findMobileListLayout(buildLayoutContext(), describeExt, isListPage());
        listLayouts.forEach(this::doRender);

        return listLayouts;
    }

    protected boolean isListPage() {
        return true;
    }

    protected void doRender(ILayout x) {
        getComponentRender(x).render();
    }

    protected TableComponentRender getComponentRender(ILayout x) {
        return TableComponentRender.builder()
                .functionPrivilegeService(serviceFacade)
                .user(controllerContext.getUser())
                .describeExt(objectDescribe)
                .tableComponentExt(TableComponentExt.of(LayoutExt.of(x).getTableComponent()
                        .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)))))
                .build();
    }

    /**
     * 修改 SearchTemplateQuery 中的 filter、wheres、pattern 是危险的行为，可能导致查询失败，或者查询结果不符合期望
     * 建议在 {@link #defineQuery()} 中增删改筛选条件。
     *
     * @return
     * @see #defineQuery()
     */
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(getQueryDescribe());
        SearchTemplateQuery searchTemplateQuery;
        if (Objects.isNull(defaultTemplate)) {
            ISearchTemplate searchTemplate = serviceFacade.findSearchTemplateByIdAndType(controllerContext.getUser(), getSearchTemplateId(), objectDescribe.getApiName(), getSearchTemplateType());
            setSceneShowTag(searchTemplate);
            searchTemplateQuery = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(controllerContext.getUser(), objectDescribe, searchTemplate, getSearchQueryInfo(),
                    false, isIgnoreSceneFilter(), isIgnoreSceneRecordType());
            handleScene(searchTemplate, searchTemplateQuery);
        } else {
            setSceneShowTag(defaultTemplate);
            searchTemplateQuery = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(controllerContext.getUser(), describeExt,
                    defaultTemplate, getSearchQueryInfo(), false, isIgnoreSceneFilter(), isIgnoreSceneRecordType());
            handleScene(defaultTemplate, searchTemplateQuery);
        }
        searchTemplateQuery.setFindExplicitTotalNum(getFindExplicitTotalNum());
        searchTemplateQuery.setNeedReturnCountNum(getNeedReturnCountNum());
        FilterExt.handlePlainContentFilter(searchTemplateQuery.getFilters());
        return searchTemplateQuery;
    }

    protected void setSceneShowTag(ISearchTemplate searchTemplate) {
        showTag = Objects.nonNull(searchTemplate) && BooleanUtils.isTrue(searchTemplate.getShowTag());
    }

    protected void handleScene(ISearchTemplate searchTemplate, ISearchTemplateQuery searchTemplateQuery) {
        sceneApiName = Objects.nonNull(searchTemplate) ? searchTemplate.getApiName() : null;
        if (isRecentVisitSceneReOrder(searchTemplateQuery)) {
            idSort = searchTemplateQuery.getFilters().stream()
                    .filter(x -> IObjectData.ID.equals(x.getFieldName()))
                    .map(IFilter::getFieldValues)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
        }
    }

    protected Query defineQuery() {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(getSearchTemplateType())
                .templateId(getSearchTemplateId())
                .isIgnoreSceneFilter(isIgnoreSceneFilter())
                .isIgnoreSceneRecordType(isIgnoreSceneRecordType())
                .isRelatedPage(isRelatedPage())
                .searchTemplate(defaultTemplate)
                .build();
        Query searchQuery = serviceFacade.findSearchQuery(controllerContext.getUser(), getQueryDescribe(), getSearchQueryInfo(), queryContext);
        searchQuery.setFindExplicitTotalNum(getFindExplicitTotalNum());
        searchQuery.setNeedReturnCountNum(getNeedReturnCountNum());
        setSceneShowTag(searchQuery.getSearchTemplate());
        handleScene(searchQuery.getSearchTemplate(), searchQuery.toSearchTemplateQuery());
        searchQuery.handlePlainContentFilter();
        return searchQuery;
    }

    /**
     * @return 构造 SearchTemplateQuery 时需要的 describe
     */
    protected IObjectDescribe getQueryDescribe() {
        return objectDescribe;
    }

    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        if (!skipFindDataByFilter.get()) {
            queryResult = findData(query);
        }
        stopWatch.lap("findData");
        if (CollectionUtils.empty(queryResult.getData())) {
            return queryResult;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SYNCHRONIZE_DATA_GRAY, controllerContext.getTenantId())) {
            queryResult.setData(ObjectDataExt.synchronize(queryResult.getData()));
        }
        //排除gdpr个人字段
        removeGdprField(queryResult.getData());
        if (isGetDataOnly()) {
            return queryResult;
        }
        //补充__r等特殊字段
        asyncFillFieldInfo(objectDescribe, queryResult.getData());
        stopWatch.lap("asyncFillFieldInfo");
        return queryResult;
    }

    protected void beforeQueryData(SearchTemplateQuery query) {
    }

    protected Result buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        Result result = Result.builder()
                .total(queryResult.getTotalNumber())
                .limit(query.getLimit())
                .offset(query.getOffset())
//                .allPageSummaryData(summaryData)
                .build();

        if (isRecentVisitSceneReOrder(query)) {
            result.setLimit(limit);
            result.setOffset(offset);
            List<IObjectData> data = queryResult.getData();
            List<IObjectData> orderDataList = CollectionUtils.sortByGivenOrder(data, idSort, DBRecord::getId);
            List<ObjectDataDocument> objectDataDocuments = ObjectDataDocument.ofList(orderDataList);
            int pageNum = SearchTemplateQueryExt.calculatePageNum(offset, limit);
            List<List<ObjectDataDocument>> partition = Lists.partition(objectDataDocuments, limit);
            if (CollectionUtils.empty(partition) || partition.size() <= pageNum) {
                result.setDataList(Lists.newArrayList());
            } else {
                result.setDataList(partition.get(pageNum));
            }
        } else {
            result.setDataList(ObjectDataDocument.ofList(queryResult.getData()));
        }

        if (needDescribe(query)) {
            result.setObjectDescribe(ObjectDescribeDocument.handleDescribeCache(describeVersionMap, objectDescribe));
            result.setObjectDescribeExt(objectDescribeExt);
        }

        if (needLayout(query)) {
            result.setListLayouts(LayoutDocument.ofList(layouts));
            result.setLayout(LayoutDocument.of(layout));
            if (needRecordTypeMapping()) {
                result.setRecordTypeMapping(recordTypeMapping);
            }
        } else {
            result.setListLayouts(Lists.newArrayList());
        }

        if (needButtonInfo()) {
            result.setButtonInfo(buttonInfo);
        }

        if (needTagInfo()) {
            result.setDataTagInfo(dataTagInfo);
        }

        if (extractExtendInfo()) {
            result.setExtendInfo(buildExtendInfo());
        }
        return result;
    }

    protected boolean isRecentVisitSceneReOrder(ISearchTemplateQuery query) {
        return Objects.equals(SearchTemplateExt.DefaultScene.RECENT_VISIT.getApiName(), sceneApiName) && SearchTemplateQueryExt.of(query).onlyIdOrder();
    }

    protected Map<String, Object> buildExtendInfo() {
        Map<String, Object> extendInfo = Maps.newHashMap();
        if (CollectionUtils.notEmpty(userInfoMap)) {
            List<EmployeeDto> employees = userInfoMap.values().stream().map(EmployeeDto::of).collect(Collectors.toList());
            extendInfo.put("employee", employees);
        }
        if (CollectionUtils.notEmpty(deptInfoMap)) {
            List<DepartmentDto> departments = deptInfoMap.values().stream().map(DepartmentDto::of).collect(Collectors.toList());
            extendInfo.put("department", departments);
        }
        if (CollectionUtils.notEmpty(dataVisibilityRangeMap)) {
            Collection<ConnectedEnterpriseDTO.Helper> values = dataVisibilityRangeMap.values();
            extendInfo.put("data_visibility_range", values);
        }
        return extendInfo;
    }

    protected ObjectDescribeDocument buildDescribeExt(ISearchTemplateQuery query) {
        return ObjectDescribeDocument.of(getDescribeExtra());
    }

    private DescribeExtra getDescribeExtra() {
        if (Objects.nonNull(describeExtra)) {
            return describeExtra;
        }
        describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), objectDescribe,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);
        return describeExtra;
    }

    protected boolean needDescribe(ISearchTemplateQuery query) {
        return !Boolean.TRUE.equals(query.getFindExplicitTotalNum()) && !isGetDataOnly();
    }

    protected boolean needLayout(ISearchTemplateQuery query) {
        return !Boolean.TRUE.equals(query.getFindExplicitTotalNum()) && !isGetDataOnly();
    }

    protected boolean needRecordTypeMapping() {
        return false;
    }

    protected boolean needButtonInfo() {
        return true;
    }

    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        boolean esSearchSkipRecentUpdateCheck = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ES_REDIS_RECENT_UPDATE_CHECK_GRAY, controllerContext.getTenantId());
        QueryResult<IObjectData> result;
        if (isSelectPartly()) {
            List<String> projectFields = parseProjectFields();
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(controllerContext.getUser())
                    .isSimple(false)
                    .esSearchSkipRecentUpdateCheck(esSearchSkipRecentUpdateCheck)
                    .skipRelevantTeam(RequestUtil.skipRelevantTeam())
                    .projectionFields(projectFields)
                    .includeInvalid(isIncludeInvalidData())
                    .calculateFormula(true)
                    .calculateQuote(true)
                    .convertQuoteForView(true)
                    .searchRichTextExtra(needSearchRichTextExtra())
                    .build();
            result = serviceFacade.findByQueryWithContext(queryContext, objectDescribe.getApiName(), query);
        } else {
            //不需要元数据补引用字段
            query.setNeedReturnQuote(false);
            if (!isIncludeInvalidData()) {
                result = serviceFacade.findBySearchQuery(controllerContext.getUser(), objectDescribe,
                        objectDescribe.getApiName(), query, false, esSearchSkipRecentUpdateCheck,
                        RequestUtil.skipRelevantTeam(), needSearchRichTextExtra());
            } else {
                IFilter filter = new Filter();
                filter.setFieldName(IObjectData.IS_DELETED);
                filter.setOperator(Operator.IN);
                filter.setFieldValues(Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())));
                query.addFilters(Lists.newArrayList(filter));
                result = serviceFacade.findBySearchQueryWithDeleted(controllerContext.getUser(), objectDescribe, query,
                        RequestUtil.skipRelevantTeam(), needSearchRichTextExtra());
            }
        }

        ObjectDataExt.correctValue(controllerContext.getUser(), result.getData(), objectDescribe);

        List<IObjectData> objectDataList = ObjectDataExt.synchronize(result.getData());
        if (CollectionUtils.notEmpty(objectDataList)) {
            result.setData(objectDataList);
        }
        return result;
    }

    private boolean isSelectPartly() {
        return CollectionUtils.notEmpty(parseProjectFields()) && UdobjGrayConfig.isAllow(LIST_SELECT_PARTLY_GRAY_EI, controllerContext.getTenantId());
    }

    protected boolean needSearchRichTextExtra() {
        return false;
    }

    protected List<String> parseProjectFields() {
        return Lists.newArrayList();
    }

    protected void asyncFillFieldInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //指定了返回字段的话由底层处理引用字段，这里不再进行计算
            if (!isSelectPartly()) {
                //处理引用字段
                fillQuoteFieldValue(describe, synchronizedDataList, null);
            }
            // 补充掩码需要在补充引用字段之后执行,异步执行可能会导致引用字段没有掩码的情况
            fillMaskFieldValue(describe, synchronizedDataList);
        });

        parallelTask.submit(() -> {
            //添加lookup字段的主属性__r
            fillRefObjectName(describe, synchronizedDataList, null);
        });
        parallelTask.submit(() -> {
            fillInfo(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            fillDimensionFieldValue(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            fillCountryAreaLabel(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            fillCurrencyFieldValue(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            fillDataVisibilityRange(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            fillCountMultiRegion(describe, synchronizedDataList);
        });
        try {
            parallelTask.await(getWaitTime(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill info of listController, ei:{}, object:{}", controllerContext.getTenantId(), describe.getApiName(), e);
        }
    }

    private void fillCountMultiRegion(IObjectDescribe describe, List<IObjectData> synchronizedDataList) {
        serviceFacade.fillCountWithMultiRegion(controllerContext.getUser(), describe, synchronizedDataList);
    }

    private int getWaitTime() {
        if (RequestUtil.isCepRequest()) {
            return 5;
        }
        return AppFrameworkConfig.listAsyncFillFieldInfoWaitTime();
    }

    private void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillCountryAreaLabel(describe, dataList, controllerContext.getUser());
    }

    private void fillCurrencyFieldValue(IObjectDescribe describe, List<IObjectData> dataList) {
        serviceFacade.fillCurrencyFieldInfo(describe, dataList, controllerContext.getUser());
    }

    protected void fillDimensionFieldValue(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), objectDescribe, dataList);
    }

    protected void fillQuoteFieldValue(IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, List<IObjectData>> refObjectDataMap) {
        infraServiceFacade.fillQuoteFieldValue(controllerContext.getUser(), dataList, objectDescribe, refObjectDataMap, false);
    }

    protected void fillRefObjectName(IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, List<IObjectData>> refObjectDataMap) {
        serviceFacade.fillObjectDataWithRefObject(objectDescribe, dataList, controllerContext.getUser(), refObjectDataMap);
    }

    protected void fillDataVisibilityRange(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        Map<String, ConnectedEnterpriseDTO.Helper> stringHelperMap = serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), objectDescribe, dataList, extractExtendInfo());
        stringHelperMap.forEach(dataVisibilityRangeMap::putIfAbsent);
    }

    protected void fillInfo(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        //拼装组织机构信息
        Map<String, UserInfo> userInfo = serviceFacade.fillUserInfo(objectDescribe, dataList, controllerContext.getUser(), extractExtendInfo());
        userInfo.forEach((key, val) -> userInfoMap.putIfAbsent(key, val));
        Map<String, QueryDeptInfoByDeptIds.DeptInfo> deptInfo = serviceFacade.fillDepartmentInfo(objectDescribe, dataList, controllerContext.getUser(),
                QueryDeptInfoByDeptIds.DeptStatusEnum.ALL, extractExtendInfo());
        deptInfo.forEach((key, val) -> deptInfoMap.putIfAbsent(key, val));
        serviceFacade.fillRichTextImageInfo(objectDescribe, dataList, controllerContext.getUser());
    }

    protected void fillMaskFieldValue(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        if (AppFrameworkConfig.maskFieldEncryptGray(controllerContext.getTenantId(), objectDescribe.getApiName())) {
            if (maskFieldEncryptObjectPagesGray()) {
                return;
            }
            List<IFieldDescribe> maskEncryptFields = getMaskEncryptFields();
            if (CollectionUtils.notEmpty(maskEncryptFields) || needEncryptMaskFieldsWithDescribe()) {
                if (Objects.isNull(maskFieldLogicService)) {
                    maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
                }
                MaskFieldLogicService.MaskFieldConfig maskFieldConfig = MaskFieldLogicService.MaskFieldConfig.createByRemoveMaskOrigValue(needRemoveMaskOrigValue());
                maskFieldConfig.setNeedEncryptMaskFieldsWithDescribe(needEncryptMaskFieldsWithDescribe());
                maskFieldLogicService.fillMaskFieldValue(controllerContext.getUser(), objectDescribe, dataList, maskEncryptFields, maskFieldConfig);
                return;
            }
        }
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), dataList, objectDescribe, needRemoveMaskOrigValue());
    }

    protected boolean needRemoveMaskOrigValue() {
        return RequestUtil.isCepRequest();
    }

    protected Set<String> getUnauthorizedFields() {
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), objectDescribe.getApiName());
        return CollectionUtils.empty(unauthorizedFields) ? Collections.emptySet() : unauthorizedFields;
    }

    @Override
    protected void finallyDo() {
        if (Objects.nonNull(result)) {
            projectField();
            stopWatch.lap("projectField");
            filterUnauthorizedFields();
        }
        super.finallyDo();
    }

    private void projectField() {
        List<String> projectFields = parseProjectFields();
        if (Objects.isNull(result) || CollectionUtils.empty(projectFields)) {
            return;
        }
        ObjectDataDocument.projectField(result.getDataList(), projectFields);
    }

    protected void decodeMaskFieldEncryptValue(IObjectDescribe objectDescribe, IObjectData objectData) {
        if (Objects.isNull(objectData) || Objects.isNull(objectDescribe)) {
            return;
        }
        if (!AppFrameworkConfig.maskFieldEncryptGray(controllerContext.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        if (Objects.isNull(maskFieldLogicService)) {
            maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        }
        maskFieldLogicService.decodeMaskFieldEncryptValue(controllerContext.getUser(), Lists.newArrayList(objectData), objectDescribe);
    }

    /**
     * 需要加密的字段，这些字段不判断是否开启掩码，也不判断去掩码的角色
     *
     * @return
     */
    protected List<IFieldDescribe> getMaskEncryptFields() {
        return Lists.newArrayList();
    }

    /**
     * 根据描述处理需要加密的掩码字段
     *
     * @return
     */
    protected boolean needEncryptMaskFieldsWithDescribe() {
        return false;
    }

    protected boolean maskFieldEncryptObjectPagesGray() {
        return false;
    }

    @Override
    protected void mergeAPLPluginArg(APLControllerPlugin.Result aplPluginResult) {
        String searchQueryInfo = getSearchQueryInfoFromAplPlugin(aplPluginResult);
        if (Strings.isNullOrEmpty(searchQueryInfo)) {
            return;
        }
        try {
            SearchTemplateQuery query = SearchTemplateQueryExt.mergeByAPLPlugin(getSearchQueryInfo(), searchQueryInfo);
            setSearchTemplateQuery2Arg(query);
            if (log.isInfoEnabled()) {
                log.info("mergeAPLPluginArg, arg:{}", JacksonUtils.toJson(arg));
            }
        } catch (Exception e) {
            log.warn("mergeArg fail! ei:{}, objectApiName:{}, searchQueryInfo:{}", controllerContext.getTenantId(), controllerContext.getObjectApiName(), searchQueryInfo, e);
            throw new ValidateException(I18N.text(I18NKey.FUNCTION_PLUGIN_FAIL));
        }
    }

    protected String getSearchQueryInfoFromAplPlugin(APLControllerPlugin.Result aplPluginResult) {
        StandardListController.Arg controllerArg = aplPluginResult.getControllerArg(StandardListController.Arg.class);
        if (Objects.isNull(controllerArg)) {
            return null;
        }
        return controllerArg.getSearchQueryInfo();
    }

    protected void setSearchTemplateQuery2Arg(SearchTemplateQuery searchTemplateQuery) {

    }

    @Override
    protected void mergeAPLPluginResult(APLControllerPlugin.Result aplPluginResult) {
        Result controllerResult = aplPluginResult.getControllerResult(Result.class);
        Optional.ofNullable(controllerResult)
                .map(Result::getDataList)
                .ifPresent(result::setDataList);
    }

    protected void convertFieldForView(User user, List<IFieldDescribe> fieldDescribes, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        fieldDescribes.stream()
                .filter(this::needConvertFieldType)
                .forEach(field -> {
                    String fieldRenderType = ObjectDescribeExt.getFieldRenderType(field);
                    boolean formatValue = isExtractFormatValue() || (isExtractTimeFormatValue() && JsonFieldHandler.timeJsonField.contains(fieldRenderType));
                    dataList.forEach(data -> {
                        String convertValue = infraServiceFacade.convertData(data, field, user, true);
                        if (formatValue) {
                            data.set(FieldDescribeExt.getLookupNameByFieldName(field.getApiName()), convertValue);
                        } else {
                            data.set(field.getApiName(), convertValue);
                        }
                    });
                });
        // 补充移动端摘要布局中的国家字段
        fillTableComponentCountryField(dataList);
    }

    /**
     * 对于时间类型的字段，原字段下发时间戳，__r字段下发格式化后的字段
     *
     * @return
     */
    protected boolean isExtractTimeFormatValue() {
        return false;
    }

    protected boolean isExtractFormatValue() {
        return false;
    }

    protected boolean needConvertFieldType(IFieldDescribe fieldDescribe) {
        String fieldRenderType = ObjectDescribeExt.getFieldRenderType(fieldDescribe);
        return JsonFieldHandler.timeJsonField.contains(fieldRenderType)
                || JsonFieldHandler.optionJsonField.contains(fieldRenderType)
                || IFieldType.COUNTRY.equals(fieldRenderType)
                || IFieldType.PROVINCE.equals(fieldRenderType)
                || IFieldType.CITY.equals(fieldRenderType)
                || IFieldType.DISTRICT.equals(fieldRenderType);
    }

    private void fillTableComponentCountryField(List<IObjectData> dataList) {
        if (Objects.isNull(layout)) {
            return;
        }
        LayoutExt.of(layout).getTableComponent().ifPresent(tableComponent -> {
            // 国家字段apiName与地区组件的映射
            Map<String, Area> countryApiNameToArea = objectDescribe.getAreaGroupFields().stream()
                    .collect(Collectors.toMap(Area::getAreaCountryFieldApiName, area -> area));
            tableComponent.getIncludeFields().forEach(includeField -> {
                // 移动端摘要中的国家字段
                if (countryApiNameToArea.containsKey(TableColumnExt.of(includeField).getApiName())) {
                    // 获取对应的 area 组件字段
                    String apiName = TableColumnExt.of(includeField).getApiName();
                    AreaFieldDescribe areaField = (AreaFieldDescribe) countryApiNameToArea.get(apiName);
                    // 是否支持乡镇字段
                    boolean isAddTown = BooleanUtils.isTrue(areaField.getIsSupportTown());
                    boolean isAddVillage = BooleanUtils.isTrue(areaField.getIsSupportVillage());
                    // 补充布局
                    String labelName = areaField.getFieldList(objectDescribe).stream()
                            .filter(it -> !it.getApiName().equals(areaField.getAreaLocationFieldApiName()) && !it.getApiName().equals(areaField.getAreaDetailAddressFieldApiName()))
                            .map(IFieldDescribe::getLabel)
                            .collect(Collectors.joining("/"));
                    includeField.setLabelName(labelName);
                    // 补充数据
                    for (IObjectData objectData : dataList) {
                        String oldData = objectData.get(apiName, String.class, null);
                        if (StringUtils.isEmpty(oldData)) {
                            break;
                        }
                        // 获取数据
                        List<String> areaData = Lists.newArrayList(oldData,
                                objectData.get(areaField.getAreaProvinceFieldApiName(), String.class, null),
                                objectData.get(areaField.getAreaCityFieldApiName(), String.class, null),
                                objectData.get(areaField.getAreaDistrictFieldApiName(), String.class, null));
                        if (isAddTown) {
                            areaData.add(objectData.get(areaField.getAreaTownFieldApiName() + "__r", String.class, null));
                            if (isAddVillage) {
                                areaData.add(objectData.get(areaField.getAreaVillageFieldApiName() + "__r", String.class, null));
                            }
                        }
                        String fillData = areaData.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining("/"));
                        ObjectDataExt.of(objectData).set(apiName + "__r", fillData);
                    }
                }
            });
        });
    }

    private void filterUnauthorizedFields() {
        if (controllerContext.getUser().isSupperAdmin() || skipFilterUnauthorizedFields()) {
            return;
        }
        ObjectDataExt.filterUnauthorizedFieldsByDataList(ObjectDataDocument.ofDataList(result.getDataList()), getUnauthorizedFields(), objectDescribe);
        stopWatch.lap("filterUnauthorizedFieldsByDataList");
    }

    protected boolean skipFilterUnauthorizedFields() {
        return false;
    }

    private void removeGdprField(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (!RequestUtil.isOpenAPIRequest()) {
            return;
        }
        List<GdprCompliance> personalField = infraServiceFacade.findGdprCompliance(controllerContext.getUser(), objectDescribe.getApiName());
        if (CollectionUtils.notEmpty(personalField)) {
            Set<String> needFilterPersonalField = personalField.stream()
                    .filter(x -> Objects.equals(x.getApiName(), objectDescribe.getApiName()))
                    .filter(x -> x.isOpenStatus())
                    .filter(x -> x.getForbidExport().contains(GdprCompliance.ORDINARY_AND_SENSITIVE)
                            || x.getForbidExport().contains(GdprCompliance.SENSITIVE))
                    .map(x -> {
                        if (CollectionUtils.notEmpty(x.getOrdinaryFields())) {
                            return x.getOrdinaryFields();
                        }
                        if (CollectionUtils.notEmpty(x.getSensitiveFields())) {
                            return x.getSensitiveFields();
                        }
                        return new ArrayList<String>();
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
            dataList.forEach(data -> ObjectDataExt.of(data).remove(needFilterPersonalField));
        }
    }

    @Override
    protected final IObjectDescribe doGetObjectDescribe() {
        if (objectDescribe == null) {
            objectDescribe = findObject();
        }
        return objectDescribe.getObjectDescribe();
    }

    @Override
    protected final Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        BaseListHandler.Arg<A> handlerArg = buildListHandlerArg();
        if (Objects.nonNull(handlerArg)) {
            processHandlerArg(handlerArg);
        }
        return handlerArg;
    }

    protected BaseListHandler.Arg<A> buildListHandlerArg() {
        return null;
    }

    private void processHandlerArg(BaseListHandler.Arg<A> handlerArg) {
        if (Objects.nonNull(query)) {
            handlerArg.setSearchTemplateQuery(query.toJsonString());
        }
        if (Objects.nonNull(queryResult)) {
            handlerArg.setDataList(ObjectDataDocument.ofList(queryResult.getData()));
        }
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<Result> handlerResult) {
        if (handlerResult instanceof BaseListHandler.Result) {
            BaseListHandler.Result listHandlerResult = (BaseListHandler.Result) handlerResult;
            if (!Strings.isNullOrEmpty(listHandlerResult.getSearchTemplateQuery())) {
                if (Objects.nonNull(query)) {
                    query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(listHandlerResult.getSearchTemplateQuery());
                } else {
                    updateSearchQueryInfo(listHandlerResult.getSearchTemplateQuery());
                }
            }
            if (Objects.nonNull(listHandlerResult.getDataList()) && Objects.nonNull(queryResult)) {
                queryResult.setData(ObjectDataDocument.ofDataList(listHandlerResult.getDataList()));
            }
        }
    }

    protected void updateSearchQueryInfo(String searchQueryInfo) {

    }

    @Override
    protected void registerHandlerExecuteFunctions(Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap) {
        executeFunctionMap.put("defaultListGetSearchTemplateQueryHandler", this::doGetSearchTemplateQuery);
        executeFunctionMap.put("defaultListFindDataHandler", this::doFindData);
        executeFunctionMap.put("defaultListFindOtherInfoHandler", this::doFindOtherInfo);
        executeFunctionMap.put("defaultListBuildResultHandler", () -> this.result = doBuildResult());
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        private List<ObjectDataDocument> dataList;

        @JSONField(name = "M2")
        private Integer offset;

        @JSONField(name = "M3")
        private Integer limit;

        @JSONField(name = "M4")
        private Integer total;

//        @JSONField(name = "M5")
//        private List<LookupData> refObjects;

        @JSONField(name = "M6")
        private List<LayoutDocument> listLayouts;

        @JSONField(name = "M7")
        private ObjectDescribeDocument objectDescribe;

        @JSONField(name = "M8")
        private List<UserInfo> userInfos;

        @JSONField(name = "M9")
        private ButtonInfo buttonInfo;

        private LayoutDocument layout;

        @JSONField(name = "M12")
        private ObjectDescribeDocument objectDescribeExt;

        private List<FindAllTagByBulkDataId.DataTag> dataTagInfo;

        //所有页汇总字段的值
        private Map<String, String> allPageSummaryData;

        //字段的扩展信息
        private Map<String, Object> extendInfo;
        // lookup选对象列表新建数据指定数据类型
        private Map<String, Object> relatedObjectDataSpecified;
        /**
         * 列表页支持全字段搜索
         */
        private Boolean supportFullFieldSearchOnListPage;

        private Map<String, String> recordTypeMapping;

        /**
         * 为 plugin 服务的 copy 方法
         *
         * @return plugin 需要用到的属性
         */
        public Result copy2Plugin() {
            return Result.builder()
                    .dataList(dataList)
                    .build();
        }

        public IFieldDescribe copyFieldToDescribeExt(String fieldApiName) {
            if (Objects.isNull(objectDescribe) || !objectDescribe.toObjectDescribe().containsField(fieldApiName)) {
                return null;
            }
            if (Objects.isNull(objectDescribeExt)) {
                objectDescribeExt = ObjectDescribeDocument.of(new ObjectDescribe());
            }
            IFieldDescribe fieldDescribe = objectDescribe.toObjectDescribe().getFieldDescribe(fieldApiName);
            return ObjectDescribeExt.of(objectDescribeExt).getOrCopyField(fieldDescribe);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ButtonInfo {
        private List<ButtonDocument> buttons;
        private Map<String, List<String>> buttonMap;
        private Map<String, Set<Map>> recordTypeButton;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeDto {
        private String id;
        private String name;
        private String picAddr;

        public static EmployeeDto of(UserInfo userInfo) {
            return EmployeeDto.builder()
                    .id(userInfo.getId())
                    .name(userInfo.getName())
                    .picAddr(userInfo.getPicAddr())
                    .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepartmentDto {
        private String deptId;
        private String deptName;

        public static DepartmentDto of(DeptInfo deptInfo) {
            return DepartmentDto.builder()
                    .deptId(deptInfo.getDeptId())
                    .deptName(deptInfo.getDeptName())
                    .build();
        }
    }
}
