package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.domain.ControllerDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.model.plugin.Plugin;
import com.facishare.paas.appframework.core.model.plugin.PluginContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController.Arg;
import com.facishare.paas.appframework.core.predef.domain.RelatedListControllerDomainPlugin;
import com.facishare.paas.appframework.core.predef.handler.list.BaseListHandler;
import com.facishare.paas.appframework.core.predef.handler.list.RelatedListHandler;
import com.facishare.paas.appframework.core.util.FunctionParamBuildUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.function.util.FunctionQueryTemplateUtils;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.RelatedObjectGroupComponentBuilder;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.payment.dto.PaymentRecord;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.WhatFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import java.util.stream.Collectors;


public class StandardRelatedListController extends BaseListController<Arg> {
    private IObjectData masterObjectData;
    private ObjectDescribeExt sourceDescribeExt;
    private Map<String, Object> relatedObjectDataSpecified;
    private static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";

    private static final String BUTTON = "Button";
    private static final String REFERENCE_CREATE = "ReferenceCreate";

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.RelatedList.getFuncPrivilegeCodes();
    }

    @Override
    protected String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    @Override
    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    @Override
    protected String getSearchTemplateApiName() {
        return arg.getSearchTemplateApiName();
    }

    @Override
    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    @Override
    protected boolean isIgnoreSceneFilter() {
        return arg.isIgnoreSceneFilter();
    }

    @Override
    protected Boolean getFindExplicitTotalNum() {
        if (RequestUtil.isCepRequest() && isListPage()) {
            return Boolean.TRUE.equals(arg.getFindExplicitTotalNum());
        }
        return arg.getFindExplicitTotalNum();
    }

    @Override
    protected boolean isIgnoreSceneRecordType() {
        return arg.isIgnoreSceneRecordType();
    }

    @Override
    protected boolean isRelatedPage() {
        return true;
    }

    @Override
    protected boolean serializeEmpty() {
        return arg.serializeEmpty() || ObjectDescribeExt.of(objectDescribe).isChangeOrderObject();
    }

    @Override
    protected boolean extractExtendInfo() {
        return arg.extractExtendInfo();
    }

    @Override
    protected Map<String, Object> getRelatedListComponent() {
        return arg.getRelatedListComponent();
    }

    @Override
    protected void init() {
        if (isPayment()) {
            objectDescribe = ObjectDescribeExt.buildPaymentDescribe();
        } else {
            super.init();
            findMasterObjectData();
            stopWatch.lap("findMasterObjectData");
            // 将主对象的字段（归属组织、归属部门和币种）回填到从对象中
            initFieldFromMasterData();
            stopWatch.lap("initFieldFromMasterData");
        }
    }

    private void initFieldFromMasterData() {
        if (masterObjectData == null || arg.getObjectData() == null) {
            if (Objects.nonNull(arg.getObjectData())) {
                decodeMaskFieldEncryptValue(sourceDescribeExt, arg.getObjectData().toObjectData());
            }
            return;
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (objectData == null) {
            return;
        }
        if (objectData.getDataOwnOrganization() == null) {
            objectData.setDataOwnOrganization(masterObjectData.getDataOwnOrganization());
        }
        if (objectData.getDataOwnDepartment() == null) {
            objectData.setDataOwnDepartment(masterObjectData.getDataOwnDepartment());
        }
        if (sourceDescribeExt == null) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        if (objectDataExt.getCurrency() == null && sourceDescribeExt.containsMultiCurrencyField()) {
            objectDataExt.setCurrency(ObjectDataExt.of(masterObjectData).getCurrency());
        }
    }

    @Override
    protected ObjectDescribeExt findObject() {
        if (isPayment()) {
            return ObjectDescribeExt.buildPaymentDescribe();
        }
        IObjectDescribe objectDescribe = serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(),
                controllerContext.getObjectApiName());
//        objectDescribe.removeFieldDescribe("extend_obj_data_id");
        return ObjectDescribeExt.of(objectDescribe);
    }

    @Override
    protected Result doService(Arg arg) {
        Result result;
        if (isPayment()) {
            result = getPaymentListResult();
        } else {
            result = super.doService(arg);
        }
        return result;
    }

    @Override
    protected boolean noNeedCheckObjectOfCustomObjectListFiltersOrAppLicense(String objectApiName) {
        if (!isListPage() && arg.isIncludeAssociated()) {
            return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NO_NEED_CHECK_OBJECT_OF_CUSTOM_OBJECT_LIST_FILTERS_OR_APP_LICENSE, objectApiName);
        }
        return super.noNeedCheckObjectOfCustomObjectListFiltersOrAppLicense(objectApiName);
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (isListPage() && RequestUtil.isCepRequest()) {
            RequestUtil.setEsRecentUpdate();
        }
    }

    @Override
    protected List<String> parseProjectFields() {
        return arg.getFieldProjection();
    }

    @Override
    protected Plugin.Arg buildAPLPluginArg(String method) {
        APLControllerPlugin.TriggerInfo triggerInfo = buildTriggerInfo();
        Arg controllerArg = Objects.isNull(arg) ? null : arg.copy2Plugin();
        Result controllerResult = Objects.isNull(result) ? null : result.copy2Plugin();
        return new APLControllerPlugin.Arg(controllerContext.getObjectApiName(), controllerArg, controllerResult, triggerInfo);
    }

    @Override
    protected APLControllerPlugin.TriggerInfo buildTriggerInfo() {
        APLControllerPlugin.TriggerInfo triggerInfo = new APLControllerPlugin.TriggerInfo();
        triggerInfo.setTriggerObject(getSourceDescribeApiName());
        triggerInfo.setTriggerField(getRelatedFieldName());
        triggerInfo.setTriggerPage(getTriggerPage());
        return triggerInfo;
    }

    private String getTriggerPage() {
        return Optional.ofNullable(arg.getTriggerInfo())
                .map(TriggerInfo::getTriggerPage)
                .orElse(null);
    }

    private String getTriggerSource() {
        return Optional.ofNullable(arg.getTriggerInfo())
                .map(TriggerInfo::getSource)
                .orElse(null);
    }

    private String getRelatedFieldName() {
        if (!Strings.isNullOrEmpty(arg.getRelatedFieldName())) {
            return arg.getRelatedFieldName();
        }
        ObjectDescribeExt describe;
        String targetApiName;
        if (isListPage()) {
            describe = getSourceDescribe();
            targetApiName = objectDescribe.getApiName();
        } else {
            describe = objectDescribe;
            targetApiName = arg.getTargetObjectApiName();
        }
        return Optional.ofNullable(describe)
                .flatMap(describeExt -> describeExt.getRefFieldName(targetApiName, arg.getRelatedListName()))
                .orElse(null);
    }


    private Optional<ObjectReferenceWrapper> getReferenceField() {
        return objectDescribe.getReferenceField(arg.getTargetObjectApiName(), arg.getRelatedListName());
    }

    @Override
    protected PluginContext buildPluginContext() {
        PluginContext pluginContext = super.buildPluginContext();
        String moduleType = getModuleType();
        pluginContext.setModuleType(moduleType);
        return pluginContext;
    }

    private String getModuleType() {
        if (isListPage()) {
            return "select";
        }
        if (arg.isIncludeAssociated()) {
            return "list";
        }
        return "related";
    }

    @Override
    protected String getSearchQueryInfoFromAplPlugin(APLControllerPlugin.Result aplPluginResult) {
        Arg controllerArg = aplPluginResult.getControllerArg(Arg.class);
        if (Objects.isNull(controllerArg)) {
            return null;
        }
        return controllerArg.getSearchQueryInfo();
    }

    @Override
    protected void setSearchTemplateQuery2Arg(SearchTemplateQuery query) {
        arg.setSearchQueryInfo(query.toJsonString());
    }

    private void handleGeoInfo(Result result) {
        CollectionUtils.nullToEmpty(result.getDataList()).forEach(a -> ObjectDataExt.of(a).fillGeoDistance(objectDescribe));
    }

    @Override
    protected boolean needDescribe(ISearchTemplateQuery query) {
        if (!arg.isIncludeDescribe()) {
            return false;
        }
        return super.needDescribe(query);
    }

    @Override
    protected boolean needLayout(ISearchTemplateQuery query) {
        if (!arg.isIncludeLayout()) {
            return false;
        }
        return super.needLayout(query);
    }

    @Override
    protected boolean isListPage() {
        return Strings.isNullOrEmpty(arg.getTargetObjectApiName())
                || Strings.isNullOrEmpty(arg.getTargetObjectDataId());
    }

    @Override
    protected ILayout findLayout() {
        ILayout layout = super.findLayout();
        if (layout == null) {
            return null;
        }

        List<IButton> buttonList = getButtons(layout);
        layout.setButtons(buttonList);

        return layout;
    }

    protected List<IButton> getButtons(ILayout layout) {
        if (isListPage()) {
            // 选对象列表不下发列表页通用按钮
            return CollectionUtils.nullToEmpty(layout.getButtons()).stream()
                    .filter(button -> LayoutButtonExt.of(button).isSystemButton())
                    .collect(Collectors.toList());
        }

        IObjectDescribe targetDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getTargetObjectApiName());
        IObjectData targetData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(),
                arg.getTargetObjectDataId(), targetDescribe.getApiName());
        RelatedObjectDescribeStructure relatedStructure = RelatedObjectDescribeStructure.builder()
                .relatedObjectDescribe(objectDescribe.getObjectDescribe())
                .fieldApiName(objectDescribe.getRefFieldName(targetDescribe.getApiName(), arg.getRelatedListName()).get())
                .build();

        List<IButton> buttonList = RelatedObjectGroupComponentBuilder.builder()
                .functionPrivilegeService(serviceFacade)
                .buttonLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .layout(LayoutExt.of(layout))
                .objectDescribe(ObjectDescribeExt.of(targetDescribe))
                .objectData(targetData)
                .build()
                .getButtons(relatedStructure);

        if (CollectionUtils.notEmpty(getRelatedListComponent()) && getRelatedListComponent().containsKey(ListComponentExt.SCENE_INFO)) {
            buttonList = ListComponentExt.of(getRelatedListComponent())
                    .fillButtonInfoPageType(IComponentInfo.PAGE_TYPE_RELATED)
                    .filterButtonByUsePage(buttonList, ButtonUsePageType.ListNormal, IComponentInfo.PAGE_TYPE_RELATED);
        }

        return buttonList;
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        ISearchTemplate searchTemplate = serviceFacade.findSearchTemplateByIdAndType(controllerContext.getUser(), getSearchTemplateId(), objectDescribe.getApiName(), getSearchTemplateType());
        setSceneShowTag(searchTemplate);
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(controllerContext.getUser(), objectDescribe, searchTemplate, getSearchQueryInfo(),
                true, isIgnoreSceneFilter(), isIgnoreSceneRecordType());
        handleScene(searchTemplate, query);
        relatedObjectDataSpecified = serviceFacade.getFunctionLogicService().handleFiltersByValueType(controllerContext.getUser(), getFunctionBindingObjectApiName(), query, getTupleSupplier());
        infraServiceFacade.processFilterByConvertRule(controllerContext.getUser(), objectDescribe, query, getTupleSupplier(), isDoublePull());
        checkQueryLimit(query);
        handleFilters(query);
        if (arg.whatRelatedType()) {
            modifyQueryByWhatFieldName(query);
        } else {
            modifyQueryByRefFieldName(query);
        }
        handleDataRights(query);
        //地理位置筛选
        handleGeoFilter(query);
        handleOrderBys(query);

        // 处理精确总数
        query.setFindExplicitTotalNum(getFindExplicitTotalNum());
        return query;
    }


    private Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> getTupleSupplier() {
        return () -> {
            Tuple<IObjectData, Map<String, List<IObjectData>>> result = getFunctionData();
            infraServiceFacade.fillQuoteValueVirtualField(controllerContext.getUser(), result.getKey(), result.getValue());
            return result;
        };
    }

    @Override
    protected Query defineQuery() {
        Query query = super.defineQuery();
        checkQueryLimit(query);
        handleFiltersByFiledRelation(query);
        infraServiceFacade.processFilterByConvertRule(controllerContext.getUser(), objectDescribe, query, getTupleSupplier(), isDoublePull());
        if (arg.whatRelatedType()) {
            modifyQueryByWhatFieldName(query);
        } else {
            modifyQueryByRefFieldName(query);
        }
        handleDataRights(query);
        handleGeoFilter(query);
        handleOrderBys(query);
        return query;
    }

    @Override
    protected Result buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        Result result = super.buildResult(layouts, query, queryResult);
        if (CollectionUtils.notEmpty(relatedObjectDataSpecified)) {
            result.setRelatedObjectDataSpecified(relatedObjectDataSpecified);
        }
        //处理地址位置筛选结果的距离信息
        handleGeoInfo(result);
        return result;
    }

    private void handleGeoFilter(SearchTemplateQuery query) {
        if (Objects.isNull(arg.getLocationInfo())) {
            return;
        }

        List<IFieldDescribe> geoSearchFields = ObjectDescribeExt.of(objectDescribe).findGeoSearchField();
        CollectionUtils.nullToEmpty(geoSearchFields).forEach(a -> {
            IFilter filter = FilterExt.buildGeoFilter(a.getApiName(), arg.getLocationInfo().getLongitude(),
                    arg.getLocationInfo().getLatitude(), arg.getLocationInfo().getDistance());
            query.addFilters(Lists.newArrayList(filter));
        });

    }

    private void handleGeoFilter(Query query) {
        if (Objects.isNull(arg.getLocationInfo())) {
            return;
        }

        List<IFieldDescribe> geoSearchFields = ObjectDescribeExt.of(objectDescribe).findGeoSearchField();
        CollectionUtils.nullToEmpty(geoSearchFields).forEach(a -> {
            IFilter filter = FilterExt.buildGeoFilter(a.getApiName(), arg.getLocationInfo().getLongitude(),
                    arg.getLocationInfo().getLatitude(), arg.getLocationInfo().getDistance());
            query.and(filter);
        });
    }

    /**
     * 处理 related List 接口 的默认排序字段：
     * 0. 地理位置筛选时，优先按距离排序
     * 1. 当前对象为从对象，且没有指定场景 id 也没有指定排序字段
     * 处理从数据按照order_by字段排序
     * <p>
     * 如果前端传了"is_ordered"字段，且前端没有指定排序时
     * 优先按照"order_by"字段升序排序，其次按照serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter方法构造出的order排序
     * <p>
     * 否则，只按照serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter方法构造出的order排序
     * <p>
     * 2. 当前为选对象列表页，且没有指定场景 id 也没有指定排序字段
     * 清空当前的默认排序，使用元数据指定的排序规则
     */
    private void handleOrderBys(SearchTemplateQuery query) {
        if (Objects.nonNull(arg.getLocationInfo())) {
            //地理位置筛选，距离优先
            List<IFieldDescribe> geoSearchFields = ObjectDescribeExt.of(objectDescribe).findGeoSearchField();
            CollectionUtils.nullToEmpty(geoSearchFields).stream()
                    .findFirst()
                    .ifPresent(a -> {
                        OrderBy geoLocationOrderBy = OrderByExt.geoLocationOrderBy(a.getApiName(),
                                arg.getLocationInfo().getLongitude(), arg.getLocationInfo().getLatitude());
                        SearchTemplateQueryExt.of(query).resetOrderBy(Lists.newArrayList(geoLocationOrderBy));
                    });
            return;
        }

        if (!notAssignedOrder()) {
            return;
        }
        if (arg.isOrdered()) {
            SearchTemplateQueryExt.of(query).orderByFirst();
            return;
        }
        if (isListPage()) {
            SearchTemplateQueryExt.of(query).resetOrderBy(Collections.emptyList());
        }

    }

    /**
     * 处理 related List 接口 的默认排序字段：
     * 0. 地理位置筛选时，优先按距离排序
     * 1. 当前对象为从对象，且没有指定场景 id 也没有指定排序字段
     * 处理从数据按照order_by字段排序
     * <p>
     * 如果前端传了"is_ordered"字段，且前端没有指定排序时
     * 优先按照"order_by"字段升序排序，其次按照serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter方法构造出的order排序
     * <p>
     * 否则，只按照serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter方法构造出的order排序
     * <p>
     * 2. 当前为选对象列表页，且没有指定场景 id 也没有指定排序字段
     * 清空当前的默认排序，使用元数据指定的排序规则
     *
     * @param query
     */
    private void handleOrderBys(Query query) {
        if (Objects.nonNull(arg.getLocationInfo())) {
            //地理位置筛选，距离优先
            List<IFieldDescribe> geoSearchFields = ObjectDescribeExt.of(objectDescribe).findGeoSearchField();
            CollectionUtils.nullToEmpty(geoSearchFields).stream()
                    .findFirst()
                    .ifPresent(a -> {
                        OrderBy geoLocationOrderBy = OrderByExt.geoLocationOrderBy(a.getApiName(),
                                arg.getLocationInfo().getLongitude(), arg.getLocationInfo().getLatitude());
                        query.resetOrders(Lists.newArrayList(geoLocationOrderBy));
                    });
            return;
        }

        if (!notAssignedOrder()) {
            return;
        }
        if (arg.isOrdered()) {
            // 从对象明细按照"order_by"字段升序排序
            List<OrderBy> newOrderBys = Lists.newArrayList(OrderByExt.relatedDetailOrderBy());
            Optional.ofNullable(query.getOrders()).ifPresent(newOrderBys::addAll);
            query.resetOrders(newOrderBys);
            return;
        }
        if (isListPage()) {
            query.resetOrders(Collections.emptyList());
        }
    }

    /*
     * 前端是否指定了排序
     * searchQueryInfo中指定了Orders或者指定了场景ID
     */
    private boolean notAssignedOrder() {
        ISearchTemplateQuery tmpQuery = SearchTemplateQuery.fromJsonString(getSearchQueryInfo());
        return CollectionUtils.empty(tmpQuery.getOrders()) &&
                StringUtils.isBlank(arg.getSearchTemplateId());
    }

    protected void handleDataRights(SearchTemplateQuery query) {
        //多级关联（3级以上）的从对象不校验数据权限
        objectDescribe.getMasterDetailFieldDescribe().ifPresent(x -> {
            IObjectDescribe masterDescribe = serviceFacade.findObject(controllerContext.getTenantId(), x.getTargetApiName());
            if (ObjectDescribeExt.of(masterDescribe).isSlaveObject()) {
                query.setDataRightsParameter(null);
            }
        });
    }

    protected void handleDataRights(Query query) {
        //多级关联（3级以上）的从对象不校验数据权限
        objectDescribe.getMasterDetailFieldDescribe().ifPresent(x -> {
            IObjectDescribe masterDescribe = serviceFacade.findObject(controllerContext.getTenantId(), x.getTargetApiName());
            if (ObjectDescribeExt.of(masterDescribe).isSlaveObject()) {
                query.setDataRightsParameter(null);
            }
        });
    }

    protected void handleFilters(SearchTemplateQuery query) {
        if (Objects.isNull(query)) {
            return;
        }

        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_660) && SearchTemplateQueryExt.of(query).isRectangleOrPentagonRelation()) {
            log.warn("mobile request before version {}, queryInfo=>{}", RequestUtil.VERSION_660, JsonUtil.toJson(query));
            throw new ValidateException(I18N.text(I18NKey.API_UPGRADE));
        }

        ObjectDataExt data = Objects.isNull(arg.getObjectData()) ? null : ObjectDataExt.of(arg.getObjectData());

        IObjectDescribe masterDescribe = null;
        if (Objects.nonNull(sourceDescribeExt) && sourceDescribeExt.isSlaveObject()) {
            masterDescribe = sourceDescribeExt.getMasterDetailFieldDescribe().map(field -> serviceFacade.findObject(controllerContext.getTenantId(), field.getTargetApiName())).orElse(null);
        }
        // 按钮不支持筛选
        if (BUTTON.equals(getTriggerPage())) {
            SearchTemplateQueryExt.removeMasterAndNativeObjVariableFilter(query.getWheres());
        }
        // 主从新建处理主对象，本对象作为变量的筛选条件
        skipFindDataByFilter.set(SearchTemplateQueryExt.of(query).handleWheresFilterByObjVariable(data, masterObjectData, masterDescribe, objectDescribe.getApiName()));

        // 主从同时新建的从对象处理四角、五角关系
        if (Objects.nonNull(sourceDescribeExt) && sourceDescribeExt.isCreateWithMaster()) {
            MasterDetailFieldDescribe field = sourceDescribeExt.getMasterDetailFieldDescribe()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
            if (Objects.isNull(masterDescribe)) {
                masterDescribe = serviceFacade.findObject(controllerContext.getTenantId(),
                        field.getTargetApiName());
            }
            SearchTemplateQueryExt.of(query).handleWheresFilter(data, masterObjectData, masterDescribe);
            infraServiceFacade.handleChainCycle(controllerContext.getUser(), query, masterObjectData, masterDescribe);
        } else {
            SearchTemplateQueryExt.of(query).handleWheresFilter(data);
        }
    }


    private Tuple<String, String> getObjectAndFunctionAPIName(IFilter filter) {
        String objectAPIName = getFunctionBindingObjectApiName();
        String functionAPIName = filter.getFieldValues().get(0);

        if (StringUtils.isEmpty(objectAPIName) || StringUtils.isEmpty(functionAPIName)) {
            return null;
        }

        return Tuple.of(objectAPIName, functionAPIName);
    }

    private String getFunctionBindingObjectApiName() {
        return _getFunctionBindingObjectApiName();
    }

    private String _getFunctionBindingObjectApiName() {
        // 点击关联按钮，函数绑定在当前对象的 lookup 字段上，查询的也是当前对象的数据
        if (!isListPage()) {
            return arg.getTargetObjectApiName();
        }
        if (REFERENCE_CREATE.equals(getTriggerSource())) {
            return arg.getObjectApiName();
        }
        // 函数绑定在相关对象的 lookup 字段上，查询的是关联对象的数据
        return CollectionUtils.notEmpty(arg.getObjectData()) ?
                arg.getObjectData().toObjectData().getDescribeApiName() :
                arg.getTargetObjectApiName();
    }

    private Tuple<IObjectData, Map<String, List<IObjectData>>> getFunctionData() {
        // 点击关联按钮，获取当前详情页数据的id
        if ((!isListPage())) {
            IObjectData objectData = serviceFacade.findObjectData(controllerContext.getUser(), arg.getTargetObjectDataId(), arg.getTargetObjectApiName());
            return Tuple.of(objectData, Maps.newHashMap());
        }
        return FunctionParamBuildUtils.getFunctionData(arg.getMasterData(), arg.getObjectData(), arg.getDetails());
    }


    protected void handleFiltersByFiledRelation(Query query) {
        // lookup 多角关系
        handleFiltersByFieldCascade(query);
        // lookup 函数过滤
        handleFiltersByFunc(query);
    }

    /**
     * lookup 级联关系
     *
     * @param query
     */
    private void handleFiltersByFieldCascade(Query query) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_660) && isRectangleOrPentagonRelation(query.getFilters())) {
            log.warn("mobile request before version {}, queryInfo=>{}", RequestUtil.VERSION_660, JsonUtil.toJson(query));
            throw new ValidateException(I18N.text(I18NKey.API_UPGRADE));
        }
        ObjectDataExt data = Objects.isNull(arg.getObjectData()) ? null : ObjectDataExt.of(arg.getObjectData());
        // 处理三角、四角、五角关系筛选条件中的变量替换
        handleFilters(query, data);
        //主对象、本对象筛选条件中的变量替换
        handleFiltersByObjVariable(query, data);
    }

    public void handleFiltersByObjVariable(Query query, ObjectDataExt data) {
        // 按钮不支持筛选
        if (BUTTON.equals(getTriggerPage())) {
            query.removeIf(it -> it.isFilterNode()
                    && (FilterExt.of(it.getFilter()).hasNativeObjectVariable()
                    || FilterExt.of(it.getFilter()).hasMasterFieldVariableValueType()));
            return;
        }
        if (Objects.isNull(sourceDescribeExt) || !sourceDescribeExt.isSlaveObject()) {
            if (CollectionUtils.notEmpty(query.getFilters().stream().filter(x -> FilterExt.of(x).hasMasterFieldVariableValueType()).collect(Collectors.toList())) && Objects.nonNull(sourceDescribeExt)) {
                log.error("filter has dirty data ,tenantId:{}, describeApiName:{},targetApiName:{}", sourceDescribeExt.getTenantId(), sourceDescribeExt.getApiName(), objectDescribe.getApiName());
            }
            query.removeIf(it -> it.isFilterNode()
                    && FilterExt.of(it.getFilter()).hasMasterFieldVariableValueType());
        }
        String pattern = query.toSearchTemplateQuery().getPattern();
        query.getFilters().stream()
                .filter(filter -> (FilterExt.of(filter).hasMasterFieldVariableValueType()
                        || FilterExt.of(filter).hasNativeObjectVariable())
                        && CollectionUtils.notEmpty(filter.getFieldValues()))
                .forEach(filter -> {
                    Object o = getFieldValue(data, masterObjectData, filter);
                    if (FilterExt.of(filter).handleFilterValueReturnSkipData(o) && !pattern.contains("OR")) {
                        skipFindDataByFilter.set(true);
                    }
                });
    }

    public static void removeQueryFilter(SearchQuery searchQuery, Set<String> removeFilterVariableSet) {
        if (Objects.isNull(searchQuery) || CollectionUtils.empty(removeFilterVariableSet)) {
            return;
        }
        List<SearchQuery> searchQueryList = searchQuery.getSearchQueryContainer();
        recursionSearchQuery(searchQueryList, removeFilterVariableSet, false);
    }

    private static boolean recursionSearchQuery(List<SearchQuery> searchQueryList, Set<String> removeFilterVariableSet, boolean isRemoveSuperior) {
        AtomicBoolean isRemove = new AtomicBoolean(false);
        searchQueryList.removeIf(searchQuery -> {
            if (isRemove.get()) {
                return false;
            }
            SearchQuery.Connector connector = searchQuery.getConnector();
            IFilter filter = searchQuery.getFilter();
            List<SearchQuery> searchQueryContainerList = searchQuery.getSearchQueryContainer();
            if (CollectionUtils.notEmpty(searchQueryContainerList)) {
                boolean b = recursionSearchQuery(searchQueryContainerList, removeFilterVariableSet, "AND".equals(connector.toString()));
                if (isRemoveSuperior && b) {
                    isRemove.set(true);
                    return true;
                }
                return b;
            }
            if (Objects.isNull(filter)) {
                return false;
            }
            if (removeFilterVariableSet.contains(filter.getValueType() + "." + filter.getFieldName() + "." + filter.getFieldValues().get(0))) {
                if ("AND".equals(connector.toString())) {
                    isRemove.set(true);
                } else {
                    return true;
                }
            }
            return false;
        });
        return isRemove.get();
    }


    private void handleFilters(Query query, ObjectDataExt data) {
        // data 为空，移除所有三角、四角关系筛选条件
        if (Objects.isNull(data)) {
            query.removeIf(it -> it.isFilterNode()
                    && (FilterExt.of(it.getFilter()).hasRefObjectVariableValueType() ||
                    FilterExt.of(it.getFilter()).hasRelatedChainObjectVariableValueType() ||
                    FilterExt.of(it.getFilter()).hasSpecialRefObjectVariable() ||
                    FilterExt.of(it.getFilter()).hasLookupI18NVariableValueType()));
            return;
        }
        // 三角、四角关系的变量替换
        query.getFilters().stream()
                .filter(filter -> (FilterExt.of(filter).hasRefObjectVariableValueType()
                        || FilterExt.of(filter).hasLookupI18NVariableValueType()
                        || FilterExt.of(filter).hasSpecialRefObjectVariable())
                        && CollectionUtils.notEmpty(filter.getFieldValues()))
                .forEach(filter -> {
                    Object o = getFieldValue(data, masterObjectData, filter);
                    FilterExt filterExt = FilterExt.of(filter);
                    filterExt.setMasterFieldWhenHasSpecialRefObjectVariable();
                    filterExt.handleFilterValueReturnSkipData(o);
                });

        if (Objects.isNull(sourceDescribeExt) || !sourceDescribeExt.isCreateWithMaster()) {
            return;
        }
        // 处理五角关系
        Optional<MasterDetailFieldDescribe> masterField = sourceDescribeExt.getMasterDetailFieldDescribe();
        if (!masterField.isPresent()) {
            return;
        }
        IObjectDescribe masterDescribe = serviceFacade.findObject(controllerContext.getTenantId(), masterField.get().getTargetApiName());
        infraServiceFacade.handleChainCycle(controllerContext.getUser(), query, masterObjectData, masterDescribe);
    }

    private Object getFieldValue(ObjectDataExt objectData, IObjectData masterData, IFilter filter) {
        if (Objects.isNull(sourceDescribeExt) || !sourceDescribeExt.isCreateWithMaster()) {
            String expression = FilterExt.of(filter).getVariableNameInValues();
            return Objects.isNull(objectData) ? null : objectData.get(expression);
        }
        Optional<MasterDetailFieldDescribe> masterField = sourceDescribeExt.getMasterDetailFieldDescribe();
        if (!masterField.isPresent()) {
            return null;
        }
        IObjectDescribe masterDescribe = serviceFacade.findObject(controllerContext.getTenantId(), masterField.get().getTargetApiName());

        Tuple<Boolean, String> tuple = FilterExt.of(filter).getVariableNameInValuesAndIsInMaster();
        if (Boolean.FALSE.equals(tuple.getKey())) {
            return Objects.isNull(objectData) ? null : objectData.get(tuple.getValue());
        }
        if (Objects.nonNull(masterData)) {
            if (Objects.nonNull(masterData.get(tuple.getValue())) || FilterExt.of(filter).hasMasterFieldVariableValueType()) {
                return masterData.get(tuple.getValue());
            }
        }
        if (FilterExt.of(filter).hasMasterFieldVariableValueType()) {
            return null;
        }
        log.warn("field value is empty, masterApiName=>{}, fieldApiName=>{}", masterDescribe.getApiName(), tuple.getValue());
        throw new ValidateException(I18N.text(I18NKey.SELECT_FIELD_FIRST, masterDescribe.getDisplayName(),
                ObjectDescribeExt.of(masterDescribe).getFieldLabelByName(tuple.getValue())));
    }

    /**
     * 有四角或五角关系
     *
     * @param filters
     * @return
     */
    private boolean isRectangleOrPentagonRelation(List<IFilter> filters) {
        return CollectionUtils.nullToEmpty(filters).stream()
                .anyMatch(it -> FilterExt.of(it).hasRelatedChainObjectVariableValueType()
                        || FilterExt.of(it).isRectangleRelation());
    }

    /**
     * 当走到这个方法的时候，说明 对象和企业都灰度了或查询。
     *
     * @param query
     */
    private void handleFiltersByFunc(Query query) {
        IFilter filter = null;
        for (int i = 0; i < query.getFilters().size(); i++) {
            IFilter currentFilter = query.getFilters().get(i);
            if (ObjectUtils.equals(currentFilter.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE)) {
                filter = currentFilter;
            }
        }
        if (Objects.isNull(filter)) {
            return;
        }

        Tuple<String, String> objectAndFunctionAPIName = getObjectAndFunctionAPIName(filter);
        if (Objects.isNull(objectAndFunctionAPIName)) {
            return;
        }
        RunResult runResult = serviceFacade.getFunctionLogicService().findAndExecuteFunction(controllerContext.getUser(), getTupleSupplier(), objectAndFunctionAPIName.getKey(), objectAndFunctionAPIName.getValue());
        relatedObjectDataSpecified = FunctionQueryTemplateUtils.handleFunctionResult(controllerContext.getTenantId(), query, filter, runResult.getReturnType(), runResult.getFunctionResult());
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        if (arg.whatRelatedType()) {
            WhatFieldDescribe whatFieldDescribe = getWhatFieldDescribe();
            if (!objectDescribe.isActive() || !whatFieldDescribe.isActive()) {
                log.warn("The object or field is disabled! tenantId:{}, objectApiName:{}, fieldApiName:{}", controllerContext.getTenantId(), objectDescribe.getApiName(), whatFieldDescribe.getApiName());
                return new QueryResult<>();
            }
        }
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        // changeOrder 对象补充原始字段 original_
        if (!isListPage() && isAssociated()) {
            objectDescribe.getMasterDetailFieldDescribe()
                    .filter(field -> Objects.equals(field.getTargetApiName(), arg.getTargetObjectApiName())
                            && Objects.equals(field.getTargetRelatedListName(), arg.getRelatedListName()))
                    .ifPresent(field -> infraServiceFacade.findAndMergeObjectDataWithOriginalData(controllerContext.getUser(), objectDescribe, queryResult.getData()));
        }
        log.debug("StandardRelatedListController getQueryResult,tenantId:{},describeApiName:{}, size:{}",
                controllerContext.getTenantId(), objectDescribe.getApiName(), queryResult.getData().size());
        return queryResult;
    }

    private boolean isPayment() {
        return ObjectDescribeExt.isPayment(arg.getObjectApiName());
    }

    private Result getPaymentListResult() {
        List<LayoutDocument> layouts = Lists.newArrayList(LayoutDocument.of(LayoutExt.buildPaymentListLayout()));
        String tenantId = controllerContext.getTenantId();
        IObjectDescribe describe = serviceFacade.findObject(tenantId, arg.getTargetObjectApiName());
        IObjectData data = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(), arg
                .getTargetObjectDataId(), describe.getApiName());

        List<PaymentRecord> paymentRecords = serviceFacade.findPaymentList(controllerContext.getUser(), describe, data);

        return Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(objectDescribe))
                .listLayouts(layouts)
                .layout(layouts.get(0))
                .dataList(ObjectDataDocument.ofPaymentList(paymentRecords))
                .total(paymentRecords.size())
                .build();
    }

    protected void modifyQueryByWhatFieldName(Query query) {
        if (isListPage()) {
            return;
        }
        WhatFieldDescribe whatField = getWhatFieldDescribe();
        String apiNameFieldApiName = whatField.getApiNameFieldApiName();
        String idFieldApiName = whatField.getIdFieldApiName();
        query.and(FilterExt.of(Operator.EQ, apiNameFieldApiName, arg.getTargetObjectApiName()).getFilter());
        query.and(FilterExt.of(Operator.HASANYOF, idFieldApiName, arg.getTargetObjectDataId()).getFilter());
    }

    protected void modifyQueryByWhatFieldName(SearchTemplateQuery query) {
        if (isListPage()) {
            return;
        }
        WhatFieldDescribe whatField = getWhatFieldDescribe();
        String apiNameFieldApiName = whatField.getApiNameFieldApiName();
        String idFieldApiName = whatField.getIdFieldApiName();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, apiNameFieldApiName, arg.getTargetObjectApiName());
        SearchTemplateQueryExt.of(query).addFilter(Operator.HASANYOF, idFieldApiName, arg.getTargetObjectDataId());
    }

    private WhatFieldDescribe getWhatFieldDescribe() {
        String fieldApiName = (String) arg.getRelatedListComponent().get("field_api_name");
        if (Strings.isNullOrEmpty(fieldApiName)
                || Objects.isNull(objectDescribe.getFieldDescribe(fieldApiName))
                || !FieldDescribeExt.of(objectDescribe.getFieldDescribe(fieldApiName)).isWhatField()) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        WhatFieldDescribe whatField = (WhatFieldDescribe) objectDescribe.getFieldDescribe(fieldApiName);
        return whatField;
    }

    @Override
    protected boolean needSearchRichTextExtra() {
        return Boolean.TRUE.equals(arg.getSearchRichTextExtra());
    }

    @Override
    protected List<IFieldDescribe> getMaskEncryptFields() {
        Map<String, List<String>> maskFieldApiNames = arg.getMaskFieldApiNames();
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return Lists.newArrayList();
        }
        return objectDescribe.getFieldByApiNames(maskFieldApiNames.get(objectDescribe.getApiName()));
    }

    @Override
    protected boolean needEncryptMaskFieldsWithDescribe() {
        return BooleanUtils.isTrue(arg.getEncryptMaskFieldsWithDescribe());
    }

    protected boolean maskFieldEncryptObjectPagesGray() {
        return AppFrameworkConfig.maskFieldEncryptObjectPagesGray(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    protected void modifyQueryByRefFieldName(SearchTemplateQuery query) {
        if (isListPage()) {
            return;
        }
        Optional<ObjectReferenceWrapper> refField = objectDescribe.getReferenceField(arg.getTargetObjectApiName(), arg.getRelatedListName());
        String refFieldName = objectDescribe.getRefFieldName(refField)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        if (refField.isPresent() && IFieldType.OBJECT_REFERENCE_MANY.equals(refField.get().getType())) {
            if (isAssociated()) {
                SearchTemplateQueryExt.of(query).addFilter(Operator.HASANYOF, refFieldName, arg.getTargetObjectDataId());
            } else if (isNotAssociated()) {
                SearchTemplateQueryExt.of(query).addFilter(Operator.NHASANYOF, refFieldName, arg.getTargetObjectDataId());
            }
            return;
        }
        if (isAssociated()) {
            //场景2.在详情页的相关对象下点击 "查看全部"
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, refFieldName, arg.getTargetObjectDataId());
        } else if (isNotAssociated()) {
            //场景3:在详情页中的相关对象下点击 "关联" 的场景,这时要查询出来为空
            Boolean isMultiField = objectDescribe.getFieldDescribe(refFieldName).getIsMultiField();
            if (Boolean.TRUE.equals(isMultiField)) {
                SearchTemplateQueryExt.of(query).addFilter(Operator.N, refFieldName, arg.getTargetObjectDataId());
            } else {
                SearchTemplateQueryExt.of(query).addFilter(Operator.IS, refFieldName, "");
            }
        }
        // 点击关联按钮时,处理配置的函数过滤条件
        // from_associate_range 为空,且是在详情页的相关对象下点击 "查看全部"的场景,不需要执行函数
        if (!fromAssociate() && isAssociated()) {
            return;
        }
        // 关联按钮用户绑定的筛选条件
        List<Wheres> wheres = refField.map(ObjectReferenceWrapper::getRelatedWheres).orElseGet(Collections::emptyList);
        query.setWheres(wheres);
        relatedObjectDataSpecified = serviceFacade.getFunctionLogicService().handleFiltersByValueType(controllerContext.getUser(), getFunctionBindingObjectApiName(), query, getTupleSupplier());
    }

    private boolean fromAssociate() {
        return !Strings.isNullOrEmpty(arg.getFromAssociateRange());
    }

    protected void modifyQueryByRefFieldName(Query query) {
        if (isListPage()) {
            return;
        }
        Optional<ObjectReferenceWrapper> refField = objectDescribe.getReferenceField(arg.getTargetObjectApiName(), arg.getRelatedListName());
        String refFieldName = objectDescribe.getRefFieldName(refField)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));

        if (refField.isPresent() && IFieldType.OBJECT_REFERENCE_MANY.equals(refField.get().getType())) {
            if (isAssociated()) {
                query.and(FilterExt.of(Operator.HASANYOF, refFieldName, arg.getTargetObjectDataId()).getFilter());
            } else if (isNotAssociated()) {
                query.and(FilterExt.of(Operator.NHASANYOF, refFieldName, arg.getTargetObjectDataId()).getFilter());
            }
            return;
        }

        if (isAssociated()) {
            //场景2.在详情页的相关对象下点击 "查看全部"
            query.and(FilterExt.of(Operator.EQ, refFieldName, arg.getTargetObjectDataId()).getFilter());
        } else if (isNotAssociated()) {
            //场景3:在详情页中的相关对象下点击 "关联" 的场景,这时要查询出来为空
            Boolean isMultiField = objectDescribe.getFieldDescribe(refFieldName).getIsMultiField();
            if (Boolean.TRUE.equals(isMultiField)) {
                query.and(FilterExt.of(Operator.N, refFieldName, arg.getTargetObjectDataId()).getFilter());
            } else {
                query.and(FilterExt.of(Operator.IS, refFieldName, "").getFilter());
            }
        }
        // 点击关联按钮时,处理配置的函数过滤条件
        // from_associate_range 为空,且是在详情页的相关对象下点击 "查看全部"的场景,不需要执行函数
        if (!fromAssociate() && isAssociated()) {
            return;
        }
        // 关联按钮用户绑定的筛选条件
        SearchQuery searchQuery = refField
                .map(ObjectReferenceWrapper::getRelatedWheres)
                .map(SearchQueryImpl::wheres)
                .orElse(null);
        query.and(searchQuery);
        handleFiltersByFunc(query);
    }

    private boolean isAssociated() {
        return arg.isIncludeAssociated();
    }

    private boolean isNotAssociated() {
        if (isAssociated()) {
            return false;
        }
        if (!fromAssociate()) {
            return true;
        }
        return "notAssociated".equals(arg.getFromAssociateRange());
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return arg.getDescribeVersionMap();
    }

    @Override
    protected String getUsePageType() {
        return ButtonUsePageType.RelatedList.getId();
    }

    private void findMasterObjectData() {
        if (!isListPage()) {
            return;
        }
        if (CollectionUtils.notEmpty(arg.getMasterData())) {
            masterObjectData = arg.getMasterData().toObjectData();

            Optional.ofNullable(getSourceDescribe())
                    .flatMap(ObjectDescribeExt::getMasterDetailFieldDescribe)
                    .ifPresent(field -> {
                        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), field.getTargetApiName());
                        decodeMaskFieldEncryptValue(describe, masterObjectData);
                    });
        }
        if (getSourceDescribe() == null || !sourceDescribeExt.isCreateWithMaster() || Objects.nonNull(masterObjectData)) {
            return;
        }

        Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe =
                sourceDescribeExt.getMasterDetailFieldDescribe();
        masterDetailFieldDescribe.ifPresent(x -> {
            Object o = arg.getObjectData().toObjectData().get(x.getApiName());
            if (o instanceof String && !Strings.isNullOrEmpty((String) o)) {
                masterObjectData = serviceFacade.findObjectData(controllerContext.getUser(), (String) o,
                        x.getTargetApiName());
            }
        });
    }

    protected ObjectDescribeExt getSourceDescribe() {
        if (sourceDescribeExt != null) {
            return sourceDescribeExt;
        }
        String sourceDescribeApiName = getSourceDescribeApiName();
        if (Strings.isNullOrEmpty(sourceDescribeApiName)) {
            return null;
        }
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), sourceDescribeApiName);
        sourceDescribeExt = ObjectDescribeExt.of(describe);
        return sourceDescribeExt;
    }

    private String getSourceDescribeApiName() {

        if (CollectionUtils.empty(arg.getObjectData())) {
            return arg.getSourceDescribeApiName();
        }
        String sourceDescribeApiName = arg.getObjectData().toObjectData().getDescribeApiName();
        if (Strings.isNullOrEmpty(sourceDescribeApiName)) {
            return arg.getSourceDescribeApiName();
        }
        return sourceDescribeApiName;
    }

    public boolean isDoublePull() {
        return REFERENCE_CREATE.equals(getTriggerSource())
                && (StringUtils.equalsIgnoreCase(getTriggerPage(), "Add") || StringUtils.equalsIgnoreCase(getTriggerPage(), "Edit"));
    }

    @Override
    protected final boolean customSkipHandler() {
        return isPayment();
    }

    @Override
    protected final BaseListHandler.Arg<StandardRelatedListController.Arg> buildListHandlerArg() {
        return new RelatedListHandler.Arg();
    }

    @Override
    protected final void updateSearchQueryInfo(String searchQueryInfo) {
        this.arg.setSearchQueryInfo(searchQueryInfo);
    }

    @Override
    protected final DomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return RelatedListControllerDomainPlugin.Arg.builder().arg(arg).result(result).build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        if (pluginResult instanceof RelatedListControllerDomainPlugin.Result) {
            RelatedListControllerDomainPlugin.Result relateListPluginResult = (RelatedListControllerDomainPlugin.Result) pluginResult;
            if (ControllerDomainPlugin.BEFORE.equals(method) && Objects.nonNull(relateListPluginResult.getArg())) {
                if (!Strings.isNullOrEmpty(relateListPluginResult.getArg().getSearchQueryInfo())) {
                    updateSearchQueryInfo(relateListPluginResult.getArg().getSearchQueryInfo());
                }
            }
            if (ControllerDomainPlugin.AFTER.equals(method) && Objects.nonNull(relateListPluginResult.getResult())) {
                if (Objects.nonNull(relateListPluginResult.getResult().getDataList())) {
                    this.result.setDataList(relateListPluginResult.getResult().getDataList());
                }
            }
        }
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("associated_object_describe_api_name")
        @SerializedName("associated_object_describe_api_name")
        private String objectApiName;

        @JSONField(name = "M2")
        @JsonProperty("associated_object_field_related_list_name")
        @SerializedName("associated_object_field_related_list_name")
        private String relatedListName;

        @JSONField(name = "M3")
        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        private String searchQueryInfo;

        @JSONField(name = "M4")
        @JsonProperty("associate_object_data_id")
        @SerializedName("associate_object_data_id")
        private String targetObjectDataId;

        @JSONField(name = "M5")
        @JsonProperty("associate_object_describe_api_name")
        @SerializedName("associate_object_describe_api_name")
        private String targetObjectApiName;

        /**
         * 是否为关联的
         */
        @JSONField(name = "M6")
        @JsonProperty("include_associated")
        @SerializedName("include_associated")
        private boolean includeAssociated;

        @JSONField(name = "M7")
        @JsonProperty("search_template_id")
        @SerializedName("search_template_id")
        private String searchTemplateId;

        @JSONField(name = "M8")
        @JsonProperty("object_data")
        @SerializedName("object_data")
        private ObjectDataDocument objectData;

        //返回值是否包含组织机构信息
        @JSONField(name = "M9")
        @JsonProperty("include_org_info")
        @SerializedName("include_org_info")
        private Boolean includeOrgInfo = false;

        @JSONField(name = "M10")
        private Map<String, Integer> describeVersionMap;

        @JSONField(name = "M11")
        @JsonProperty("master_data")
        @SerializedName("master_data")
        private ObjectDataDocument masterData;

        @JSONField(name = "M12")
        @JsonProperty("ignore_scene_filter")
        @SerializedName("ignore_scene_filter")
        private boolean isIgnoreSceneFilter;

        @JSONField(name = "M13")
        @JsonProperty("include_describe")
        @SerializedName("include_describe")
        private boolean includeDescribe = true;

        @JSONField(name = "M14")
        @JsonProperty("include_layout")
        @SerializedName("include_layout")
        private boolean includeLayout = true;

        @JSONField(name = "M15")
        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        String searchTemplateType;

        @JsonProperty("search_template_api_name")
        @SerializedName("search_template_api_name")
        private String searchTemplateApiName;

        @JsonProperty("ignore_scene_record_type")
        @SerializedName("ignore_scene_record_type")
        boolean isIgnoreSceneRecordType;

        @JSONField(name = "M18")
        @JsonProperty("is_ordered")
        @SerializedName("is_ordered")
        boolean isOrdered;

        @JSONField(name = "M19")
        @JsonProperty("location_info")
        @SerializedName("location_info")
        LocationInfo locationInfo;


        @JsonProperty("find_explicit_total_num")
        @SerializedName("find_explicit_total_num")
        private Boolean findExplicitTotalNum;

        @JsonProperty("untranslation")
        @SerializedName("untranslation")
        private Boolean untranslation;

        @JsonProperty("filter_product_ids")
        @SerializedName("filter_product_ids")
        private List<String> filterProductIds;

        @JSONField(name = "M23")
        @JsonProperty("details")
        @SerializedName("details")
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();

        @JSONField(name = "M24")
        @JsonProperty("field_projection")
        @SerializedName("field_projection")
        List<String> fieldProjection;

        /**
         * 查询富文本
         */
        @JSONField(name = "search_rich_text_extra")
        @JsonProperty("search_rich_text_extra")
        @SerializedName("search_rich_text_extra")
        private Boolean searchRichTextExtra;
        /**
         * 通过那个字段调用的选数据接口
         */
        private String relatedFieldName;

        /**
         * 源对象 apiName
         */
        private String sourceDescribeApiName;

        //是否序列化返回对象中的空值
        private Boolean serializeEmpty;
        //是否将人员部门字段的扩展信息提取到外层
        private Boolean extractExtendInfo;

        //Detail接口返回的布局中的相关对象组件(用于加工场景和按钮)
        @JSONField(name = "related_list_component")
        @JsonProperty("related_list_component")
        @SerializedName("related_list_component")
        private Map<String, Object> relatedListComponent;

        @JSONField(name = "related_type")
        @JsonProperty("related_type")
        @SerializedName("related_type")
        private String relatedType;

        @JSONField(name = "trigger_info")
        @JsonProperty("trigger_info")
        @SerializedName("trigger_info")
        private TriggerInfo triggerInfo;

        /**
         * 全部数据： all
         * 已关联数据：associated
         * 未关联数据：notAssociated
         */
        @JSONField(name = "from_associate_range")
        @JsonProperty("from_associate_range")
        @SerializedName("from_associate_range")
        private String fromAssociateRange;

        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;
        /**
         * 将描述下所有掩码字段都加密
         */
        private Boolean encryptMaskFieldsWithDescribe;

        //扩展属性，用于传递业务参数
        private Map<String, Object> extraData;

        public boolean whatRelatedType() {
            return GroupField.GROUP_TYPE_WHAT.equals(getRelatedType());
        }

        public boolean serializeEmpty() {
            return !Boolean.FALSE.equals(serializeEmpty);
        }

        public boolean extractExtendInfo() {
            return Boolean.TRUE.equals(extractExtendInfo);
        }


        /**
         * 为 plugin 服务的 copy 方法
         *
         * @return plugin 需要用到的属性
         */
        public Arg copy2Plugin() {
            Arg arg = new Arg();
            arg.setObjectApiName(objectApiName);
            arg.setRelatedFieldName(relatedListName);
            arg.setSearchQueryInfo(searchQueryInfo);
            arg.setTargetObjectDataId(targetObjectDataId);
            arg.setTargetObjectApiName(targetObjectApiName);
            if (CollectionUtils.notEmpty(objectData)) {
                arg.setObjectData(ObjectDataDocument.of(ObjectDataExt.of(objectData).copy()));
            }
            return arg;
        }
    }

    @Data
    public static class LocationInfo {
        String longitude;
        String latitude;
        String distance;
    }

    @Data
    public static class TriggerInfo {
        /**
         * Add 新建页面
         * Edit 编辑页面
         * Button 按钮
         */
        @JSONField(name = "trigger_page")
        @JsonProperty("trigger_page")
        @SerializedName("trigger_page")
        private String triggerPage;

        private String source;
    }

}
