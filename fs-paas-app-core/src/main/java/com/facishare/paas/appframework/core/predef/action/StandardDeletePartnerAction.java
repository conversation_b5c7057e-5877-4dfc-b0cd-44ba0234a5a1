package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class StandardDeletePartnerAction extends BaseUpdatePartnerAction<StandardDeletePartnerAction.Arg, BaseUpdatePartnerAction.Result> {
    protected StandardDeletePartnerAction.Result result = StandardDeletePartnerAction.Result.builder().errorCode("0").message(I18N.text(I18NKey.REMOVE_PARTNER_SUCCESS)).build();

    protected PartnerRemindOutUserService partnerRemindOutUserService = SpringUtil.getContext().getBean(PartnerRemindOutUserService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(StandardAction.DeletePartner.getFunPrivilegeCodes());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIds();
    }

    private List<OutInfoChangeModel> outInfoChangeModelList = Lists.newArrayList();

    @Override
    protected void before(Arg arg) {
        log.info("StandardDeletePartnerAction before arg {}", arg);
        super.before(arg);
        if (CollectionUtils.isEmpty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(I18NKey.DATA_ID_EMPTY));
        }
        objectDataList = this.findByIdsIncludeLookUpName(this.actionContext.getUser(), this.objectDescribe.getApiName(), arg.getDataIds());
        if (CollectionUtils.isEmpty(objectDataList)) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_DATA_NOT_FOUND));
        }

        for (IObjectData objectData : objectDataList) {
            ObjectDataExt objectExt = ObjectDataExt.of(objectData);
            if (objectExt.isInvalid()) {
                throw new ValidateException(I18N.text(I18NKey.IS_INVALID_CAN_NOT_OPERATION, objectExt.getName()));
            }
            if (objectExt.isLock()) {
                throw new ValidateException(I18N.text(I18NKey.IS_LOCK_CAN_NOT_OPERATION, objectExt.getName()));
            }
            OutInfoChangeModel model = OutInfoChangeModel
                    .builder()
                    .dataId(objectData.getId())
                    .dataName(objectData.getName())
                    .newOutEI(0)
                    .newOutUserId(0)
                    .displayName(objectDescribe.getDisplayName())
                    .isPreDefineObj("package".equals(objectDescribe.getDefineType()) ? true : false)
                    .build();
            Long outUserId = 0L;
            model.setOldOutEI(StringUtils.isBlank(objectData.getOutTenantId()) ? 0 : Integer.parseInt(objectData.getOutTenantId()));
            if (objectData.getOutOwner().size() > 0) {
                outUserId = StringUtils.isBlank(objectData.getOutOwner().get(0).toString()) ?
                        0L : Long.parseLong(objectData.getOutOwner().get(0).toString());
            }
            model.setOldOutUserId(outUserId.intValue());
            outInfoChangeModelList.add(model);

        }
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!result.isSuccess()) {
            return result;
        }
        for (IObjectData objectData : dataList) {
            objectData.set(ObjectDataExt.PARTNER_ID, Strings.EMPTY);
            removeOutOwnerAndTeam(objectData, arg.getRemoveOutOwner());
        }
        List<String> updateFields = Lists.newArrayList(ObjectDataExt.PARTNER_ID,
                ObjectDataExt.OUTER_TENANT,
                ObjectDataExt.OUTER_OWNER);
        serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, updateFields);
        serviceFacade.batchUpdateRelevantTeam(actionContext.getUser(), dataList, false);
        return result;
    }

    private void removeOutOwnerAndTeam(IObjectData objectData, Boolean removeOutOwner) {
        String partnerId = Optional.ofNullable(objectData.get("partner_id")).orElse("").toString();
        StandardChangePartnerAction.OutUser outUser = getOutUser(actionContext.getUser(), partnerId);
        removeNormalMember(objectData, outUser.getOutUserId(), outUser.getOutTenantId());
        if (removeOutOwner == null || Boolean.TRUE.equals(removeOutOwner)) {
            objectData.setOutOwner(null);
            objectData.setOutTenantId(null);
            super.updateOwnerToTeamMember(objectData, null, null);
        }
    }

    private void removeNormalMember(IObjectData objectData, Long outUserId, Integer outTenantId) {
        if (outTenantId == null || outUserId == null) {
            return;
        }
        //如果是从对象,则不需要处理相关团队,因为从对象的相关团队是绑定在主上的。
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        List<TeamMember> needRemoveMember = teamMembers.stream().filter(f -> f.getRole() == TeamMember.Role.NORMAL_STAFF)
                .filter(TeamMember::isOutMember)
                .filter(f -> String.valueOf(outUserId).equals(f.getEmployee()))
                .filter(f -> String.valueOf(outTenantId).equals(f.getOutTenantId()))
                .collect(Collectors.toList());
        teamMembers.removeAll(needRemoveMember);
        objectDataExt.setTeamMembers(teamMembers);

    }

    private StandardChangePartnerAction.OutUser getOutUser(User user, String partnerId) {
        Integer outTenantId = 0;
        Long outOwnerId = 0L;
        if (StringUtils.isBlank(partnerId)) {
            return StandardChangePartnerAction.OutUser.builder()
                    .outUserId(outOwnerId)
                    .outTenantId(outTenantId)
                    .partnerId(partnerId)
                    .build();
        }
        Map<String, RelationDownstreamResult> downstreamMap = enterpriseRelationService.getRelationDownstreamInfo(user.getTenantId(), Sets.newHashSet(partnerId));
        RelationDownstreamResult downstream = downstreamMap.get(partnerId);
        if (downstream != null) {
            outTenantId = downstream.getDownstreamOuterTenantId();
            outOwnerId = downstream.getRelationOwnerOuterUid();
        }
        return StandardChangePartnerAction.OutUser.builder()
                .outUserId(outOwnerId)
                .outTenantId(outTenantId)
                .partnerId(partnerId)
                .build();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        dealDetail(dataList, null, 0, 0L);
        String apiName = actionContext.getObjectApiName();
        Integer remindRecordType = 0;
        if (PrmConstant.unSupportOldObject.contains(apiName)) {
            remindRecordType = partnerRemindOutUserService.getChangePartnerRemindRecordType(apiName);
        } else {
            remindRecordType = 92;
        }
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> {
            for (IObjectData data : objectDataList) {
                this.logDeletePartner(actionContext.getUser(), EventType.DELETE, ActionType.DELETE_PARTNER, data, objectDescribe);
            }
        });
        try {
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        partnerRemindOutUserService.remindOutUser(actionContext.getUser(), apiName, remindRecordType, outInfoChangeModelList);

        return result;
    }


    protected void logDeletePartner(User user, EventType eventType, ActionType actionType,
                                    IObjectData data, IObjectDescribe objectDescribe) {
        String partnerName = getPartnerName(data);

        List<LogInfo.LintMessage> textMsg = new ArrayList<>();
        textMsg.add(new LogInfo.LintMessage(I18N.text(I18NKey.IN, objectDescribe.getDisplayName()), "", ""));
        textMsg.add(new LogInfo.LintMessage(data.getName(), data.getId(), objectDescribe.getApiName()));
        textMsg.add(new LogInfo.LintMessage(I18N.text(I18NKey.REMOVE, partnerName), "", ""));
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder().textMsg(textMsg).build();
        serviceFacade.logWithCustomMessage(user, eventType, actionType, objectDescribe, data, snapshot.getMessage());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.DELETE_PARTNER.getButtonApiName();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        /**
         * 数据ID列表
         */
        private List<String> dataIds;

        /**
         * 合作伙伴数据id
         */
        private String partnerId;

        /**
         * 是否移除外部负责人，默认 true
         */
        private Boolean removeOutOwner;

        public static StandardDeletePartnerAction.Arg of(String dataId, String partnerId) {
            return new StandardDeletePartnerAction.Arg(Lists.newArrayList(dataId), partnerId, true);
        }

        public static StandardDeletePartnerAction.Arg of(String dataId, String partnerId, Boolean removeOutOwner) {
            return new StandardDeletePartnerAction.Arg(Lists.newArrayList(dataId), partnerId, removeOutOwner);
        }

        public static StandardDeletePartnerAction.Arg of(String dataId) {
            return new StandardDeletePartnerAction.Arg(Lists.newArrayList(dataId), "", true);
        }
    }
}