package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.StandardAddUIAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardAddUIAction.Result;
import static com.facishare.paas.appframework.metadata.ObjectDataExt.*;

/**
 * create by zhaoju on 2020/09/06
 */
public class StandardAddUIAction extends AbstractStandardUIAction<Arg, Result> {

    private ILayout layout;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.AddUI.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void init() {
        super.init();
        this.objectData = arg.getObjectData().toObjectData();
        objectData.setTenantId(actionContext.getTenantId());
        objectData.setDescribeApiName(objectDescribe.getApiName());
        objectData.setCreatedBy(actionContext.getUser().getUserId());
        decodeMaskFieldEncryptValue();
        layout = findLayout();
    }

    @Override
    protected Result doAct(Arg arg) {
        doCalculate();
        stopWatch.lap("doCalculate");
        fillInfo(objectData);
        stopWatch.lap("fillInfo");
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .build();
    }

    /**
     * 参考对象映射的逻辑
     */
    private void doCalculate() {
        if (!AppFrameworkConfig.isAddEditUIActionGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return;
        }
        // 根据下游企业Id，补充『客户』『合作伙伴』id
        fillRelationOuterDataPrivilegeField(actionContext.getUser(), objectDescribe, objectData);
        boolean hasOwner = of(objectData).hasOwner();
        // 计算常量默认值
        calcDefaultValueWithConstant();
        modifyDataBeforeCalculate(actionContext.getUser(), objectDescribe, objectData, layout, hasOwner);
        // 需要计算的字段
        calcFormulaValue();
        ObjectDataExt.of(objectData).removeEmptyMultiCurrencyFields();
    }

    private void calcFormulaValue() {
        CalculateFields calculateFields = infraServiceFacade.computeCalculateFieldsForAddAction(objectDescribe, null, false);
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<CalculateRelation.RelateField> relateFields = calculateFields.getCalculateFieldMap().get(objectDataExt.getDescribeApiName());
        if (CollectionUtils.empty(relateFields)) {
            return;
        }
        // data 中已有的字段不需要再次计算（计算、统计还是需要计算的）
        Set<String> countAndFormula = ObjectDescribeExt.of(objectDescribe).getCountAndFormulaFields().stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        relateFields.removeIf(it -> objectDataExt.containsField(it.getFieldName()) && !countAndFormula.contains(it.getFieldName()));
        serviceFacade.batchCalculateBySortFields(actionContext.getUser(), objectData, null, calculateFields);
    }

    protected void modifyDataBeforeCalculate(User user, IObjectDescribe objectDescribe, IObjectData objectData, ILayout layout, boolean hasOwner) {
        //新建时，布局中隐藏了「负责人」字段且负责人为非必输字段，数据中默认不下发
        if (needRemoveOwnerField(user, objectDescribe, layout)
                // 按钮入参中的 data 存在 owner，则不清空负责人
                && !hasOwner) {
            ObjectDataExt.of(objectData).remove(Sets.newHashSet(IObjectData.OWNER, FieldDescribeExt.getLookupNameByFieldName(IObjectData.OWNER)));
        }
        ObjectDataExt.of(objectData).removeDefaultValueIfInvalid(objectDescribe);
    }

    private boolean needRemoveOwnerField(User user, IObjectDescribe describe, ILayout layout) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId())
                && user.isOutUser()) {
            return false;
        }
        Optional<IFieldDescribe> ownerField = ObjectDescribeExt.of(describe).getOwnerField();
        return !LayoutExt.of(layout).getField(IObjectData.OWNER).isPresent()
                && ownerField.isPresent()
                && !ownerField.get().isRequired();
    }

    private void calcDefaultValueWithConstant() {
        serviceFacade.calculateDefaultValueWithConstant(actionContext.getUser(), objectData, objectDescribe);
    }

    private void fillRelationOuterDataPrivilegeField(User user, IObjectDescribe describe, IObjectData data) {
        if (!user.isOutUser()) {
            return;
        }
        List<IObjectReferenceField> dataPrivilegeFields = ObjectDescribeExt.of(describe).getSupportRelationOuterOwnerFields();
        if (CollectionUtils.empty(dataPrivilegeFields)) {
            return;
        }
        // 根据下游企业Id，补充『客户』『合作伙伴』id
        for (IObjectReferenceField referenceField : dataPrivilegeFields) {
            String referenceId = data.get(referenceField.getApiName(), String.class);
            if (Strings.isNullOrEmpty(referenceId)) {
                String objectId = infraServiceFacade.getUpstreamMapperObjectId(user, referenceField.getTargetApiName());
                data.set(referenceField.getApiName(), objectId);
            }
        }
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        if (AppFrameworkConfig.isAddEditUIActionGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return ObjectAction.CREATE.getButtonApiName();
        }
        return null;
    }

    @Override
    protected Collection<String> getIgnoreFields() {
        Set<String> invisibleFields = Sets.newHashSet(super.getIgnoreFields());
        // 新建不能放出来的字段
        invisibleFields.addAll(DefObjConstants.getInvisibleFieldNameListForAddLayout(actionContext.getTenantId(), objectDescribe.getApiName()));

        invisibleFields.addAll(Arrays.asList(OWNER_DEPARTMENT, DATA_OWN_DEPARTMENT, DATA_OWN_ORGANIZATION));
        return invisibleFields;
    }

    private ILayout findLayout() {
        String recordType = objectData.getRecordType();
        return serviceFacade.findObjectLayoutWithTypeIncludeAllComponent(buildLayoutContext(),
                recordType, objectDescribe, LayoutTypes.ADD, objectData);
    }

    @Data
    public static class Arg {
        @JsonProperty("object_data")
        @JSONField(name = "object_data")
        private ObjectDataDocument objectData;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JsonProperty("object_data")
        @JSONField(name = "object_data")
        private ObjectDataDocument objectData;
    }
}
