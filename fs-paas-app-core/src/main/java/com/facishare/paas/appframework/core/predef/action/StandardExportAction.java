package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.openapi.Utils;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.plugin.APLExportPlugin;
import com.facishare.paas.appframework.core.model.plugin.PluginContext;
import com.facishare.paas.appframework.core.util.*;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.CustomSceneResult;
import com.facishare.paas.appframework.metadata.exception.ExportException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.util.ExcelUtil;
import com.facishare.paas.appframework.metadata.util.FileExtUtil;
import com.facishare.paas.appframework.metadata.util.XmlUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.paas.token.api.ITokenService;
import com.facishare.paas.token.model.TokenInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.log.dto.SecurityEventTrackingDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Charsets;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import com.google.common.hash.Hashing;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.util.AppIdMapping.DISPATCH_ORDERS;
import static com.facishare.paas.appframework.metadata.ImportServiceImpl.TEAM_MEMBER;
import static com.facishare.paas.appframework.metadata.util.FileExtUtil.IMAGE_FLAG;
import static com.facishare.paas.metadata.api.DBRecord.OUT_OWNER;
import static com.facishare.paas.metadata.api.IObjectData.*;

/**
 * Created by liyiguang on 2017/10/11.
 */
@Slf4j
public class StandardExportAction extends PreDefineAction<StandardExportAction.Arg, StandardExportAction.Result> {
    // 分批查询数据数量
    public final int dataBatchSize;
    public final int tokenExpireSeconds;
    public final int exportRowsThrottle;
    public final int exportRowsThrottleVip;
    public final int exportFileExpireDay;
    public final int exportFileAttachmentThrottle;
    public final int exportFileAttachmentThrottleVip;
    public final int exportMDThrottle;
    public final long exportFileMaxSize;
    public final long embeddedExcelImageMaxSize;

    public static final String ERROR_SPLIT = "$$";
    protected Map<String, IObjectDescribe> describeMap = Maps.newLinkedHashMap();
    protected Map<String, IObjectDescribe> detailDescribeMap = Maps.newLinkedHashMap();
    protected Map<String, IObjectDescribe> whatListDescribeMap = Maps.newLinkedHashMap();
    protected Map<String, Set<String>> objectApiNameAndNoExportPrivilegeFields = Maps.newHashMap();
    protected SearchTemplateQuery searchQuery;
    protected String token;
    protected boolean isGenerating;
    protected int totalCount = 0;
    protected Map<String, String> pathAndName = Maps.newLinkedHashMap();
    protected final ExcelUtil excelUtil;
    protected final ITokenService tokenService;
    private final TeamMemberRoleService teamMemberRoleService;

    /**
     * 导出是否按列表页
     */
    private boolean accordingToListOrder = true;
    private Map<String, OptionalFeaturesSwitchDTO> objectOptionalFeaturesSwitches = Maps.newHashMap();

    private static final String appName = ConfigHelper.getProcessInfo().getName();
    private static final String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static final String profile = ConfigHelper.getProcessInfo().getProfile();
    private static final String action = "export";
    protected Map<String, List<IObjectData>> fileAttachmentDataList = Maps.newLinkedHashMap();
    protected Map<String, List<IFieldDescribe>> fileAttachmentFields = Maps.newLinkedHashMap();

    protected Map<String, List<String>> exportFileAttachmentFields = Maps.newLinkedHashMap();

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final List<String> whatListObjectApiNames = Lists.newArrayList("ApprovalTaskObj", "BpmTask", "StageTaskObj");
    private boolean customTeamRoleGray;
    private List<TeamRoleInfo> teamRoleInfos = Lists.newArrayList();
    protected boolean isExportFileAttachment = false;
    protected long exportFileSize = 0L;
    protected long embeddedExcelImageSize = 0L;

    public StandardExportAction() {
        dataBatchSize = ImportConfig.getExportPageSize();
        tokenExpireSeconds = ImportConfig.getTokenExpireSeconds();
        exportRowsThrottle = ImportConfig.getExportRowsThrottle();
        exportRowsThrottleVip = ImportConfig.getExportRowsThrottleVip();
        exportMDThrottle = ImportConfig.getExportMDThrottle();
        exportFileExpireDay = ImportConfig.getExportFileExpireDay();
        exportFileAttachmentThrottle = ImportConfig.getExportFileAttachmentThrottle();
        exportFileAttachmentThrottleVip = ImportConfig.getExportFileAttachmentThrottleVip();
        exportFileMaxSize = ImportConfig.getExportFileMaxSize();
        embeddedExcelImageMaxSize = ImportConfig.getEmbeddedExcelImageMaxSize();
        tokenService = SpringUtil.getContext().getBean(ITokenService.class);
        excelUtil = SpringUtil.getContext().getBean(ExcelUtil.class);
        teamMemberRoleService = SpringUtil.getContext().getBean(TeamMemberRoleService.class);
    }

    @Override
    protected void before(Arg arg) {
        if (StringUtils.isBlank(arg.getToken())) {
            super.before(arg);
            this.searchQuery = getSearchQuery(arg);
            initDetailDescribeMapToExport();
            totalCount = validateThrottle();
            token = generateToken();
            isGenerating = false;
            accordingToListOrder = arg.isAccording_to_list();
            initDescribeMapToExport();
            initWhatListDescribeMap();
            initOptionalFeaturesSwitch();
            initNoExportFieldPrivilege();
            initExportFileAttachment();
            initCustomTeamRoleInfo();
        } else {
            token = arg.getToken();
            isGenerating = true;
        }
    }

    private void initCustomTeamRoleInfo() {
        customTeamRoleGray = TeamMember.isTeamRoleGray(actionContext.getTenantId());
        if (customTeamRoleGray) {
            teamRoleInfos.addAll(teamMemberRoleService.queryTeamRoleInfo(actionContext.getTenantId(), objectDescribe.getApiName()));
        }
    }

    private void initWhatListDescribeMap() {
        if (!isExportWhatList()) {
            return;
        }
        String whatApiName = arg.getWhatApiName();
        IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), whatApiName);
        whatListDescribeMap.put(whatApiName, describe);
    }

    private void initExportFileAttachment() {
        List<String> fileFields = arg.getFileFields();
        if (CollectionUtils.notEmpty(fileFields)) {
            exportFileAttachmentFields.put(objectDescribe.getApiName(), fileFields);
        }
        if (Objects.nonNull(arg.getDetailArg()) && CollectionUtils.notEmpty(arg.getDetailArg().getDetailInfo())) {
            Map<String, List<String>> apiNameAndImageMap = arg.getDetailArg().getDetailInfo().stream()
                    .collect(Collectors.toMap(DetailInfo::getApiName, DetailInfo::getFileFields, (x, y) -> x));
            if (CollectionUtils.notEmpty(apiNameAndImageMap)) {
                exportFileAttachmentFields.putAll(apiNameAndImageMap);
            }
        }
    }

    private void initNoExportFieldPrivilege() {
        if (serviceFacade.isAdmin(actionContext.getUser())) {
            return;
        }
        Set<String> objectApiNames = Sets.newHashSet();
        if (CollectionUtils.notEmpty(describeMap)) {
            objectApiNames.addAll(describeMap.keySet());
        }
        if (CollectionUtils.notEmpty(detailDescribeMap)) {
            objectApiNames.addAll(detailDescribeMap.keySet());
        }
        if (isExportWhatList() && CollectionUtils.notEmpty(whatListDescribeMap)) {
            objectApiNames.addAll(whatListDescribeMap.keySet());
        }
        if (CollectionUtils.empty(objectApiNames)) {
            return;
        }
        Map<String, Set<String>> userNoExportFieldPrivilege = serviceFacade.getUserNoExportFieldPrivilege(actionContext.getUser(), objectApiNames);
        if (CollectionUtils.notEmpty(userNoExportFieldPrivilege)) {
            if (isExportWhatList() && userNoExportFieldPrivilege.containsKey(arg.getWhatApiName())) {
                Set<String> noPrivilegeExportFields = userNoExportFieldPrivilege.get(arg.getWhatApiName());
                Set<String> whatNoPrivilegeExportFields = noPrivilegeExportFields.stream().map(x -> WhatComponentExt.getWhatFieldName(arg.getWhatApiName(), x)).collect(Collectors.toSet());
                userNoExportFieldPrivilege.put(arg.getWhatApiName(), whatNoPrivilegeExportFields);
            }
            objectApiNameAndNoExportPrivilegeFields.putAll(userNoExportFieldPrivilege);
        }
    }

    private void initOptionalFeaturesSwitch() {
        List<IObjectDescribe> objectDescribes = Lists.newArrayList();
        objectDescribes.addAll(detailDescribeMap.values());
        objectDescribes.add(objectDescribe);
        Map<String, OptionalFeaturesSwitchDTO> optionalFeaturesSwitchMap = infraServiceFacade.batchQueryOptionalFeaturesSwitch(actionContext.getTenantId(), objectDescribes);
        objectOptionalFeaturesSwitches.putAll(optionalFeaturesSwitchMap);
    }

    private SearchTemplateQuery getSearchQuery(Arg arg) {
        SearchTemplateQuery searchTemplateQuery;
        if (serviceFacade.isSupportOrFilter(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            Query query = defineQuery(actionContext.getUser(), arg.getSearch_template_id(),
                    arg.getSearch_query_info(), arg.getDataIdList());
            query.handlePlainContentFilter();
            searchTemplateQuery = (SearchTemplateQuery) query.toSearchTemplateQuery();
        } else {
            searchTemplateQuery = generateSearchQuery(actionContext.getUser(), arg.getSearch_template_id(),
                    arg.getSearch_query_info(), arg.getDataIdList());
            FilterExt.handlePlainContentFilter(searchTemplateQuery.getFilters());
        }
        if (AppFrameworkConfig.isExportUseDbTenantId(actionContext.getTenantId())) {
            searchTemplateQuery.setSearchSource("db");
        }
        if (isExportWhatList()) {
            String apiNameField = getWhatApiNameField();
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, apiNameField, arg.getWhatApiName());
        }
        return customSearchTemplate(searchTemplateQuery);
    }

    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchTemplateQuery) {
        return searchTemplateQuery;
    }

    /**
     * 修改 SearchTemplateQuery 中的 filter、wheres、pattern 是危险的行为，可能导致查询失败，或者查询结果不符合期望
     * 建议在 {@link #defineQuery(User, String, String, List)} 中增删改筛选条件。
     *
     * @param user
     * @param searchTemplateId
     * @param searchQuery
     * @param dataIdList
     * @return
     * @see #defineQuery(User, String, String, List)
     */
    protected SearchTemplateQuery generateSearchQuery(User user, String searchTemplateId, String searchQuery, List<String> dataIdList) {
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(user,
                ObjectDescribeExt.of(objectDescribe), searchTemplateId, arg.getSearchTemplateType(),
                searchQuery, false, arg.isIgnoreSceneFilter(), arg.isIgnoreSceneRecordType());
        //忽略掉前端传过来的offset
        query.setOffset(0);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, IObjectData.ID, dataIdList);
        }
        return query;
    }

    protected Query defineQuery(User user, String searchTemplateId, String searchQueryInfo, List<String> dataIdList) {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(arg.getSearchTemplateType())
                .templateId(searchTemplateId)
                .isIgnoreSceneFilter(arg.isIgnoreSceneFilter())
                .isIgnoreSceneRecordType(arg.isIgnoreSceneRecordType())
                .isRelatedPage(false)
                .build();
        Query query = serviceFacade.findSearchQuery(user, objectDescribe, searchQueryInfo, queryContext);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchQuery searchQuery = query.getSearchQuery()
                    .map(it -> it.and(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()))
                    .orElse(SearchQueryImpl.filter(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()));
            query.setSearchQuery(searchQuery);
        }
        // 忽略掉前端传过来的offset
        query.setOffset(0);
        return query;
    }

    protected int getExportRowsThrottle() {
        if (isExportFileAttachment) {
            return getExportFileAttachmentThrottle();
        }
        if (isMdExportLimit() && isExportDetailObjects()) {
            return exportMDThrottle;
        }
        if (isExportVip()) {
            return exportRowsThrottleVip;
        }
        return exportRowsThrottle;
    }

    private boolean isMdExportLimit() {
        return UdobjGrayConfig.isAllow("md_export_limit", actionContext.getTenantId()) || isPlainContentSearch();
    }

    private boolean isPlainContentSearch() {
        return searchQuery.getFilters().stream().anyMatch(x -> Objects.equals(FilterExt.FilterValueTypes.PLAIN_CONTENT, x.getValueType()));
    }

    private int getExportFileAttachmentThrottle() {
        if (isExportVip()) {
            return exportFileAttachmentThrottleVip;
        }
        return exportFileAttachmentThrottle;
    }

    protected int validateThrottle() {
        if (ObjectImportConfig.isGrayExportTotalCount(actionContext.getTenantId())) {
            RedisDao redisDao = serviceFacade.getBean(RedisDao.class);
            String strCache = redisDao.getStrCache(getRedisKey());
            if (NumberUtils.isCreatable(strCache)) {
                return Integer.parseInt(strCache);
            }
        }
        isExportFileAttachment = isExportFileAttachment(objectDescribe);
        int totalCount = 0;
        ISearchTemplateQuery searchTemplateQuery = SearchTemplateQueryExt.of(searchQuery).copy();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        // 查询精确总数，不返回数据
        searchTemplateQuery.setFindExplicitTotalNum(true);
        QueryResult<IObjectData> data = findObjectByQuery(actionContext.getUser(), objectDescribe, (SearchTemplateQuery) searchTemplateQuery);
        totalCount += data.getTotalNumber();
        if (!isMdExportLimit() && isExportDetailObjects() && CollectionUtils.notEmpty(detailDescribeMap)) {
            for (IObjectDescribe detailObjectDescribe : detailDescribeMap.values()) {
                ISearchTemplateQuery detailSearchQuery = buildDetailSearchQuery(detailObjectDescribe);
                QueryResult<IObjectData> detailData = findObjectByQuery(actionContext.getUser(), detailObjectDescribe, (SearchTemplateQuery) detailSearchQuery);
                totalCount += detailData.getTotalNumber();
                if (!isExportFileAttachment) {
                    isExportFileAttachment = isExportFileAttachment(detailObjectDescribe);
                }
            }
        }
        if (totalCount > getExportRowsThrottle()) {
            throw new ValidateException(buildThrottleExceedMessage());
        }
        return totalCount;
    }

    private ISearchTemplateQuery buildDetailSearchQuery(IObjectDescribe detailObjectDescribe) {
        ISearchTemplateQuery detailSearchQuery = SearchTemplateQueryExt.of(searchQuery).copy();
        CollectionUtils.nullToEmpty(detailSearchQuery.getFilters()).forEach(x -> x.setIsMasterField(true));
        IDataRightsParameter dataRightsParameter = detailSearchQuery.getDataRightsParameter();
        ObjectDescribeExt.of(detailObjectDescribe).getMasterDetailFieldDescribe().ifPresent(it -> {
            if (Objects.nonNull(dataRightsParameter)) {
                dataRightsParameter.setIsDetailObject(true);
                dataRightsParameter.setMasterIdFieldApiName(it.getApiName());
                dataRightsParameter.setMasterObjectApiName(it.getTargetApiName());
            }
        });
        detailSearchQuery.setDataRightsParameter(dataRightsParameter);
        return detailSearchQuery;
    }

    private String getRedisKey() {
        String md5Key = Hashing.sha256().newHasher()
                .putString(Strings.nullToEmpty(arg.getSearch_template_id()), Charsets.UTF_8)
                .putString(Strings.nullToEmpty(arg.getSearch_query_info()), Charsets.UTF_8)
                .putString(Strings.nullToEmpty(arg.getSearchTemplateType()), Charsets.UTF_8)
                .putString(Objects.toString(arg.getDataIdList()), Charsets.UTF_8)
                .putString(actionContext.getTenantId(), Charsets.UTF_8)
                .putString(actionContext.getObjectApiName(), Charsets.UTF_8)
                .hash()
                .toString();
        return String.format("%s_%s_%s_%s", actionContext.getTenantId(), actionContext.getUser().getUserIdOrOutUserIdIfOutUser(),
                actionContext.getObjectApiName(), md5Key);
    }

    protected boolean isExportVip() {
        return AppFrameworkConfig.getExportVipTenantIds().contains(actionContext.getTenantId());
    }

    protected String buildThrottleExceedMessage() {
        return I18N.text(I18NKey.EXPORT_EXCEED_THROTTLE, new DecimalFormat(",###").format(getExportRowsThrottle()));
    }

    protected QueryResult<IObjectData> findObjectByQuery(User user, IObjectDescribe describe, SearchTemplateQuery query) {
        if (AppFrameworkConfig.isGrayEsPaginationOptimization(user.getTenantId())) {
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(user)
                    .isSimple(false)
                    .esSearchSkipRecentUpdateCheck(true)
                    .paginationOptimization(true)
                    .skipRelevantTeam(isSkipRelevantTeam(describe))
                    .keepAllMultiLangValue(ObjectDescribeExt.of(describe).enableMultiLangField())
                    .searchRichTextExtra(true)
                    .build();
            return serviceFacade.findBySearchQuery(queryContext, describe.getApiName(), query);
        }
        //true代表不导相关团队，false或者默认为导相关团队
        if (isSkipRelevantTeam(describe)) {
            return serviceFacade.findBySearchQuery(user, describe, describe.getApiName(), query,
                    false, true, true, true);
        }
        return serviceFacade.findBySearchQuery(user, describe, describe.getApiName(), query,
                false, true, false, true);
    }

    private boolean isSkipRelevantTeam(IObjectDescribe describe) {
        return ObjectDescribeExt.of(describe).isSlaveObject()
                && (arg.isNoExportRelevantTeam() || (Objects.nonNull(objectOptionalFeaturesSwitches.get(describe.getApiName()))
                && !objectOptionalFeaturesSwitches.get(describe.getApiName()).getIsRelatedTeamEnabled()));
    }

    private String generateToken() {
        return tokenService.create(tokenExpireSeconds);
    }

    public SearchTemplateQuery buildDetailSearchTemplateQuery(String detailIdOffset, List<String> masterIds, String masterDetailField) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(getDataBatchSize());
        searchTemplateQuery.setFindExplicitTotalNum(null);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, masterDetailField, masterIds);
        if (detailIdOffset != null) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.LT, IObjectData.ID, detailIdOffset, IFieldDescribe.DEFINE_TYPE_SYSTEM);
        }
        return searchTemplateQuery;
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!isGenerating) {
            serviceFacade.logInternational(actionContext.getUser(), EventType.ADD, ActionType.Export, objectDescribe.getApiName(),
                    I18N.text(I18NKey.OBJECT_SPECIFY, objectDescribe.getDisplayName()),
                    InternationalItem.builder()
                            .internationalKey(I18NKey.OBJECT_SPECIFY)
                            .defaultInternationalValue(I18N.text(I18NKey.OBJECT_SPECIFY, objectDescribe.getDisplayName()))
                            .internationalParameters(Lists.newArrayList(objectDescribe.getDisplayName()))
                            .build());

            String traceId = TraceContext.get().getTraceId();
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
            parallelTask.submit(() -> {
                try {
                    doExport();
                } catch (AppBusinessException e) {
                    log.warn("export data failed!tenantId:{},arg:{}", actionContext.getTenantId(), arg, e);
                    TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId + ERROR_SPLIT + e.getMessage());
                    tokenService.complete(tokenInfo, tokenExpireSeconds);
                } catch (Exception e) {
                    log.error("export data error!tenantId:{},arg:{}", actionContext.getTenantId(), arg, e);
                    TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId);
                    tokenService.complete(tokenInfo, tokenExpireSeconds);
                }
            });
            parallelTask.run();
        }
        return generateResult(this.token);
    }

    private void doExport() throws IOException {
        doExportData();
        // 导出结束的后动作函数
        doExportPluginAfter();
    }

    enum ExportType {
        export_data,
        export_file;
    }

    private void doExportData() throws IOException {
        log.warn("doExportData start,user:{},arg:{}", actionContext.getUser(), arg);
        StopWatch exportStopWatch = StopWatch.create("doExportData");
        long startTime = System.currentTimeMillis();
        Map<String, List<IFieldDescribe>> fieldMap = findFieldMap(arg.getRecordType_apiName());
        exportStopWatch.lap("findFieldMap");
        Workbook workbook = new SXSSFWorkbook(getDataBatchSize());
        // 每个workbook只能设置64000个style，在循环外创建防止超限
        Map<String, CellStyle> styleMap = excelUtil.createStyle(workbook);
        Map<String, Sheet> sheetMap = Maps.newLinkedHashMap();
        Map<String, Integer> indexMap = Maps.newHashMap();
        Map<String, Sheet> detailSheetMap = Maps.newLinkedHashMap();
        Map<String, Integer> detailIndexMap = Maps.newHashMap();
        describeMap.forEach((apiName, describe) -> {
            //补充id字段
            supplementIdAndGenerateSheetAndIndex(fieldMap, workbook, sheetMap, indexMap, apiName, describe);
        });
        if (isExportDetailObjects()) {
            detailDescribeMap.forEach((detailApiName, detailDescribe) -> {
                //补充ID字段
                supplementIdAndGenerateSheetAndIndex(fieldMap, workbook, detailSheetMap, detailIndexMap, detailApiName, detailDescribe);
            });
        }
        exportStopWatch.lap("supplementIdAndGenerateSheetAndIndex");
        String idOffset = null;
        int batchDataNum;
        int queryCount = 0;
        int maxQueryCount = SearchTemplateQueryExt.calculateTotalPage(getExportRowsThrottle(), getDataBatchSize());
        // 发审计日志时传入ea和导出字段
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        String fieldNames = fieldMap.get(objectDescribe.getApiName()).stream().map(IFieldDescribe::getApiName).collect(Collectors.joining(","));
        String message = "Export token is " + token + ", export fields are " + fieldNames;
        List<IFilter> filters = Lists.newArrayList();
        CollectionUtils.nullToEmpty(searchQuery.getFilters()).forEach(x -> filters.add(x.copy()));
        int total = 0;
        do {
            if (!isExportDetailObjects() && queryCount >= maxQueryCount) {
                log.warn("query count exceed max value,queryCount:{},maxCount:{}", queryCount, maxQueryCount);
                break;
            }
            resetMasterSearchTemplateQuery(filters, idOffset, fieldMap.get(objectDescribe.getApiName()));
            StopWatch exportDataStopWatch = StopWatch.create("exportData");
            log.warn("doExportData findObjectByQuery start,user:{},searchQuery:{}", actionContext.getUser(), searchQuery.toJsonString());
            QueryResult<IObjectData> queryResult = findObjectByQuery(actionContext.getUser(), objectDescribe, searchQuery);
            List<IObjectData> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                log.warn("doExportData queryData result,user:{},dataSize:{}", actionContext.getUser(), queryData.size());
                exportDataStopWatch.lap("findObjectByQuery");
                batchDataNum = queryData.size();
                total += batchDataNum;
                idOffset = queryData.get(batchDataNum - 1).getId();
                //过滤掉gdpr屏蔽的数据
                infraServiceFacade.removeGdprData(actionContext.getUser(), objectDescribe.getApiName(), queryData, ObjectAction.BATCH_EXPORT.getActionCode());
                Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
                dataMap.put(objectDescribe.getApiName(), queryData);
                dataMap.putAll(getRelatedDataMap(queryResult));
                exportDataToExcel(fieldMap, workbook, styleMap, sheetMap, indexMap, describeMap, dataMap);
                exportDataStopWatch.lap("exportDataToExcel");
                exportDetailData(fieldMap, workbook, styleMap, detailSheetMap, detailIndexMap, queryCount, queryData);
                exportDataStopWatch.lap("exportDetailData");
                // 发送审计日志
                try {
                    sendAuditLog(objectDescribe.getApiName(), queryData, ea, message);
                } catch (Exception e) {
                    log.warn("Send audit log error, tenantId:{}, userId:{},describeApiName:{}",
                            actionContext.getTenantId(), actionContext.getUser().getUserId(), objectDescribe.getApiName(), e);
                }
            } else {
                batchDataNum = 0;
            }
            // 保存导出进度
            int currentExportedDataNumber = indexMap.values().stream().mapToInt(i -> i).sum();
            int detailExportDataNumber = detailIndexMap.values().stream().mapToInt(i -> i).sum();
            TokenInfo tokenInfo = TokenInfo.builder().id(token).progress(currentExportedDataNumber + detailExportDataNumber).build();
            tokenService.update(tokenInfo, tokenExpireSeconds);
            exportDataStopWatch.lap("tokenInfo update");
            exportDataStopWatch.logSlow(5 * 1000);
            queryCount++;
        } while (batchDataNum > 0 && idOffset != null);
        // 将相关团队还原为一个字段，以免造成下拉框错位
        fieldMap.forEach((name, field) -> removeRelevantFields(field));
        // 为单选字段生成下拉框
        sheetMap.forEach((apiName, sheet) -> excelUtil.makeDropDownMenu(workbook, sheet, fieldMap.get(apiName)));
        detailSheetMap.forEach((detailApiName, detailSheet) -> excelUtil.makeDropDownMenu(workbook, detailSheet, fieldMap.get(detailApiName)));
        exportStopWatch.lap("makeDropDownMenu");
        NUploadFileDirect.Result result = uploadFile(workbook);
        exportStopWatch.lap("uploadFile");
        TokenInfo tokenInfo;
        if (CollectionUtils.notEmpty(fileAttachmentFields)) {
            //将数据的excel打包到压缩包里
            pathAndName.put(result.getFinalNPath(), getFileName());
            List<IObjectDescribe> describeList = Lists.newArrayList();
            if (CollectionUtils.notEmpty(describeMap)) {
                describeList.addAll(describeMap.values());
            }
            if (CollectionUtils.notEmpty(detailDescribeMap)) {
                describeList.addAll(detailDescribeMap.values());
            }
            String fileAndImage = XmlUtil.createExportFileAndImage(pathAndName, getFileName(), describeList, fileAttachmentFields, fileAttachmentDataList);
            log.info("doExport packedFile tenantId:{},userId:{},xml:{}", actionContext.getTenantId(), actionContext.getUser().getUserId(), fileAndImage);
            infraServiceFacade.packedFile(actionContext.getUser(), fileAndImage, arg.getJobId(), totalCount);
            tokenInfo = TokenInfo.buildSuccess(token, buildExportResult(result.getFinalNPath(), result.getFileSize(),
                    "zip", ExportType.export_file.name()));
        } else {
            tokenInfo = TokenInfo.buildSuccess(token, buildExportResult(result.getFinalNPath(), result.getFileSize(),
                    "xlsx", ExportType.export_data.name()));
        }
        log.warn("doExportData tokenInfo complete,user:{},result:{}", actionContext.getUser(), tokenInfo);
        tokenService.complete(tokenInfo, tokenExpireSeconds);
        exportStopWatch.lap("exportComplete");
        exportStopWatch.logSlow(10 * 60 * 1000);
        List<String> dataNumInfo = Lists.newArrayListWithExpectedSize(indexMap.size());
        indexMap.forEach((apiName, index) -> dataNumInfo.add(apiName + "/" + index));
        log.warn("doExportData end,user:{},apiName:{},dataNum:{},detailNum:{},cost:{}ms",
                actionContext.getUser(), arg.getObject_describe_api_name(),
                StringUtils.join(dataNumInfo, "|"), detailIndexMap, System.currentTimeMillis() - startTime);
        // 导出事件安全埋点
        try {
            if (RequestUtil.isCepRequest() && UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.SECURITY_INCIDENTS_EXPORT,
                    actionContext.getTenantId(), actionContext.getObjectApiName())) {
                SecurityEventTrackingDTO trackingDTO = SecurityEventTrackingDTO.builder()
                        .eventId(UUID.randomUUID().toString())
                        .traceId(TraceContext.get().getTraceId())
                        .eventTime(System.currentTimeMillis())
                        .tenantId(actionContext.getTenantId())
                        .userId(actionContext.getUser().getUserId())
                        .sourceIp(Objects.isNull(RequestContextManager.getContext()) ? "" : RequestContextManager.getContext().getClientIp())
                        .deviceId(RequestUtil.getCustomHeader(RequestContext.FS_DEVICE_ID))
                        .deviceType(RequestUtil.getCustomHeader(RequestContext.FS_DEVICE_TYPE))
                        .objects(actionContext.getObjectApiName())
                        .operation("Export")
                        .actionType("UIActionEvent")
                        .parameters(JSON.toJSONString(arg))
                        .records(Lists.newArrayList("totalNumber:" + total))
                        .query(searchQuery.toJsonString())
                        .build();
                BizLogClient.send("biz-log-security-event-tracking", Pojo2Protobuf.toMessage(trackingDTO, com.fxiaoke.log.SecurityEventTrackingDTO.class).toByteArray());
            }
        } catch (Exception e) {
            log.warn("send message error ,exception: ", e);
        }
    }

    private NUploadFileDirect.Result uploadFile(Workbook workbook) throws IOException {
        NUploadFileDirect.Result result = excelUtil.exportDataDirect(workbook, actionContext, exportFileExpireDay);
        if (Objects.isNull(result) || Strings.isNullOrEmpty(result.getFinalNPath())) {
            throw new ExportException(I18N.text(I18NKey.UPLOAD_EXCEL_FAIL));
        }
        ExportTaskHookService.Arg hookArg = new ExportTaskHookService.Arg();
        hookArg.setType("export");
        hookArg.setDescribeApiName(objectDescribe.getApiName());
        hookArg.setJobId(arg.getJobId());
        hookArg.setPath(result.getFinalNPath());
        hookArg.setOriginalFilename(getFileName());
        hookArg.setFileType("xlsx");
        hookArg.setExpiredDay(exportFileExpireDay);
        ExportTaskHookService.Result hookResult = serviceFacade.getExportTaskHookService().after(actionContext.getUser(), hookArg);
        String filePath = hookResult.getFilePath();
        if (ObjectUtils.isEmpty(filePath)) {
            return result;
        }
        log.warn("encrypt_file end,oldPath:{}, newPath:{}", result.getFinalNPath(), filePath);
        result.setFinalNPath(filePath);
        return result;
    }

    private static String buildExportResult(String path, Long size, String fileExt, String exportType) {
        return path + "|" + size + "|" + fileExt + "|" + exportType + "|" + ImportConfig.getFileExpiredTimeWithNow();
    }

    private void exportDetailData(Map<String, List<IFieldDescribe>> fieldMap,
                                  Workbook workbook,
                                  Map<String, CellStyle> styleMap,
                                  Map<String, Sheet> detailSheetMap,
                                  Map<String, Integer> detailIndexMap,
                                  int queryCount,
                                  List<IObjectData> queryData) {
        if (!isExportDetailObjects()) {
            return;
        }
        List<String> masterDataIds = queryData.stream().map(DBRecord::getId).collect(Collectors.toList());
        for (IObjectDescribe detailObjectDescribe : detailDescribeMap.values()) {
            String detailIdOffset = null;
            int detailBatchDataNum;
            Optional<MasterDetailFieldDescribe> masterDetailField = ObjectDescribeExt.of(detailObjectDescribe).getMasterDetailFieldDescribe();
            if (!masterDetailField.isPresent()) {
                continue;
            }
            do {
                SearchTemplateQuery detailSearchTemplateQuery = buildDetailSearchTemplateQuery(detailIdOffset, masterDataIds, masterDetailField.get().getApiName());
                QueryResult<IObjectData> detailQueryResult = findObjectByQuery(actionContext.getUser(), detailObjectDescribe, detailSearchTemplateQuery);
                List<IObjectData> detailQueryData = detailQueryResult.getData();
                if (CollectionUtils.notEmpty(detailQueryData)) {
                    detailBatchDataNum = detailQueryData.size();
                    detailIdOffset = detailQueryData.get(detailQueryData.size() - 1).getId();
                    infraServiceFacade.removeGdprData(actionContext.getUser(), detailObjectDescribe.getApiName(), detailQueryData, ObjectAction.BATCH_EXPORT.getActionCode());
                    Map<String, List<IObjectData>> detailDataMap = Maps.newHashMap();
                    detailDataMap.put(detailObjectDescribe.getApiName(), detailQueryData);
                    exportDataToExcel(fieldMap, workbook, styleMap, detailSheetMap, detailIndexMap, detailDescribeMap, detailDataMap);
                } else {
                    detailBatchDataNum = 0;
                }
                queryCount++;
            } while (detailBatchDataNum > 0 && detailIdOffset != null);
        }
    }

    private void supplementIdAndGenerateSheetAndIndex(Map<String, List<IFieldDescribe>> fieldMap, Workbook workbook,
                                                      Map<String, Sheet> sheetMap, Map<String, Integer> indexMap,
                                                      String apiName, IObjectDescribe describe) {
        //补充ID字段
        supplementFieldsWithID(fieldMap.get(apiName), describe);
        //根据策略补充图片字段
        supplementImageFieldsByStrategy(fieldMap.get(apiName), describe);
        //补充多语字段
        ImportExportExt.supportMultiLangField(actionContext.getTenantId(), fieldMap.get(apiName), objectApiNameAndNoExportPrivilegeFields.get(apiName), describe.getApiName());
        Sheet sheet = excelUtil.createSheetWithTitle(workbook, fieldMap.get(apiName), describe.getDisplayName() + I18N.text(I18NKey.DATA));
        sheetMap.put(describe.getApiName(), sheet);
        indexMap.put(apiName, 0);
    }

    private void exportDataToExcel(Map<String, List<IFieldDescribe>> fieldMap,
                                   Workbook workbook,
                                   Map<String, CellStyle> styleMap,
                                   Map<String, Sheet> sheetMap,
                                   Map<String, Integer> indexMap,
                                   Map<String, IObjectDescribe> describeMap,
                                   Map<String, List<IObjectData>> dataMap) {
        for (Map.Entry<String, List<IObjectData>> entry : dataMap.entrySet()) {
            String apiName = entry.getKey();
            List<IObjectData> dataToExport = entry.getValue();
            List<IObjectData> originalDataList = ObjectDataExt.copyList(dataToExport);
            Set<String> notExportFields = objectApiNameAndNoExportPrivilegeFields.get(apiName);
            Sheet sheet = sheetMap.get(apiName);
            int index = indexMap.get(apiName);
            IObjectDescribe describe = describeMap.get(apiName);
            //补充ID数据
            supplementDataWithID(dataToExport, fieldMap.get(apiName), describe);
            fillDataList(actionContext.getUser(), describe, fieldMap.get(apiName), dataToExport);
            // 处理函数插件
            pluginDoExport(arg, describe, originalDataList, dataToExport);
            excelUtil.generateSheetWithCellStyle(workbook, sheet, index, describe, fieldMap.get(apiName),
                    dataToExport, styleMap, fileAttachmentFields.get(apiName), notExportFields);
            indexMap.put(apiName, index + dataToExport.size());
        }
    }

    /**
     * 过滤掉相关团队
     */
    protected void removeRelevantFields(List<IFieldDescribe> fields) {
        fields.removeIf(f -> ObjectDataExt.RELEVANT_TEAM.equals(f.getApiName()));
    }


    /**
     * 调用接口发送审计日志
     */
    private void sendAuditLog(String apiName,
                              List<IObjectData> queryData,
                              String ea,
                              String message) {
        // 导出数据的ID
        String idListStr = queryData.stream().map(IObjectData::getId).collect(Collectors.joining(","));
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(appName)
                .serverIp(serverIp)
                .profile(profile)
                .action(action)
                .ea(ea)
                .tenantId(actionContext.getTenantId())
                .userId(actionContext.getUser().getUserId())
                .objectApiNames(apiName)
                .num(queryData.size())
                .message(message)
                .extra(idListStr)
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    private void resetMasterSearchTemplateQuery(List<IFilter> filters, String idOffset, List<IFieldDescribe> fieldDescribes) {
        List<IFilter> copiedFilters = Lists.newArrayList();
        CollectionUtils.nullToEmpty(filters).forEach(x -> copiedFilters.add(x.copy()));
        searchQuery.resetFilters(copiedFilters);
        searchQuery.setOffset(0);
        searchQuery.setLimit(getDataBatchSize());
        searchQuery.setNeedReturnCountNum(Boolean.FALSE);
        searchQuery.setFindExplicitTotalNum(null);

        if (isExportWhatList()) {
            searchQuery.setWhatDescribeApiName(arg.getWhatApiName());
            List<String> whatFieldApiNames = fieldDescribes.stream().map(IFieldDescribe::getApiName)
                    .filter(apiName -> StringUtils.startsWith(apiName, arg.getWhatApiName()) && WhatComponentExt.isWhatField(apiName))
                    .collect(Collectors.toList());
            searchQuery.setWhatFieldApiNames(whatFieldApiNames);
        }

        //按照数据id倒序排
        SearchTemplateQueryExt.of(searchQuery).resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));
        if (idOffset != null) {
            Optional<IFilter> filter = searchQuery.getFilters().stream()
                    .filter(x -> IObjectData.ID.equals(x.getFieldName())
                            && Operator.LT.equals(x.getOperator())
                            && IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(x.getFieldDefineType()))
                    .findFirst();
            if (filter.isPresent()) {
                filter.get().setFieldValues(Lists.newArrayList(idOffset));
            } else {
                SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.LT, IObjectData.ID, idOffset, IFieldDescribe.DEFINE_TYPE_SYSTEM);
            }
        }
    }

    private String getWhatApiNameField() {
        if (AppFrameworkConfig.isWhatListWhatFieldGray(actionContext.getTenantId())) {
            return getWhatField().getApiNameFieldApiName();
        }
        if (Utils.BPM_TASK_API_NAME.equals(objectDescribe.getApiName())) {
            return ObjectDataExt.OBJECT_API_NAME_HUMP;
        }
        return ObjectDataExt.OBJECT_API_NAME;
    }

    private What getWhatField() {
        return ObjectDescribeExt.of(objectDescribe).getWhatField().orElseThrow(IllegalArgumentException::new);
    }

    /**
     * 补充ID字段
     */
    private void supplementFieldsWithID(List<IFieldDescribe> fields, IObjectDescribe describe) {
        if (!isExportId(describe)) {
            return;
        }

        /* 调整fields */
        String IDLabel = I18N.text(I18NKey.DATAID_LABEL);   //避免每次从缓存中取
        //创建数据数据ID字段描述并放到第一列
        fields.add(0, makeTempFieldDescribe(IFieldDescribe.ID, IDLabel, describe.getApiName(), true));
        LinkedList<IFieldDescribe> tempFieldDescribes = Lists.newLinkedList();
        //创建其它lookup字段的ID字段描述
        for (IFieldDescribe field : fields) {
            String type = field.getType();
            if (IFieldType.MASTER_DETAIL.equals(type)
                    || IFieldType.OBJECT_REFERENCE.equals(type)
                    || IFieldType.OBJECT_REFERENCE_MANY.equals(type)) {
                IFieldDescribe tempFieldDescribe = makeTempFieldDescribe(field.getApiName() + IFieldDescribe.ID,
                        field.getLabel() + "_" + IDLabel, describe.getApiName(), field.isRequired());
                Set<String> noExportFields = objectApiNameAndNoExportPrivilegeFields.get(describe.getApiName());
                if (CollectionUtils.notEmpty(noExportFields) && noExportFields.contains(field.getApiName())) {
                    noExportFields.add(tempFieldDescribe.getApiName());
                }
                tempFieldDescribes.add(tempFieldDescribe);
            }
        }
        //将tempFieldDescribes中的lookup字段放在fields的相应位置
        ListIterator<IFieldDescribe> lit = fields.listIterator();
        while (lit.hasNext()) {
            IFieldDescribe field = lit.next();
            String type = field.getType();
            // 将相关字段的ID字段放在前面
            if (IFieldType.MASTER_DETAIL.equals(type) || IFieldType.OBJECT_REFERENCE.equals(type) || IFieldType.OBJECT_REFERENCE_MANY.equals(type)) {
                lit.previous();
                lit.add(tempFieldDescribes.pollFirst());
                lit.next();
            }
        }

    }

    private boolean isExportId(IObjectDescribe describe) {
        if (Objects.equals(describe.getApiName(), objectDescribe.getApiName())) {
            return arg.isInclude_id();
        }
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            return Objects.nonNull(arg.getDetailArg()) && arg.getDetailArg().isIncludeId();
        }
        return false;
    }

    /**
     * 对Datalist补充ID数据
     */
    private void supplementDataWithID(List<IObjectData> dataList, List<IFieldDescribe> fields, IObjectDescribe describe) {
        if (!isExportId(describe)) {
            return;
        }
        /* 调整dataList */
        dataList.forEach(data -> {
            for (IFieldDescribe field : fields) {
                String fieldApiName = field.getApiName();
                Object fieldValue = data.get(fieldApiName);
                if (IFieldType.MASTER_DETAIL.equals(field.getType()) || IFieldType.OBJECT_REFERENCE.equals(field.getType())) {
                    data.set(fieldApiName + IFieldDescribe.ID, fieldValue);
                } else if (IFieldType.OBJECT_REFERENCE_MANY.equals(field.getType())) {
                    String value = null;
                    if (fieldValue instanceof List) {
                        value = ((List<String>) fieldValue).stream()
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining("|"));
                    }
                    data.set(fieldApiName + IFieldDescribe.ID, value);
                }
            }
        });
    }

    private IFieldDescribe makeTempFieldDescribe(String apiName, String label, String describeApiName, boolean isRequired) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(IFieldDescribe.API_NAME, apiName);
        map.put(IFieldDescribe.LABEL, label);
        map.put(IFieldDescribe.TYPE, IFieldType.TEXT);
        map.put(IFieldDescribe.IS_REQUIRED, isRequired);
        map.put(IFieldDescribe.DESCRIBE_API_NAME, describeApiName);
        return FieldDescribeFactory.newInstance(map);
    }

    protected int getDataBatchSize() {
        return dataBatchSize;
    }

    protected void initDetailDescribeMapToExport() {
        //初始化从对象描述
        if (isExportDetailObjects()) {
            Map<String, IObjectDescribe> detailDescribes = serviceFacade.findObjects(actionContext.getTenantId(), arg.getDetailArg().getDetailObjectApiNames());
            List<IObjectDescribe> detailDescribeList = Lists.newArrayList(detailDescribes.values());
            List<IObjectDescribe> orderedDetailDescribes = CollectionUtils.sortByGivenOrder(detailDescribeList,
                    arg.getDetailArg().getDetailObjectApiNames(), IObjectDescribe::getApiName);
            for (IObjectDescribe detailDescribe : orderedDetailDescribes) {
                if (ObjectDescribeExt.of(detailDescribe).isSlaveObject()) {
                    detailDescribeMap.put(detailDescribe.getApiName(), detailDescribe);
                }
            }
        }
    }

    protected void initDescribeMapToExport() {
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
    }

    public boolean isExportDetailObjects() {
        return Objects.nonNull(arg.getDetailArg()) && CollectionUtils.notEmpty(arg.getDetailArg().getDetailObjectApiNames());
    }

    private boolean isExportWhatList() {
        return StringUtils.isNotBlank(arg.getWhatApiName());
    }

    /**
     * 判断当前导出的对象是否为联合导出的从对象
     * 联合导出的从对象，指定字段导出的时候不需要去场景查询字段
     *
     * @param apiName 正在导出的对象
     * @return 是否需要查询场景获取显示字段
     */
    private boolean isNeedFindTemplate(String apiName) {
        if (accordingToListOrder) {
            if (isExportDetailObjects()) {
                return !arg.getDetailArg().getDetailObjectApiNames().contains(apiName);
            }
            return true;
        }
        return false;
    }


    protected Map<String, List<IObjectData>> getRelatedDataMap(QueryResult<IObjectData> queryResult) {
        return Maps.newHashMap();
    }


    protected Map<String, List<IFieldDescribe>> findFieldMap(String recordType) {
        Map<String, List<IFieldDescribe>> fieldMap = new HashMap<>();
        for (String describeApiName : describeMap.keySet()) {
            List<IFieldDescribe> fields = findFields(describeApiName, recordType);
            fieldMap.put(describeApiName, fields);
            for (String whatApiName : whatListDescribeMap.keySet()) {
                List<IFieldDescribe> whatFields = findWhatListFieldToExport(whatApiName);
                fieldMap.computeIfAbsent(describeApiName, (k) -> Lists.newArrayList()).addAll(whatFields);
            }
        }

        for (String describeApiName : detailDescribeMap.keySet()) {
            List<IFieldDescribe> fields = findFields(describeApiName, null);
            fieldMap.put(describeApiName, fields);
        }

        return fieldMap;
    }

    /**
     * 补充相关团队
     * 在相关团队字段之后补充
     */
    private void fillMembers(List<IFieldDescribe> fields) {
        // 普通成员
        List<IFieldDescribe> members = createNormalMember();
        if (!customTeamRoleGray) {
            List<IFieldDescribe> otherMembers = customTeamMembers();
            if (CollectionUtils.notEmpty(otherMembers)) {
                members.addAll(otherMembers);
            }
        }
        // 在相关团队处插入
        int index = -1;
        for (int i = 0; i < fields.size(); i++) {
            if (ObjectDataExt.RELEVANT_TEAM.equals(fields.get(i).getApiName())) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            fields.addAll(index, members);
        }
    }

    /**
     * 客户、商机等，如果需要补充新的相关团队字段，请override此方法
     */
    protected List<IFieldDescribe> customTeamMembers() {
        return null;
    }

    private List<IFieldDescribe> createNormalMember() {
        return infraServiceFacade.generateTeamMemberField(actionContext.getUser(), objectDescribe.getApiName());
    }

    protected List<IFieldDescribe> findFields(String describeApiName, String recordType) {
        IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), describeApiName);
        return findValidFieldToExport(describe, recordType);
    }

    private List<IFieldDescribe> findWhatListFieldToExport(String describeApiName) {
        if (!isExportWhatList()) {
            return Lists.newArrayList();
        }
        IObjectDescribe describe = whatListDescribeMap.get(describeApiName);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<String> whatFields = arg.getAccordingFieldList().stream()
                .filter(x -> StringUtils.startsWith(x, describe.getApiName()))
                .map(x -> StringUtils.substringAfter(x, "."))
                .collect(Collectors.toList());
        List<IFieldDescribe> whatFieldDescribes = describeExt.getFieldByApiNames(whatFields).stream().map(x -> FieldDescribeExt.of(x).copyOnWrite()).collect(Collectors.toList());
        whatFieldDescribes.forEach(x -> x.setApiName(WhatComponentExt.getWhatFieldName(describe.getApiName(), x.getApiName())));
        return whatFieldDescribes;
    }

    /**
     * 找出需要导出的字段
     */
    private List<IFieldDescribe> findValidFieldToExport(IObjectDescribe describe, String recordType) {
        List<IFieldDescribe> fields = getHeadFields(describe, recordType);
        if (CollectionUtils.empty(fields)) {
            return fields;
        }

        //过滤gdpr个人字段
        List<String> needFilterGdprFields = infraServiceFacade.needFilterGdprFields(actionContext.getUser(),
                objectDescribe.getApiName(), ObjectAction.BATCH_EXPORT.getActionCode());

        fields.removeIf(x -> needFilterGdprFields.contains(x.getApiName()));

        //去掉公共对象上2个字段
        fields.removeIf(x -> Objects.equals(x.getApiName(), DOWNSTREAM_TENANT_ID)
                || Objects.equals(x.getApiName(), PUBLIC_DATA_TYPE)
                || Objects.equals(x.getApiName(), CREATE_ENTERPRISE));

        fillOtherFields(describe, fields);

        // 重复的字段、只保留第一个
        Set<String> fieldNames = Sets.newHashSet();
        List<IFieldDescribe> result = fields.stream()
                .filter(it -> fieldNames.add(it.getApiName()))
                .collect(Collectors.toList());
        if (fieldNames.size() < fields.size()) {
            log.warn("export field duplicated! ei:{}, api:{}", actionContext.getTenantId(), describe.getApiName());
        }
        return result;
    }

    private void fillOtherFields(IObjectDescribe describe, List<IFieldDescribe> fields) {
        //主对象补充相关团队
        if (!isSkipRelevantTeam(describe)) {
            // 对相关团队进行特殊处理，支持导出"普通成员-只读"之类的列
            fillMembers(fields);
        }
        fillDepartmentName(fields);
        fillDimensionName(fields);
        fillEmployeeName(fields);
    }

    private void fillEmployeeName(List<IFieldDescribe> fields) {
        if (!isSupportExportEmployeeCode(actionContext.getTenantId())) {
            return;
        }
        insertFields(fields, Lists.newArrayList(IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY), Lists.newArrayList(OUT_OWNER));
    }

    private void fillDepartmentName(List<IFieldDescribe> fields) {
        if (!isSupportExportDepartmentCode(actionContext.getTenantId())) {
            return;
        }
        insertFields(fields, Lists.newArrayList(IFieldType.DEPARTMENT, IFieldType.DEPARTMENT_MANY), Lists.newArrayList());
    }

    private void insertFields(List<IFieldDescribe> fields, List<String> fieldTypes, List<String> notSupportFieldApiNames) {
        ListIterator<IFieldDescribe> iterator = fields.listIterator();
        while (iterator.hasNext()) {
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(iterator.next());
            if (notSupportFieldApiNames.contains(fieldDescribeExt.getApiName())) {
                continue;
            }
            if (fieldTypes.contains(fieldDescribeExt.getType())) {
                String label = fieldDescribeExt.getLabel() + I18NExt.getOrDefault(I18NKey.EXPORT_NAME_LABEL, "（名称）");// ignoreI18n
                String apiName = FieldDescribeExt.getLookupNameByFieldName(fieldDescribeExt.getApiName());
                IFieldDescribe field = ImportExportExt.createField(label, apiName, fieldDescribeExt.getType(), fieldDescribeExt.isRequired());
                iterator.add(field);
            }
        }
    }

    private void fillDimensionName(List<IFieldDescribe> fields) {
        if (!isSupportExportDimensionCode(actionContext.getTenantId())) {
            return;
        }
        insertFields(fields, Lists.newArrayList(IFieldType.DIMENSION), Lists.newArrayList());
    }

    private List<IFieldDescribe> getHeadFields(IObjectDescribe describe, String recordType) {
        LinkedList<IFieldDescribe> fields = Lists.newLinkedList();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (isNeedFindTemplate(describe.getApiName())) {
            // 指定显示列
            if (CollectionUtils.notEmpty(arg.getAccordingFieldList()) && Objects.equals(actionContext.getObjectApiName(), describe.getApiName())) {
                return describeExt.getFieldByApiNames(arg.getAccordingFieldList());
            }
            // 主属对象查询具体场景的显示列
            SearchTemplate template = getSearchTemplate(arg.getSearch_template_id(), describe.getApiName(), arg.getSearchTemplateType());
            // 跟随显示列设置，使用显示列设置
            if (Objects.nonNull(template)) {
                if (SearchTemplateExt.of(template).isUseFieldList()) {
                    List<String> showList = HeadField.getShowList(template);
                    showList.stream()
                            .map(describe::getFieldDescribe)
                            .filter(Objects::nonNull)
                            .filter(field -> !StringUtils.equalsAny(field.getType(), IFieldType.OUT_EMPLOYEE, IFieldType.OUT_DEPARTMENT))
                            .forEach(fields::add);
                    return fields;
                }
                if (!arg.isIgnoreSceneRecordType()) {
                    recordType = template.getRecordType();
                }
            }
        }
        if (isExportDetailObjectByFields(describe)) {
            DetailInfo detailInfo = arg.getDetailArg().getDetailInfo().stream()
                    .filter(x -> Objects.equals(x.getApiName(), describe.getApiName()))
                    .findFirst()
                    .get();
            List<String> detailInfoFields = detailInfo.getFields();
            if (CollectionUtils.notEmpty(detailInfoFields)) {
                return describeExt.getFieldByApiNames(detailInfoFields);
            }
        }
        LayoutExt layoutExt = findLayoutByRecordType(describe, recordType);
        return layoutExt.getFormComponent()
                .map(FormComponentExt::getFieldList)
                .map(describeExt::getFieldByApiNames)
                .orElse(fields);
    }

    private boolean isExportDetailObjectByFields(IObjectDescribe describe) {
        if (isExportDetailObjects() && ObjectDescribeExt.of(describe).isSlaveObject()) {
            return CollectionUtils.nullToEmpty(arg.getDetailArg().getDetailInfo()).stream().anyMatch(x -> Objects.equals(x.getApiName(), describe.getApiName()));
        }
        return false;
    }

    private LayoutExt findLayoutByRecordType(IObjectDescribe describe, String recordType) {
        ILayout layout = serviceFacade.findObjectLayoutWithType(buildLayoutContext(), recordType, describe,
                ILayout.LIST_LAYOUT_TYPE, null);
        LayoutExt layoutExt = LayoutExt.of(layout);
//        //导出布局过滤不支持的特殊类型字段
//        layoutExt.removeExportUnsupportedFields();
        //补充相关团队
        if (!isSkipRelevantTeam(describe)) {
            layoutExt.fillRelevantTeamField();
        }
        return layoutExt;
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(actionContext.getUser(), actionContext.getAppId());
    }

    /**
     * 注意本方法被订单重写了，导出订单，默认导出订单产品的时候，会走到这里查询订单产品的全部场景
     * <p>
     * 根据场景id，查询指定场景。获取场景配置的显示字段
     *
     * @param templateId         场景id
     * @param describeApiName    正在导出的对象
     * @param searchTemplateType 场景类型
     * @return 场景
     */
    protected SearchTemplate getSearchTemplate(String templateId, String describeApiName, String searchTemplateType) {
        //已经全网
        if (AppFrameworkConfig.isGrayExportAccordingToTemplateFieldList(actionContext.getTenantId())) {
            List<ISearchTemplate> templates = serviceFacade.findByDescribeApiNameAndExtendAttribute(describeApiName,
                    null, actionContext.getUser());
            return templates.stream()
                    .filter(it -> Objects.equals(templateId, it.getId()))
                    .map(it -> ((SearchTemplate) it))
                    .findFirst()
                    // 加一个按 id 查询的补偿逻辑
                    .orElseGet(() -> {
                        // 场景id为空默认返回全部场景
                        if (Strings.isNullOrEmpty(templateId)) {
                            return templates.stream()
                                    .filter(it -> Objects.equals(SearchTemplateExt.DefaultScene.ALL.getApiName(), it.getApiName()))
                                    .findFirst()
                                    .map(SearchTemplate.class::cast)
                                    .orElse(null);
                        }
                        return findSearchTemplate(templateId, describeApiName, searchTemplateType);
                    });
        }
        return findSearchTemplate(templateId, describeApiName, searchTemplateType);
    }

    private SearchTemplate findSearchTemplate(String templateId, String describeApiName, String searchTemplateType) {
        CustomSceneResult byIdWithMerge = serviceFacade.findByIdWithMerge(templateId, describeApiName, searchTemplateType, actionContext.getUser());
        return new SearchTemplate(byIdWithMerge.getSearchTemplate());
    }

    private Result generateResult(String token) {
        TokenInfo tokenInfo = tokenService.query(token);
        if (tokenInfo == null) {
            throw new MetaDataException("token not exist!");
        }
        int num = Optional.ofNullable(tokenInfo.getProgress()).orElse(0);
        if (tokenInfo.isOngoing()) {
            return Result.builder()
                    .ext("")
                    .token(token)
                    .path("")
                    .file_name("")
                    .total_count(totalCount)
                    .currentCount(num)
                    .export_type("")
                    .build();
        }

        if (tokenInfo.isSuccess()) {
            log.info("export success,result:{}", JSON.toJSONString(tokenInfo.getResult()));
            if (null == objectDescribe) {
                objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
            }
            String pathAndSize = tokenInfo.getResult();
            String[] arr = pathAndSize.split("\\|");
            String fileName = getFileName();
            return Result.builder()
                    .token(token)
                    .path(arr[0])
                    .size(Long.parseLong(arr[1]))
                    .ext(arr[2])
                    .export_type(arr[3])
                    .fileExpiredTime(getFileExpiredTime(arr))
                    .file_name(fileName)
                    .total_count(totalCount)
                    .currentCount(num).build();
        }

        if (tokenInfo.isError()) {
            String errorMsg = tokenInfo.getMessage();
            if (StringUtils.isNotBlank(errorMsg) && errorMsg.contains(ERROR_SPLIT)) {
                String message = StringUtils.substringAfter(errorMsg, ERROR_SPLIT);
                throw new MetaDataBusinessException(message);
            }
            throw new MetaDataException("export data failed! " + errorMsg);
        }
        throw new MetaDataException("export data failed!");
    }

    private Long getFileExpiredTime(String[] arr) {
        if (arr.length > 4) {
            return Long.parseLong(arr[4]);
        }
        return null;
    }

    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        if (CollectionUtils.empty(fields) || CollectionUtils.empty(dataList)) {
            return;
        }
        infraServiceFacade.fillQuoteFieldValue(user, dataList, describe, true);
        batchConvertMultiLangField(dataList, fields, describe);
        convertFieldForView(user, describe, fields, dataList);
    }

    private void convertFieldForView(User user, IObjectDescribe describe, List<IFieldDescribe> exportFiledDescribe, List<IObjectData> objectDataList) {
        String tenantId = user.getTenantId();
        String userId = user.getUserId();
        //字段掩码处理，因为后面负责人会被处理成名字，所以掩码处理要放在最前面。
        batchConvertMaskField(user, objectDataList, exportFiledDescribe);
        batchConvertWhatGroupField(user, objectDataList, exportFiledDescribe);
        for (IFieldDescribe fieldDescribe : exportFiledDescribe) {
            if (fieldDescribe == null) {
                continue;
            }
            String type = fieldDescribe.getType();
            if (IObjectDescribe.CREATED_BY.equals(fieldDescribe.getApiName())
                    || IObjectDescribe.LAST_MODIFIED_BY.equals(fieldDescribe.getApiName())
                    || IFieldType.EMPLOYEE.equals(type)
                    || IFieldType.EMPLOYEE_MANY.equals(type)) {
                batchConvertOwnerIdToName(objectDataList, fieldDescribe, tenantId, userId);
                continue;
            }
            if (IFieldType.DEPARTMENT.equals(type) || IFieldType.DEPARTMENT_MANY.equals(type)) {
                batchConvertDeptIdToName(objectDataList, fieldDescribe, tenantId, userId);
                continue;
            }
            if (IFieldType.OUT_EMPLOYEE.equals(type)) {
                batchConvertOutEmployeeIdToName(objectDataList, fieldDescribe, user);
                continue;
            }
            if (IFieldType.OUT_DEPARTMENT.equals(type)) {
                batchConvertOutDeptIdToName(objectDataList, fieldDescribe, user);
                continue;
            }
            if (type.equals(IFieldType.OBJECT_REFERENCE) || type.equals(IFieldType.MASTER_DETAIL)) {
                batchConvertRefIdToName(objectDataList, tenantId, fieldDescribe, userId);
                continue;
            }
            if (type.equals(IFieldType.OBJECT_REFERENCE_MANY)) {
                batchConvertManyRefIdToName(objectDataList, tenantId, fieldDescribe, userId);
                continue;
            }
            // 处理相关团队
            if (arg.exportRelevantTeam() && Objects.equals(ObjectDataExt.RELEVANT_TEAM, fieldDescribe.getApiName())) {
                OptionalFeaturesSwitchDTO optionalFeaturesSwitch = objectOptionalFeaturesSwitches.get(fieldDescribe.getDescribeApiName());
                if (Objects.nonNull(optionalFeaturesSwitch) && !optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
                    continue;
                }
                batchConvertRelevantTeam(objectDataList, fieldDescribe, user);
                continue;
            }
            if (IFieldType.QUOTE.equals(type)) {
                batchConvertQuote(describe, objectDataList, (QuoteFieldDescribe) fieldDescribe, user);
                continue;
            }
            if (IFieldType.TOWN.equals(type) || IFieldType.VILLAGE.equals(type)) {
                batchConvertTownOrVillage(objectDataList, fieldDescribe, user);
                continue;
            }
            if (IFieldType.DIMENSION.equals(type)) {
                batchConvertDimension(objectDataList, fieldDescribe, user);
                continue;
            }
            // 处理嵌入式图片字段
            if (IFieldType.IMAGE.equals(type) && FileExtUtil.isEmbeddedImageField(fieldDescribe)) {
                batchConvertEmbeddedImageField(objectDataList, fieldDescribe, user);
                continue;
            }
            if (isExportFileAttachment(describe) && (IFieldType.FILE_ATTACHMENT.equals(type) || IFieldType.IMAGE.equals(type) || IFieldType.SIGNATURE.equals(type))) {
                batchConvertImageAndFileAttachment(objectDataList, describe, fieldDescribe, user);
                continue;
            }
            for (IObjectData objectData : objectDataList) {
                Object value = infraServiceFacade.convertData(objectData, fieldDescribe, user);
                objectData.set(fieldDescribe.getApiName(), value);
            }
        }
    }

    private void batchConvertImageAndFileAttachment(List<IObjectData> objectDataList, IObjectDescribe describe, IFieldDescribe fieldDescribe, User user) {
        List<String> exportFields = exportFileAttachmentFields.get(describe.getApiName());
        if (CollectionUtils.empty(exportFields) || !exportFields.contains(fieldDescribe.getApiName())) {
            return;
        }
        if (CollectionUtils.nullToEmpty(fileAttachmentFields.get(describe.getApiName())).stream().noneMatch(x -> Objects.equals(x.getApiName(), fieldDescribe.getApiName()))) {
            fileAttachmentFields.computeIfAbsent(describe.getApiName(), (k) -> Lists.newArrayList()).add(fieldDescribe);
        }
        calculateFileSize(objectDataList, fieldDescribe);
        for (IObjectData objectData : objectDataList) {
            String value = infraServiceFacade.convertData(objectData, fieldDescribe, user);
            objectData.set(fieldDescribe.getApiName(), value);
            IObjectData newData = new ObjectData();
            newData.setName(objectData.getName());
            newData.set(fieldDescribe.getApiName(), value);
            fileAttachmentDataList.computeIfAbsent(describe.getApiName(), (k) -> Lists.newArrayList()).add(newData);
        }
    }

    public void calculateFileSize(List<IObjectData> dataList, IFieldDescribe fieldDescribe) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        ImageExportStrategy imageExportStrategy = getImageExportStrategy();
        boolean isImageField = FieldDescribeExt.of(fieldDescribe).isImageField();

        for (IObjectData data : dataList) {
            Object o = data.get(fieldDescribe.getApiName());
            if (!(o instanceof List)) {
                continue;
            }
            List<ExportFile> exportFiles;
            try {
                exportFiles = JSONArray.parseArray(o.toString(), ExportFile.class);
            } catch (Exception e) {
                continue;
            }
            if (CollectionUtils.empty(exportFiles)) {
                continue;
            }

            // 根据不同的图片导出策略计算嵌入式图片大小
            calculateEmbeddedImageSize(exportFiles, imageExportStrategy, isImageField);

            // 计算总文件大小（所有策略都需要）
            for (ExportFile exportFile : exportFiles) {
                Long size = exportFile.getSize();
                if (Objects.nonNull(size)) {
                    exportFileSize += size;
                } else if (!FieldDescribeExt.of(fieldDescribe).isSignatureField()) {
                    exportFileSize += 1024 * 1024 * 3L;
                }
            }

            // 检查大小限制
            validateFileSizeLimit();
        }
    }

    /**
     * 根据图片导出策略计算嵌入式图片大小
     *
     * @param exportFiles 导出文件列表
     * @param imageExportStrategy 图片导出策略
     * @param isImageField 是否为图片字段
     */
    private void calculateEmbeddedImageSize(List<ExportFile> exportFiles, ImageExportStrategy imageExportStrategy, boolean isImageField) {
        if (!isImageField) {
            return;
        }

        switch (imageExportStrategy) {
            case PREVIEW_AND_LINK:
                // 预览+链接模式：只计算第一张图片的大小
                if (!exportFiles.isEmpty()) {
                    ExportFile firstImage = exportFiles.get(0);
                    if (Objects.nonNull(firstImage.getSize())) {
                        embeddedExcelImageSize += firstImage.getSize();
                    }
                }
                break;

            case EMBED_ONLY:
                // 仅嵌入模式：计算所有图片的大小
                for (ExportFile exportFile : exportFiles) {
                    if (Objects.nonNull(exportFile.getSize())) {
                        embeddedExcelImageSize += exportFile.getSize();
                    }
                }
                break;

            case LINK_ONLY:
            default:
                // 仅链接模式：不计算嵌入式图片大小
                break;
        }
    }

    /**
     * 验证文件大小限制
     */
    private void validateFileSizeLimit() {
        if (embeddedExcelImageSize > embeddedExcelImageMaxSize) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPORT_EMBEDDED_EXCEL_IMAGE_MAX_SIZE_ERROR,
                    "嵌入到excel中的图片大小{0},超过最大允许大小{1},请减少数据量后重新操作.",// ignoreI18n
                    FileSizeConverter.convertFileSize(embeddedExcelImageSize), FileSizeConverter.convertFileSize(embeddedExcelImageMaxSize)));
        }
        if (exportFileSize > exportFileMaxSize) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPORT_FILE_MAX_SIZE_ERROR,
                    "导出图片附件大小{0},超过最大允许大小{1},请减少数据量后重新操作.",// ignoreI18n
                    FileSizeConverter.convertFileSize(exportFileSize), FileSizeConverter.convertFileSize(exportFileMaxSize)));
        }
    }

    private void batchConvertMultiLangField(List<IObjectData> objectDataList, List<IFieldDescribe> fieldDescribes, IObjectDescribe describe) {
        List<IFieldDescribe> multiLangFields = fieldDescribes.stream()
                .filter(it -> BooleanUtils.isTrue(it.getEnableMultiLang()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(multiLangFields)) {
            return;
        }
        for (IObjectData data : objectDataList) {
            for (IFieldDescribe multiLangField : multiLangFields) {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                Map<String, Object> multiLangValue = dataExt.getMultiLangValue(actionContext.getTenantId(), describe.getApiName(), multiLangField);
                dataExt.putAll(multiLangValue);
            }
        }
    }

    private void batchConvertWhatGroupField(User user, List<IObjectData> objectDataList, List<IFieldDescribe> exportFiledDescribe) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXPORT_SUPPORT_WHAT_FIELD, user.getTenantId())) {
            return;
        }
        if (CollectionUtils.empty(objectDataList) || CollectionUtils.empty(exportFiledDescribe)) {
            return;
        }
        List<What> whatFields = ObjectDescribeExt.of(objectDescribe).getWhatFields();
        List<String> exportFieldApiNames = exportFiledDescribe.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        Map<String, Set<String>> whatObjectApiNameAndIds = Maps.newHashMap();
        whatFields.forEach(whatField -> {
            if (!(exportFieldApiNames.contains(whatField.getApiNameFieldApiName()) && exportFieldApiNames.contains(whatField.getIdFieldApiName()))) {
                return;
            }
            for (IObjectData data : objectDataList) {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                String objectApiName = dataExt.getWhatObjectApiName(whatField);
                String objectDataId = dataExt.getWhatObjectDataId(whatField);
                if (StringUtils.isBlank(objectApiName)) {
                    dataExt.set(whatField.getIdFieldApiName(), "");
                    continue;
                }
                Set<String> ids = whatObjectApiNameAndIds.computeIfAbsent(objectApiName, k -> Sets.newHashSet());
                if (StringUtils.isNotBlank(objectDataId)) {
                    ids.add(objectDataId);
                }
            }
        });

        if (CollectionUtils.empty(whatObjectApiNameAndIds)) {
            return;
        }

        Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjects(user.getTenantId(), whatObjectApiNameAndIds.keySet());
        Table<String, String, String> apiNameAndIdAndLabels = HashBasedTable.create();
        whatObjectApiNameAndIds.forEach((apiName, ids) -> {
            IObjectDescribe describe = objectDescribeMap.get(apiName);
            if (Objects.isNull(describe)) {
                return;
            }
            List<IObjectData> dataList = findWhatDataBySearchQuery(user, apiName, ids);
            dataList.forEach(data -> {
                String label = BooleanUtils.isTrue(describe.isOpenDisplayName()) ? data.getDisplayName() : data.getName();
                if (StringUtils.isBlank(label)) {
                    label = "";
                }
                apiNameAndIdAndLabels.put(apiName, data.getId(), label);
            });
        });

        for (What whatField : whatFields) {
            for (IObjectData data : objectDataList) {
                ObjectDataExt dataExt = ObjectDataExt.of(data);

                String objectApiName = dataExt.getWhatObjectApiName(whatField);
                String objectDataId = dataExt.getWhatObjectDataId(whatField);
                if (StringUtils.isBlank(objectApiName)) {
                    dataExt.set(whatField.getApiNameFieldApiName(), "");
                    dataExt.set(whatField.getIdFieldApiName(), "");
                    continue;
                }

                IObjectDescribe describe = objectDescribeMap.getOrDefault(objectApiName, null);
                if (Objects.isNull(describe)) {
                    dataExt.set(whatField.getApiNameFieldApiName(), I18NExt.getOrDefault(I18NKey.OBJECT_NOT_EXIST, "对象{0}不存在", objectApiName));// ignoreI18n
                    dataExt.set(whatField.getIdFieldApiName(), I18NExt.getOrDefault(I18NKey.DATA_NOT_FIND_OR_DELETE, "数据不存在或已删除"));// ignoreI18n
                    continue;
                }
                dataExt.set(whatField.getApiNameFieldApiName(), describe.getDisplayName());

                String label = apiNameAndIdAndLabels.get(objectApiName, objectDataId);
                if (StringUtils.isBlank(label)) {
                    label = I18NExt.getOrDefault(I18NKey.DATA_NOT_FIND_OR_DELETE, "数据不存在或已删除");// ignoreI18n
                }
                dataExt.set(whatField.getIdFieldApiName(), label);
            }
        }
    }

    private List<IObjectData> findWhatDataBySearchQuery(User user, String apiName, Set<String> ids) {
        List<String> fields = Lists.newArrayList(IObjectData.ID, IObjectData.NAME, IObjectData.DISPLAY_NAME);
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(user).skipRelevantTeam(true).projectionFields(fields).build();
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, Lists.newArrayList(ids), apiName);
        return dataList;
    }

    private void batchConvertDimension(List<IObjectData> objectDataList, IFieldDescribe fieldDescribe, User user) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        if (FieldDescribeExt.of(fieldDescribe).isTempField()) {
            return;
        }
        Set<String> dimensionIds = Sets.newHashSet();
        for (IObjectData data : objectDataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            List<String> dimensionValues = dataExt.getDimensionValues(fieldDescribe.getApiName());
            if (CollectionUtils.notEmpty(dimensionValues)) {
                dimensionIds.addAll(dimensionValues);
            }
        }
        List<DimensionInfo> dimensionInfos = infraServiceFacade.getDimensionInfoByIds(Lists.newArrayList(dimensionIds), user);
        for (IObjectData data : objectDataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            List<String> dimensionValues = dataExt.getDimensionValues(fieldDescribe.getApiName());
            List<DimensionInfo> dimensionInfoList = dimensionInfos.stream().filter(it -> dimensionValues.contains(it.getId())).collect(Collectors.toList());
            if (isSupportExportDimensionCode(actionContext.getTenantId())) {
                String name = dimensionInfoList.stream().map(DimensionInfo::getName).collect(Collectors.joining("|"));
                String code = dimensionInfoList.stream().map(DimensionInfo::getDimCode).collect(Collectors.joining("|"));
                dataExt.set(fieldDescribe.getApiName(), code);
                dataExt.set(FieldDescribeExt.getLookupNameByFieldName(fieldDescribe.getApiName()), name);
            } else {
                String name = dimensionInfoList.stream().map(DimensionInfo::getName).collect(Collectors.joining("|"));
                dataExt.set(fieldDescribe.getApiName(), name);
            }
        }
    }

    private void batchConvertTownOrVillage(List<IObjectData> objectDataList, IFieldDescribe fieldDescribe, User user) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        Set<String> codes = objectDataList.stream()
                .map(x -> ObjectDataExt.of(x).getStringValue(fieldDescribe.getApiName()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(codes)) {
            return;
        }
        List<MetaDataGlobalService.CountryInfo> townOrVillageInfos = serviceFacade.batchQueryAreaLabelsByCodes(user, codes);
        for (IObjectData data : objectDataList) {
            String code = data.get(fieldDescribe.getApiName(), String.class);
            if (StringUtils.isBlank(code)) {
                data.set(fieldDescribe.getApiName(), "");
                continue;
            }
            String label = townOrVillageInfos.stream()
                    .filter(x -> Objects.equals(x.getValue(), code))
                    .map(MetaDataGlobalService.CountryInfo::getLabel)
                    .findFirst().orElse(null);
            if (StringUtils.isNotBlank(label)) {
                data.set(fieldDescribe.getApiName(), label);
            } else {
                data.set(fieldDescribe.getApiName(), "");
            }
        }
    }

    private void batchConvertMaskField(User user, List<IObjectData> dataList, List<IFieldDescribe> fields) {
        List<IFieldDescribe> maskFields = fields.stream()
                .filter(x -> FieldDescribeExt.of(x).isShowMask())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        serviceFacade.fillMaskFieldValue(user, dataList, maskFields, false, true);
    }

    /**
     * 引用字段如果是引用图片或者附件就置空
     */
    private void batchConvertQuote(IObjectDescribe describe, List<IObjectData> objectDataList, QuoteFieldDescribe fieldDescribe, User user) {
        if (IFieldType.IMAGE.equals(fieldDescribe.getQuoteFieldType()) ||
                IFieldType.FILE_ATTACHMENT.equals(fieldDescribe.getQuoteFieldType())) {
            if (isExportFileAttachment(describe)) {
                String type = fieldDescribe.getQuoteFieldType();
                IFieldDescribe iFieldDescribe = FieldDescribeExt.of(fieldDescribe).copy();
                Map<String, Object> map = FieldDescribeExt.of(iFieldDescribe).toMap();
                map.put(IFieldDescribe.TYPE, type);
                IFieldDescribe imageOrFileAttachmentFieldDescribe = FieldDescribeFactory.newInstance(map);
                batchConvertImageAndFileAttachment(objectDataList, describe, imageOrFileAttachmentFieldDescribe, user);
            } else {
                objectDataList.forEach(data -> data.set(fieldDescribe.getApiName(), ""));
            }
        }
    }

    public boolean isExportFileAttachment(IObjectDescribe describe) {
        if (ObjectDescribeExt.of(describe).isSlaveObject() && !Objects.equals(describe.getApiName(), objectDescribe.getApiName())) {
            if (Objects.nonNull(arg.getDetailArg()) && CollectionUtils.notEmpty(arg.getDetailArg().getDetailInfo())) {
                return arg.getDetailArg().getDetailInfo().stream().anyMatch(x -> CollectionUtils.notEmpty(x.getFileFields()));
            }
            return false;
        } else {
            return CollectionUtils.notEmpty(arg.getFileFields());
        }
    }

    /**
     * 批量处理相关团队成员名称
     */
    private void batchConvertRelevantTeam(List<IObjectData> objectDataList, IFieldDescribe fieldDescribe, User user) {
        if (TeamMember.isTeamMemberTypeExportGray(user.getTenantId())) {
            batchConvertRelevantTeamByType(objectDataList, user);
            return;
        }

        //统计相关团队成员ID
        List<String> idList = objectDataList.stream().flatMap(data -> {
            List teamList = Lists.newArrayList();
            if (data.get(fieldDescribe.getApiName()) instanceof List) {
                teamList = (List) data.get(fieldDescribe.getApiName());
            }
            if (CollectionUtils.notEmpty(teamList)) {
                return getRelevantMemberIdSet(teamList).stream();
            }
            return Lists.newArrayList().stream();
        }).distinct().map(Object::toString).collect(Collectors.toList());
        //根据ID查询userInfo
        if (CollectionUtils.notEmpty(idList)) {
            // key: userId value: userName
            List<UserInfo> userNameByIds = serviceFacade.getUserNameByIds(user.getTenantId(), user.getUserId(), idList);
            userNameByIds.stream().filter(UserInfo::disabled).forEach(x -> x.setName(I18N.text(I18NKey.DISABLED, x.getName())));
            Map<String, String> userNameMap = userNameByIds.stream().collect(Collectors.toMap(UserInfo::getId, UserInfo::getName, (x, y) -> x));
            // key: userId value: roleType
            //替换相关团队字段
            objectDataList.forEach(data -> {
                List teamList = (List) data.get(fieldDescribe.getApiName());
                Map<TeamMember.RoleWithPermission, List<String>> roleNameMap = Maps.newHashMap();
                Map<String, List<String>> rolePermissionNameMap = Maps.newHashMap();
                if (CollectionUtils.notEmpty(teamList)) {
                    // key: 相关团队类型   value: 人员姓名列表
                    List<TeamMember> teamMembers = (List<TeamMember>) teamList.stream().map(tMap -> new TeamMember((Map) tMap)).collect(Collectors.toList());

                    teamMembers.stream()
                            .filter(x -> TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()))
                            .forEach(t -> {
                                if (customTeamRoleGray) {
                                    if (StringUtils.isNotEmpty(t.getRoleCode())
                                            && !StringUtils.equals(TeamMember.Role.OWNER.getValue(), t.getRoleCode())
                                            && Objects.nonNull(t.getPermission())) {
                                        rolePermissionNameMap.merge(
                                                String.join("_", t.getRoleCode(), t.getPermission().getValue()),
                                                Lists.newArrayList(userNameMap.get(t.getEmployee())),
                                                (prev, one) -> Stream.of(prev, one).flatMap(Collection::stream).distinct().collect(Collectors.toList()));
                                    }
                                } else {
                                    if (Objects.nonNull(t.getRoleWithPermission())) {
                                        roleNameMap.merge(
                                                t.getRoleWithPermission(),
                                                Lists.newArrayList(userNameMap.get(t.getEmployee())),
                                                (prev, one) -> Stream.of(prev, one).flatMap(Collection::stream).distinct().collect(Collectors.toList()));
                                    }
                                }
                            });
                }
                // 补充相关团队字段的值
                if (customTeamRoleGray) {
                    rolePermissionNameMap.forEach((key, names) -> {
                        String[] rolePermission = key.split("_");
                        if (rolePermission.length != 2) {
                            return;
                        }
                        String nameStr = StringUtils.join(CollectionUtils.nullToEmpty(names).toArray(), "|");
                        String apiName = String.join("_", TEAM_MEMBER, key);
                        data.set(apiName, nameStr);
                    });
                } else {
                    List<String> names = CollectionUtils.nullToEmpty(roleNameMap.get(TeamMember.RoleWithPermission.NORMAL_STAFF_READONLY));
                    String nameStr = StringUtils.join(names.toArray(), "|");
                    String readApiName = String.join("_", TEAM_MEMBER, TeamMember.Role.NORMAL_STAFF.getValue(), TeamMember.Permission.READONLY.getValue());
                    data.set(readApiName, nameStr);
                    names = CollectionUtils.nullToEmpty(roleNameMap.get(TeamMember.RoleWithPermission.NORMAL_STAFF_READWRITE));
                    nameStr = StringUtils.join(names.toArray(), "|");
                    String writeApiName = String.join("_", TEAM_MEMBER, TeamMember.Role.NORMAL_STAFF.getValue(), TeamMember.Permission.READANDWRITE.getValue());
                    data.set(writeApiName, nameStr);
                }

                // 补充老对象相关团队的值
                if (!customTeamRoleGray) {
                    customFillTeamMemberValues(data, roleNameMap);
                }
            });
        }
    }

    private void batchConvertRelevantTeamByType(List<IObjectData> objectDataList, User user) {
        List<TeamMemberInfoPoJo> teamMemberList = Lists.newArrayList();
        for (IObjectData data : objectDataList) {
            List<TeamMemberInfoPoJo> teamMembers = ObjectDataExt.of(data).getRelevantTeamFromObjectData();
            teamMemberList.addAll(teamMembers);
        }
        if (CollectionUtils.empty(teamMemberList)) {
            return;
        }
        List<TeamMemberInfoPoJo> teamMemberInfoPoJos = Lists.newArrayList();
        Map<String, List<TeamMemberInfoPoJo>> memberTypeList = teamMemberList.stream()
                .filter(x -> TeamMember.MemberType.of(x.getTeamMemberType()).isInnerMember())
                .collect(Collectors.groupingBy(TeamMemberInfoPoJo::getTeamMemberType));
        for (Map.Entry<String, List<TeamMemberInfoPoJo>> entry : memberTypeList.entrySet()) {
            String type = entry.getKey();
            List<TeamMemberInfoPoJo> memberInfoPoJos = entry.getValue();
            List<String> uniqueMemberIds = memberInfoPoJos.stream().map(TeamMemberInfoPoJo::parseInnerMemberValue).distinct().collect(Collectors.toList());
            Map<String, String> idAndName = Maps.newHashMap();
            if (TeamMember.MemberType.EMPLOYEE.getValue().equals(type)) {
                List<UserInfo> userNameByIds = serviceFacade.getUserNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                userNameByIds.stream().filter(UserInfo::disabled).forEach(x -> x.setName(I18N.text(I18NKey.DISABLED, x.getName())));
                idAndName = userNameByIds.stream().collect(Collectors.toMap(UserInfo::getId, UserInfo::getName, (x, y) -> x));
            }
            if (TeamMember.MemberType.GROUP.getValue().equals(type)) {
                idAndName = serviceFacade.getGroupNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
            }
            if (TeamMember.MemberType.DEPARTMENT.getValue().equals(type)) {
                List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoIds = serviceFacade.getDeptInfoNameByIdsAndStatus(user.getTenantId(), user.getUserId(), uniqueMemberIds, QueryDeptInfoByDeptIds.DeptStatusEnum.ALL);
                idAndName = deptInfoIds.stream().collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, QueryDeptInfoByDeptIds.DeptInfo::getDeptName));
            }
            if (TeamMember.MemberType.ROLE.getValue().equals(type)) {
                idAndName = serviceFacade.queryRoleNameByRoleCode(user, uniqueMemberIds);
            }
            for (TeamMemberInfoPoJo teamMemberInfoPoJo : memberInfoPoJos) {
                String name = idAndName.get(teamMemberInfoPoJo.parseInnerMemberValue());
                if (StringUtils.isNotBlank(name)) {
                    TeamMemberInfoPoJo memberInfoPoJo = TeamMemberInfoPoJo.builder()
                            .teamMemberEmployee(teamMemberInfoPoJo.getTeamMemberEmployee())
                            .teamMemberName(name)
                            .teamMemberType(teamMemberInfoPoJo.getTeamMemberType())
                            .teamMemberRole(teamMemberInfoPoJo.getTeamMemberRole())
                            .teamMemberRoleList(teamMemberInfoPoJo.getTeamMemberRoleList())
                            .teamMemberPermissionType(teamMemberInfoPoJo.getTeamMemberPermissionType())
                            .build();
                    teamMemberInfoPoJos.add(memberInfoPoJo);
                }
            }
        }
        for (IObjectData data : objectDataList) {
            Map<String, List<String>> memberDataMap = Maps.newHashMap();
            List<TeamMemberInfoPoJo> memberInfoPoJos = ObjectDataExt.of(data).getRelevantTeamFromObjectData();
            Map<String, List<TeamMemberInfoPoJo>> teamMemberInfoMap = memberInfoPoJos.stream()
                    .collect(Collectors.groupingBy(TeamMemberInfoPoJo::getTeamMemberType));
            for (Map.Entry<String, List<TeamMemberInfoPoJo>> entry : teamMemberInfoMap.entrySet()) {
                String memberType = entry.getKey();
                List<TeamMemberInfoPoJo> dataMembers = entry.getValue();
                List<TeamMemberInfoPoJo> typeMembers = teamMemberInfoPoJos.stream()
                        .filter(x -> Objects.equals(x.getTeamMemberType(), memberType))
                        .collect(Collectors.toList());
                for (TeamMemberInfoPoJo dataMember : dataMembers) {
                    for (String role : dataMember.getTeamMemberRoleList()) {
                        String name = typeMembers.stream()
                                .filter(x -> Objects.equals(x.parseInnerMemberValue(), dataMember.parseInnerMemberValue()))
                                .map(TeamMemberInfoPoJo::getTeamMemberName)
                                .findFirst().orElse("");
                        if (StringUtils.isBlank(name)) {
                            continue;
                        }
                        memberDataMap.computeIfAbsent(FieldDescribeExt.getTeamMemberFieldApiName(memberType, role, dataMember.getTeamMemberPermissionType()),
                                k -> Lists.newArrayList()).add(name);
                    }
                }
            }
            memberDataMap.forEach((fieldApiName, names) -> data.set(fieldApiName, StringUtils.join(names, "|")));
        }
    }

    /**
     * 老对象override此方法
     */
    protected void customFillTeamMemberValues(IObjectData data, Map<TeamMember.RoleWithPermission, List<String>> roleNameMap) {

    }

    private Set<String> getRelevantMemberIdSet(List teamList) {
        Set<String> ids = Sets.newHashSet();
        for (Object team : teamList) {
            Object teamMemberType = ((Map) team).get(TeamMember.TEAM_MEMBER_TYPE);
            String memberType = TeamMember.MemberType.of(Objects.isNull(teamMemberType) ? null : String.valueOf(teamMemberType)).getValue();
            if (TeamMember.MemberType.EMPLOYEE.getValue().equals(memberType)) {
                List<String> list = (List<String>) ((Map) team).get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME);
                ids.addAll(list);
            }
        }
        return ids;
    }

    private void batchConvertOwnerIdToName(List<IObjectData> dataList, IFieldDescribe fieldDescribe, String tenantId, String userId) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        String fieldApiName = fieldDescribe.getApiName();
        if (StringUtils.endsWith(fieldApiName, "__r")) {
            return;
        }
        Set<String> idList = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType()) || IFieldType.EMPLOYEE_MANY.equals(fieldDescribe.getType())) {
                Object o = objectData.get(fieldApiName);
                if (o instanceof List && ((List<String>) o).size() > 0) {
                    idList.addAll((List<String>) o);
                }
            } else {
                idList.add(objectData.get(fieldApiName, String.class));
            }
        }
        if (CollectionUtils.empty(idList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        List<UserInfo> userInfoList = serviceFacade.getUserNameByIds(tenantId, userId, Lists.newArrayList(idList));
        if (CollectionUtils.empty(userInfoList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        for (IObjectData objectData : dataList) {
            List<String> fieldValues = Lists.newArrayList();
            if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType()) || IFieldType.EMPLOYEE_MANY.equals(fieldDescribe.getType())) {
                Object o = objectData.get(fieldApiName);
                if (o instanceof List) {
                    fieldValues.addAll((List<String>) o);
                }
            } else {
                fieldValues.add(objectData.get(fieldApiName, String.class));
            }
            List<UserInfo> infoList = userInfoList.stream()
                    .filter(x -> fieldValues.contains(x.getId()))
                    .collect(Collectors.toList());
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_OWNER_GRAY, tenantId) && OUT_OWNER.equals(fieldApiName)) {
                String enterpriseNameAndName = infoList.stream().map(x -> {
                    String name = x.getName();
                    if (StringUtils.isNotBlank(x.getEnterpriseName()) && StringUtils.isNotBlank(x.getName())) {
                        name = StringUtils.join(x.getEnterpriseName(), ".", x.getName());
                    }
                    if (x.disabled()) {
                        name = I18N.text(I18NKey.DISABLED, name);
                    }
                    return name;
                }).collect(Collectors.joining("|"));
                objectData.set(fieldApiName, enterpriseNameAndName);
                continue;
            }
            String employeeNames = infoList.stream().map(x -> {
                String name = x.getName();
                if (x.disabled()) {
                    name = I18N.text(I18NKey.DISABLED, name);
                }
                return name;
            }).collect(Collectors.joining("|"));
            if (OUT_OWNER.equals(fieldApiName) || !isSupportExportEmployeeCode(tenantId)) {
                objectData.set(fieldApiName, employeeNames);
            } else {
                String employeeCodes = infoList.stream().map(UserInfo::getEmpNum).collect(Collectors.joining("|"));
                objectData.set(FieldDescribeExt.getLookupNameByFieldName(fieldApiName), employeeNames);
                objectData.set(fieldApiName, employeeCodes);
            }
        }
    }

    private void batchConvertOutEmployeeIdToName(List<IObjectData> dataList, IFieldDescribe fieldDescribe, User user) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        String fieldApiName = fieldDescribe.getApiName();
        Set<String> idList = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            if (IFieldType.OUT_EMPLOYEE.equals(fieldDescribe.getType())) {
                Object o = objectData.get(fieldApiName);
                if (o instanceof List && ((List<String>) o).size() > 0) {
                    idList.addAll((List<String>) o);
                }
            } else {
                idList.add(objectData.get(fieldApiName, String.class));
            }
        }
        if (CollectionUtils.empty(idList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        List<OutUserInfo> userInfoList = serviceFacade.batchGetOutUsers(user.getTenantId(), idList);
        if (CollectionUtils.empty(userInfoList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        for (IObjectData objectData : dataList) {
            Object o = objectData.get(fieldApiName);
            if (o instanceof List) {
                List<String> fieldValues = (List<String>) o;
                List<OutUserInfo> infoList = userInfoList.stream()
                        .filter(x -> fieldValues.contains(x.getId()))
                        .collect(Collectors.toList());
                String employeeNames = infoList.stream().map(x -> {
                    String name = x.getName();
                    if (x.disabled()) {
                        name = I18N.text(I18NKey.DISABLED, name);
                    }
                    return name;
                }).collect(Collectors.joining("|"));
                objectData.set(fieldApiName, employeeNames);
            }
        }
    }


    @SuppressWarnings("unchecked")
    private void batchConvertDeptIdToName(List<IObjectData> dataList, IFieldDescribe fieldDescribe, String tenantId, String userId) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        String fieldApiName = fieldDescribe.getApiName();
        if (StringUtils.endsWith(fieldApiName, "__r")) {
            return;
        }
        Set<String> idList = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            Object o = objectData.get(fieldApiName);
            if (o instanceof List && ((List<String>) o).size() > 0) {
                idList.addAll((List) o);
            }
        }
        if (CollectionUtils.empty(idList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        List<DeptInfo> deptInfos = Lists.newArrayList();
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEPARTMENT_SUPPORT_REPEAT, tenantId)) {
            List<DeptInfo> deptInfoList = serviceFacade.batchGetDeptInfosByDeptIds(actionContext.getUser(), Lists.newArrayList(idList));
            if (CollectionUtils.empty(deptInfoList)) {
                setDataToEmpty(dataList, fieldApiName);
                return;
            }
            deptInfos.addAll(deptInfoList);
        } else {
            List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoList = serviceFacade.getDeptInfoNameByIdsAndStatus(tenantId,
                    userId, Lists.newArrayList(idList), QueryDeptInfoByDeptIds.DeptStatusEnum.ALL);
            if (CollectionUtils.empty(deptInfoList)) {
                setDataToEmpty(dataList, fieldApiName);
                return;
            }
            for (QueryDeptInfoByDeptIds.DeptInfo deptInfo : deptInfoList) {
                DeptInfo info = new DeptInfo();
                info.setDeptId(deptInfo.getDeptId());
                info.setDeptName(deptInfo.getDeptName());
                deptInfos.add(info);
            }
        }

        for (IObjectData objectData : dataList) {
            Object o = objectData.get(fieldApiName);
            if (o instanceof List) {
                List<String> fieldValues = (List<String>) o;
                List<DeptInfo> infoList = deptInfos.stream()
                        .filter(x -> fieldValues.contains(x.getDeptId()))
                        .collect(Collectors.toList());
                String departNames = infoList.stream().map(DeptInfo::getDeptName).collect(Collectors.joining("|"));
                if (isSupportExportDepartmentCode(tenantId)) {
                    String departCodes = infoList.stream().map(DeptInfo::getDeptCode).collect(Collectors.joining("|"));
                    objectData.set(FieldDescribeExt.getLookupNameByFieldName(fieldApiName), departNames);
                    objectData.set(fieldApiName, departCodes);
                } else {
                    objectData.set(fieldApiName, departNames);
                }
            }
        }
    }

    private void batchConvertOutDeptIdToName(List<IObjectData> dataList, IFieldDescribe fieldDescribe, User user) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        String fieldApiName = fieldDescribe.getApiName();
        Set<String> idList = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            Object o = objectData.get(fieldApiName);
            if (o instanceof List && ((List<String>) o).size() > 0) {
                idList.addAll((List<String>) o);
            }
        }
        if (CollectionUtils.empty(idList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoList = serviceFacade.batchGetOutDeptInfosByDeptIds(user,
                Lists.newArrayList(idList), QueryDeptInfoByDeptIds.DeptStatusEnum.ALL);
        if (CollectionUtils.empty(deptInfoList)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        for (IObjectData objectData : dataList) {
            Object o = objectData.get(fieldApiName);
            if (o instanceof List) {
                List<String> fieldValues = (List<String>) o;
                List<QueryDeptInfoByDeptIds.DeptInfo> infoList = deptInfoList.stream()
                        .filter(x -> fieldValues.contains(x.getDeptId()))
                        .collect(Collectors.toList());
                String departNames = infoList.stream().map(x -> x.getDeptName()).collect(Collectors.joining("|"));
                objectData.set(fieldApiName, departNames);
            }
        }
    }

    private boolean isSupportExportEmployeeCode(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EMPLOYEE_SUPPORT_REPEAT, tenantId);
    }

    private boolean isSupportExportDepartmentCode(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEPARTMENT_SUPPORT_REPEAT, tenantId);
    }

    private boolean isSupportExportDimensionCode(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DIMENSION_SUPPORT_REPEAT, tenantId);
    }

    private void batchConvertRefIdToName(List<IObjectData> dataList, String tenantId, IFieldDescribe fieldDescribe, String userId) {

        String targetApiName = "";
        if (fieldDescribe instanceof ObjectReferenceFieldDescribe) {
            targetApiName = ((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName();
        } else if (fieldDescribe instanceof MasterDetailFieldDescribe) {
            targetApiName = ((MasterDetailFieldDescribe) fieldDescribe).getTargetApiName();
        } else {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        String fieldApiName = fieldDescribe.getApiName();
        Set<String> idSet = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            String refId = objectData.get(fieldApiName, String.class);
            if (Strings.isNullOrEmpty(refId)) {
                continue;
            }
            idSet.add(refId);
        }

        List<String> idList = Lists.newArrayList();
        idList.addAll(idSet);
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(userId);
        actionContext.setEnterpriseId(tenantId);
        Map<String, String> refNameMap = getObjectDataByIdList(idList, tenantId, targetApiName);

        if (CollectionUtils.empty(refNameMap)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        for (IObjectData objectData : dataList) {
            String refId = objectData.get(fieldApiName, String.class);
            if (Strings.isNullOrEmpty(refId)) {
                continue;
            }
            if (Strings.isNullOrEmpty(refNameMap.get(refId))) {
                objectData.set(fieldApiName, "");
                emptyLookupId(objectData, fieldApiName);
            } else {
                objectData.set(fieldApiName, refNameMap.get(refId));
            }
        }
    }

    private void batchConvertManyRefIdToName(List<IObjectData> dataList, String tenantId, IFieldDescribe fieldDescribe, String userId) {

        String targetApiName = "";
        if (!(fieldDescribe instanceof IObjectReferenceMany)) {
            return;
        }
        targetApiName = ((IObjectReferenceMany) fieldDescribe).getTargetApiName();
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        String fieldApiName = fieldDescribe.getApiName();
        Set<String> idSet = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            List<String> refIds = objectData.get(fieldApiName, List.class);
            if (CollectionUtils.empty(refIds)) {
                continue;
            }
            idSet.addAll(refIds);
        }

        List<String> idList = Lists.newArrayList();
        idList.addAll(idSet);
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(userId);
        actionContext.setEnterpriseId(tenantId);
        Map<String, String> refNameMap = getObjectDataByIdList(idList, tenantId, targetApiName);

        if (CollectionUtils.empty(refNameMap)) {
            setDataToEmpty(dataList, fieldApiName);
            return;
        }
        for (IObjectData objectData : dataList) {
            List<String> refIds = objectData.get(fieldApiName, List.class);
            if (CollectionUtils.empty(refIds)) {
                continue;
            }

            List<String> names = refIds.stream()
                    .filter(refId -> Objects.nonNull(refId) && !Strings.isNullOrEmpty(refId))
                    .map(id -> refNameMap.get(id))
                    .filter(id -> !Strings.isNullOrEmpty(id))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(names)) {
                objectData.set(fieldApiName, "");
                emptyLookupId(objectData, fieldApiName);
            } else {
                objectData.set(fieldApiName, Joiner.on("|").join(names));
            }
        }
    }

    private void emptyLookupId(IObjectData objectData, String fieldApiName) {
        //使labelID列与label保持一直，即关联对象数据删除，不应该显示该数据ID
        if (!Strings.isNullOrEmpty(objectData.get(fieldApiName + IFieldDescribe.ID, String.class))) {
            objectData.set(fieldApiName + IFieldDescribe.ID, "");
        }
    }

    private Map<String, String> getObjectDataByIdList(List<String> idList, String tenantId, String targetApiName) {
        if (CollectionUtils.empty(idList)) {
            return Collections.emptyMap();
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, idList, targetApiName);
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, targetApiName);
        Map<String, String> result = Maps.newHashMap();

        for (IObjectData data : dataList) {
            //查找关联数据替换为多语
            if (ObjectDescribeExt.of(objectDescribe).isSupportDisplayName()) {
                result.put(data.getId(), ObjectDataExt.of(data).getFieldLangValue(FieldDescribeExt.DISPLAY_NAME));
            } else {
                result.put(data.getId(), ObjectDataExt.of(data).getFieldLangValue(IObjectData.NAME));
            }
        }
        return result;
    }

    private void setDataToEmpty(List<IObjectData> objectDataList, String fieldApiName) {
        for (IObjectData objectData : objectDataList) {
            objectData.set(fieldApiName, "");
            emptyLookupId(objectData, fieldApiName);
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (DISPATCH_ORDERS.equals(AppIdMapping.getNamedAppId(actionContext.getAppId())) && actionContext.getUser().isOutUser()) {
            return null;
        }
        if (whatListObjectApiNames.contains(actionContext.getObjectApiName())) {
            return null;
        }
        return StandardAction.Export.getFunPrivilegeCodes();
    }

    protected String getFileName() {
        if (DateTimeUtils.isGrayTimeZone()) {
            return I18N.text(I18NKey.EXPORT_FILE_NAME, objectDescribe.getDisplayName(), formatter.format(LocalDate.now(TimeZoneContextHolder.getUserTimeZone())));
        }
        return I18N.text(I18NKey.EXPORT_FILE_NAME, objectDescribe.getDisplayName(), formatter.format(LocalDate.now()));
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    protected final void pluginDoExport(Arg arg, IObjectDescribe describe, List<IObjectData> originalDataList, List<IObjectData> displayDataList) {
        runPlugin(describe.getApiName(), APLExportPlugin.DO_EXPORT, true, (plugin, pluginArg) -> {
            APLExportPlugin.Arg exportPluginArg = (APLExportPlugin.Arg) pluginArg;
            exportPluginArg.setOriginalDataList(originalDataList);
            exportPluginArg.setDisplayDataList(displayDataList);
            return ((APLExportPlugin) plugin).doExport(getContext(describe), exportPluginArg);
        });
    }

    private PluginContext getContext(IObjectDescribe describe) {
        return PluginContext.fromActionContext(actionContext, describe.getApiName());
    }

    @Override
    protected APLExportPlugin.Arg buildAPLExportPluginArg(String methodName) {
        String searchQueryInfo = arg.getSearch_query_info();
        SearchTemplateQuery searchTemplateQuery = SearchTemplateQueryExt.mergeByAPLPlugin(searchQueryInfo, searchQuery.toJsonString());
        arg.setSearch_query_info(searchTemplateQuery.toJsonString());
        APLExportPlugin.Arg aplExportPluginArg = new APLExportPlugin.Arg(objectDescribe.getApiName(), this.arg);
        // 后动作插件需要传 totalCount
        if (APLExportPlugin.EXPORT_AFTER.equals(methodName)) {
            aplExportPluginArg.setTotalCount(totalCount);
        }
        return aplExportPluginArg;
    }

    @Override
    protected void processAPLExportPluginResult(String method, APLExportPlugin.Arg pluginArg, APLExportPlugin.Result pluginResult) {
        switch (method) {
            case APLExportPlugin.BEFORE:
                mergeAPLExportPluginArg(pluginResult);
                break;
            case APLExportPlugin.DO_EXPORT:
                mergeAPLExportPluginResult(pluginArg, pluginResult);
                break;
            default:
        }
    }

    protected void doExportPluginAfter() {
        runPlugin(APLExportPlugin.EXPORT_AFTER, false, (plugin, pluginArg) ->
                ((APLExportPlugin) plugin).after(PluginContext.fromActionContext(actionContext), (APLExportPlugin.Arg) pluginArg));
    }

    private void mergeAPLExportPluginResult(APLExportPlugin.Arg pluginArg, APLExportPlugin.Result pluginResult) {
        Map<String, ObjectDataExt> objectDataMap = Maps.newHashMap();
        for (Map map : pluginResult.getDisplayDataList()) {
            ObjectDataExt dataExt = ObjectDataExt.of(map);
            objectDataMap.put(dataExt.getId(), dataExt);
        }
        for (Map data : pluginArg.getDisplayDataList()) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            Optional.ofNullable(objectDataMap.get(dataExt.getId()))
                    .ifPresent(it -> dataExt.putAll(it.toMap()));
        }
    }

    private void mergeAPLExportPluginArg(APLExportPlugin.Result pluginResult) {
        Arg exportArg = pluginResult.getExportArg();
        // 合并查询条件
        String searchQueryInfo = exportArg.getSearch_query_info();
        searchQuery = SearchTemplateQueryExt.mergeByAPLPlugin(searchQuery.toJsonString(), searchQueryInfo);
        // 合并支持导出的字段
        List<String> accordingFieldList = exportArg.getAccordingFieldList();
        arg.setAccording_to_list(exportArg.isAccording_to_list());
        arg.setAccordingFieldList(accordingFieldList);

        if (log.isInfoEnabled()) {
            log.info("mergeAPLExportPluginArg, arg:{}, searchQuery:{}", arg, searchQuery.toJsonString());
        }
    }

    // 图片导出策略枚举
    public enum ImageExportStrategy {
        LINK_ONLY,          // 仅链接（当前方式）
        PREVIEW_AND_LINK,   // 预览+链接（过渡方案）
        EMBED_ONLY          // 仅嵌入（未来方案）
    }

    /**
     * 获取图片导出策略
     */
    private ImageExportStrategy getImageExportStrategy() {
        // 通过灰度配置或租户配置来控制策略
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMAGE_EXPORT_EMBED_ONLY, actionContext.getTenantId())) {
            return ImageExportStrategy.EMBED_ONLY;
        } else if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMAGE_EXPORT_PREVIEW_AND_LINK, actionContext.getTenantId())) {
            return ImageExportStrategy.PREVIEW_AND_LINK;
        } else {
            return ImageExportStrategy.LINK_ONLY;
        }
    }

    /**
     * 根据策略补充图片字段
     */
    private void supplementImageFieldsByStrategy(List<IFieldDescribe> fields, IObjectDescribe describe) {
        ImageExportStrategy strategy = getImageExportStrategy();
        if (strategy == ImageExportStrategy.LINK_ONLY) {
            return; // 保持原有逻辑，不做修改
        }

        if (!isExportFileAttachment(describe)) {
            return;
        }

        List<String> exportFields = exportFileAttachmentFields.get(describe.getApiName());
        if (CollectionUtils.empty(exportFields)) {
            return;
        }

        ListIterator<IFieldDescribe> iterator = fields.listIterator();
        while (iterator.hasNext()) {
            IFieldDescribe field = iterator.next();
            String type = field.getType();

            if (IFieldType.IMAGE.equals(type) && exportFields.contains(field.getApiName())) {
                switch (strategy) {
                    case PREVIEW_AND_LINK:
                        // 只添加一个预览列
                        addSinglePreviewColumn(iterator, field, describe);
                        break;

                    case EMBED_ONLY:
                        // 根据配置添加多个嵌入列
                        replaceWithEmbeddedColumns(iterator, field, describe);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 添加单个预览列（预览+链接模式）
     */
    private void addSinglePreviewColumn(ListIterator<IFieldDescribe> iterator, IFieldDescribe originalField, IObjectDescribe describe) {
        iterator.previous(); // 回到当前位置
        // 只添加一个预览列
        IFieldDescribe previewField = createImageField(
                originalField.getApiName() + IMAGE_FLAG,
                originalField.getLabel(),
                describe.getApiName());
        iterator.add(previewField);
        iterator.next(); // 跳过原字段，保持链接字段
    }

    /**
     * 替换为多个嵌入列（仅嵌入模式）
     */
    private void replaceWithEmbeddedColumns(ListIterator<IFieldDescribe> iterator, IFieldDescribe originalField, IObjectDescribe describe) {
        iterator.remove(); // 移除原字段
        IFieldDescribe imageField = createImageField(
                originalField.getApiName() + IMAGE_FLAG,
                originalField.getLabel(),
                describe.getApiName());
        iterator.add(imageField);
    }

    /**
     * 收集图片字段的最大图片数量
     */
    private Map<String, Integer> collectImageFieldMaxCounts(List<IFieldDescribe> fields, List<String> exportFields) {
        Map<String, Integer> maxCounts = Maps.newHashMap();

        // 这里需要预先查询数据来确定每个图片字段的最大图片数量
        // 为了性能考虑，可以设置一个默认的最大值，或者通过配置获取
        for (String fieldApiName : exportFields) {
            IFieldDescribe field = fields.stream()
                    .filter(f -> fieldApiName.equals(f.getApiName()) && IFieldType.IMAGE.equals(f.getType()))
                    .findFirst()
                    .orElse(null);

            if (field != null) {
                // 可以通过字段配置或者预查询来确定最大图片数量
                int maxCount = getMaxImageCountForField(field);
                maxCounts.put(fieldApiName, maxCount);
            }
        }

        return maxCounts;
    }

    /**
     * 获取字段的最大图片数量
     */
    private int getMaxImageCountForField(IFieldDescribe field) {
        // 可以从字段配置中获取，或者设置默认值
        // 默认最大3张图片
        return getDefaultMaxImagesPerField();
    }

    /**
     * 获取默认的每个字段最大图片数量
     */
    private int getDefaultMaxImagesPerField() {
        return 3; // 默认值
    }

    /**
     * 创建嵌入图片字段描述
     */
    private IFieldDescribe createImageField(String apiName, String label, String describeApiName) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(IFieldDescribe.API_NAME, apiName);
        map.put(IFieldDescribe.LABEL, label);
        map.put(IFieldDescribe.TYPE, IFieldType.IMAGE);
        map.put(IFieldDescribe.IS_REQUIRED, false);
        map.put(IFieldDescribe.DESCRIBE_API_NAME, describeApiName);
        return FieldDescribeFactory.newInstance(map);
    }


    /**
     * 处理嵌入式图片字段的数据转换
     * 嵌入式图片字段需要从原始字段获取数据，因为其字段名包含__image__标识符
     */
    private void batchConvertEmbeddedImageField(List<IObjectData> objectDataList, IFieldDescribe fieldDescribe, User user) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        // 提取原始字段名（移除__image__标识符）
        String originalFieldName = FileExtUtil.extractImageOriginalFieldName(fieldDescribe.getApiName());
        for (IObjectData objectData : objectDataList) {
            // 从原始字段获取图片数据
            Object originalValue = objectData.get(originalFieldName);
            objectData.set(fieldDescribe.getApiName(), originalValue);
        }
    }

    @Data
    public static class Arg {

        @JSONField(name = "M1")
        String object_describe_api_name;

        @JSONField(name = "M2")
        List<String> dataIdList;

        @JSONField(name = "M3")
        String recordType_apiName;

        @JSONField(name = "M4")
        String search_template_id;

        @JSONField(name = "M5")
        String search_query_info;

        @JSONField(name = "M6")
        String token;

        String jobId;

        @JSONField(name = "M7")
        List<String> other_name_list;

        @JSONField(name = "M8")
        boolean include_id;

        @JSONField(name = "M9")
        @JsonProperty("ignore_scene_filter")
        @SerializedName("ignore_scene_filter")
        boolean isIgnoreSceneFilter;

        @JSONField(name = "M11")
        boolean according_to_list = false;

        @JSONField(name = "M12")
        @JsonProperty("search_template_api_name")
        @SerializedName("search_template_api_name")
        String searchTemplateApiName;

        @JSONField(name = "M13")
        @JsonProperty("ignore_scene_record_type")
        @SerializedName("ignore_scene_record_type")
        boolean isIgnoreSceneRecordType;

        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        String searchTemplateType;

        @JsonProperty("no_export_relevant_team")
        @SerializedName("no_export_relevant_team")
        boolean isNoExportRelevantTeam;

        @JsonProperty("detail_arg")
        @SerializedName("detail_arg")
        DetailArg detailArg;

        @JsonProperty("according_field_list")
        @SerializedName("according_field_list")
        private List<String> accordingFieldList;

        @JsonProperty("what_api_name")
        private String whatApiName;

        @JsonProperty("file_fields")
        @SerializedName("file_fields")
        List<String> fileFields;

        public boolean exportRelevantTeam() {
            return !Boolean.TRUE.equals(isNoExportRelevantTeam);
        }
    }

    @Data
    public static class DetailArg {

        @JsonProperty("detail_object_api_names")
        @SerializedName("detail_object_api_names")
        List<String> detailObjectApiNames;

        @JsonProperty("include_id")
        @SerializedName("include_id")
        boolean includeId;

        @JsonProperty("detail_info")
        @SerializedName("detail_info")
        List<DetailInfo> detailInfo;
    }


    @Data
    public static class DetailInfo {
        String apiName;
        @JsonProperty("file_fields")
        @SerializedName("file_fields")
        List<String> fileFields;
        @JsonProperty("fields")
        @SerializedName("fields")
        List<String> fields;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {

        @JSONField(name = "M1")
        String ext;

        @JSONField(name = "M2")
        String file_name;

        @JSONField(name = "M3")
        String path;

        @JSONField(name = "M4")
        String token;

        long size;

        @JSONField(name = "M5")
        int total_count;

        int currentCount;

        String export_type;

        Long fileExpiredTime;
    }

    @Data
    public static class ExportFile {
        @JSONField(name = "ext")
        String extension;
        @JSONField(name = "path")
        String path;
        @JSONField(name = "filename")
        String fileName;
        @JSONField(name = "size")
        Long size;
    }
}
