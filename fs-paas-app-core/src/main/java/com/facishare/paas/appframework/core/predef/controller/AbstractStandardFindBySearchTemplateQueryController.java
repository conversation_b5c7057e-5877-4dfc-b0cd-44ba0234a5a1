package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.LIST_SELECT_PARTLY_GRAY_EI;

/**
 * create by zhaoju on 2021/04/25
 */
public abstract class AbstractStandardFindBySearchTemplateQueryController<A extends StandardFindBySearchTemplateQueryController.Arg> extends PreDefineController<A, StandardFindBySearchTemplateQueryController.Result> {
    protected ObjectDescribeExt objectDescribe;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.FindBySearchTemplateQuery.getFuncPrivilegeCodes();
    }

    @Override
    protected void before(A arg) {
        init();
        stopWatch.lap("init");
        super.before(arg);
    }

    @Override
    protected StandardFindBySearchTemplateQueryController.Result after(A arg, StandardFindBySearchTemplateQueryController.Result result) {
        StandardFindBySearchTemplateQueryController.Result ret = super.after(arg, result);
        ObjectDataDocument.projectField(ret.getDataList(), arg.getFieldProjection());
        return ret;
    }

    protected void init() {
        objectDescribe = findDescribe();
    }

    private ObjectDescribeExt findDescribe() {
        IObjectDescribe describe = serviceFacade.findObjectUseThreadLocalCache(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        return ObjectDescribeExt.of(describe);
    }

    @Override
    protected StandardFindBySearchTemplateQueryController.Result doService(A arg) {
        ISearchTemplateQuery query = getSearchTemplateQuery();
        stopWatch.lap("buildSearchTemplateQuery");

        QueryResult<IObjectData> queryResult = getQueryResult(query);
        stopWatch.lap("queryResult");
        return buildResult(queryResult);
    }

    private StandardFindBySearchTemplateQueryController.Result buildResult(QueryResult<IObjectData> queryResult) {
        return StandardFindBySearchTemplateQueryController.Result.builder()
                .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                .total(queryResult.getTotalNumber())
                .build();
    }

    private QueryResult<IObjectData> getQueryResult(ISearchTemplateQuery query) {
        List<String> projectFields = parseProjectFields();
        QueryResult<IObjectData> queryResult;
        if (CollectionUtils.notEmpty(projectFields) && UdobjGrayConfig.isAllow(LIST_SELECT_PARTLY_GRAY_EI, controllerContext.getTenantId())) {
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(controllerContext.getUser())
                    .isSimple(false)
                    .esSearchSkipRecentUpdateCheck(false)
                    .skipRelevantTeam(RequestUtil.skipRelevantTeam())
                    .projectionFields(projectFields)
                    .build();
            queryResult = serviceFacade.findByQueryWithContext(queryContext, objectDescribe.getApiName(), (SearchTemplateQuery) query);
        } else {
            queryResult = serviceFacade.findBySearchQuery(controllerContext.getUser(), objectDescribe,
                    objectDescribe.getApiName(), (SearchTemplateQuery) query);
        }
        if (isFillFieldInfo()) {
            asyncFillFieldInfo(objectDescribe, queryResult.getData());
            stopWatch.lap("asyncFillFieldInfo");
        }
        return queryResult;
    }

    /**
     * 指定查询字段的apiName
     *
     * @return
     */
    protected abstract List<String> parseProjectFields();

    private ISearchTemplateQuery getSearchTemplateQuery() {
        Query query = defineQuery();
        query = customizeQuery(query);
        ISearchTemplateQuery templateQuery = query.toSearchTemplateQuery();
        if (log.isInfoEnabled()) {
            log.info("defineQuery ending, SearchTemplateQuery:{}", templateQuery.toJsonString());
        }
        validateQuery(templateQuery);
        return templateQuery;
    }

    protected Query customizeQuery(Query query) {
        return query;
    }

    private void validateQuery(ISearchTemplateQuery query) {
        if (!SearchTemplateQueryExt.validateQueryPattern(query)) {
            log.warn("defineQuery fail, ei:{}, objectApiName:{}, SearchTemplateQuery:{}",
                    controllerContext.getTenantId(), controllerContext.getObjectApiName(), query.toJsonString());
        }
    }

    protected Query defineQuery() {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(getSearchTemplateType())
                .templateId(getSearchTemplateId())
                .build();
        Query searchQuery = serviceFacade.findSearchQuery(controllerContext.getUser(), objectDescribe, getSearchQueryInfo(), queryContext);
        searchQuery.setFindExplicitTotalNum(getFindExplicitTotalNum());
        searchQuery.setNeedReturnCountNum(getNeedReturnCountNum());
        searchQuery.setNeedReturnQuote(getNeedReturnQuote());
        return searchQuery;
    }

    private boolean getNeedReturnQuote() {
        return arg.isNeedReturnQuote();
    }

    private boolean getNeedReturnCountNum() {
        return arg.isNeedReturnCountNum();
    }

    private Boolean getFindExplicitTotalNum() {
        return arg.getFindExplicitTotalNum();
    }

    private String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    private String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    private String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    private boolean isFillFieldInfo() {
        return arg.isFillFieldInfo();
    }

    private void asyncFillFieldInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        //查数据不打印计算日志
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //处理引用字段
            fillQuoteFieldValue(describe, synchronizedDataList, null);
            stopWatch.lap("fillQuoteFieldValue");
            // 补充掩码需要在补充引用字段之后执行,异步执行可能会导致引用字段没有掩码的情况
            fillMaskFieldValue(describe, synchronizedDataList);
            stopWatch.lap("fillMaskFieldValue");
        }).submit(() -> {
            //添加lookup字段的主属性__r
            fillRefObjectName(describe, synchronizedDataList, null);
            stopWatch.lap("fillRefObjectName");
        }).submit(() -> {
            fillInfo(describe, synchronizedDataList);
            stopWatch.lap("fillInfo");
        }).submit(() -> {
            fillDimensionFieldValue(describe, synchronizedDataList);
            stopWatch.lap("fillDimensionFieldValue");
        }).submit(() -> {
            fillCountryAreaLabel(describe, synchronizedDataList);
            stopWatch.lap("fillCountryAreaLabel");
        }).submit(() -> {
            fillDataVisibilityRange(describe, synchronizedDataList);
            stopWatch.lap("fillDataVisibilityRange");
        });
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill info of listController, ei:{}, object:{}", controllerContext.getTenantId(), describe.getApiName(), e);
        }
    }

    private void fillDimensionFieldValue(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), objectDescribe, dataList);
    }

    private void fillQuoteFieldValue(IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, List<IObjectData>> refObjectDataMap) {
        infraServiceFacade.fillQuoteFieldValue(controllerContext.getUser(), dataList, objectDescribe, refObjectDataMap, false);
    }

    private void fillRefObjectName(IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, List<IObjectData>> refObjectDataMap) {
        serviceFacade.fillObjectDataWithRefObject(objectDescribe, dataList, controllerContext.getUser(), refObjectDataMap);
    }

    private void fillInfo(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        //拼装组织机构信息
        serviceFacade.fillUserInfo(objectDescribe, dataList, controllerContext.getUser());
        serviceFacade.fillDepartmentInfo(objectDescribe, dataList, controllerContext.getUser());
    }

    private void fillMaskFieldValue(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), dataList, objectDescribe, needRemoveMaskOrigValue());
    }

    private void fillCountryAreaLabel(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillCountryAreaLabel(objectDescribe, dataList, controllerContext.getUser());
    }

    private void fillDataVisibilityRange(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), objectDescribe, dataList);
    }

    private boolean needRemoveMaskOrigValue() {
        return RequestUtil.isCepRequest();
    }

}
