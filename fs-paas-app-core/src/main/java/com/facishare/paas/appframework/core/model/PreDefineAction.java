package com.facishare.paas.appframework.core.model;

import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.IdempotentService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.*;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.domain.*;
import com.facishare.paas.appframework.core.model.handler.*;
import com.facishare.paas.appframework.core.model.plugin.*;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.domain.SimpleDomainPluginDescribe;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.core.status.GlobalStatus;
import com.fxiaoke.transaction.tcc.api.GlobalTransactionHook;
import com.fxiaoke.transaction.tcc.api.context.GlobalTransactionApplicationData;
import com.fxiaoke.transaction.tcc.api.context.GlobalTransactionHookManger;
import com.google.common.base.Charsets;
import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;

import java.util.*;
import java.util.function.Supplier;

/**
 * 预定义操作
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
public abstract class PreDefineAction<A, R> extends AbstractAction<A, R> {

    private static final Set<String> SUPPORT_DOMAIN_PLUGIN_ACTIONS = ImmutableSet.of(StandardAction.Add.name(),
            StandardAction.Edit.name(), StandardAction.IncrementUpdate.name(), StandardAction.BulkInvalid.name(),
            StandardAction.Invalid.name(), StandardAction.FlowCompleted.name(), StandardAction.InsertImportTemplate.name(),
            StandardAction.InsertImportVerify.name(), StandardAction.InsertImportData.name(), StandardAction.UnionInsertImportTemplate.name(),
            StandardAction.UnionInsertImportVerify.name(), StandardAction.UnionInsertImportData.name(), StandardAction.UpdateImportTemplate.name(),
            StandardAction.UpdateImportVerify.name(), StandardAction.UpdateImportData.name(),
            StandardAction.DesignerCreateLayout.name(), StandardAction.DesignerUpdateLayout.name(),
            StandardAction.EnterAccount.name(), StandardAction.CancelEntry.name(), "ConfirmReceipt");

    protected StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());

    protected ServiceFacade serviceFacade;
    protected InfraServiceFacade infraServiceFacade;
    protected IObjectDescribe objectDescribe;   // 主对象描述
    protected List<IObjectData> dataList;   // ?
    private List<String> funcPrivilegeCodes;

    protected List<ActionListener<A, R>> actionListeners = Lists.newArrayList();

    private DomainPluginManager domainPluginManager;
    protected List<SimpleDomainPluginDescribe> pluginDescribes;
    private final Map<String, String> domainPluginContextData = Maps.newHashMap();

    private PluginManager pluginManager;

    private HandlerLogicService handlerLogicService;
    private HandlerManager handlerManager;
    private List<SimpleHandlerDescribe> handlerDescribes;
    private HandlerContext handlerContext;
    private final Map<String, String> handlerContextData = Maps.newHashMap();
    private Map<String, HandlerFunctions.ExecuteFunction> handlerExecuteFunctions;
    private R result;

    protected abstract List<String> getFuncPrivilegeCodes();

    protected abstract List<String> getDataPrivilegeIds(A arg);

    protected boolean skipDomainPlugin(String method) {
        return false;
    }

    protected <T extends Plugin.Arg> T buildPluginArg(String method) {
        return null;
    }

    protected APLExportPlugin.Arg buildAPLExportPluginArg(String methodName) {
        return null;
    }

    protected void processPluginResult(String method, Plugin.Arg pluginArg, Plugin.Result pluginResult) {

    }

    protected void processAPLExportPluginResult(String methodName, APLExportPlugin.Arg pluginArg, APLExportPluginImpl.Result pluginResult) {

    }

    @Override
    protected final void pluginInit(A arg) {
        runPlugin(ActionPlugin.INIT, true, (plugin, pluginArg) ->
                ((ActionPlugin) plugin).init(PluginContext.fromActionContext(actionContext), pluginArg));
    }

    @Override
    protected final void pluginBefore(A arg) {
        runPlugin(ActionPlugin.BEFORE, true, (plugin, pluginArg) ->
                ((ActionPlugin) plugin).before(PluginContext.fromActionContext(actionContext), pluginArg));
    }

    @Override
    protected final void pluginAfter(A arg, R result) {
        runPlugin(ActionPlugin.AFTER, false, (plugin, pluginArg) ->
                ((ActionPlugin) plugin).after(PluginContext.fromActionContext(actionContext), pluginArg));
    }

    @Override
    protected final void pluginFinallyDo(A arg, R result) {
        runPlugin(ActionPlugin.FINALLY_DO, false, (plugin, pluginArg) ->
                ((ActionPlugin) plugin).finallyDo(PluginContext.fromActionContext(actionContext), pluginArg));
    }

    protected final void runPlugin(String method, boolean needProcessResult, PluginFunction.RunFunction runFunction) {
        runPlugin(actionContext.getObjectApiName(), method, needProcessResult, runFunction);
    }

    protected final void runPlugin(String describeApiName, String method, boolean needProcessResult, PluginFunction.RunFunction runFunction) {
        PluginRunner.builder()
                .pluginManager(pluginManager)
                .tenantId(actionContext.getTenantId())
                .needProcessResult(needProcessResult)
                .requestType(RequestType.Action)
                .objectApiName(describeApiName)
                .requestCode(actionContext.getActionCode())
                .method(method)
                .buildArgFunction((plugin, methodName) -> {
                    if (plugin instanceof APLExportPlugin) {
                        return buildAPLExportPluginArg(methodName);
                    }
                    return buildPluginArg(methodName);
                })
                .runFunction(runFunction)
                .processResultFunction((plugin, methodName, pluginArg, pluginResult) -> {
                    if (Objects.isNull(pluginResult)) {
                        return;
                    }
                    if (plugin instanceof APLExportPlugin) {
                        processAPLExportPluginResult(methodName, ((APLExportPlugin.Arg) pluginArg), ((APLExportPlugin.Result) pluginResult));
                        return;
                    }
                    processPluginResult(methodName, pluginArg, pluginResult);
                })
                .build()
                .run();
        stopWatch.lap("runPlugin_" + method);
    }

    protected DomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return null;
    }

    protected void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
    }

    private boolean supportDomainPlugin() {
        return SUPPORT_DOMAIN_PLUGIN_ACTIONS.contains(actionContext.getActionCode());
    }

    protected List<String> getRecordTypes() {
        return null;
    }

    private void domainPluginInit() {
        if (!supportDomainPlugin()) {
            return;
        }
        //查询插件信息
        pluginDescribes = findDomainPluginDescribes();
        stopWatch.lap("findDomainPluginDescribes");
    }

    @Override
    protected final void domainPluginBefore(A arg) {
        if (!supportDomainPlugin()) {
            return;
        }
        if (skipDomainPlugin(ActionDomainPlugin.BEFORE)) {
            return;
        }
        runDomainPlugin(ActionDomainPlugin.BEFORE, true, (domainPlugin, pluginArg) ->
                ((ActionDomainPlugin) domainPlugin).before(actionContext, pluginArg));
    }

    @Override
    protected final void domainPluginPreAct(A arg) {
        if (!supportDomainPlugin()) {
            return;
        }
        if (skipDomainPlugin(ActionDomainPlugin.PRE_ACT)) {
            return;
        }
        runDomainPlugin(ActionDomainPlugin.PRE_ACT, true, (domainPlugin, pluginArg) ->
                ((ActionDomainPlugin) domainPlugin).preAct(actionContext, pluginArg));
    }

    @Override
    protected final void domainPluginPostAct(A arg, R result) {
        if (!supportDomainPlugin()) {
            return;
        }
        if (skipDomainPlugin(ActionDomainPlugin.POST_ACT)) {
            return;
        }
        runDomainPlugin(ActionDomainPlugin.POST_ACT, false, (domainPlugin, pluginArg) ->
                ((ActionDomainPlugin) domainPlugin).postAct(actionContext, pluginArg));
    }

    @Override
    protected final void domainPluginAfter(A arg, R result) {
        if (!supportDomainPlugin()) {
            return;
        }
        if (skipDomainPlugin(ActionDomainPlugin.AFTER)) {
            return;
        }
        runDomainPlugin(ActionDomainPlugin.AFTER, false, (domainPlugin, pluginArg) ->
                ((ActionDomainPlugin) domainPlugin).after(actionContext, pluginArg));
    }

    @Override
    protected final void domainPluginFinallyDo(A arg, R result) {
        if (!supportDomainPlugin()) {
            return;
        }
        if (skipDomainPlugin(ActionDomainPlugin.FINALLY_DO)) {
            return;
        }
        runDomainPlugin(ActionDomainPlugin.FINALLY_DO, false, (domainPlugin, pluginArg) ->
                ((ActionDomainPlugin) domainPlugin).finallyDo(actionContext, pluginArg));
    }

    protected final void runDomainPlugin(String methodName, boolean needProcessResult, DomainPluginFunction.RunFunction runFunction) {
        DomainPluginRunner.builder()
                .domainPluginManager(domainPluginManager)
                .pluginDescribes(pluginDescribes)
                .method(methodName)
                .requestType(RequestType.Action)
                .requestCode(actionContext.getActionCode())
                .skippedValidatorList(getSkippedValidatorList())
                .buildArgFunction((method, recordTypeList) -> {
                    DomainPlugin.Arg pluginArg = buildDomainPluginArg(method, recordTypeList);
                    pluginArg.setObjectApiName(actionContext.getObjectApiName());
                    pluginArg.setContextData(this.domainPluginContextData);
                    pluginArg.setDoActComplete(this.isDoActComplete());
                    pluginArg.setProcessComplete(this.isProcessComplete());
                    pluginArg.setActByHandler(supportHandler());
                    return pluginArg;
                })
                .runFunction(runFunction)
                .processResultFunction((method, pluginArg, pluginResult) -> {
                    if (Objects.isNull(pluginResult)) {
                        return;
                    }
                    if (Objects.nonNull(pluginResult.getValidationResult())) {
                        ValidatorType validatorType = ValidatorType.getDomainPluginValidatorTypeByMethod(method);
                        throwExceptionWithValidationResult(pluginResult.getValidationResult(), validatorType,
                                pluginArg.getPluginDescribe().getApiName());
                    }
                    if (CollectionUtils.notEmpty(pluginResult.getContextData())) {
                        this.domainPluginContextData.putAll(pluginResult.getContextData());
                    }
                    if (needProcessResult) {
                        processDomainPluginResult(method, pluginArg, pluginResult);
                    }
                })
                .build()
                .run();
        stopWatch.lap("runDomainPlugin_" + methodName);
    }

    private List<SimpleDomainPluginDescribe> findDomainPluginDescribes() {
        List<String> recordTypeList = getRecordTypes();
        return infraServiceFacade.findSimplePluginByActionCode(actionContext.getTenantId(), actionContext.getObjectApiName(),
                actionContext.getActionCode(), recordTypeList, RequestType.Action);
    }

    private void throwExceptionWithValidationResult(ValidationResult validationResult, ValidatorType validatorType, String validatorKey) {
        if (!supportValidationResult()) {
            log.warn("validationResult not supported cause no param in arg,actionCode{},validatorKey:{},validatorType:{}",
                    actionContext.getActionCode(), validatorKey, validatorType);
            throw new ValidateException(validationResult.getMessage());
        }
        if (Objects.isNull(validatorType)) {
            log.warn("validationResult not supported cause validatorType is null,actionCode{},validatorKey:{}",
                    actionContext.getActionCode(), validatorKey);
            throw new ValidateException(validationResult.getMessage());
        }
        ValidatorInfo validatorInfo = ValidatorInfo.of(validatorType, validatorKey, validationResult);
        List<ValidatorInfo> skippedValidatorList = Lists.newArrayList(CollectionUtils.nullToEmpty(getSkippedValidatorList()));
        skippedValidatorList.add(validatorInfo);
        ValidationResultDocument validationResultDocument = ValidationResultDocument.builder()
                .block(validationResult.isBlock())
                .message(validationResult.getMessage())
                .skippedValidatorList(skippedValidatorList)
                .build();
        R actionResult = buildValidationResult(validationResultDocument);
        if (Objects.isNull(actionResult)) {
            log.warn("validationResult not supported cause no implementation of buildValidationResult,actionCode{},validatorKey:{},validatorType:{}",
                    actionContext.getActionCode(), validatorKey, validatorType);
            throw new ValidateException(validationResult.getMessage());
        }
        throw new AcceptableValidateException(actionResult);
    }

    protected R buildValidationResult(ValidationResultDocument validationResultDocument) {
        return null;
    }

    protected boolean supportValidationResult() {
        return false;
    }

    protected List<ValidatorInfo> getSkippedValidatorList() {
        return null;
    }

    protected String getIndustryCode(A arg) {
        return null;
    }

    protected Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        return null;
    }

    protected void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<R> handlerResult) {

    }

    @Override
    protected final void handlerInit() {
        initHandlers();
        this.funcPrivilegeCodes = getFuncPrivilegeCodes();
    }

    private void initHandlers() {
        try {
            handlerDescribes = handlerLogicService.findHandlerDescribeByInterfaceCode(actionContext.getTenantId(),
                    actionContext.getObjectApiName(), getInterfaceCode());
            stopWatch.lap("findHandlerDescribes");
            //如果查不到handler，属于异常情况，抛异常出去
            if (CollectionUtils.empty(handlerDescribes)) {
                throw new InitHandlerException("handlerDescribes is empty");
            }
            //标准Action没有平台预置的handler，属于异常情况，抛异常出去
            if (isStandardAction() && handlerDescribes.stream().noneMatch(x -> HandlerDefinition.PROVIDER_TYPE_SYSTEM.equals(x.getProviderType()))) {
                throw new InitHandlerException("system handlerDescribes is empty");
            }
            this.handlerContext = buildHandlerContext();
            this.handlerExecuteFunctions = getHandlerExecuteFunctions();
        } catch (InitHandlerException e) {
            throw e;
        } catch (Throwable e) {
            throw new InitHandlerException(e);
        }
    }

    private boolean isStandardAction() {
        return Objects.nonNull(StandardAction.valueOfAction(actionContext.getActionCode()));
    }

    private HandlerContext buildHandlerContext() {
        return HandlerContext.builder()
                .requestContext(actionContext.getRequestContext())
                .interfaceCode(getInterfaceCode())
                .build();
    }

    private Map<String, HandlerFunctions.ExecuteFunction> getHandlerExecuteFunctions() {
        Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap = Maps.newHashMap();
        executeFunctionMap.put(VirtualActionHandlers.PROCESS_ARG, () -> processArg(arg));
        executeFunctionMap.put(VirtualActionHandlers.PLUGIN_INIT, () -> pluginInit(arg));
        executeFunctionMap.put(VirtualActionHandlers.PLUGIN_BEFORE, () -> pluginBefore(arg));
        executeFunctionMap.put(VirtualActionHandlers.DOMAIN_PLUGIN_BEFORE, () -> domainPluginBefore(arg));
        executeFunctionMap.put(VirtualActionHandlers.PRE_ACTION, () -> preAction(arg));
        executeFunctionMap.put(VirtualActionHandlers.DOMAIN_PLUGIN_PRE_ACT, () -> domainPluginPreAct(arg));
        executeFunctionMap.put(VirtualActionHandlers.DOMAIN_PLUGIN_POST_ACT, () -> domainPluginPostAct(arg, result));
        executeFunctionMap.put(VirtualActionHandlers.PLUGIN_AFTER, () -> pluginAfter(arg, result));
        executeFunctionMap.put(VirtualActionHandlers.DOMAIN_PLUGIN_AFTER, () -> domainPluginAfter(arg, result));
        executeFunctionMap.put(VirtualActionHandlers.POST_ACTION, () -> postAction(arg, result));
        executeFunctionMap.put(VirtualActionHandlers.PROCESS_RESULT, () -> processResult(result));
        executeFunctionMap.put(VirtualActionHandlers.PLUGIN_FINALLY_DO, () -> pluginFinallyDo(arg, result));
        executeFunctionMap.put(VirtualActionHandlers.DOMAIN_PLUGIN_FINALLY_DO, () -> domainPluginFinallyDo(arg, result));
        return ImmutableMap.copyOf(executeFunctionMap);
    }

    @Override
    protected final void handlerBefore() {
        executeHandler(HandlerType.BEFORE.getCode());
    }

    @Override
    protected final R handlerDoAct() {
        executeHandler(HandlerType.ACT.getCode());
        return this.result;
    }

    @Override
    protected final void handlerAfter() {
        actionListeners.forEach(x -> x.after(actionContext, arg, result));
        stopWatch.lap("afterActionListener");

        executeHandler(HandlerType.AFTER.getCode());
    }

    @Override
    protected final void handlerFinallyDo() {
        executeHandler(HandlerType.FINALLY.getCode());
    }

    @Override
    protected final R callWithGlobalTransaction(Supplier<R> supplier) {
        if (!HandlerGrayConfig.supportGlobalTransaction(actionContext.getTenantId(), actionContext.getActionCode(), actionContext.getObjectApiName())) {
            return supplier.get();
        }
        return infraServiceFacade.callWithGlobalTransaction(actionContext.getObjectApiName() + "|" + actionContext.getActionCode(),
                300, actionContext.getTenantId(), Collections.emptyList(), Lists.newArrayList(AcceptableValidateNoRollbackException.class), () -> {
                    // 注册事务hook
                    registerGlobalTransactionHook();
                    actionContext.setAttribute(RequestContext.Attributes.PRE_MATCH_APPROVAL,
                            HandlerGrayConfig.supportPreMatchApproval(actionContext.getTenantId(), actionContext.getActionCode(), actionContext.getObjectApiName()));
                    return supplier.get();
                });
    }

    private void executeHandler(String handlerType) {
        HandlerExecutor.builder()
                .handlerManager(handlerManager)
                .handlerContext(handlerContext)
                .handlerDescribeList(handlerDescribes)
                .handlerType(handlerType)
                .interfaceCode(getInterfaceCode())
                .objectApiName(actionContext.getObjectApiName())
                .industryCode(getIndustryCode(arg))
                .buildArgFunction(handlerDescribe -> {
                    Handler.Arg handlerArg = getHandlerArg(handlerDescribe);
                    return handlerArg;
                })
                .processResultFunction((handlerContext, handlerArg, handlerResult) -> {
                    if (Objects.isNull(handlerResult)) {
                        return;
                    }
                    preProcessHandlerResult(handlerResult);
                    processHandlerResult(handlerContext, handlerArg, handlerResult);
                })
                .executeFunctions(this.handlerExecuteFunctions)
                .build()
                .execute();
        stopWatch.lap("executeHandler_" + handlerType);
    }

    protected final Handler.Arg getHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        Handler.Arg handlerArg = buildHandlerArg(handlerDescribe);
        processHandlerArg(handlerArg, handlerDescribe);
        return handlerArg;
    }

    protected String getInterfaceCode() {
        return actionContext.getActionCode();
    }

    protected Object getInterfaceArg() {
        return this.arg;
    }

    private void processHandlerArg(Handler.Arg handlerArg, SimpleHandlerDescribe handlerDescribe) {
        handlerArg.setContextData(this.handlerContextData);
        handlerArg.setHandlerDescribe(handlerDescribe);
        handlerArg.setObjectApiName(this.actionContext.getObjectApiName());
        handlerArg.setObjectDescribe(this.objectDescribe);
        handlerArg.setInterfaceArg(getInterfaceArg());
        handlerArg.setFunctionPrivilegeCodes(this.funcPrivilegeCodes);
        handlerArg.setDoActComplete(isDoActComplete());
        handlerArg.setProcessComplete(isProcessComplete());
        if (handlerArg instanceof ActionHandler.Arg) {
            ActionHandler.Arg actionHandlerArg = (ActionHandler.Arg) handlerArg;
            actionHandlerArg.setDataListForDataPrivilegeCheck(ObjectDataDocument.ofList(this.dataList));
            ApprovalFlowTriggerType approvalFlowTriggerType = CacheContext.getContext().getCache(ApprovalFlowServiceFacade.APPROVAL_TRIGGER_TYPE);
            Map<String, ApprovalFlowStartResult> flowStartResultMap = CacheContext.getContext().getCache(ApprovalFlowServiceFacade.APPROVAL_TRIGGER_RESULT);
            Boolean triggerApprovalFlowAsync = CacheContext.getContext().getCache(ApprovalFlowServiceFacade.APPROVAL_TRIGGER_ASYNC);
            actionHandlerArg.setApprovalFlowTriggerType(Optional.ofNullable(approvalFlowTriggerType)
                    .map(ApprovalFlowTriggerType::getId).orElse(null));
            actionHandlerArg.setTriggerApprovalFlowSuccess(CollectionUtils.notEmpty(flowStartResultMap)
                    && flowStartResultMap.containsValue(ApprovalFlowStartResult.SUCCESS));
            if (Objects.nonNull(triggerApprovalFlowAsync)) {
                actionHandlerArg.setTriggerApprovalFlowAsync(triggerApprovalFlowAsync);
            }
        }
    }

    protected R getInterfaceResult(Handler.Result handlerResult) {
        return (R) handlerResult.getInterfaceResult();
    }

    private void preProcessHandlerResult(Handler.Result handlerResult) {
        if (CollectionUtils.notEmpty(handlerResult.getContextData())) {
            this.handlerContextData.putAll(handlerResult.getContextData());
        }
        if (Objects.nonNull(handlerResult.getInterfaceResult())) {
            this.result = getInterfaceResult(handlerResult);
        }
        if (Objects.nonNull(handlerResult.getSkipFunctionPrivilegeCheck())) {
            this.handlerContext.setAttribute(HandlerAttributes.SKIP_FUNCTION_PRIVILEGE_CHECK, handlerResult.getSkipFunctionPrivilegeCheck());
        }
        if (handlerResult instanceof ActionHandler) {
            ActionHandler.Result actionHandlerResult = (ActionHandler.Result) handlerResult;
            if (Objects.nonNull(actionHandlerResult.getSkipDataPrivilegeCheck())) {
                this.handlerContext.setAttribute(HandlerAttributes.SKIP_DATA_PRIVILEGE_CHECK, actionHandlerResult.getSkipDataPrivilegeCheck());
            }
            if (Objects.nonNull(actionHandlerResult.getSkipApprovalFlow())) {
                this.handlerContext.setAttribute(HandlerAttributes.SKIP_APPROVAL_FLOW, actionHandlerResult.getSkipApprovalFlow());
            }
            if (Objects.nonNull(actionHandlerResult.getSkipRecordLog())) {
                this.handlerContext.setAttribute(HandlerAttributes.SKIP_RECORD_LOG, actionHandlerResult.getSkipRecordLog());
            }
        }
    }

    protected boolean skipBaseValidate() {
        return Boolean.TRUE.equals(actionContext.getAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE));
    }

    protected void doFunPrivilegeCheck() {
        if (skipBaseValidate()) {
            return;
        }
        serviceFacade.doFunPrivilegeCheck(actionContext.getUser(), actionContext.getObjectApiName(), getFuncPrivilegeCodes());
    }

    protected void doDataPrivilegeCheck() {
        if (skipBaseValidate()) {
            return;
        }
        List<String> funcPrivilegeCodes = getFuncPrivilegeCodes();
        if (CollectionUtils.empty(funcPrivilegeCodes)) {
            return;
        }
        serviceFacade.doDataPrivilegeCheck(actionContext.getUser(), dataList, objectDescribe,
                funcPrivilegeCodes.get(0));
    }

    public List<Class<? extends ActionListener<A, R>>> getActionListenerClassList() {
        return Lists.newArrayList();
    }

    protected boolean needInvalidData() {
        return false;
    }

    protected boolean skipQueryRelateTeam() {
        return false;
    }

    protected void initDataList() {
        List<String> ids = getDataPrivilegeIds(arg);
        if (CollectionUtils.empty(ids)) {
            dataList = Lists.newArrayList();
            return;
        }
        //不查相关团队
        if (skipQueryRelateTeam()) {
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(actionContext.getUser())
                    .skipRelevantTeam(true)
                    .includeInvalid(needInvalidData())
                    .searchRichTextExtra(needSearchRichTextExtra())
                    .keepAllMultiLangValue(keepAllMultiLangValue())
                    .isSimple(needSimple())
                    .build();
            dataList = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, ids, actionContext.getObjectApiName());
        } else {
            if (needInvalidData()) {
                dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                        ids,
                        actionContext.getObjectApiName());
            } else {
                IActionContext context = ActionContextExt.of(actionContext.getUser()).getContext();
                dataList = serviceFacade.findObjectDataByIdsExcludeInvalid(context,
                        ids,
                        actionContext.getObjectApiName());
            }
        }
        if (CollectionUtils.empty(dataList)) {
            throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
        }
    }

    protected boolean needSimple() {
        return false;
    }

    protected boolean needSearchRichTextExtra() {
        return true;
    }

    protected boolean keepAllMultiLangValue() {
        return false;
    }

    private void doInit() {
        findDescribe();
        stopWatch.lap("findObject");

        if (!objectDescribe.isActive()) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_DESCRIBE_DIABLED));
        }

        //初始化数据
        init();
        stopWatch.lap("init");

        //查询业务插件实例
        domainPluginInit();
    }

    private void handlerDoInit() {
        doInit();

        actionListeners.forEach(x -> x.before(actionContext, arg));
        stopWatch.lap("beforeActionListener");
    }

    @Override
    protected void before(A arg) {
        //校验主对象的权限
        doFunPrivilegeCheck();
        stopWatch.lap("doFunPrivilegeCheck");

        doInit();

        //数据权限校验
        doDataPrivilegeCheck();
        stopWatch.lap("doDataPrivilegeCheck");

        actionListeners.forEach(x -> x.before(actionContext, arg));
        stopWatch.lap("beforeActionListener");
    }

    protected void init() {
        initDataList();
    }

    @Override
    public final R act(A arg) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        Idempotent idempotent = getIdempotent();
        if (idempotent != null) {
            result = doIdempotentAct(arg, idempotent);
            stopWatch.lap("doIdempotentAct");
        } else {
            result = super.act(arg);
            stopWatch.lap("act");
        }
        return result;
    }

    @Override
    protected void registerGlobalTransactionHook() {
        GlobalTransactionHookManger.registerHook(new GlobalTransactionHook() {
            @Override
            public void beforeConfirm(GlobalTransactionApplicationData globalTransactionApplicationData) {
                beforeGlobalTransactionCommit(globalTransactionApplicationData);
            }

            @Override
            public void beforeCancel(GlobalTransactionApplicationData globalTransactionApplicationData) {
                beforeGlobalTransactionRollback(globalTransactionApplicationData);
            }

            @Override
            public void afterConfirm() {
                afterGlobalTransactionCommit();
            }

            @Override
            public void afterCompletion(GlobalStatus globalStatus) {
                afterGlobalTransactionCompletion(globalStatus);
            }
        });
    }

    /**
     * 全局事务提交前执行
     *
     * @param globalTransactionApplicationData 全局事务上下文
     */
    protected void beforeGlobalTransactionCommit(GlobalTransactionApplicationData globalTransactionApplicationData) {
        beforeGlobalTransactionCompletion(globalTransactionApplicationData);
    }

    /**
     * 全局事务回滚前执行
     *
     * @param globalTransactionApplicationData 全局事务上下文
     */
    protected void beforeGlobalTransactionRollback(GlobalTransactionApplicationData globalTransactionApplicationData) {
        beforeGlobalTransactionCompletion(globalTransactionApplicationData);
    }

    /**
     * 全局事务提交后执行
     */
    protected void afterGlobalTransactionCommit() {
    }

    /**
     * 全局事务完成后执行
     *
     * @param globalStatus 事务状态
     */
    protected void afterGlobalTransactionCompletion(GlobalStatus globalStatus) {
    }

    /**
     * 分布式事务完成前执行
     * 记录 handlerDoAct 执行完成
     *
     * @param globalTransactionApplicationData
     */
    protected void beforeGlobalTransactionCompletion(GlobalTransactionApplicationData globalTransactionApplicationData) {
        globalTransactionApplicationData.setAttribute(GlobalTransactionConstant.DO_ACT_COMPLETE, isDoActComplete());
    }

    @Override
    protected final void processResult(R result) {
        customProcessResult(result);
    }

    protected void customProcessResult(R result) {

    }

    protected void customProcessArg(A arg) {

    }

    @Override
    protected final void processArg(A arg) {
        if (Objects.nonNull(actionContext) && Objects.nonNull(actionContext.getRequestContext())) {
            actionContext.getRequestContext().setAttribute(RequestContext.Attributes.ACTION_CODE, actionContext.getActionCode());
        }
        customProcessArg(arg);
        if (supportHandler()) {
            handlerDoInit();
        }
    }

    private R doIdempotentAct(A arg, Idempotent idempotent) {
        IdempotentService idempotentService = serviceFacade.getBean(IdempotentService.class);

        if (idempotentService != null) {
            String key = getIdempotentKey();
            return idempotentService.doWithIdempotent(key, () -> super.act(arg), idempotent, null,
                    new MetaDataBusinessException(I18N.text(I18NKey.DO_NOT_SUBMIT_REPEATEDLY)));
        }
        return super.act(arg);
    }

    private String getIdempotentKey() {
        String postId = actionContext.getPostId();
        if (Strings.isNullOrEmpty(postId)) {
            return null;
        }
        return Hashing.sha256().newHasher()
                .putString(postId, Charsets.UTF_8)
                .putString(actionContext.getTenantId(), Charsets.UTF_8)
                .putString(actionContext.getObjectApiName() + "/" + actionContext.getActionCode(), Charsets.UTF_8)
                .hash().toString();
    }

    private Idempotent getIdempotent() {
        Idempotent idempotent = this.getClass().getAnnotation(Idempotent.class);
        if (Objects.isNull(idempotent)) {
            return null;
        }
        //开启了幂等灰度的Action，则只对灰度名单内的企业生效
        if (enableIdempotentGrayRule() && !isInIdempotentGrayList()) {
            return null;
        }
        return idempotent;
    }

    protected boolean enableIdempotentGrayRule() {
        return false;
    }

    private boolean isInIdempotentGrayList() {
        String ruleKey = String.format(UdobjGrayConfigKey.ACTION_IDEMPOTENT_GRAY_EI, actionContext.getActionCode());
        return UdobjGrayConfig.isAllow(ruleKey, actionContext.getTenantId());
    }


    @Override
    protected R after(A arg, R result) {

        actionListeners.forEach(x -> x.after(actionContext, arg, result));
        // 添加按钮
        stopWatch.lap("afterActionListener");
        return result;
    }

    @Override
    protected void finallyDo() {
        stopWatch.logSlow(500);
    }

    public final void setServiceFacade(ServiceFacade serviceFacade) {
        this.serviceFacade = serviceFacade;
    }

    public final void setInfraServiceFacade(InfraServiceFacade infraServiceFacade) {
        this.infraServiceFacade = infraServiceFacade;
        SpringBeanHolder springBeanHolder = infraServiceFacade.getSpringBeanHolder();
        this.handlerManager = springBeanHolder.getHandlerManager();
        this.handlerLogicService = springBeanHolder.getHandlerLogicService();
        this.pluginManager = springBeanHolder.getPluginManager();
        this.domainPluginManager = springBeanHolder.getDomainPluginManager();
    }

    @Deprecated
    public void setActionListener(ActionListener<A, R> actionListener) {
        addActionListener(actionListener);
    }

    public void addActionListener(ActionListener<A, R> actionListener) {
        this.actionListeners.add(actionListener);
    }

    protected void findDescribe() {
        if (Objects.nonNull(objectDescribe)) {
            return;
        }
        //灰度企业不拷贝对象描述
        if (AppFrameworkConfig.notCopyDescribeInAction(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            objectDescribe = serviceFacade.findObjectWithoutCopy(actionContext.getTenantId(), actionContext.getObjectApiName());
        } else {
            objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
        }
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("argClass", argClass).add("arg", arg).toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        PreDefineAction<?, ?> action = (PreDefineAction<?, ?>) o;
        return Objects.equals(argClass, action.argClass) && Objects.equals(arg, action.arg);
    }

    @Override
    public int hashCode() {
        return Objects.hash(argClass, arg);
    }


}
