package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.button.action.ActionExecutorContext;
import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.action.ValidateFuncAction;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.service.dto.button.FunctionAction;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhaooju on 2023/4/3
 */
public class StandardChangeAction extends AbstractStandardAction<StandardChangeAction.Arg, StandardChangeAction.Result> {
    private List<MtChangeOrderRule> changeOrderRules;
    protected IObjectData objectData;

    private ValidateFuncAction validateFuncAction;

    @Override
    protected void init() {
        super.init();
        initChangeOrderRule();
        objectData = dataList.get(0);
        validateFuncAction = serviceFacade.getBean(ValidateFuncAction.class);
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateByChangeStatus();
        validateByLifeStatus();
        validateChangeOrderRuleExistence(arg);
    }

    private void validateChangeOrderRuleExistence(Arg arg) {
        // 验证指定的变更规则是否存在
        String changeOrderRuleApiName = arg.getChangeOrderRuleApiName();
        if (Strings.isNullOrEmpty(changeOrderRuleApiName)) {
            return;
        }
        boolean ruleExists = false;
        for (MtChangeOrderRule rule : changeOrderRules) {
            if (Objects.equals(rule.getApiName(), changeOrderRuleApiName)) {
                ruleExists = true;
                break;
            }
        }
        if (!ruleExists) {
            throw new ValidateException(I18NExt.text(I18NKey.CHANGE_ORDER_RULE_NOT_FOUND, changeOrderRuleApiName));
        }
    }

    private void validateByLifeStatus() {
        if (!ObjectDataExt.of(objectData).isNormal()) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_OPERATION));
        }
    }

    private void validateByChangeStatus() {
        infraServiceFacade.validateByChangeStatus(actionContext.getUser(), objectDescribe, objectData);
    }

    private void initChangeOrderRule() {
        changeOrderRules = infraServiceFacade.findByDescribeApiName(actionContext.getUser(), actionContext.getObjectApiName());
        if (CollectionUtils.empty(changeOrderRules)) {
            throw new ValidateException(I18NExt.text(I18NKey.NO_AVAILABLE_CHANGE_RULES));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        for (MtChangeOrderRule changeOrderRule : changeOrderRules) {
            validateByChangeRule(changeOrderRule);
        }
        return Result.buildSuccess();
    }

    private void validateByChangeRule(MtChangeOrderRule changeOrderRule) {
        MtChangeOrderRule.ChangeCondition changeCondition = changeOrderRule.getChangeCondition();
        // 函数条件
        if (MtChangeOrderRule.ChangeCondition.WHERE_TYPE_FUNCTION.equals(changeCondition.getWhereType())) {
            String functionApiName = changeCondition.getFunctionApiName();
            callValidationFunction(functionApiName);
            return;
        }
        // 字段条件
        List<Wheres> wheres = changeCondition.toWheres(objectDescribe);
        IFilter filter = FilterExt.of(Operator.EQ, IObjectData.ID, objectData.getId()).getFilter();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(wheres, filter);
        ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .queryExt(queryExt)
                .filterLabel(changeOrderRule.getLabel())
                .build();
        List<IObjectData> dataList = dataFilter.doFilter(Lists.newArrayList(objectData));
        if (CollectionUtils.empty(dataList)) {
            throw new ValidateException(changeCondition.getMessage());
        }
    }

    private void callValidationFunction(String funcApiName) {
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                .describeApiName(actionContext.getObjectApiName())
                .objectData(objectData)
                .build();
        ButtonExt buttonExt = ButtonExt.of(LayoutButtonExt.of(ObjectAction.BATCH_IMPORT.createButton()).toMap());
        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .describe(objectDescribe)
                .user(actionContext.getUser())
                .button(buttonExt.getButton())
                .build();

        IUdefAction action = FunctionAction.ActionParameter.createByFuncApiName(funcApiName).toIUdefAction();
        ActionExecutorContext actionContext = ActionExecutorContext.of(executorContext, action);
        ButtonExecutor.Result validatedFunctionResult = validateFuncAction.invoke(executorArg, actionContext);
        if (validatedFunctionResult.isHasReturnValue() && Objects.nonNull(validatedFunctionResult.getReturnValue())) {
            throw new ValidateException(validatedFunctionResult.getReturnValue().toString());
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Change.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHANGE.getButtonApiName();
    }

    @Data
    public static class Arg {
        @JsonProperty("object_id")
        @JSONField(name = "object_id")
        private String objectDataId;

        @JsonProperty("change_order_rule_api_name")
        @JSONField(name = "change_order_rule_api_name")
        private String changeOrderRuleApiName;
    }

    public static class Result {

        public static Result buildSuccess() {
            return new Result();
        }
    }
}
