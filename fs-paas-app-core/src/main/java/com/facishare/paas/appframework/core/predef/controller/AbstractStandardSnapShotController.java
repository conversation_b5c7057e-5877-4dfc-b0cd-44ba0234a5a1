package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.log.metadatahandle.FillFieldInfoHandle;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.metadatahandle.FillFieldInfoHandleImpl;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverterManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.facishare.paas.appframework.log.dto.ModifyRecord.SNAP_TYPE_NOT_SHOW;
import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.TRANSFERED_OLD_OBJ_LIST;

/**
 * @Desc
 * <AUTHOR>
 * @CreateTime 2019-09-24 15:57
 */
public abstract class AbstractStandardSnapShotController<A, R> extends PreDefineController<A, R> {

    private FillFieldInfoHandle fillFieldInfoHandle = SpringUtil.getContext().getBean(FillFieldInfoHandleImpl.class);
    private ObjectDataConverterManager objectDataConverterManager = SpringUtil.getContext().getBean(ObjectDataConverterManager.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    protected abstract Optional<IObjectDescribe> getDescribe(R result);

    protected abstract Optional<IObjectData> getObjectData(R result);


    protected Map<String, Object> getFieldExtra(IObjectDescribe objectDescribe) {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), objectDescribe,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);
        return ObjectDescribeDocument.of(describeExtra);
    }


    protected Map getSnapShotLayout(Map<String, Object> objData, IObjectDescribe objectDescribe, ControllerContext context) {
        IObjectData objectData = ObjectDataExt.of(objData).getObjectData();
        ILayout layout = serviceFacade.findObjectLayoutWithType(buildLayoutContext(), objectData.getRecordType(),
                objectDescribe, ILayout.DETAIL_LAYOUT_TYPE, null);
        LayoutExt.of(layout).completeDateTimeComponentFields(objectDescribe);
        LayoutExt.of(layout).removeOthersExceptFormComponent();
        //根据插件配置过滤布局中的字段
        filterFieldsByDomainPlugin(layout, objectDescribe, objectData);
        return LayoutExt.of(layout).toMap();
    }

    private void filterFieldsByDomainPlugin(ILayout layout, IObjectDescribe objectDescribe, IObjectData objectData) {
        try {
            serviceFacade.processLayoutByDomainPlugin(controllerContext.getTenantId(), objectDescribe, layout,
                    Lists.newArrayList(objectData.getRecordType()), PageType.Detail.name());
        } catch (Exception e) {
            log.warn("filterFieldsByDomainPlugin failed", e);
        }
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    protected int getSnapShotType(LogInfo logInfo) {
        if (logInfo == null) {
            return SNAP_TYPE_NOT_SHOW;
        }
        if (Objects.equals(ActionType.Add.getId(), logInfo.getBizOperationName()) ||
                (Objects.equals(ActionType.Modify.getId(), logInfo.getBizOperationName()))) {
            return ModifyRecord.SNAP_TYPE_SHOW;
        } else {
            return SNAP_TYPE_NOT_SHOW;
        }
    }

    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        if (CollectionUtils.empty(objData)) {
            return;
        }
        try {
            List<ObjectReferenceWrapper> referenceFieldDescribes = ObjectDescribeExt.of(objectDescribe).getReferenceFieldDescribes();
            for (ObjectReferenceWrapper fieldDescribe : referenceFieldDescribes) {
                Object value = objData.get(fieldDescribe.getApiName());
                if (value instanceof String) {
                    continue;
                }
                if (value instanceof Map) {
                    objData.put(fieldDescribe.getApiName(), ((Map) value).get("ID"));
                }
            }
            fillFieldInfoHandle.asyncFillFieldInfo(objectDescribe, Lists.newArrayList(ObjectDataExt.of(objData)), user);
        } catch (Exception e) {
            log.warn("fillFieldInfo error", e);
        }
    }

    protected void fillOwnerDeptName(User user, Map<String, Object> objData) {
        if (objData == null || !StringUtils.isEmpty(objData.get(ObjectDataExt.OWNER_DEPARTMENT))) {
            return;
        }
        try {
            Map<String, String> deptName =
                    serviceFacade.getDeptName(user.getTenantId(), user.getUserId(), (List<String>) objData.get(ObjectDataExt.OWNER));
            objData.put(ObjectDataExt.OWNER_DEPARTMENT, deptName.get(((List<String>) objData.get(ObjectDataExt.OWNER)).get(0)));
        } catch (Exception e) {
            log.warn("fillOwnerDeptName error user:{},objData:{}", JSON.toJSONString(user), JSON.toJSONString(objData));
        }
    }

    protected void fillMaskFieldValue(Map<String, Object> objData, IObjectDescribe describe) {
        if (!needFillMaskFieldValue()) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList(ObjectDataExt.of(objData).getObjectData());
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), dataList, describe, true);
    }

    protected void fillDimensionFiledValue(Map<String, Object> objData, IObjectDescribe describe) {
        List<IObjectData> dataList = Lists.newArrayList(ObjectDataExt.of(objData).getObjectData());
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), describe, dataList);
    }

    protected boolean needFillMaskFieldValue() {
        return RequestUtil.isCepRequest();
    }

    /**
     * 加工装饰ObjectData
     */
    protected Map<String, Object> decorateObjectData(User user, Map<String, Object> objMap, IObjectDescribe describe) {
        if (objMap == null) return null;
        List<IObjectData> objectDatas = Lists.newArrayList((ObjectDataExt.of(objMap).getObjectData()));
        infraServiceFacade.fillQuoteFieldValue(user, objectDatas, describe, false);
        //特殊处理UserScope类型，价目表适用客户范围
//        ObjectDataFieldConvertService.me().fillFieldUserScope(user, describe, objectDatas);
        //迁移的老对象，历史数据的key是老的apiname，要转换成新apiname
        Map<String, Object> result = ObjectDataExt.of(objectDatas.get(0)).toMap();
        if (isNeedConvertApiName(describe.getApiName())) {
            ObjectDataConverter dataConverter = objectDataConverterManager.getObjectDataConverter(describe.getApiName());
            if (!Objects.isNull(dataConverter)) {
                Map<String, Object> newDataMap = Maps.newHashMap();
                result.forEach((k, v) -> newDataMap.put(dataConverter.toNewFieldName(k), objMap.get(k)));
                result = newDataMap;
            }
        }
        dealNPathSign(describe, result);
        return result;
    }

    protected boolean isNeedConvertApiName(String apiName) {
        return TRANSFERED_OLD_OBJ_LIST.contains(apiName);
    }

    //兼容日志查询出来是String类型的情况
    protected void handleBoolean(Map<String, Object> objData) {
        if (objData == null) return;
        if (!StringUtils.isEmpty(objData.get("is_spec"))) {
            objData.put("is_spec", objData.get("is_spec").toString().equals("true"));
        }
        if (!StringUtils.isEmpty(objData.get("is_deleted"))) {
            objData.put("is_deleted", objData.get("is_deleted").toString().equals("true"));
        }
    }

    /**
     * 处理图片签名
     *
     * @param objDesc 描述
     * @param objData 数据
     */
    private void dealNPathSign(IObjectDescribe objDesc, Map<String, Object> objData) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(objDesc).getFieldByTypes(AppFrameworkConfig.signedUrlFieldTypes(objDesc.getTenantId()));
        if (CollectionUtils.empty(fields)) {
            return;
        }
        IActionContext ctx = ActionContextExt.of(controllerContext.getUser()).getContext();
        fields.forEach(field -> {
            Object value = objData.get(field.getApiName());
            if (ObjectUtils.isEmpty(value)) {
                return;
            }
            if (!(value instanceof List)) {
                return;
            }

            AuthModel authModel = infraServiceFacade.getFileStoreService().getAuthModel(field.getType());

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> safeList = (List<Map<String, Object>>) value;
            infraServiceFacade.getFileStoreService().generateNPathSignedUrl(ctx, authModel, safeList);
        });
    }


    @Override
    protected R doService(A arg) {
        return null;
    }
}
