package com.facishare.paas.appframework.core.model.plugin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2023/1/31
 */
public interface ListHeaderControllerPluginDTO {
    @Data
    @Builder
    class Arg {
        private String describeApiName;
        private String layoutAgentType;
        private String recordType;
        private String listType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private Map<String, String> recordLayoutMapping;
        private Map<String, List<String>> layoutFieldsMap;
        private List<String> filterFields;
        private boolean personalConfigFilterFields;
    }
}
