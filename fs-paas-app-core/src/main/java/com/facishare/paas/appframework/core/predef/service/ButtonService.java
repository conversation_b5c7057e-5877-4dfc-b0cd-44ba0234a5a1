package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.service.BrushButtonDescribeService;
import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.service.model.PollingMsgEndType;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.button.*;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.button.ButtonType;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataManager;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProvider;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig;
import com.facishare.paas.appframework.metadata.dto.ButtonJobExecuteResult;
import com.facishare.paas.appframework.metadata.handler.HandlerInfo;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.UdefAction;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ButtonExt.ADD_EDIT_BUTTON_API_NAME;
import static com.facishare.paas.appframework.metadata.ObjectMappingExt.MAPPING_ACTION_TYPE;
import static com.facishare.paas.appframework.metadata.objects.ObjectListConfig.OBJECT_MANAGEMENT;

/**
 * Created by linqiuying on 17/10/17.
 */
@ServiceModule("button")
@Component
public class ButtonService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private CustomButtonServiceImpl buttonService;
    @Autowired
    private PostActionService actionService;
    @Autowired
    private UserDefinedButtonService userDefinedButtonService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private UdefFunctionService udefFunctionService;
    @Autowired
    private LogService logService;
    @Autowired
    private FunctionLogicService functionLogicService;

    @Autowired
    private JobScheduleService jobScheduleService;
    @Autowired
    private MessagePollingService messagePollingService;
    @Autowired
    private BrushButtonDescribeService brushButtonDescribeService;
    @Autowired
    private UIEventLogicService eventLogicService;
    @Autowired
    private ButtonLogicService buttonLogicService;
    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private FindAndFilterButtonsByDataManager findAndFilterButtonsByDataManager;
    @Autowired
    private HandlerLogicService handlerLogicService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;

    @Transactional
    @ServiceMethod("create")
    public CreateButton.Result create(CreateButton.Arg arg, ServiceContext context) {
        CreateButton.Result result = new CreateButton.Result();
        IUdefButton button = getButton(arg.getButton(), context);

        ButtonExt buttonExt = ButtonExt.of(button);
        if (buttonExt.isSystemButton()) {
            buttonExt.setId(null);
            createAction(context, button, arg.getPost_actions());
            IUdefButton customButton = buttonService.createCustomButton(context.getUser(), button);
            if (customButton != null) {
                List<String> roles = getAndIntRole(arg);
                userDefinedButtonService.createUserDefinedButton(context.getUser(), customButton.getDescribeApiName(),
                        customButton.getApiName(), customButton.getLabel(), roles);
                result.setSuccess(true);
            } else {
                result.setSuccess(false);
            }
            return result;
        }

        checkButtonByUsePage(button);
        // 新建按钮校验个数校验
        List<IUdefButton> buttonList = buttonService.findButtonList(context.getUser(), button.getDescribeApiName()).stream()
                .filter(x -> !ButtonExt.of(x).isSystemButton())
                .collect(Collectors.toList());
        buttonService.checkCustomButtonCountLimit(context.getUser(), button.getDescribeApiName(), buttonList.size());

        // TODO: 2018/3/61 ）自定义函数action只能有一个 2）button和action里的参数匹配校验
        List<ActionPojo> actions = arg.getPost_actions();
        createAction(context, button, actions);
        IUdefButton customButton = buttonService.createCustomButton(context.getUser(), button);

        if (customButton != null) {
            List<String> roles = getAndIntRole(arg);
            userDefinedButtonService.createUserDefinedButton(context.getUser(), button.getDescribeApiName(), button.getApiName(), button.getLabel(), roles);
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }
        return result;
    }

    private IUdefButton getButton(String buttonJson, ServiceContext context) {
        IUdefButton button = new UdefButton();
        button.fromJsonString(buttonJson);
        return button;
    }

    private void createAction(ServiceContext context, IUdefButton button, List<ActionPojo> actions) {
        if (CollectionUtils.empty(actions)) {
            return;
        }
        if (ButtonExt.of(button).isSystemButton()) {
            // 预置按钮的自定义动作默认是执行后动作
            for (ActionPojo action : actions) {
                String stage = action.getStage();
                if (Strings.isNullOrEmpty(stage) || UdefActionExt.CURRENT.equals(stage)) {
                    action.setStage(UdefActionExt.POST);
                }
            }
        }
        uiActionSupportUIEvent(context, button, actions);
        List<IUdefAction> actionList = generateActionList(actions, context.getTenantId(), button.getDescribeApiName());
        List<IUdefAction> createActions = actionService.bulkCreateAction(context.getUser(), actionList);
        changeFunctionStatus(context, button, createActions, IUdefFunction.USED);
        button.setActions(createActions.stream().map(IUdefAction::getId).collect(Collectors.toList()));
    }

    private void uiActionSupportUIEvent(ServiceContext context, IUdefButton button, List<ActionPojo> actions) {
        if (ButtonExt.of(button).isUIAction()) {
            List<String> functionApiNames = actions.stream()
                    .filter(action -> IAcionType.UI_EVENT.equals(action.getAction_type()))
                    .map(actionPojo -> {
                        FunctionAction.ActionParameter actionParameter = FunctionAction.ActionParameter.of(actionPojo.getAction_paramter());
                        return actionParameter.getFunctionApiName();
                    }).filter(Objects::nonNull).collect(Collectors.toList());
            List<IUdefFunction> udefFunctions = udefFunctionService.findFunctionByApiNames(context.getTenantId(), functionApiNames, button.getDescribeApiName());
            List<IUIEvent> uiEventList = udefFunctions.stream()
                    .filter(function -> "UIEvent".equals(function.getReturnType())
                            && SourceTypes.UI_EVENT.equals(function.getNameSpace()))
                    .map(func -> {
                        Map<String, Object> event = Maps.newHashMap();
                        event.put(IUIEvent.DESCRIBE_API_NAME, button.getDescribeApiName());
                        event.put(IUIEvent.FUNC_API_NAME, func.getApiName());
                        event.put(IUIEvent.TENANT_ID, context.getTenantId());
                        event.put(IUIEvent.TRIGGER_DESCRIBE_API_NAME, button.getDescribeApiName());
                        event.put(IUIEvent.TRIGGER_FIELD_API_NAMES, Lists.newArrayList());
                        event.put(IUIEvent.TRIGGERS, Lists.newArrayList(7));
                        event.put(IUIEvent.TYPE, 6);
                        return UIEventExt.of(event).toUIEvent();
                    }).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(uiEventList) && !ButtonExt.of(button).isUseInCreateOrEdit()) {
                throw new ValidateException(I18NExt.text(I18NKey.BUTTON_NOT_SUPPORT_UI_EVENT));
            }
            List<IUIEvent> eventList = eventLogicService.createEvents(uiEventList, context.getUser());
            if (CollectionUtils.notEmpty(eventList)) {
                Map<String, String> funcUIEventMap = eventList.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(IUIEvent::getFuncApiName, IUIEvent::getId));
                actions.stream().filter(action -> IAcionType.UI_EVENT.equals(action.getAction_type()))
                        .forEach(actionPojo -> {
                            FunctionAction.ActionParameter actionParameter = FunctionAction.ActionParameter.of(actionPojo.getAction_paramter());
                            if (funcUIEventMap.containsKey(actionParameter.getFunctionApiName())) {
                                actionParameter.setUiEventId(funcUIEventMap.get(actionParameter.getFunctionApiName()));
                                actionPojo.setAction_paramter(JSON.toJSONString(actionParameter));
                            }
                        });
            }
        }
    }

    private List<String> getAndIntRole(CreateButton.Arg arg) {
        List<String> roles = arg.getRoles();
        if (arg.isIgnoreDefaultRole()) {
            return CollectionUtils.nullToEmpty(roles);
        }
        if (CollectionUtils.empty(roles)) {
            return Lists.newArrayList(PrivilegeConstants.ADMIN_ROLE_CODE);
        }
        return roles;
    }

    private void checkButtonByUsePage(IUdefButton button) {
        ButtonExt buttonExt = ButtonExt.of(button);
        if (!buttonExt.isListNormalButton() || buttonExt.isTransFormButton()) {
            return;
        }
        if (!buttonExt.isUIAction()) {
            throw new ValidateException(I18NExt.text(I18NKey.GENERIC_BUTTON_ONLY_SUPPORTS_UIACTION));
        }

        if (CollectionUtils.notEmpty(buttonExt.getParamForm())) {
            throw new ValidateException(I18NExt.text(I18NKey.LIST_GENERIC_BUTTON_CANNOT_SET_BUTTON_PARAMETERS));
        }

        if (CollectionUtils.notEmpty(buttonExt.getWheres())) {
            throw new ValidateException(I18NExt.text(I18NKey.LIST_GENERIC_BUTTON_CANNOT_SET_BUTTON_DISPLAY_CONDITIONS));
        }
    }

    @ServiceMethod("update")
    public UpdateButton.Result update(UpdateButton.Arg arg, ServiceContext context) {
        UpdateButton.Result result = new UpdateButton.Result();
        context.getRequestContext().setFromManage(OBJECT_MANAGEMENT.equals(arg.getSourceInfo()));

        IUdefButton button = getButton(arg.getButton(), context);

        checkButtonByUsePage(button);
        IUdefButton findButton = buttonService.findButtonByApiName(context.getUser(), button.getApiName(), button.getDescribeApiName());
        if (Objects.isNull(findButton)) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }

        UpdateResult updateResult = infraServiceFacade.callWithTransaction(() -> updateButton(context, arg, button, findButton));
        if (updateResult.getCustomButton() != null) {
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }

        //更新业务链
        if (CollectionUtils.notEmpty(arg.getHandlerList())) {
            handlerLogicService.batchUpsertTenantHandler(context.getUser(), arg.getHandlerList(),
                    ObjectAction.getActionCodeByButtonApiName(button.getApiName()), button.getDescribeApiName());
        }

        return result;
    }

    private UpdateResult updateButton(ServiceContext context, UpdateButton.Arg arg, IUdefButton button, IUdefButton findButton) {
        ButtonExt buttonExt = ButtonExt.of(button);
        if (buttonExt.isSystemButton() && Strings.isNullOrEmpty(findButton.getId())) {
            //创建预置按钮
            button.setId(null);
            button = findButton = buttonService.createCustomButton(context.getUser(), button);
        }

        List<IUdefAction> findActionList = findActionList(context.getUser(), findButton, button.getDescribeApiName());
        changeFunctionStatus(context, findButton, findActionList, IUdefFunction.NOT_USED);
        // 编辑按钮时，不处理转换按钮的按钮动作
        if (!buttonExt.isConvert()) {
            clearButtonUIEvent(context, buttonExt, findActionList);
            actionService.bulkDeleteAction(context.getUser(), findButton.getActions(), button.getDescribeApiName());
            //action删了，同时清空对应id
            button.setActions(Lists.newArrayList());
            List<ActionPojo> actions = arg.getPost_actions();
            // 编辑按钮时，不处理转换按钮的按钮动作
            if (CollectionUtils.notEmpty(actions)) {
                uiActionSupportUIEvent(context, button, actions);
                List<IUdefAction> actionList = generateActionList(actions, context.getTenantId(), button.getDescribeApiName());
                // 预置按钮只保留 custom_function or custom_biz or updates 类型的执行动作
                if (buttonExt.isSystemButton()) {
                    if (StringUtils.equals(buttonExt.getApiName(), ObjectAction.INVALID.getButtonApiName())) {
                        actionList.removeIf(it -> !(
                                ButtonActionType.CustomFunction.getId().equals(it.getActionType())
                                        || ButtonActionType.CustomBiz.getId().equals(it.getActionType())
                                        || ButtonActionType.Updates.getId().equals(it.getActionType())));
                    } else {
                        actionList.removeIf(it -> !(
                                ButtonActionType.CustomFunction.getId().equals(it.getActionType())
                                        || ButtonActionType.CustomBiz.getId().equals(it.getActionType())));
                    }
                }
                List<IUdefAction> createActions = actionService.bulkCreateAction(context.getUser(), actionList);
                button.setActions(createActions.stream().map(x -> x.getId()).collect(Collectors.toList()));
                changeFunctionStatus(context, button, createActions, IUdefFunction.USED);
            }
        }

        IUdefButton customButton = buttonLogicService.updateButtonAndFunctionPrivilegePrivilege(context.getUser(), button, arg.getRoles());
        return UpdateResult.builder().button(button).customButton(customButton).build();
    }

    private void clearButtonUIEvent(ServiceContext context, ButtonExt buttonExt, List<IUdefAction> findActionList) {
        if (buttonExt.isUIAction()) {
            List<String> eventIds = findActionList.stream()
                    .filter(action -> IAcionType.UI_EVENT.equals(action.getActionType()))
                    .map(actionPojo -> {
                        FunctionAction.ActionParameter actionParameter = FunctionAction.ActionParameter.of(actionPojo.getActionParamter());
                        return actionParameter.getUiEventId();
                    })
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            eventLogicService.batchDelete(eventIds, context.getUser());
        }
    }

    @Transactional
    @ServiceMethod("delete")
    public DeleteButton.Result delete(DeleteButton.Arg arg, ServiceContext context) {
        IUdefButton button = buttonService.findButtonByApiName(context.getUser(), arg.getButtonApiName(), arg.getDescribeApiName());
        if (button == null || button.isDeleted()) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
        List<IUdefAction> findActionList = findActionList(context.getUser(), button, button.getDescribeApiName());
        actionService.bulkDeleteAction(context.getUser(), button.getActions(), button.getDescribeApiName());
        clearButtonUIEvent(context, ButtonExt.of(button), findActionList);
        boolean success = buttonService.deleteCustomButton(context.getUser(), arg.getButtonApiName(), arg.getDescribeApiName());
        userDefinedButtonService.deleteUserDefinedButton(context.getUser(), arg.getDescribeApiName(), arg.getButtonApiName());
        cleanFunctionStatus(context, button, findActionList, IUdefFunction.NOT_USED);
        DeleteButton.Result result = new DeleteButton.Result();
        result.setSuccess(success);
        return result;
    }

    @ServiceMethod("findButtonList")
    public FindButtonList.Result findButtonList(FindButtonList.Arg arg, ServiceContext context) {
        List<IUdefButton> buttonList = buttonService.findButtonList(context.getUser(), arg.getDescribeApiName(), true);
        filterButtonsByExcludeRules(buttonList, arg.isExcludeUIButton(), arg.isExcludeRedirectType(), arg.getDescribeApiName());
        // 批量查询按钮 config
        List<IUdefButtonConfig> buttonConfigs = buttonService.findButtonConfigListByApiName(context.getUser(), arg.getDescribeApiName(), buttonList);
        FindButtonList.Result result = new FindButtonList.Result();
        result.setButtonConfigList(ButtonConfigDocument.ofList(buttonConfigs));
        result.setButtonList(ButtonDocument.ofList(buttonList));
        result.setSupportValidateRule(UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BUTTON_VALIDATE_RULE_GRAY_EI, context.getTenantId()));
        return result;
    }

    private void filterButtonsByExcludeRules(List<IUdefButton> buttonList, boolean excludeUIButton, boolean excludeRedirectType, String describeApiName) {
        // UIPaaS的不能触发流程,不下发
        buttonList.removeIf(x -> excludeUIButton && ButtonExt.REDIRECT_UIPAAS.equals(x.getRedirectType()));
        // 根据excludeRedirectType属性判断是否彻底排除UI按钮的下发
        buttonList.removeIf(x -> excludeRedirectType && ButtonType.REDIRECT.getId().equals(x.getButtonType()));
        // 黑名单中的按钮不展示
        buttonList.removeIf(button -> ButtonConfig.isDesignerButtonBlacklist(describeApiName, button.getApiName()));
    }

    @ServiceMethod("findButtonNamesByDescribeApiNames")
    public FindButtonsByDescribeApiNames.Result findButtonsByDescribeApiNames(FindButtonsByDescribeApiNames.Arg arg, ServiceContext context) {
        Map<String, List<IUdefButton>> buttonMap = buttonService.findButtonsByDescribeApiNames(context.getUser(), arg.getDescribeApiNames(), true);
        FindButtonsByDescribeApiNames.Result result = new FindButtonsByDescribeApiNames.Result();
        Map<String, List<ButtonDocument>> buttonDocumentMap = Maps.newHashMap();
        buttonMap.forEach((describeApiName, buttonList) -> {
            filterButtonsByExcludeRules(buttonList, BooleanUtils.isTrue(arg.getExcludeUIButton()), BooleanUtils.isTrue(arg.getExcludeRedirectType()), describeApiName);
            buttonDocumentMap.put(describeApiName, ButtonDocument.ofList(buttonList));
        });
        result.setButtonList(buttonDocumentMap);
        return result;
    }

    @ServiceMethod("findButtonInfo")
    public FindButtonInfo.Result findButtonInfo(FindButtonInfo.Arg arg, ServiceContext context) {
        context.getRequestContext().setFromManage(OBJECT_MANAGEMENT.equals(arg.getSourceInfo()));
        IUdefButton button = buttonService.findButtonByApiNameForDesigner(context.getUser(), arg.getButtonApiName(), arg.getDescribeApiName());
        if (Objects.isNull(button)) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
        List<IUdefAction> actionList = findActionListForDesigner(context.getUser(), button, arg.getDescribeApiName());
        List<ActionPojo> actionPojoList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(actionList)) {
            Map<String, IUdefAction> actionMap = actionList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
            actionPojoList = button.getActions().stream().map(id -> {
                IUdefAction action = actionMap.get(id);
                ActionPojo pojo = new ActionPojo();
                pojo.setTenantId(action.getTenantId());
                pojo.setRemark(action.getRemark());
                pojo.setLabel(action.getLabel());
                pojo.setAction_type(action.getActionType());
                pojo.setAction_paramter(action.getActionParamter());
                pojo.setId(action.getId());
                pojo.setDescribe_api_name(action.getDescribeApiName());
                pojo.setStage(action.getStage());
                pojo.setBizKey(action.getBizKey());
                return pojo;
            }).collect(Collectors.toList());
        }
        FindButtonInfo.Result result = new FindButtonInfo.Result();
        result.setPost_actions(actionPojoList);
        List<String> queryRoles = userDefinedButtonService.getHavePrivilegeRolesByUserDefinedButton(context.getUser(),
                button.getDescribeApiName(), button.getApiName());
        result.setRoles(queryRoles);

        IObjectMappingRuleInfo mappingRule = findMappingRule(context, button, actionList);
        result.setMapping_rule(Objects.isNull(mappingRule) ? null : MappingRuleDocument.of(mappingRule));
        result.setButton(ButtonDocument.of(button));

        //业务链配置
        String actionCode = ObjectAction.getActionCodeByButtonApiName(button.getApiName());
        boolean supportHandler = HandlerGrayConfig.supportManagement(context.getTenantId(), actionCode, button.getDescribeApiName());
        //灰度了AddUI和EditUI的新建按钮和编辑按钮，不支持业务链配置
        if ((ObjectAction.CREATE.getButtonApiName().equals(button.getApiName()) || ObjectAction.UPDATE.getButtonApiName().equals(button.getApiName()))
                && AppFrameworkConfig.isAddEditUIActionGray(context.getTenantId(), button.getDescribeApiName())) {
            supportHandler = false;
        }
        result.setSupportHandler(supportHandler);
        if (supportHandler) {
            List<HandlerInfo> handlerList = handlerLogicService.findHandlerInfoForManagement(context.getTenantId(), button.getDescribeApiName(), actionCode);
            result.setHandlerList(handlerList);
        }
        if (BooleanUtils.isTrue(arg.getHandleButtonParam())) {
            IObjectDescribe objectDescribe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
            ButtonExt.of(button).handleButtonParam(context.getUser(), null, objectDescribe, null, Collections.emptySet(), false);
        }
        return result;
    }

    private List<IUdefAction> findActionListForDesigner(User user, IUdefButton button, String describeApiName) {
        List<IUdefAction> actions = actionService.findActionListForDesigner(user, null == button ? new UdefButton() : button, describeApiName);
        return actions.stream()
                .map(UdefActionExt::of)
                .peek(x -> x.computeActionStage(button))
                .map(UdefActionExt::getUdefAction)
                .collect(Collectors.toList());
    }

    private List<IUdefAction> findActionList(User user, IUdefButton button, String describeApiName) {
        List<IUdefAction> actions = actionService.findActionList(user, null == button ? new UdefButton() : button, describeApiName);
        return actions.stream()
                .map(UdefActionExt::of)
                .peek(x -> x.computeActionStage(button))
                .map(UdefActionExt::getUdefAction)
                .collect(Collectors.toList());
    }

    private IObjectMappingRuleInfo findMappingRule(ServiceContext context, IUdefButton button, List<IUdefAction> actionList) {
        if (!Objects.equals(MAPPING_ACTION_TYPE, button.getButtonType()) || CollectionUtils.empty(actionList)) {
            return null;
        }

        String actionParameter = actionList.get(0).getActionParamter();
        ObjectMappingExt.ObjectMappingActionParam param = JSON.parseObject(actionParameter, ObjectMappingExt.ObjectMappingActionParam.class);
        List<IObjectMappingRuleInfo> ruleList = objectMappingService.findByApiName(context.getUser(), param.getObjectMappingApiName());
        if (CollectionUtils.empty(ruleList)) {
            return null;
        }
        IObjectMappingRuleInfo first = ObjectMappingExt.of(ruleList).getObjectMappingRuleInfo();
        if (Objects.isNull(first)) {
            return null;
        }

        IObjectDescribe targetObject = describeLogicService.findObject(context.getTenantId(), first.getTargetApiName());
        if (!Objects.isNull(targetObject)) {
            first.setTargetDisplayName(targetObject.getDisplayName());
        }
        return first;
    }

    @ServiceMethod("changeButtonStatus")
    public ChangeButtonStatus.Result changeButtonStatus(ChangeButtonStatus.Arg arg, ServiceContext context) {
        boolean success = buttonService.updateStatus(context.getUser(), arg.getButtonApiName(), arg.getDescribeApiName(), arg.isActive());
        ChangeButtonStatus.Result result = new ChangeButtonStatus.Result();
        result.setSuccess(success);
        return result;
    }

    @ServiceMethod("startButton")
    public StartButton.Result startButton(StartButton.Arg arg, ServiceContext context) {
        StartButton.Result result = new StartButton.Result();
        return result;
    }

    @ServiceMethod("validateButtonCount")
    public ValidateButtonCount.Result validateButtonCount(ValidateButtonCount.Arg arg, ServiceContext context) {
        if (ObjectAction.getByButtonApiName(arg.getButtonApiName()) != ObjectAction.UNKNOWN_ACTION) {
            return ValidateButtonCount.Result.builder().success(true).build();
        }
        List<IUdefButton> buttonList = buttonService.findButtonList(context.getUser(), arg.getDescribeApiName()).stream()
                .filter(button -> !ButtonExt.of(button).isSystemButton())
                .collect(Collectors.toList());
        //ButtonCountValidator.validate(arg.getButtonApiName(), arg.getUsePage(), buttonList);
        buttonService.checkCustomButtonCountLimit(context.getUser(), arg.getDescribeApiName(), buttonList.size());
        return ValidateButtonCount.Result.builder().success(true).build();
    }

    @ServiceMethod("fetchButtonInfo")
    public FetchButtonInfo.Result fetchButtonInfo(FetchButtonInfo.Arg arg, ServiceContext context) {
        List<IUdefButton> buttonList = buttonService.findButtonsByLastModifiedTime(context.getUser(), 0);
        //新建，编辑的ui按钮灰度企业特殊逻辑处理
        handleAddEditGrayTenant(context, buttonList);
        List<FetchButtonInfo.ButtonInfo> buttonInfoList = FetchButtonInfo.ButtonInfo.from(buttonList, RequestUtil.isMobileRequest());
        //根据配置文件获取lookup页面跳转配置
        handleLookupPage(context, buttonInfoList);
        //根据配置文件获取web端的跳转配置-UIAction方式
        handleUIAction(context, buttonInfoList);
        return FetchButtonInfo.Result.builder()
                .buttonList(buttonInfoList)
                .build();
    }

    /**
     * 根据配置文件获取web端的跳转配置-UIAction方式
     *
     * @param context
     * @param buttonInfoList
     */
    private void handleUIAction(ServiceContext context, List<FetchButtonInfo.ButtonInfo> buttonInfoList) {
        List<Map<String, String>> uiActionNaviURL = AppFrameworkConfig.getUIActionNaviURL(context.getTenantId());
        List<FetchButtonInfo.NaviUIAction> uiActionList = uiActionNaviURL.stream().map(x -> FetchButtonInfo.NaviUIAction.of(x)).collect(Collectors.toList());
        List<FetchButtonInfo.NaviUIAction> newActions = Lists.newArrayList();
        uiActionList.forEach(a -> {
            Optional<FetchButtonInfo.ButtonInfo> buttonInfo = buttonInfoList.stream()
                    .filter(b -> Objects.equals(b.getActionCode(), a.getActionCode())
                            && Objects.equals(b.getDescribeApiName(), a.getDescribeApiName()))
                    .findAny();
            if (buttonInfo.isPresent()) {
                buttonInfo.get().setUiAction(a);
            } else {
                newActions.add(a);
            }
        });
        newActions.forEach(a -> buttonInfoList.add(FetchButtonInfo.ButtonInfo.from(a)));
    }

    /**
     * 根据配置文件获取lookup页面跳转配置
     *
     * @param context
     * @param buttonInfoList
     */
    private void handleLookupPage(ServiceContext context, List<FetchButtonInfo.ButtonInfo> buttonInfoList) {
        List<Map<String, String>> relatedListNaviURL = AppFrameworkConfig.getRelatedListNaviURL(context.getTenantId());
        relatedListNaviURL.forEach(a -> buttonInfoList.add(FetchButtonInfo.ButtonInfo.from(a)));
    }

    /**
     * 新建，编辑的ui按钮灰度企业特殊逻辑处理
     *
     * @param context
     * @param buttonList
     */
    private void handleAddEditGrayTenant(ServiceContext context, List<IUdefButton> buttonList) {
        if (!AppFrameworkConfig.isAddEditUIActionGrayTenant(context.getTenantId())) {
            return;
        }

        List<IUdefButton> list = buttonList.stream()
                .filter(a -> AppFrameworkConfig.isAddEditUIActionGray(context.getTenantId(), a.getDescribeApiName()))
                .filter(a -> ADD_EDIT_BUTTON_API_NAME.contains(a.getApiName()))
                .collect(Collectors.toList());
        Map<String, List<IUdefButton>> map = list.stream().collect(Collectors.groupingBy(IUdefButton::getDescribeApiName));
        map.forEach((describeApiName, buttons) -> {
            //新建，编辑按钮灰度企业，获取按钮时
            Optional<IUdefButton> addButton = getButtonByApiName(buttons, ObjectAction.CREATE);
            Optional<IUdefButton> addSaveButton = getButtonByApiName(buttons, ObjectAction.CREATE_SAVE);
            copyUrl(buttonList, describeApiName, addButton, addSaveButton, ObjectAction.CREATE);

            Optional<IUdefButton> editButton = getButtonByApiName(buttons, ObjectAction.UPDATE);
            Optional<IUdefButton> editSaveButton = getButtonByApiName(buttons, ObjectAction.UPDATE_SAVE);
            copyUrl(buttonList, describeApiName, editButton, editSaveButton, ObjectAction.UPDATE);
        });
    }

    private void copyUrl(List<IUdefButton> buttonList, String describeApiName, Optional<IUdefButton> button,
                         Optional<IUdefButton> saveButton, ObjectAction action) {
        if (saveButton.isPresent() && !Strings.isNullOrEmpty(saveButton.get().getUrl())) {
            if (!button.isPresent()) {
                IUdefButton newButton = new UdefButton();
                newButton.setApiName(action.getButtonApiName());
                newButton.setUrl(saveButton.get().getUrl());
                newButton.setDescribeApiName(describeApiName);
                buttonList.add(newButton);
            } else {
                button.get().setUrl(saveButton.get().getUrl());
            }
            saveButton.get().setUrl(null);
        }
    }

    private Optional<IUdefButton> getButtonByApiName(List<IUdefButton> buttonList, ObjectAction action) {
        return buttonList.stream().filter(a -> Objects.equals(a.getApiName(), action.getButtonApiName())).findAny();
    }

    @ServiceMethod("findButtonConfig")
    public FindUdefButtonConfig.Result findButtonConfig(FindUdefButtonConfig.Arg arg, ServiceContext context) {
        IUdefButtonConfig buttonConfig = buttonService.findButtonConfigByApiName(arg.getDescribeApiName(), arg.getButtonApiName(), context.getUser());
        return FindUdefButtonConfig.Result.of(ButtonConfigDocument.of(buttonConfig));
    }

    @ServiceMethod("findButtonJobResult")
    public FindButtonJobResult.Result findButtonJobResult(FindButtonJobResult.Arg arg, ServiceContext context) {
        ButtonJobExecuteResult results = jobScheduleService.queryButtonJobExecuteResult(context.getUser(), arg.getDescribeApiName(), arg.getJobId());
        return FindButtonJobResult.Result.of(results);
    }

    @ServiceMethod("updateButtonUrl")
    public UpdateButtonUrl.Result updateButtonUrl(UpdateButtonUrl.Arg arg, ServiceContext context) {
        buttonService.updateButtonUrl(context.getUser(), arg.getObjectDescribeApiName(), arg.getButtonURL(), arg.getButtonApiNames());
        messagePollingService.sendPollingMessage(context.getUser(), "paas_ui_button", PollingMsgEndType.ALL, false);
        return UpdateButtonUrl.Result.builder().build();
    }

    @ServiceMethod("brushButtonDescribe")
    public BrushButton.Result brushButtonDescribe(BrushButton.Arg arg, ServiceContext context) {
        if (CollectionUtils.notEmpty(arg.getTenantIds())) {
            brushButtonDescribeService.brushButtonDescribe(arg.getObjectApiNames(), arg.getTenantIds());
            return BrushButton.Result.builder().build();
        }
        brushButtonDescribeService.brushButtonDescribe(arg.getObjectApiNames(), context.getUser());
        return BrushButton.Result.builder().build();
    }

    @Transactional
    @ServiceMethod("updateButtonPostAction")
    public UpdateButtonPostAction.Result updateButtonPostAction(UpdateButtonPostAction.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getButtonApiName()) || StringUtils.isBlank(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_PARAM_ERROR));
        }
        IUdefButton button = buttonService.findButtonByApiName(context.getUser(), arg.getButtonApiName(), arg.getDescribeApiName());
        if (Objects.isNull(button)) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
        List<IUdefAction> actionList = findActionListForDesigner(context.getUser(), button, button.getDescribeApiName());
        if (CollectionUtils.empty(arg.getUdefAction())) {
            throw new ValidateException("action is null");
        }
        List<String> actionIds = ButtonExt.of(button).getActions();
        List<IUdefAction> addActionList = Lists.newArrayList();
        List<IUdefAction> udefAction = arg.getUdefAction().stream().map(ActionPojo::toUDefAction).collect(Collectors.toList());
        doValidateAction(udefAction);
        for (IUdefAction action : udefAction) {
            if (StringUtils.isBlank(action.getId())) {
                addActionList.add(action);
            } else {
                // 覆盖原有的action
                if (actionIds.contains(action.getId())) {
                    actionList.removeIf(x -> Objects.equals(x.getId(), action.getId()));
                }
                actionList.add(action);
            }
        }
        if (!CollectionUtils.empty(addActionList)) {
            List<IUdefAction> bulkCreateAction = actionService.bulkCreateAction(context.getUser(), addActionList);
            List<String> addActionIds = bulkCreateAction.stream().map(x -> x.getId()).collect(Collectors.toList());
            ButtonExt.of(button).getActions().addAll(addActionIds);
            actionList.addAll(bulkCreateAction);
        }
        actionService.bulkUpdateAction(context.getUser(), actionList);
        buttonService.updateCustomButton(context.getUser(), button);
        return UpdateButtonPostAction.Result.builder().success(true).build();
    }

    private void doValidateAction(List<IUdefAction> udefActions) {
        for (IUdefAction udefAction : udefActions) {
            if (StringUtils.isBlank(udefAction.getDescribeApiName())) {
                throw new ValidateException(I18N.text(I18NKey.BUTTON_PARAM_ERROR));
            }
        }
    }

    @Transactional
    @ServiceMethod("deleteButtonPostAction")
    public DeleteButtonPostAction.Result deleteButtonPostAction(DeleteButtonPostAction.Arg arg, ServiceContext context) {
        IUdefButton button = buttonService.findButtonByApiName(context.getUser(), arg.getButtonApiName(), arg.getDescribeApiName());
        if (Objects.isNull(button)) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
        List<IUdefAction> actionList = findActionListForDesigner(context.getUser(), button, button.getDescribeApiName());
        if (CollectionUtils.empty(arg.getActionIds())) {
            throw new ValidateException("actionId is null");
        }
        List<String> deleteActionIds = actionList.stream()
                .map(x -> x.getId())
                .filter(id -> arg.getActionIds().contains(id))
                .collect(Collectors.toList());
        ButtonExt.of(button).getActions().removeIf(deleteActionIds::contains);
        actionService.bulkDeleteAction(context.getUser(), deleteActionIds, arg.getDescribeApiName());
        buttonService.updateCustomButton(context.getUser(), button);
        return DeleteButtonPostAction.Result.builder().success(true).build();
    }

    @ServiceMethod("findAndFilterButtonsByData")
    public FindAndFilterButtonsByData.Result findAndFilterButtonsByData(FindAndFilterButtonsByData.Arg arg, ServiceContext context) {

        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());

        IObjectData objectData = metaDataFindService.findObjectData(context.getUser(), arg.getDataId(), describe);

        Map<String, List<IUdefButton>> buttonMap = buttonService.findListButtonByButtonFilter(describe, Lists.newArrayList(objectData), context.getUser(), arg.getUsePageType());

        List<IUdefButton> buttons = buttonMap.get(arg.getDataId());
        FindAndFilterButtonsByDataProvider provider = findAndFilterButtonsByDataManager.getProvider(arg.getDescribeApiName());
        List<IUdefButton> result = provider.findAndFilterButtonsByData(context.getUser(), arg.convertTo(buttons));
        return FindAndFilterButtonsByData.Result.fromButtons(result);
    }

    @ServiceMethod("findButtonLabelByApiName")
    public FindButtonLabelByApiName.Result findButtonLabelByApiName(FindButtonLabelByApiName.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescribeApiName());
        Integer tenantIdInt = context.getUser().getTenantIdInt();
        FindButtonLabelByApiName.Result.ResultBuilder builder = FindButtonLabelByApiName.Result.builder();
        for (String buttonApiName : CollectionUtils.nullToEmpty(arg.getButtonApiNames())) {
            String i18nKey = Optional.of(ObjectAction.getByButtonApiName(buttonApiName))
                    .filter(objectAction -> objectAction != ObjectAction.UNKNOWN_ACTION)
                    .map(ObjectAction::getI18NKey)
                    .orElseGet(() -> GetI18nKeyUtil.getUdefButtonLabelKey(describe.getApiName(), buttonApiName));
            Localization localization = I18nClient.getInstance().get(i18nKey, tenantIdInt);
            FindButtonLabelByApiName.LangValue.build(buttonApiName, localization).forEach(builder::langValue);
        }
        return builder.build();
    }

    private List<IUdefAction> generateActionList(List<ActionPojo> actions, String tenantId, String describeApiName) {
        return actions.stream().map(pojo -> {
            IUdefAction action = new UdefAction();
            action.setTenantId(tenantId);
            action.setDescribeApiName(pojo.getDescribe_api_name());
            action.setActionParamter(pojo.getAction_paramter());
            action.setActionType(pojo.getAction_type());
            action.setLabel(pojo.getLabel());
            action.setRemark(pojo.getRemark());
            action.setDescribeApiName(describeApiName);
            action.setStage(pojo.getStage());
            action.setBizKey(pojo.getBizKey());
            return action;
        }).collect(Collectors.toList());
    }

    private void changeFunctionStatus(ServiceContext context, IUdefButton button, List<IUdefAction> createActions, String status) {
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(context.getUser().getUserId());
        List<ReferenceData> referenceDataList = new ArrayList<>();
        createActions.stream()
                .filter(x -> UdefActionExt.of(x).isFunctionAction())
                .forEach(x -> {
                    Gson gson = new Gson();
                    Map<String, String> functionMap = gson.fromJson(x.getActionParamter(), Map.class);
                    String functionApiName = functionMap.get("func_api_name");
                    IUdefFunction function = udefFunctionService.findFunctionByApiName(context.getTenantId(), functionApiName
                            , button.getDescribeApiName());

                    if (IUdefFunction.USED.equals(status)) {
                        if (Objects.isNull(function)) {
                            throw new FunctionException(I18NExt.text(I18NKey.FUNCTION_DOES_NOT_EXIST));
                        }
                        udefFunctionService.updateUdefFunctionStatus(context.getTenantId(), functionApiName,
                                button.getDescribeApiName(), status, function.getUsedInfo(), actionContext);
                        referenceDataList.add(buildReferenceData(button, functionApiName));
                    } else {
                        functionLogicService.clearUdefFunctionUsedInfo(context.getUser(), function, button.getDescribeApiName(), button.getApiName(), status);
                        functionLogicService.deleteRelation(context.getUser(), SourceTypes.BUTTON, button.getApiName(), functionApiName);
                    }

                    logService.logUdefFunction(context.getUser(), EventType.MODIFY, ActionType.UPDATE_FUNCTION_REFERENCE, button.getDescribeApiName(), functionMap.get("func_api_name"), function);
                });
        if (!CollectionUtils.empty(referenceDataList)) {
            functionLogicService.saveRelation(context.getUser(), referenceDataList);
        }
    }

    private ReferenceData buildReferenceData(IUdefButton button, String functionApiName) {
        ReferenceData referenceData = ReferenceData.builder()
                .sourceType(SourceTypes.BUTTON)
                .sourceLabel(button.getLabel())
                .sourceValue(button.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(functionApiName).build();
        return referenceData;
    }

    private void cleanFunctionStatus(ServiceContext context, IUdefButton button, List<IUdefAction> createActions, String status) {
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(context.getUser().getUserId());
        createActions.forEach(x -> {
            if (UdefActionExt.of(x).isFunctionAction()) {
                Gson gson = new Gson();
                Map<String, String> functionMap = gson.fromJson(x.getActionParamter(), Map.class);
                String functionApiName = functionMap.get("func_api_name");
                IUdefFunction function = udefFunctionService.findFunctionByApiName(context.getTenantId(), functionApiName
                        , button.getDescribeApiName());
                if (function != null) {
                    functionLogicService.clearUdefFunctionUsedInfo(context.getUser(), function, button.getDescribeApiName(), button.getApiName(), status);
                }
                functionLogicService.deleteRelation(context.getUser(), SourceTypes.BUTTON, button.getApiName(), functionApiName);
                logService.logUdefFunction(context.getUser(), EventType.MODIFY, ActionType.UPDATE_FUNCTION_REFERENCE, button.getDescribeApiName(), functionMap.get("func_api_name"), null);
            }
        });
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    private static class UpdateResult {
        private IUdefButton button;
        private IUdefButton customButton;
    }

}
