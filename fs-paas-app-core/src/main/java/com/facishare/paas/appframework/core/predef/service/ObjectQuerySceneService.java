package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.queryscene.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.dto.CustomSceneResult;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneExt;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IOrderBy;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.userdefobj.DefObjConstants.invisibleFieldListFormObjectMap;
import static com.facishare.crm.userdefobj.DefObjConstants.invisibleFieldNameListForListLayout;
import static com.facishare.paas.appframework.common.util.CollectionUtils.sortByGivenOrder;
import static com.facishare.paas.appframework.common.util.DebuggingLogger.DEBUGGING_LOGGER;
import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_740;

/**
 * Created by liyiguang on 2017/10/11.
 */
@ServiceModule("custom_scene")
@Component
@Slf4j
// TODO: 2017/10/24 shaobo
public class ObjectQuerySceneService {

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    InfraServiceFacade infraServiceFacade;

    @Autowired
    CustomSceneService customSceneService;

    @Autowired
    SearchTemplateLogicService searchTemplateLogicService;

    @Autowired
    private SceneLogicService sceneLogicService;

    @ServiceMethod("createCustomScene")
    public CreateCustomScene.Result createCustomScene(CreateCustomScene.Arg arg, ServiceContext context) {
        log.debug("Entering createCustomScene(sceneJson={})",
                arg.getScene_data());
        CustomSceneResult customSceneResult = customSceneService.create(arg.getScene_data(), context.getUser());
        return CreateCustomScene.Result.builder()
                .searchTemplate(customSceneResult.getSearchTemplate()).build();
    }

    @ServiceMethod("updateCustomScene")
    public UpdateCustomScene.Result updateCustomScene(UpdateCustomScene.Arg arg, ServiceContext context) {
        log.debug("Entering updateCustomScene(sceneJson={})", arg.getScene_data());
        CustomSceneResult customSceneResult = customSceneService.update(arg.getScene_data(), context.getUser());
        return UpdateCustomScene.Result.builder()
                .searchTemplate(customSceneResult.getSearchTemplate())
                .success(true).build();
    }

    @ServiceMethod("deleteCustomScene")
    public DeleteCustomScene.Result deleteCustomScene(DeleteCustomScene.Arg arg, ServiceContext context) {
        log.debug("Entering deleteCustomScene(sceneId={}, describeApiName={})",
                arg.getScene_id(), arg.getDescribe_api_name());
        CustomSceneResult customSceneResult = customSceneService.delete(arg.getScene_id(), arg.getDescribe_api_name(), context.getUser());
        return DeleteCustomScene.Result.builder()
                .searchTemplate(customSceneResult.getSearchTemplate())
                .success(true).build();
    }

    @ServiceMethod("findCustomSceneById")
    public FindCustomeSceneById.Result findCustomSceneById(FindCustomeSceneById.Arg arg, ServiceContext context) {
        log.debug("Entering findCustomSceneById(sceneId={}, describeApiName={})",
                arg.getScene_id(), arg.getDescribe_api_name());
        CustomSceneResult customSceneResult = customSceneService.findByIdWithMerge(arg.getScene_id(), arg.getDescribe_api_name(),
                arg.getScene_type(), context.getUser());
        // 补充基础数据权限label
        String baseScene = Optional.ofNullable(customSceneResult.getSearchTemplate().get(SearchTemplate.BASE_SCENE_API_NAME))
                .map(String::valueOf)
                .orElse(null);
        String label = sceneLogicService.fillBaseSceneLabel(
                baseScene, arg.getDescribe_api_name(), context.getUser(), null);
        customSceneResult.getSearchTemplate().put(SceneExt.BASE_SCENE_LABEL_API, label);
        return FindCustomeSceneById.Result.builder()
                .searchTemplate(customSceneResult.getSearchTemplate())
                .success(true).build();
    }

    @ServiceMethod("adjustCustomSceneOrder")
    public AdjustCustomSceneOrder.Result adjustCustomSceneOrder(AdjustCustomSceneOrder.Arg arg, ServiceContext context) {
        log.debug("Entering adjustCustomSceneOrder(sceneJson={})",
                arg.getScene_data());
        CustomSceneResult customSceneResult = customSceneService.adjustOrder(arg.getScene_data(), arg.getDescribe_api_name(), context.getUser());
        return AdjustCustomSceneOrder.Result.builder()
                .searchTemplate(customSceneResult.getSearchTemplate()).success(true).build();
    }

    /**
     * 自定义对象场景获取
     */
    @ServiceMethod("getTemplate")
    public GetTemplate.Result getTemplate(GetTemplate.Arg arg, ServiceContext context) {
        List<ISearchTemplate> templateList = searchTemplateLogicService.findByDescribeApiNameAndExtendAttribute(arg.getDescribeApiName(), arg.getExtendAttribute(), context.getUser());
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());
        SearchTemplateExt.handleOrderBy(templateList, describe);

        List<String> config = infraServiceFacade.findFilterFields(context.getUser(), arg.getDescribeApiName(), arg.getExtendAttribute());
        Optional<IFieldDescribe> masterDetail = describe.getFieldDescribes().stream()
                .filter(x -> IFieldType.MASTER_DETAIL.equals(x.getType())).findAny();
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(context.getUser(), arg.getDescribeApiName());

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        describeExt.changeIsIndexWithOuter(context.getUser());
        Set<String> toRemoveFields = getInvisibleFields(arg, describeExt);
        List<CommonFilterField.FilterField> fieldList = describeExt.getIndexedFields().stream()
                .filter(field -> !unauthorizedFields.contains(field.getApiName()))
                .filter(fieldDescribe -> !FieldDescribeExt.CANNOT_FILTER_FIELDS.contains(fieldDescribe.getApiName()))
                .filter(field -> (!masterDetail.isPresent() || !ObjectDataExt.RELEVANT_TEAM.equals(field.getApiName())))
                .filter(field -> !toRemoveFields.contains(field.getApiName()))
                .map(IFieldDescribe::getApiName)
                .map(fieldName -> CommonFilterField.FilterField.builder().fieldName(fieldName)
                        .isShow(config.contains(fieldName)).build())
                .collect(Collectors.toList());

        if (!Strings.isNullOrEmpty(context.getAppId())
                && context.getAppId().equals(AppIdMapping.appIdMapping.getOrDefault(PrmConstant.PRM_APP_ID, PrmConstant.PRM_APP_ID))) {
            fieldList.removeIf(r -> PrmConstant.unSupportIndexFields.contains(r.getFieldName()));
//            Set<String> removeApiNames = describeExt.getIndexedFields().stream()
//                    .filter(field -> PrmConstant.unSupportIndexFields.contains(field.getType())).map(field -> field.getApiName())
//                    .collect(Collectors.toSet());
//            if (CollectionUtils.notEmpty(removeApiNames)) {
//                fieldList.removeIf(x -> removeApiNames.contains(x.getFieldName()));
//            }
        }

        if (context.getUser().isOutUser() || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735)) {
            fieldList.removeIf(x -> describeExt.getDimensionFieldApiNames().contains(x.getFieldName()));
        }

        Map<String, Map<String, Object>> fieldInfos =
                Stream.concat(templateList.stream().flatMap(template -> CollectionUtils.nullToEmpty(template.getOrders()).stream().map(IOrderBy::getFieldName)),
                                fieldList.stream().filter(x -> BooleanUtils.isTrue(x.getIsShow())).map(CommonFilterField.FilterField::getFieldName))
                        .filter(fieldName -> describeExt.getFieldDescribeSilently(fieldName).isPresent())
                        .distinct()
                        .collect(Collectors.toMap(x -> x, describeExt::getSimpleFieldInfos));
        // 是否支持标签
        boolean supportTag = !describeExt.isBigObject() && infraServiceFacade.isSupportTag(describeExt.getApiName(), context.getUser());
        List<IButton> batchButton = getListBatchButton(context, describeExt);

        return GetTemplate.Result
                .builder()
                .templates(QueryTemplateDocument.ofList(templateList))
                .filterFields(sortByGivenOrder(fieldList, config, CommonFilterField.FilterField::getFieldName))
                .fieldInfos(fieldInfos)
                .supportTag(supportTag)
                .buttons(ButtonDocument.fromButtons(batchButton))
                .supportGeoQuery(CollectionUtils.notEmpty(describeExt.findGeoSearchField()))
                .build();
    }

    private Set<String> getInvisibleFields(GetTemplate.Arg arg, ObjectDescribeExt describeExt) {
        Set<String> toRemoveFields = invisibleFieldNameListForListLayout.getOrDefault(describeExt.getApiName(), Sets.newHashSet());
        Set<String> udobjToRemoveFields = invisibleFieldNameListForListLayout.getOrDefault(DefObjConstants.UDOBJ, Sets.newHashSet());
        Set<String> invisibleFieldList = invisibleFieldListFormObjectMap.getOrDefault(describeExt.getApiName(), Sets.newHashSet());
        Set<String> udobjInvisibleFieldList = invisibleFieldListFormObjectMap.getOrDefault(DefObjConstants.UDOBJ, Sets.newHashSet());
        toRemoveFields.addAll(invisibleFieldList);
        toRemoveFields.addAll(udobjToRemoveFields);
        toRemoveFields.addAll(udobjInvisibleFieldList);
        // ActiveRecordObj 筛选不支持 what_list_data 类型的字段(终端和 H5 需要处理)
        if (Utils.ACTIVE_RECORD_API_NAME.equals(arg.getDescribeApiName()) && RequestUtil.isMobileOrH5Request()) {
            List<String> whatListDataApiNames = describeExt.getWhatListDatas().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            toRemoveFields.addAll(whatListDataApiNames);
            // related_api_names 这个字段也不支持筛选
            toRemoveFields.add("related_api_names");
        }
        return toRemoveFields;
    }

    private List<IButton> getListBatchButton(ServiceContext context, ObjectDescribeExt describeExt) {
        // 移动端不支持批量操作的对象名单
        if (AppFrameworkConfig.isMobileListBlackObject(describeExt.getApiName())) {
            return Collections.emptyList();
        }
        List<IButton> buttonList = serviceFacade.findListBatchButton(describeExt, false, context.getUser());
        // 灰度企业、对象，查询列表页布局配置的批量按钮
        if (!RequestUtil.isMobileRequestBeforeVersion(VERSION_740) && AppFrameworkConfig.isGrayListLayout(context.getTenantId(), describeExt.getApiName())) {
            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), context.getAppId());
            ILayout listLayout = serviceFacade.getListLayoutWitchComponents(layoutContext,
                    describeExt, PageType.ListHeader, null);
            return ListLayoutExt.of(listLayout).getFirstListComponent()
                    .map(listComponentExt -> listComponentExt.filterButtonByUsePage(buttonList, ButtonUsePageType.ListBatch, IComponentInfo.PAGE_TYPE_LIST))
                    .orElse(Lists.newArrayList());
        }
        return buttonList;
    }

    @ServiceMethod("getOutTemplate")
    public GetOutTemplate.Result getOutTemplate(GetOutTemplate.Arg arg, ServiceContext context) {
        List<ISearchTemplate> templateList = searchTemplateLogicService.findOutSearchTemplate(arg.getDescribeApiName(), context.getUser(), arg.getExtendAttribute());

        return GetOutTemplate.Result
                .builder()
                .templates(QueryTemplateDocument.ofList(templateList))
                .build();
    }

    @ServiceMethod("createFieldListConfig")
    public CreateFieldList.Result createFieldListConfig(CreateFieldList.Arg arg, ServiceContext context) {
        List<IHeadField> headFields = arg.getFieldList().stream()
                .filter(Objects::nonNull)
                .map(HeadField::new).collect(Collectors.toList());
        CustomSceneResult fieldListConfig = customSceneService.createFieldListConfig(arg.getObjectDescribeApiName(), arg.getSceneType(), arg.getSceneId(),
                arg.getExtendAttribute(), headFields, context.getUser());
        DEBUGGING_LOGGER.info("createFieldListConfig success, tenantId:{}, apiName:{}, user:{}, searchTemplate:{}",
                context.getTenantId(), arg.getObjectDescribeApiName(), context.getUser(), JSON.toJSONString(fieldListConfig.getSearchTemplate()));
        return CreateFieldList.Result.success();
    }

    @ServiceMethod("createOrUpdateFieldWidthConfig")
    public CreateFieldList.Result createFieldWidthConfig(CreateFieldList.Arg arg, ServiceContext context) {
        if (StringUtils.isAnyEmpty(arg.getObjectDescribeApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        List<IHeadField> headFields = arg.getFieldList().stream()
                .filter(Objects::nonNull)
                .map(HeadField::new).collect(Collectors.toList());
        CustomSceneResult fieldWidthConfig = customSceneService.createOrUpdateFieldWidthConfig(arg.getObjectDescribeApiName(), arg.getSceneApiName(),
                arg.getSceneId(),
                arg.getExtendAttribute(), headFields, context.getUser());
        return CreateFieldList.Result.success();
    }

    @ServiceMethod("deleteFieldWidthConfig")
    public CustomFieldList.Delete.Result deleteFieldWidthConfig(CustomFieldList.Delete.Arg arg, ServiceContext context) {
        String whatApiName = arg.getWhatApiName();
        if (Strings.isNullOrEmpty(whatApiName)) {
            searchTemplateLogicService.wipeColumnWidth(context.getUser(), arg.getObjApiName(), arg.getExtendAttribute());
            return CustomFieldList.Delete.Result.succeed();
        }
        String extendAttribute = arg.getExtendAttribute();
        String sessionKey = arg.getSessionKey();
        if (!Strings.isNullOrEmpty(whatApiName) && !Strings.isNullOrEmpty(sessionKey)) {
            extendAttribute = String.format("%s_%s", whatApiName, sessionKey);
            whatApiName = null;
        }
        infraServiceFacade.wipePersonalSettings(context.getUser(), arg.getObjApiName(), whatApiName, extendAttribute);
        return CustomFieldList.Delete.Result.succeed();
    }

    @ServiceMethod("setDefaultScene")
    public UpdateCustomScene.Result setDefaultScene(UpdateCustomScene.Arg arg, ServiceContext context) {
        log.debug("Entering setDefaultScene(sceneJson={})", arg.getScene_data());
        CustomSceneResult customSceneResult = customSceneService.setDefaultScene(arg.getScene_data(), context.getUser());
        return UpdateCustomScene.Result.builder()
                .searchTemplate(customSceneResult.getSearchTemplate()).success(true).build();
    }

    @ServiceMethod("saveAs")
    public CreateCustomScene.Result saveAs(CreateCustomScene.Arg arg, ServiceContext context) {
        log.debug("Entering saveAs(sceneJson={})", arg.getScene_data());
        CustomSceneResult customSceneResult = customSceneService.saveAs(arg.getScene_data(), context.getUser());
        return CreateCustomScene.Result.builder().searchTemplate(customSceneResult.getSearchTemplate()).build();
    }

    @ServiceMethod("findBaseSceneList")
    public FindBaseScene.Result findBaseSceneList(FindBaseScene.Arg arg, ServiceContext context) {
        List<IScene> defaultScenes = sceneLogicService.findBaseScenes(arg.getDescribeApiName(),
                null, context.getUser());
        return FindBaseScene.Result.builder().baseScenes(defaultScenes).build();
    }

    @ServiceMethod("findFieldWidthConfig")
    public FindFieldWidthConfig.Result findFieldWidthConfig(FindFieldWidthConfig.Arg arg, ServiceContext context) {
        Map<String, List<Map>> fieldWidthConfigs = infraServiceFacade.findFieldWidthConfigs(context.getUser(), arg.getDescribeApiNames());
        return FindFieldWidthConfig.Result.builder().visibleFieldsWidth(fieldWidthConfigs).build();
    }

    @ServiceMethod("findSavedFilterFields")
    public FindFilterFields.Result findSavedFilterFields(FindFilterFields.Arg arg, ServiceContext context) {
        IObjectDescribe describe = serviceFacade.findObjectUseThreadLocalCache(context.getTenantId(), arg.getDescribeApiName());
        List<CommonFilterField.FilterField> filterFields = infraServiceFacade.findFilterFields(context.getUser(), describe,
                arg.getExtendAttribute(), arg.getWhatApiName(), null);
        return FindFilterFields.Result.builder().filterFields(filterFields).build();
    }

    @ServiceMethod("deleteFieldListConfig")
    public DeleteFilterFields.Result deleteFieldListConfig(DeleteFilterFields.Arg arg, ServiceContext context) {
        String whatApiName = arg.getWhatApiName();
        if (Strings.isNullOrEmpty(whatApiName)) {
            infraServiceFacade.deleteCustomForApiNameAndUserId(arg.getDescribeApiName(), arg.getSceneApiName(), context.getUser());
            return DeleteFilterFields.Result.builder().success(true).build();
        }

        String extendAttribute = arg.getExtendAttribute();
        String sessionKey = arg.getSessionKey();
        if (!Strings.isNullOrEmpty(whatApiName) && !Strings.isNullOrEmpty(sessionKey)) {
            extendAttribute = String.format("%s_%s", whatApiName, sessionKey);
            whatApiName = null;
        }
        infraServiceFacade.wipePersonalSettings(context.getUser(), arg.getDescribeApiName(), whatApiName, extendAttribute);
        return DeleteFilterFields.Result.builder().success(true).build();
    }
}
