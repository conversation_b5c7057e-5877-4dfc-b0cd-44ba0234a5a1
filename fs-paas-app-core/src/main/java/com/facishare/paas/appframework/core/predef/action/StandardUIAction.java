package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/06/12
 */
@Slf4j
public class StandardUIAction extends AbstractCustomButtonAction<StandardUIAction.Arg> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(arg.getButtonApiName());
    }

    @Override
    protected String getButtonApiName() {
        return arg.getButtonApiName();
    }

    @Override
    protected Map<String, Object> getArgs() {
        return CollectionUtils.nullToEmpty(arg.getArgs());
    }

    @Override
    protected boolean skipPreFunction() {
        if (arg.isSkipPreFunction()) {
            return true;
        }
        // TODO 增加灰度判断？
        return !ButtonExt.of(udefButton).isUIAction();
    }

    @Override
    protected boolean skipPreFunctionArgValidate() {
        return ButtonExt.of(udefButton).isUIAction();
    }

    @Override
    protected ButtonExecutor.Result startCustomButton() {
        ButtonExecutor.Arg buttonExecutorArg = ButtonExecutor.Arg.of(objectData, detailObjectData, getArgs());
        ButtonExecutorContext buttonExecutorContext = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(udefButton)
                .searchQuery(getSearchQuery(arg.getQueryParam()))
                .build();
        return infraServiceFacade.startCustomButton(buttonExecutorArg, buttonExecutorContext);
    }

    @Override
    protected boolean skipValidate() {
        ButtonExt buttonExt = ButtonExt.of(udefButton);
        if (buttonExt.isListNormalButton()) {
            return true;
        }
        if (buttonExt.isUseInCreateOrEdit()) {
            return true;
        }
        return super.skipValidate();
    }

    @Override
    protected boolean skipTriggerApprovalFlow() {
        return true;
    }

    protected void initObjectData() {
        ButtonExt buttonExt = ButtonExt.of(udefButton);
        if (!buttonExt.isUseInCreateOrEdit()) {
            super.initObjectData();
            return;
        }

        objectData = arg.getObjectData().toObjectData();
        // 设置 data 中的 apiName 和 tenantId
        objectData.setDescribeApiName(actionContext.getObjectApiName());
        objectData.setTenantId(actionContext.getTenantId());
        detailObjectData = initDetails();
        validateData();
    }

    private Map<String, List<IObjectData>> initDetails() {
        if (CollectionUtils.empty(arg.getDetails())) {
            return Maps.newHashMap();
        }

        Map<String, List<IObjectData>> result = Maps.newHashMap();
        arg.getDetails().forEach((apiName, details) -> {
            LongAdder longAdder = new LongAdder();
            // 这里需要生成数据编号，供后续自定义组件回填数据，触发计算时使用
            List<IObjectData> dataList = details.stream().map(ObjectDataDocument::toObjectData)
                    .peek(it -> {
                        // 设置data中的 apiName 和 tenantId
                        it.setDescribeApiName(apiName);
                        it.setTenantId(actionContext.getTenantId());
                        it.set(ObjectDataExt.MARK_API_NAME, longAdder.toString());
                        longAdder.increment();
                    }).collect(Collectors.toList());

            result.put(apiName, dataList);
        });
        return result;
    }

    private void validateData() {
        if (Strings.isNullOrEmpty(arg.getObjectDataId())) {
            return;
        }
        if (!Objects.equals(objectData.getId(), arg.getObjectDataId())) {
            log.warn("param dataId fail, ei:{}, describeApiName:{}, arg:{}", actionContext.getTenantId(),
                    actionContext.getObjectApiName(), JacksonUtils.toJson(arg));
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    @Override
    protected String getObjectDataId() {
        return arg.getObjectDataId();
    }

    @Override
    protected boolean skipLockValidate() {
        return false;
    }

    @Override
    protected CustomButtonAction.Result after(Arg arg, CustomButtonAction.Result result) {
        if (Objects.nonNull(udefButton) && ButtonExt.of(udefButton).isUIAction()
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BUTTON_CLICK_RECORD_EI, actionContext.getTenantId())) {
            if (Objects.nonNull(objectData) && Objects.nonNull(objectDescribe)) {
                String msg = I18NExt.text(I18NKey.CUSTOM_BUTTON_CLICK, getButtonApiName());
                serviceFacade.logWithInternationalCustomMessage(actionContext.getUser(), EventType.MODIFY,
                        ActionType.Modify, objectDescribe, objectData, msg,
                        InternationalItem.builder()
                                .internationalKey(I18NKey.CUSTOM_BUTTON_CLICK)
                                .defaultInternationalValue(msg)
                                .internationalParameters(Lists.newArrayList("mockKey"))
                                .defaultParameterValues(new HashMap<String, String>() {{
                                    put("mockKey", getButtonApiName());
                                }})
                                .build()
                );
            }
        }
        return super.after(arg, result);
    }

    @Data
    public static class Arg {
        private String objectDataId;

        private ObjectDataDocument args;

        private String buttonApiName;

        private ObjectDataDocument objectData;

        private Map<String, List<ObjectDataDocument>> details;

        private String actionPage;

        private QueryParam queryParam;

        private boolean skipPreFunction = false;

    }
}
