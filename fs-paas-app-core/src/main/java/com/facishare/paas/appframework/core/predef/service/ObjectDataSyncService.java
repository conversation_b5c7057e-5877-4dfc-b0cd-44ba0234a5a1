package com.facishare.paas.appframework.core.predef.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.datasync.SyncField;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/03/04
 */
@Slf4j
@Service
@ServiceModule("data_sync")
public class ObjectDataSyncService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private IMtResourceService mtResourceService;
    @Autowired
    private ManageGroupService manageGroupService;

    @ServiceMethod("sync_field")
    public SyncField.Result syncField(SyncField.Arg arg, ServiceContext context) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#syncField");
        if (!AppFrameworkConfig.isSyncFieldGrayEi(context.getTenantId())) {
            throw new ValidateException("unSupport tenant!");
        }
        if (CollectionUtils.empty(arg.getTenantIds()) || CollectionUtils.empty(arg.getFieldApiNames())) {
            return SyncField.Result.builder().build();
        }
        List<IFieldDescribe> copyFieldDescribe = getCopyFieldDescribe(arg.getDescribeApiName()
                , Lists.newArrayList(arg.getFieldApiNames()), context.getTenantId());
        stopWatch.lap("getCopyFieldDescribe");
        List<SyncField.SyncErrorInfo> syncResult = Lists.newArrayList();

        // 复制上游描述到下游需求
        context.setAttribute(RequestContext.UPSTREAM_COPY_DESCRIBE, true);

        for (String tenantId : arg.getTenantIds()) {
            try {
                dealWithOne(tenantId, arg.getDescribeApiName(), copyFieldDescribe, arg.isLockDownstram(),
                        context.getTenantId());
            } catch (AppBusinessException e) {
                log.warn("syncField failed! sourceEi:{}, ei:{}, describeApiName:{}", context.getTenantId(), tenantId,
                        arg.getDescribeApiName(), e);
                syncResult.add(SyncField.SyncErrorInfo.of(tenantId, SyncField.SyncError.APP_BUSINESS_ERROR));
            } catch (Exception e) {
                log.error("syncField error! sourceEi:{}, ei:{}, describeApiName:{}", context.getTenantId(), tenantId,
                        arg.getDescribeApiName(), e);
                syncResult.add(SyncField.SyncErrorInfo.of(tenantId, SyncField.SyncError.SYSTEM_ERROR));
            }
        }
        stopWatch.logSlow(1000);
        return SyncField.Result.builder().errorInfo(syncResult).build();
    }

    private List<IFieldDescribe> getCopyFieldDescribe(String describeApiName, List<String> fieldApiNames,
                                                      String tenantId) {
        IObjectDescribe describe = describeLogicService.findObject(tenantId, describeApiName);
        return ObjectDescribeExt.of(describe).getFieldByApiNames(fieldApiNames).stream()
                .map(FieldDescribeExt::copyToSync)
                .collect(Collectors.toList());
    }

    private void dealWithOne(String tenantId, String describeApiName,
                             Collection<IFieldDescribe> fieldDescribes,
                             boolean lockDownstream, String sourceValue) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#dealWithOne" + tenantId);
        try {
            if (!licenseService.isOpenCRM(tenantId)) {
                throw new ValidateException("tenant not exit!");
            }
            IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, describeApiName);
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            // 如果是客户对象，且上游是开通组织，下游是未开通组织，需要去掉描述中的inherit_type
            if (Utils.ACCOUNT_API_NAME.equals(describeApiName)
                    && manageGroupService.isOpenOrganization(sourceValue) && !manageGroupService.isOpenOrganization(tenantId)) {
                fieldDescribes = fieldDescribes.stream()
                        .map(IFieldDescribe::copy)
                        .peek(it -> FieldDescribeExt.of(it).remove(IFieldDescribe.INHERIT_TYPE))
                        .collect(Collectors.toList());
            }
            fieldDescribes.forEach(fieldDescribe -> {
                Optional<FieldDescribeExt> fieldDescribeExtOpt = describeExt.getFieldDescribeSilently(fieldDescribe.getApiName())
                        .map(FieldDescribeExt::of);
                // 存在的字段修改描述、不存在的字段追加描述
                if (fieldDescribeExtOpt.isPresent()) {
                    fieldDescribeExtOpt.ifPresent(fieldDescribeExt -> fieldDescribeExt.mergeFrom(fieldDescribe));
                } else {
                    objectDescribe.addFieldDescribe(fieldDescribe);
                }
            });
            stopWatch.lap("findObject");
            String controlLevel = lockDownstream ? MtResource.CONTROL_LEVEL_UNCHANGEABLE : MtResource.CONTROL_LEVEL_CHANGEABLE;
            List<String> fieldNames = fieldDescribes.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            mtResourceService.updateDescribeAndModifyResource(tenantId, objectDescribe, fieldNames,
                    MtResource.SOURCE_TYPE_CONNECTION_SETTINGS, sourceValue, controlLevel);
            stopWatch.lap("updateDescribeAndModifyResource");
        } finally {
            stopWatch.logSlow(1000);
        }
    }
}
