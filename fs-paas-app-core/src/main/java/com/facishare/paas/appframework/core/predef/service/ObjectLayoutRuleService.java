package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.core.predef.service.dto.layoutrule.*;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.LayoutRuleExt;
import com.facishare.paas.appframework.metadata.LayoutRuleLogicService;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@ServiceModule("layout_rule")
@Component
public class ObjectLayoutRuleService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private LayoutRuleLogicService layoutRuleLogicService;

    @ServiceMethod("createLayoutRule")
    public CreateLayoutRule.Result createLayoutRule(CreateLayoutRule.Arg arg, ServiceContext context) {
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo(arg.getLayoutRule());
        layoutRuleLogicService.createLayoutRule(context.getUser(), layoutRuleInfo);
        return CreateLayoutRule.Result.builder().success(true).build();
    }

    @ServiceMethod("updateLayoutRule")
    public UpdateLayoutRule.Result updateLayoutRule(UpdateLayoutRule.Arg arg, ServiceContext context) {
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo(arg.getLayoutRule());
        layoutRuleLogicService.updateLayoutRule(context.getUser(), layoutRuleInfo);
        return UpdateLayoutRule.Result.builder().success(true).build();
    }

    @ServiceMethod("enableLayoutRule")
    public EnableLayoutRule.Result enableLayoutRule(EnableLayoutRule.Arg arg, ServiceContext context) {
        layoutRuleLogicService.enableLayoutRule(context.getUser(), arg.getLayoutRuleApiName(), arg.getDescribeApiName());
        return EnableLayoutRule.Result.builder().success(true).build();
    }

    @ServiceMethod("disableLayoutRule")
    public DisableLayoutRule.Result disableLayoutRule(DisableLayoutRule.Arg arg, ServiceContext context) {
        layoutRuleLogicService.disableLayoutRule(context.getUser(), arg.getLayoutRuleApiName());
        return DisableLayoutRule.Result.builder().success(true).build();
    }

    @ServiceMethod("deleteLayoutRule")
    public DeleteLayoutRule.Result deleteLayoutRule(DeleteLayoutRule.Arg arg, ServiceContext context) {
        layoutRuleLogicService.deleteLayoutRule(context.getUser(), arg.getLayoutRuleApiName());
        return DeleteLayoutRule.Result.builder().success(true).build();
    }

    @ServiceMethod("findRuleRelatedInfo")
    public FindRuleRelatedInfo.Result findRuleRelatedInfo(FindRuleRelatedInfo.Arg arg, ServiceContext context) {
        //对象描述
        IObjectDescribe describe = null;
        if (arg.includeDescribe()) {
            describe = serviceFacade.findObject(context.getTenantId(), arg.getDescribeApiName());
        }

        //布局列表
        List<ILayout> layoutList = null;
        if (arg.includeLayout()) {
            layoutList = serviceFacade.findByTypesIncludeFlowLayout(LayoutLogicService.LayoutContext.of(context.getUser()),
                    arg.getDescribeApiName(), Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT), arg.getNeedLayoutLang());
        }

        //布局规则列表
        List<LayoutRuleInfo> layoutRuleList = layoutRuleLogicService.findLayoutRuleByDescribe(context.getUser(), arg.getDescribeApiName());
        layoutRuleList.stream().filter(x -> Objects.isNull(x.getType())).forEach(x -> x.setType("field"));

        //开启了新建编辑页布局以后，布局规则列表不展示原来详情页布局上的页面控制级别的布局规则
        if (CollectionUtils.notEmpty(layoutRuleList) && serviceFacade.isEditLayoutEnable(context.getTenantId(), arg.getDescribeApiName(), false)) {
            List<String> layoutApiNames = layoutRuleList.stream().map(LayoutRuleInfo::getLayoutApiName).distinct().collect(Collectors.toList());
            Map<String, Layout> layoutMap = serviceFacade.findLayoutByApiNames(context.getTenantId(), layoutApiNames, arg.getDescribeApiName());
            layoutRuleList.removeIf(x -> LayoutRuleExt.of(x).isPageTypeRule()
                    && layoutMap.containsKey(x.getLayoutApiName())
                    && LayoutExt.of(layoutMap.get(x.getLayoutApiName())).isDetailLayout()
                    && !LayoutExt.of(layoutMap.get(x.getLayoutApiName())).isFlowLayout());
        }
        ManageGroup layoutManageGroup = serviceFacade.queryLayoutManageGroup(context.getUser(), arg.getDescribeApiName(), arg.getSourceInfo());
        return FindRuleRelatedInfo.Result.builder()
                .describe(ObjectDescribeDocument.of(describe))
                .layoutList(LayoutDocument.ofList(layoutList))
                .layoutRuleList(LayoutRuleDocument.ofList(layoutRuleList))
                .layoutManageGroup(ManageGroupDTO.of(layoutManageGroup))
                .build();

    }

    @ServiceMethod("findLayoutRuleInfo")
    public FindLayoutRuleInfo.Result findLayoutRuleInfo(FindLayoutRuleInfo.Arg arg, ServiceContext context) {
        //布局规则列表
        LayoutRuleInfo layoutRule = layoutRuleLogicService.findLayoutRuleByApiName(context.getUser(), arg.getLayoutRuleApiName());

        return FindLayoutRuleInfo.Result.builder()
                .layoutRule(LayoutRuleDocument.of(layoutRule))
                .build();

    }

}
