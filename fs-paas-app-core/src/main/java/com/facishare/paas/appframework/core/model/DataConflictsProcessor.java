package com.facishare.paas.appframework.core.model;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction.DataConflicts;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.RichTextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2022/10/12.
 */
@Slf4j
@Builder
public class DataConflictsProcessor {

    private SaveActionServiceFacade saveActionServiceFacade;

    //=======输入参数======
    private User user;
    private String seriesId;
    private boolean isDataVersionConflict;
    private Set<String> versionConflictDetails;
    private IObjectDescribe masterDescribe;
    private Map<String, IObjectDescribe> describeMap;
    private IObjectData originalData;
    private Map<String, List<IObjectData>> originalDetailDataMap;
    private IObjectData dbMasterData;
    private Map<String, List<IObjectData>> dbDetailDataMap;
    private IObjectData objectData;
    private Map<String, List<IObjectData>> detailDataMap;

    //=======输出参数======
    private AtomicBoolean masterChanged;
    private AtomicBoolean detailChanged;
    private DataConflicts dataConflicts;
    private Map<String, Map<String, DataConflicts>> detailConflicts;

    public Result process() {
        prepare();
        //处理主数据冲突
        processMasterConflicts();
        //处理从数据编辑冲突
        processDetailEditConflicts();
        //处理从数据新建或删除冲突
        processDetailAddOrDeletedConflicts();
        return buildResult();
    }

    private void processMasterConflicts() {
        if (!isDataVersionConflict || Objects.isNull(originalData)) {
            return;
        }
        dataConflicts = processEditConflicts(masterDescribe, originalData, dbMasterData, objectData);
    }

    private void processDetailEditConflicts() {
        if (CollectionUtils.empty(versionConflictDetails) || CollectionUtils.empty(originalDetailDataMap)) {
            return;
        }
        detailConflicts = Maps.newHashMap();
        versionConflictDetails.forEach(detailApiName -> {
            if (!originalDetailDataMap.containsKey(detailApiName)) {
                return;
            }
            IObjectDescribe describe = describeMap.get(detailApiName);
            List<IObjectData> dataListV2 = detailDataMap.get(detailApiName);
            Map<String, IObjectData> detailMapV0 = originalDetailDataMap.get(detailApiName).stream()
                    .collect(Collectors.toMap(DBRecord::getId, x -> x));
            Map<String, IObjectData> detailMapV1 = dbDetailDataMap.get(detailApiName).stream()
                    .collect(Collectors.toMap(DBRecord::getId, x -> x));
            dataListV2.stream()
                    .filter(dataV2 -> ObjectDataExt.of(dataV2).hasId())
                    .filter(dataV2 -> detailMapV1.containsKey(dataV2.getId()))
                    .filter(dataV2 -> detailMapV0.containsKey(dataV2.getId()))
                    .forEach(dataV2 -> {
                        IObjectData dataV1 = detailMapV1.get(dataV2.getId());
                        IObjectData dataV0 = detailMapV0.get(dataV2.getId());
                        DataConflicts conflicts = processEditConflicts(describe, dataV0, dataV1, dataV2);
                        if (Objects.nonNull(conflicts)) {
                            detailConflicts.computeIfAbsent(detailApiName, k -> Maps.newHashMap()).put(dataV2.getId(), conflicts);
                        }
                    });
        });
    }

    private void processDetailAddOrDeletedConflicts() {
        if (CollectionUtils.empty(originalDetailDataMap)) {
            return;
        }
        Lists.newArrayList(detailDataMap.keySet()).forEach(detailApiName -> {
            if (!originalDetailDataMap.containsKey(detailApiName)) {
                return;
            }
            List<IObjectData> dataListV1 = CollectionUtils.nullToEmpty(dbDetailDataMap.get(detailApiName));
            //将数据库中查到的从对象按照order_by排一下序，没有order_by的默认排最后
            ObjectDataExt.sortByOrderBy(dataListV1);
            List<IObjectData> dataListV2 = detailDataMap.get(detailApiName);
            Map<String, IObjectData> detailMapV0 = originalDetailDataMap.get(detailApiName).stream()
                    .collect(Collectors.toMap(DBRecord::getId, x -> x));
            Map<String, IObjectData> detailMapV1 = dataListV1.stream()
                    .collect(Collectors.toMap(DBRecord::getId, x -> x));
            Map<String, IObjectData> detailMapV2 = dataListV2.stream().filter(x -> ObjectDataExt.of(x).hasId())
                    .collect(Collectors.toMap(DBRecord::getId, x -> x));

            //和V0对比，V1删除的数据，在V2也要删除
            Set<String> idsToDelete = detailMapV0.keySet().stream()
                    .filter(id -> !detailMapV1.containsKey(id))
                    .collect(Collectors.toSet());
            if (CollectionUtils.notEmpty(idsToDelete)) {
                log.warn("apiName:{},idsToDelete:{}", detailApiName, idsToDelete);
                dataListV2.removeIf(x -> {
                    if (idsToDelete.contains(x.getId())) {
                        detailChanged.set(true);
                        return true;
                    }
                    return false;
                });
                detailMapV2.keySet().removeAll(idsToDelete);
            }

            //和V0对比，V1新增的数据，在V2也要新增
            List<IObjectData> dataListToAdd = dataListV1.stream()
                    .filter(x -> !detailMapV0.containsKey(x.getId()))
                    .filter(x -> !detailMapV2.containsKey(x.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(dataListToAdd)) {
                log.warn("apiName:{},dataListToAdd:{}", detailApiName, dataListToAdd);
                //校验本次操作是否是继续保存并且第一次保存时数据已经入库
                saveActionServiceFacade.checkIfUpdateKeyLocked(seriesId, objectData.getId(), masterDescribe.getApiName(), user);
                detailChanged.set(true);
            }
            dataListToAdd.forEach(dataV1 -> {
                //将v1版本数据拷贝之后再加入v2版本List，防止后续v1数据被修改
                IObjectData dataV1Copy = ObjectDataExt.of(dataV1).copy();
                int index = dataListV1.indexOf(dataV1);
                if (index == 0) {
                    dataListV2.add(0, dataV1Copy);
                } else {
                    IObjectData prevDataV1 = dataListV1.get(index - 1);
                    if (detailMapV2.containsKey(prevDataV1.getId())) {
                        index = dataListV2.indexOf(detailMapV2.get(prevDataV1.getId()));
                        dataListV2.add(index + 1, dataV1Copy);
                    } else {
                        dataListV2.add(dataV1Copy);
                    }
                }
            });
        });
    }

    private void prepare() {
        masterChanged = new AtomicBoolean(false);
        detailChanged = new AtomicBoolean(false);
    }

    private DataConflicts processEditConflicts(IObjectDescribe describe, IObjectData dataV0, IObjectData dataV1, IObjectData dataV2) {
        if (Objects.isNull(dataV0) || Objects.isNull(dataV1)) {
            return null;
        }
        DataConflicts conflicts = null;
        Map<String, Object> diffV1V2 = diffData(describe, dataV1, dataV2);
        if (CollectionUtils.notEmpty(diffV1V2)) {
            modifyObjectDataByDbData(dataV0, dataV1);
            Map<String, Object> diffV0V1 = diffData(describe, dataV0, dataV1);
            Map<String, Object> diffV0V2 = diffData(describe, dataV0, dataV2);
            log.info("apiName:{},id:{},diffV1V2:{},diffV0V1:{},diffV0V2:{}", describe.getApiName(), dataV1.getId(),
                    diffV1V2, diffV0V1, diffV0V2);
            List<String> fields = Lists.newArrayList(diffV1V2.keySet()).stream()
                    .filter(x -> diffV0V1.containsKey(x) && diffV0V2.containsKey(x))
                    .collect(Collectors.toList());
            Map<String, Object> lastData = ObjectDataExt.of(dataV1).toMap(fields);
            Map<String, Object> currentData = ObjectDataExt.of(dataV2).toMap(fields);

            //和V0对比，V1和V2都改了的字段提示冲突
            if (CollectionUtils.notEmpty(fields)) {
                //fields只下发对象字段apiName，去掉__o和其他扩展字段
                fields = fields.stream().filter(describe::containsField).collect(Collectors.toList());
                //补充富文本字段的__o
                fillExtraForRichText(describe, dataV1, dataV2, fields, lastData, currentData);
                conflicts = DataConflicts.builder()
                        .fields(fields)
                        .lastData(ObjectDataDocument.of(lastData))
                        .currentData(ObjectDataDocument.of(currentData))
                        .build();
            }

            //和V0对比，V2没改而V1改了的字段，使用V1的值
            diffV1V2.keySet().stream()
                    .filter(x -> !diffV0V2.containsKey(x) && diffV0V1.containsKey(x))
                    .forEach(x -> {
                        dataV2.set(x, dataV1.get(x));
                        if (describe.getApiName().equals(masterDescribe.getApiName())) {
                            masterChanged.set(true);
                        } else {
                            detailChanged.set(true);
                        }
                    });
        }

        return conflicts;
    }

    private void fillExtraForRichText(IObjectDescribe describe, IObjectData oldData, IObjectData newData,
                                      List<String> fields, Map<String, Object> lastData, Map<String, Object> currentData) {
        fields.stream()
                .filter(describe::containsField)
                .filter(x -> RichTextExt.isProcessableRichText(describe.getFieldDescribe(x)))
                .forEach(x -> {
                    String extraFieldApiName = RichTextExt.getRichTextAbstractName(x);
                    if (!lastData.containsKey(extraFieldApiName)) {
                        lastData.put(extraFieldApiName, oldData.get(extraFieldApiName));
                    }
                    if (!currentData.containsKey(extraFieldApiName)) {
                        currentData.put(extraFieldApiName, newData.get(extraFieldApiName));
                    }
                });
    }

    private Map<String, Object> diffData(IObjectDescribe describe, IObjectData oldData, IObjectData newData) {
        Map<String, Object> diffMap = ObjectDataExt.of(oldData).diff(newData, describe);
        ObjectDataExt.of(diffMap).removeCalculateField(describe);
        //order_by字段不需要展示冲突
        diffMap.remove(IObjectData.ORDER_BY);
        return diffMap;
    }

    private Result buildResult() {
        return Result.builder()
                .masterChanged(masterChanged.get())
                .detailChanged(detailChanged.get())
                .dataConflicts(dataConflicts)
                .detailConflicts(detailConflicts)
                .build();
    }

    private void modifyObjectDataByDbData(IObjectData newData, IObjectData dbData) {
        IObjectDescribe describe = describeMap.get(dbData.getDescribeApiName());
        Map<String, String> selectOneDataTypeMap = describe.getFieldDescribes().stream()
                .filter(a -> Objects.equals(a.getType(), IFieldType.SELECT_ONE))
                .collect(Collectors.toMap(x -> x.getApiName() + "__o", IFieldDescribe::getApiName));
        Map<String, String> selectManyDataTypeMap = describe.getFieldDescribes().stream()
                .filter(a -> Objects.equals(a.getType(), IFieldType.SELECT_MANY))
                .collect(Collectors.toMap(x -> x.getApiName() + "__o", IFieldDescribe::getApiName));

        ObjectDataExt.of(dbData).toMap().keySet().forEach(x -> {
            if (ObjectDataExt.of(newData).toMap().containsKey(x)) {
                return;
            }
            Object newDataSelectOne = newData.get(selectOneDataTypeMap.get(x));
            if (Objects.nonNull(selectOneDataTypeMap.get(x)) && Objects.nonNull(newDataSelectOne)
                    && !SelectOne.OPTION_OTHER_VALUE.equals(newDataSelectOne)) {
                return;
            }
            if (Objects.nonNull(selectManyDataTypeMap.get(x)) && Objects.nonNull(newData.get(selectManyDataTypeMap.get(x)))) {
                List<String> selectManyValue = (List<String>) newData.get(selectManyDataTypeMap.get(x));
                if (!selectManyValue.contains(SelectOne.OPTION_OTHER_VALUE)) {
                    return;
                }
            }
            newData.set(x, dbData.get(x));
        });
        //同步一下生命状态和锁定状态
        ObjectDataExt.of(newData).setLifeStatus(ObjectDataExt.of(dbData).getLifeStatus());
        ObjectDataExt.of(newData).setLockStatus(ObjectDataExt.of(dbData).getLockStatus());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Result {
        private boolean masterChanged;
        private boolean detailChanged;
        private DataConflicts dataConflicts;
        private Map<String, Map<String, DataConflicts>> detailConflicts;
    }
}
