package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.button.action.ActionExecutorType;
import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.action.UpdateFieldAction;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ButtonActionType;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.PostActionService;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

import static com.facishare.paas.appframework.metadata.ButtonExt.ACTION_SOURCE_FLOW;

/**
 * Created by zhouwr on 2018/1/19
 */
public class CustomButtonAction extends AbstractCustomButtonAction<CustomButtonAction.Arg> {
    private static final List<String> CUSTOM_BUTTON_ACTION__WHITE_LIST = Lists.newArrayList(
            "button_convertAccount__c",
            "button_convertContact__c",
            "button_convertOpportunity__c",
            "button_convertNewOpportunityObj__c",
            "button_convertPartnerContact__c",
            "button_convertPartner__c",
            "button_procurementConvertAccount__c",
            "button_procurementConvertLeads__c",
            "button_friendConvertAccount__c",
            "button_friendConvertLeads__c",
            "button_friendConvertContact__c"
    );
    private static final List<String> CUSTOM_BUTTON_OBJ_WHITE_LIST = Lists.newArrayList(
            ObjectAPINameMapping.BizQuery.getApiName()
    );

    @Override
    protected boolean skipPreFunctionArgValidate() {
        return ButtonExt.of(udefButton).isUIAction();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (isObjInWhiteList() || isActionInWhiteList()) {
            return null;
        }
        return Lists.newArrayList(actionContext.getActionCode());
    }

    @Override
    protected List<ValidatorInfo> getSkippedValidatorList() {
        return CollectionUtils.nullToEmpty(arg.getSkippedValidatorList());
    }

    private boolean isActionInWhiteList() {
        return CUSTOM_BUTTON_ACTION__WHITE_LIST.contains(actionContext.getActionCode());
    }

    private boolean isObjInWhiteList() {
        return CUSTOM_BUTTON_OBJ_WHITE_LIST.contains(actionContext.getObjectApiName());
    }

    @Override
    protected String getObjectDataId() {
        return arg.getObjectDataId();
    }


    @Override
    protected ButtonExecutor.Result startCustomButton() {
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.of(objectData, detailObjectData, getArgs());
        executorArg.setSkippedValidateRule(getSkippedValidatorList().stream().anyMatch(x -> ValidatorType.BUTTON_VALIDATE.getCode().equals(x.getValidatorType())));
        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(udefButton)
                .searchQuery(getSearchQuery(arg.getQueryParam()))
                .build();
        return infraServiceFacade.startCustomButton(executorArg, executorContext);

    }

    @Override
    protected void triggerValidateRule() {
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.of(objectData, detailObjectData, getArgs());
        executorArg.setSkippedValidateRule(getSkippedValidatorList().stream().anyMatch(x -> ValidatorType.BUTTON_VALIDATE.getCode().equals(x.getValidatorType())));
        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(udefButton)
                .actionSource(arg.getActionSource())
                .searchQuery(getSearchQuery(arg.getQueryParam()))
                .build();
        ButtonExecutor.Result executorResult = infraServiceFacade.triggerValidateRule(executorArg, executorContext);

        if (Objects.isNull(executorResult.getValidateRuleResult())) {
            return;
        }
        RuleResult validateRuleResult = executorResult.getValidateRuleResult();
        ValidationResultDocument validationResultDocument = ValidationResultDocument.builder()
                .block(validateRuleResult.isEnableBlocking())
                .multiMessage(MultiMessage.builder().blockMessages(validateRuleResult.getBlockMessages())
                        .nonBlockMessages(validateRuleResult.getNonBlockMessages()).build())
                .skippedValidatorList(Lists.newArrayList(ValidatorInfo.builder().validatorType(ValidatorType.BUTTON_VALIDATE.getCode()).build()))
                .build();
        CustomButtonAction.Result result = buildValidationResult(validationResultDocument);
        if (ACTION_SOURCE_FLOW.equals(arg.getActionSource())) {
            String msg = I18NExt.text(I18NKey.VALIDATE_RULE_BLOCK_INFO) + ":" +
                    Optional.of(validationResultDocument.getMultiMessage()).map(MultiMessage::getBlockMessages).map(Object::toString).orElse("");
            throw new ValidateException(msg);
        } else {
            throw new AcceptableValidateException(result);
        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateFieldOnlyRead(arg);
    }


    private void validateFieldOnlyRead(Arg arg) {
        if (AppFrameworkConfig.unSupportExecuteButtonBeforeValidate(actionContext.getObjectApiName()) || Objects.isNull(objectData) || Objects.isNull(udefButton)) {
            return;
        }
        PostActionService actionService = serviceFacade.getBean(PostActionService.class);
        List<IUdefAction> actionList = actionService.findActionList(actionContext.getUser(), udefButton, objectDescribe.getApiName());
        ILayout layout = serviceFacade.findObjectLayoutWithType(buildLayoutContext(), objectData.getRecordType(), objectDescribe, LayoutTypes.EDIT, objectData);

        UpdateFieldAction updateFieldAction = serviceFacade.getBean(UpdateFieldAction.class);
        for (IUdefAction action : actionList) {
            ActionExecutorType actionType = ActionExecutorType.getActionType(ButtonActionType.of(action.getActionType()), "");
            if (ButtonActionType.Updates != actionType.getActionType()) {
                continue;
            }
            updateFieldAction.validateByButtonParam(actionContext.getUser(), action, (ObjectData) objectData, objectDescribe, udefButton, layout);
        }
    }

    @Override
    protected void init() {
        if (!Strings.isNullOrEmpty(arg.getIndustryDatasource())) {
            actionContext.setAttribute(RequestContext.Attributes.INDUSTRY_DATASOURCE, arg.getIndustryDatasource());
        }
        if (!Strings.isNullOrEmpty(arg.getIndustrySupplyTag())) {
            actionContext.setAttribute(RequestContext.Attributes.INDUSTRY_SUPPLY_TAG, arg.getIndustrySupplyTag());
        }
        super.init();
    }

    @Override
    protected boolean skipPreFunction() {
        if (arg.isSkipPreFunction()) {
            return true;
        }
        return !(ButtonExt.of(udefButton).isCommonButton() || ButtonExt.of(udefButton).isUIAction());
    }

    @Override
    protected boolean skipTriggerApprovalFlow() {
        if (arg.isSkipTriggerApprovalFlow() || isPublicObject()) {
            return true;
        }
        return ButtonExt.of(udefButton).isUIAction();
    }

    private boolean isPublicObject() {
        return Objects.nonNull(objectDescribe) && objectDescribe.isPublicObject();
    }

    @Override
    protected String getButtonApiName() {
        return actionContext.getActionCode();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipCheckButtonWheres() || super.skipCheckButtonConditions();
    }

    @Override
    protected Map<String, Object> getArgs() {
        return CollectionUtils.nullToEmpty(arg.getArgs());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (Objects.nonNull(udefButton)
                && !ACTION_SOURCE_FLOW.equals(arg.getActionSource())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BUTTON_CLICK_RECORD_EI, actionContext.getTenantId())) {
            ButtonExt button = ButtonExt.of(udefButton);
            if (button.isCommonButton()) {
                if (Objects.nonNull(objectData) && Objects.nonNull(objectDescribe)) {
                    String msg = I18NExt.text(I18NKey.CUSTOM_BUTTON_CLICK, getButtonApiName());
                    serviceFacade.logWithInternationalCustomMessage(actionContext.getUser(), EventType.MODIFY,
                            ActionType.Modify, objectDescribe, objectData, msg,
                            InternationalItem.builder()
                                    .internationalKey(I18NKey.CUSTOM_BUTTON_CLICK)
                                    .defaultInternationalValue(msg)
                                    .internationalParameters(Lists.newArrayList("mockKey"))
                                    .defaultParameterValues(new HashMap<String, String>() {{
                                        put("mockKey", getButtonApiName());
                                    }})
                                    .build()
                    );
                }
            }
        }
        return super.after(arg, result);
    }

    @Override
    protected boolean skipLockValidate() {
        return arg.isSkipLockValidate();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {
        @JSONField(name = "M1")
        private String objectDataId;

        @JSONField(name = "M2")
        private ObjectDataDocument args;
        private boolean skipTriggerApprovalFlow = false;
        private boolean skipPreFunction = false;
        private boolean skipCheckButtonWheres;

        /**
         * 转换按钮，指定工商查询接口的数据源
         * <p>
         * 邓白氏 DengBaiShi
         * 天眼查 TianYanCha
         */
        private String industryDatasource;
        /**
         * 转换按钮、工商查询接口区分补充查询和高级查询
         */
        private String industrySupplyTag;

        private QueryParam queryParam;

        private boolean skipLockValidate;

        private List<ValidatorInfo> skippedValidatorList;

        private String actionSource;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        private ObjectDataDocument objectData;

        @JSONField(name = "M2")
        private Map<String, List<ObjectDataDocument>> details;

        @JSONField(name = "M3")
        private String targetDescribeApiName;

        @JSONField(name = "M4")
        private boolean hasReturnValue;

        @JSONField(name = "M5")
        private Object returnValue;

        @JSONField(name = "M6")
        private String returnType;

        private BaseObjectSaveAction.ValidationMessage validationMessage;

        //各种校验器返回的校验结果（支持非阻断提示信息）
        private ValidationResultDocument validationResult;
    }
}
