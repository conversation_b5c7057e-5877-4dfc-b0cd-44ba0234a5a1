package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ButtonConfigDocument;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON>yiguang on 2017/10/11.
 */
public interface FindButtonList {

    @Data
    class Arg {
        @JSONField(name = "M1")
        private String describeApiName;
        private boolean excludeUIButton;
        private boolean excludeRedirectType;
    }
    @Data
    class Result {
        @JSONField(name = "M1")
        private List<ButtonDocument> buttonList;
        private List<ButtonConfigDocument> buttonConfigList;
        private Boolean supportValidateRule;
    }
}
