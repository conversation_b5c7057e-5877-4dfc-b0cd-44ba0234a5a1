package com.facishare.paas.appframework.core.predef.service.dto.ai;

import java.util.Map;

import lombok.Data;

public interface SaveInsightResult {

    @Data
    class Arg {
        private String objectApiName;
        private String dataId;
        private String componentApiName;
        private Map<String, Object> insightResult;
        private Long generateTime;
        private String mappingObjectApiName;
        private Map<String, String> fieldMapping;
    }

    @Data
    class Result {
        private Boolean success;
        private String message;

        public static Result success() {
            Result result = new Result();
            result.setSuccess(true);
            return result;
        }

        public static Result fail(String message) {
            Result result = new Result();
            result.setSuccess(false);
            result.setMessage(message);
            return result;
        }
    }

}
