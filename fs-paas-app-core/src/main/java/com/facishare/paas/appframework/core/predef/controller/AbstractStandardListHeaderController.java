package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.model.plugin.Plugin;
import com.facishare.paas.appframework.core.model.plugin.dto.ListHeaderControllerPluginDTO;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.layout.RelatedObjectGroupComponentBuilder;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.service.es.abstrace.SearchSourceByQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.CollectionUtils.sortByGivenOrder;

/**
 * Created by zhouwr on 2020/7/21
 */
public class AbstractStandardListHeaderController<A extends StandardListHeaderController.Arg> extends BaseListHeaderController<A> {

    @Override
    protected void processResult(StandardListHeaderController.Result result) {
        super.processResult(result);
        result.setIsChildObj(objectDescribeExt.isSlaveObject());
        ObjectDescribeDocument describeExt = buildDescribeExt();
        result.setObjectDescribeExt(describeExt);
        result.setSupportTag(isSupportTag());
        result.setSupportOrFilter(isSupportOrFilter());
        result.setSupportFilterVariable(!AppFrameworkConfig.getNoSupportCurrentLoginUserObjects().contains(controllerContext.getObjectApiName()));
        result.setSupportFullFieldSearch(isSupportFullFieldSearch(describeExt));
        result.setSupportFullFieldSearchOnListPage(isSupportFullFieldSearchOnListPage(describeExt));
    }

    private Boolean isSupportFullFieldSearch(ObjectDescribeDocument describeExt) {
        if (!isSelectedList()) {
            return false;
        }
        Object o = describeExt.get(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH);
        if (Boolean.TRUE.equals(o)) {
            SearchSourceByQuery abstractSearchSource = serviceFacade.getBean(SearchSourceByQuery.class);
            boolean grayES = abstractSearchSource.checkGrayES(controllerContext.getTenantId(), objectDescribeExt.getApiName());
            if (grayES) {
                return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SELECT_DATA_GRAY_EI, controllerContext.getTenantId());
            }
        }
        return false;
    }


    private Boolean isSupportFullFieldSearchOnListPage(ObjectDescribeDocument describeExt) {
        if (!isList()) {
            return false;
        }
        Object o = describeExt.get(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH);
        if (Boolean.TRUE.equals(o)) {
            SearchSourceByQuery abstractSearchSource = serviceFacade.getBean(SearchSourceByQuery.class);
            boolean grayES = abstractSearchSource.checkGrayES(controllerContext.getTenantId(), objectDescribeExt.getApiName());
            if (grayES) {
                return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.LIST_DATA_GRAY_EI, controllerContext.getTenantId());
            }
        }
        return false;
    }

    private boolean isSupportTag() {
        return !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_SUPPORT_TAG_FILTER_GRAY_EI, controllerContext.getTenantId())
                && !objectDescribeExt.isBigObject() && infraServiceFacade.isSupportTag(objectDescribeExt.getApiName(), controllerContext.getUser());
    }

    @Override
    protected IObjectDescribe findObject() {
        //相关列表页需要多对多字段来获取按钮
        if (isRelatedList()) {
            return serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        }
        return super.findObject();
    }

    protected ObjectDescribeDocument buildDescribeExt() {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), objectDescribeExt,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true);
        return ObjectDescribeDocument.of(describeExtra);
    }

    protected List<IButton> getButtons() {
        if (GroupField.GROUP_TYPE_WHAT.equals(arg.getListType())) {
            return Lists.newArrayList();
        }
        if (isRelatedList()) {
            return getRelatedListButtons(layoutExt);
        }
        List<IButton> buttons = super.getButtons();
        if (CollectionUtils.notEmpty(buttons)) {
            if (StringUtils.equals(objectDescribeExt.getApiName(), Utils.APPROVAL_INSTANCE_API_NAME)) {
                buttons.removeIf(x -> StringUtils.equals(x.getName(), ButtonExt.DEFAULT_ADD_BUTTON_API_NAME));
            }
            if (objectDescribeExt.isSupportTreeViewObject() && AppFrameworkConfig.treeViewObjectNotSupportImport()) {
                buttons.removeIf(x -> StringUtils.equals(x.getName(), ButtonExt.DEFAULT_IMPORT_BUTTON_API_NAME));
            }
            if (arg.isCrossObjectFilter()) {
                buttons.removeIf(x -> StringUtils.equals(x.getName(), ObjectAction.BATCH_EXPORT.getDefaultButtonApiName()) || 
                                  StringUtils.equals(x.getName(), ObjectAction.EXPORT_FILE.getDefaultButtonApiName()));
            }
        }
        return buttons;
    }

    protected List<IButton> getRelatedListButtons(LayoutExt layoutExt) {
        IObjectDescribe targetDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getTargetObjectApiName());
        IObjectData targetData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(),
                arg.getTargetObjectDataId(), targetDescribe.getApiName());
        RelatedObjectDescribeStructure relatedStructure = RelatedObjectDescribeStructure.builder()
                .relatedObjectDescribe(objectDescribeExt)
                .fieldApiName(arg.getObjectReferenceApiName())
                .build();

        List<IButton> buttonList = RelatedObjectGroupComponentBuilder.builder()
                .functionPrivilegeService(serviceFacade)
                .buttonLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .layout(layoutExt)
                .objectDescribe(ObjectDescribeExt.of(targetDescribe))
                .objectData(targetData)
                .build()
                .getButtons(relatedStructure);
        // 按布局的配置，处理按钮信息
        return handleButtonsByListLayout(buttonList, ButtonUsePageType.ListNormal);
    }

    private boolean isSupportOrFilter() {
        return serviceFacade.isSupportOrFilter(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    @Override
    protected Plugin.Arg buildAPLPluginArg(String method) {
        APLControllerPlugin.TriggerInfo triggerInfo = buildTriggerInfo();
        ListHeaderControllerPluginDTO.Arg controllerArg = Objects.isNull(arg) ? null : arg.copy2Plugin();
        ListHeaderControllerPluginDTO.Result controllerResult = getListHeaderControllerPluginResultDTO();
        return new APLControllerPlugin.Arg(controllerContext.getObjectApiName(), controllerArg, controllerResult, triggerInfo);
    }

    private ListHeaderControllerPluginDTO.Result getListHeaderControllerPluginResultDTO() {
        if (Objects.isNull(result)) {
            return null;
        }
        List<String> filterFields = CollectionUtils.nullToEmpty(result.getFilterFields()).stream()
                .filter(it -> BooleanUtils.isTrue(it.getIsShow()))
                .map(CommonFilterField.FilterField::getFieldName)
                .collect(Collectors.toList());

        Map<String, List<String>> layoutFieldsMap = Maps.newHashMap();
        for (LayoutDocument layoutDocument : CollectionUtils.nullToEmpty(result.getAbstractLayoutList())) {
            LayoutExt abstractLayoutExt = LayoutExt.of(layoutDocument);
            List<String> fields = abstractLayoutExt.getTableComponent()
                    .map(TableComponent::getIncludeFields)
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .map(ITableColumn::getName)
                    .collect(Collectors.toList());
            layoutFieldsMap.put(abstractLayoutExt.getName(), fields);
        }

        return ListHeaderControllerPluginDTO.Result.builder()
                .recordLayoutMapping(result.getRecordLayoutMapping())
                .layoutFieldsMap(layoutFieldsMap)
                .filterFields(filterFields)
                .personalConfigFilterFields(CollectionUtils.notEmpty(filterFieldsConfig))
                .build();
    }

    @Override
    protected void mergeAPLPluginResult(APLControllerPlugin.Result aplPluginResult) {
        ListHeaderControllerPluginDTO.Result controllerResult = aplPluginResult.getControllerResult(ListHeaderControllerPluginDTO.Result.class);
        List<String> filterFields = controllerResult.getFilterFields();
        if (CollectionUtils.empty(filterFields)) {
            return;
        }
        List<CommonFilterField.FilterField> filterFieldList = result.getFilterFields();
        if (CollectionUtils.empty(filterFieldList)) {
            return;
        }
        filterFieldList.forEach(it -> it.setIsShow(filterFields.contains(it.getFieldName())));
        List<CommonFilterField.FilterField> fields = sortByGivenOrder(filterFieldList, filterFields, CommonFilterField.FilterField::getFieldName);
        result.setFilterFields(fields);
    }

    @Override
    protected void mergeAPLPluginArg(APLControllerPlugin.Result aplPluginResult) {

    }

    @Override
    protected String getAgentType() {
        return Strings.isNullOrEmpty(arg.getLayoutAgentType()) ? "web" : arg.getLayoutAgentType();
    }
}
