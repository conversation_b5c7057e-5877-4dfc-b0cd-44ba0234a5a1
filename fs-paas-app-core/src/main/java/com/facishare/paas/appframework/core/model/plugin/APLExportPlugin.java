package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by <PERSON>hao<PERSON>ju on 2022/11/30
 */
public interface APLExportPlugin extends Plugin {
    String BEFORE = "before";

    String DO_EXPORT = "doExport";

    String EXPORT_AFTER = "exportAfter";
    String AFTER = "after";

    APLExportPlugin.Result before(PluginContext context, APLExportPlugin.Arg arg);

    APLExportPlugin.Result doExport(PluginContext context, APLExportPlugin.Arg arg);

    APLExportPlugin.Result after(PluginContext context, APLExportPlugin.Arg arg);

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends Plugin.Arg {
        private final StandardExportAction.Arg exportArg;
        private String code;
        private String type;
        private Integer totalCount;

        private List<Map> originalDataList;
        private List<Map> displayDataList;

        public Arg(String objectApiName) {
            super(objectApiName);
            exportArg = null;
        }

        public Arg(String objectApiName, StandardExportAction.Arg exportArg) {
            super(objectApiName);
            this.exportArg = exportArg;
        }

        public List<Map> getOriginalDataList() {
            return originalDataList;
        }

        public void setOriginalDataList(List<IObjectData> dataList) {
            this.originalDataList = dataList.stream()
                    .map(ObjectDataExt::of)
                    .map(it -> it.toMap())
                    .collect(Collectors.toList());
        }

        public List<Map> getDisplayDataList() {
            return displayDataList;
        }

        public void setDisplayDataList(List<IObjectData> dataList) {
            this.displayDataList = dataList.stream()
                    .map(ObjectDataExt::of)
                    .map(it -> it.toMap())
                    .collect(Collectors.toList());
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends Plugin.Result {
        private final StandardExportAction.Arg exportArg;
        private final List<Map> displayDataList;
        private final Boolean supportDoExport;

        public boolean supportDoExport() {
            return BooleanUtils.isTrue(supportDoExport);
        }

        public Result(StandardExportAction.Arg exportArg, List<Map> displayDataList, Boolean supportDoExport) {
            this.exportArg = exportArg;
            this.displayDataList = displayDataList;
            this.supportDoExport = supportDoExport;
        }
    }

}
