package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.core.predef.service.dto.ai.FindInsightResult;
import com.facishare.paas.appframework.core.predef.service.dto.ai.SaveInsightResult;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ai.AiInsightLogicService;
import com.facishare.paas.appframework.metadata.repository.model.AiInsightEntity;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@ServiceModule("ai_insight")
public class ObjectAiInsightService {

    @Autowired
    private AiInsightLogicService aiInsightLogicService;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @ServiceMethod("find_insight_result")
    public FindInsightResult.Result findAiInsightResult(FindInsightResult.Arg arg, ServiceContext context) {
        AiInsightEntity aiInsightEntity = aiInsightLogicService.findByUniqKey(context.getUser(), arg.getObjectApiName(),
                arg.getDataId(), arg.getComponentApiName());
        if (aiInsightEntity == null) {
            return new FindInsightResult.Result();
        }
        return FindInsightResult.Result.builder()
                .insightResult((Map<String, Object>) JacksonUtils.fromJson(aiInsightEntity.getInsightResult(), Map.class))
                .generateTime(aiInsightEntity.getGenerateTime())
                .build();
    }

    @ServiceMethod("save_insight_result")
    public SaveInsightResult.Result saveInsightResult(SaveInsightResult.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getInsightResult())) {
            return new SaveInsightResult.Result();
        }
        try {
            AiInsightEntity aiInsightEntity = AiInsightEntity.builder()
                    .relateObjectApiName(arg.getObjectApiName())
                    .relateDataId(arg.getDataId())
                    .componentApiName(arg.getComponentApiName())
                    .insightResult(JacksonUtils.toJson(arg.getInsightResult()))
                    .generateTime(arg.getGenerateTime())
                    .build();
            aiInsightLogicService.save(context.getUser(), aiInsightEntity);
            saveObjectData(arg, context);
            return SaveInsightResult.Result.success();
        } catch (Exception e) {
            log.warn("saveInsightResult failed, tenantId:{}, arg:{} ", context.getTenantId(), JacksonUtils.toJson(arg), e);
            return SaveInsightResult.Result.fail(e.getMessage());
        }
    }

    protected void saveObjectData(SaveInsightResult.Arg arg, ServiceContext context) {
        if (CollectionUtils.empty(arg.getFieldMapping())) {
            return;
        }
        Map<String, Object> jsonResult = (Map<String, Object>) arg.getInsightResult().get("jsonResult");
        if (CollectionUtils.empty(jsonResult)) {
            return;
        }

        String mappingObjectApiName = arg.getMappingObjectApiName();
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), mappingObjectApiName);
        if (!describe.isActive()) {
            throw new ValidateException(I18NExt.text(I18NKey.OBJECT_INVALID, mappingObjectApiName));
        }

        ObjectDataDocument data = new ObjectDataDocument();
        ObjectDataExt.of(data).setDescribeApiName(mappingObjectApiName);
        List<IFieldDescribe> changeFields = Lists.newArrayList();
        arg.getFieldMapping().forEach((variableName, fieldName) -> {
            if ("_data_id".equals(variableName)) {
                data.put(fieldName, arg.getDataId());
                return;
            }
            if ("_component_id".equals(variableName)) {
                data.put(fieldName, arg.getComponentApiName());
                return;
            }
            Object value = jsonResult.get(variableName);
            if (Objects.nonNull(value)) {
                //校验字段描述是否禁用或删除
                IFieldDescribe field = ObjectDescribeExt.of(describe).getActiveFieldDescribe(fieldName);
                changeFields.add(field);
                data.put(fieldName, value);
            }
        });
        if (CollectionUtils.empty(changeFields)) {
            return;
        }

        //校验是否有字段的写权限
        Set<String> readOnlyFields = functionPrivilegeService.getReadonlyFields(context.getUser(),
                mappingObjectApiName);
        List<IFieldDescribe> noPrivilegeFields = changeFields.stream()
                .filter(field -> readOnlyFields.contains(field.getApiName()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(noPrivilegeFields)) {
            throw new ValidateException(String.join(I18NExt.text(I18NKey.HAS_NO_FIELD_WRITE_PERMISSION_WITH_FIELD_INFO,
                    describe.getDisplayName(), noPrivilegeFields.stream().map(IFieldDescribe::getLabel).collect(Collectors.joining("|")))));
        }

        if (Objects.equals(arg.getObjectApiName(), mappingObjectApiName)) {
            // 关联对象与洞察对象相同，直接更新洞察对象
            StandardEditAction.Arg editArg = new StandardEditAction.Arg();
            editArg.setObjectData(data);
            ActionContext actionContext = ContextManager.buildActionContext(mappingObjectApiName,
                    StandardAction.Edit.name());
            serviceFacade.triggerRemoteAction(actionContext, editArg, StandardEditAction.Result.class);
        } else {
            // 关联对象与洞察对象不同，创建关联对象数据
            StandardAddAction.Arg addArg = new StandardAddAction.Arg();
            addArg.setObjectData(data);
            ActionContext actionContext = ContextManager.buildActionContext(mappingObjectApiName,
                    StandardAction.Add.name());
            serviceFacade.triggerRemoteAction(actionContext, addArg, StandardAddAction.Result.class);
        }
    }
}
