package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.dubbo.common.utils.ConcurrentHashSet;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.UdobjGrayKey;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectStatusType;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class StandardObjectTabConfigController extends PreDefineController<StandardObjectTabConfigController.Arg, StandardObjectTabConfigController.Result> {


    private static final String UDOBJ = "udobj";
    private static final String DEFAULT = "default";
    private static final Splitter CONFIG_SPLITTER = Splitter.onPattern("[,|;]").omitEmptyStrings().trimResults();

    private static Map<String, ObjectTabConfig> configMap;

    protected Set<String> allowTabs = new ConcurrentHashSet<>();
    protected Set<String> hiddenTabs = new ConcurrentHashSet<>();

    static {
        ConfigFactory.getConfig("fs-paas-object-tab-config", config -> {
            configMap = JacksonUtils.fromJson(config.getString(), new TypeReference<Map<String, ObjectTabConfig>>() {
            });
        });
    }

    @Override
    protected Result doService(StandardObjectTabConfigController.Arg arg) {
        fillObjectTabList();
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getDescribeApiName());
        DescribeExtra describeExtra = serviceFacade.findDescribeExtra(controllerContext.getUser(), objectDescribe);
        boolean isOpenGdpr = infraServiceFacade.findGdprComplianceStatusByCache(controllerContext.getUser(), controllerContext.getObjectApiName());
        PublicObjectStatusType state = infraServiceFacade.queryStatus(controllerContext.getUser(), controllerContext.getObjectApiName()).getPublicObjectStatus();
        
        // 检查是否是灰度企业，判断是否支持字段合规性设置迁移
        boolean isGdprMigrated = UdobjGrayConfig.isAllow(UdobjGrayKey.FIELD_COMPLIANCE_SETTING_GRAY, controllerContext.getTenantId());
        
        return Result.builder()
                .describe(ObjectDescribeDocument.of(objectDescribe))
                .isOpenGdpr(isOpenGdpr)
                .publicObjectStatus(state.getType())
                .describeExtra(ObjectDescribeDocument.of(describeExtra))
                .tabBlackList(hiddenTabs)
                .tabWhiteList(allowTabs)
                .gdprMigrated(isGdprMigrated)
                .build();
    }

    /**
     * 业务测在代码中重写该方法,可加入自身的业务逻辑,
     * 对allowTabs(白名单)和hiddenTabs(黑名单)进行重定义操作
     */
    protected void fillObjectTabList() {
        String describeApiName = arg.getDescribeApiName();
        if (ObjectDescribeExt.isCustomObject(describeApiName)) {
            describeApiName = UDOBJ;
        }
        ObjectTabConfig objectTabConfig = configMap.get(describeApiName);
        if (Objects.nonNull(objectTabConfig)) {
            fillGrayTabs(objectTabConfig.getAllowMap(), allowTabs);
            fillGrayTabs(objectTabConfig.getHiddenMap(), hiddenTabs);
        }
    }

    private void fillGrayTabs(List<ConfigInfo> configInfoList, Set<String> tabs) {
        if (CollectionUtils.empty(configInfoList)) {
            return;
        }
        configInfoList.stream()
                .filter(x -> formatTenants(x.getTenants()).contains(controllerContext.getTenantId()))
                .filter(x -> CollectionUtils.notEmpty(x.getTabs()))
                .forEach(x -> tabs.addAll(x.getTabs()));
        if (CollectionUtils.notEmpty(tabs)) {
            return;
        }
        configInfoList.stream().filter(x -> Objects.nonNull(x.getGroup()) && DEFAULT.equals(x.getGroup())).forEach(x -> tabs.addAll(x.getTabs()));
    }

    private static Set<String> formatTenants(String tenants) {
        if (StringUtils.isBlank(tenants)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(CONFIG_SPLITTER.split(tenants));
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Arg {
        String describeApiName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    static class Result {
        Collection<String> tabBlackList;
        Collection<String> tabWhiteList;
        boolean isOpenGdpr;
        private String publicObjectStatus;
        ObjectDescribeDocument describe;
        ObjectDescribeDocument describeExtra;
        boolean gdprMigrated;
    }

    @Data
    static class ObjectTabConfig {
        List<ConfigInfo> hiddenMap;
        List<ConfigInfo> allowMap;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class ConfigInfo {
        List<String> tabs;
        String tenants;
        String group;
    }
}
