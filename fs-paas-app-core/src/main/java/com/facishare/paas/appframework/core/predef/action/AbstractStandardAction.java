package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.action.UpdateFieldAction;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2019/09/19
 */
public abstract class AbstractStandardAction<A, R> extends PreDefineAction<A, R> {

    protected IUdefButton udefButton;
    private UpdateFieldAction updateFieldAction;
    protected ButtonExecutor.Result validatedFunctionResult;

    protected IObjectData getOldObjectData() {
        return getPreObjectData();
    }

    protected IObjectData getPreObjectData() {
        return null;
    }

    protected IObjectData getPostObjectData() {
        return null;
    }

    protected String getButtonApiName() {
        return null;
    }

    protected Map<String, Object> getActionParams(A arg) {
        return Maps.newHashMap();
    }


    /**
     * 针对线索和跟进中和无效按钮，用于必填参数的校验和函数入参的绑定
     *
     * @return 用户填写的表单参数
     */
    protected Map<String, Object> getArgs() {
        return Maps.newHashMap();
    }

    /**
     * 制作用做调用审批接口入参：显示审批内容，内部那个Map格式找流程组确定<br/>
     * 给批量审批用的<br/>
     * 模板:<br/>
     * <pre>
     *     {@code
     *          objectId: {
     *              param: {
     *                  name1: value1,
     *                  name2: value2
     *              }
     *          }
     *     }
     * </pre>
     * objectId的value对应：
     * com.facishare.paas.appframework.flow.dto.StartApprovalFlow.TriggerData#data
     *
     * @return
     */
    protected Map<String, Map<String, Object>> buildDataMapByObjectId() {
        return Maps.newHashMap();
    }


    /**
     * 制作用做流程审批接口回调UDOBJ FlowCallAction入参：执行审批动作，内部那个Map格式找调用类确定<br/>
     * 给批量审批用的<br/>
     * 模板:<br/>
     * <pre>
     *     {@code
     *          objectId: {
     *              args: {
     *                  name1: value1,
     *                  name2: value2
     *              }
     *          }
     *     }
     * </pre>
     * objectId的value对应：
     *
     * @return
     */
    protected Map<String, Map<String, Object>> buildCallBackDataMapByObjectId() {
        Map<String, Map<String, Object>> dataMap = Maps.newHashMap();

        return Maps.newHashMap();
    }

    protected boolean needSkipFunctionAction() {
        return BooleanUtils.isTrue((Boolean) actionContext.getAttribute(RequestContext.Attributes.SKIP_FUNCTION_ACTION));
    }

    protected boolean needSkipButtonConditions() {
        return BooleanUtils.isTrue((Boolean) actionContext.getAttribute(RequestContext.Attributes.SKIP_BUTTON_CONDITIONS));
    }

    @Override
    protected final void preAction(A arg) {
        stopWatch.lap("before");
        // 初始化按钮描述
        initButton();
        doCheckButtonConditions();
        stopWatch.lap("doCheckButtonConditions");

        doCheckParam();
        stopWatch.lap("doCheckParam");

        prepareData();
        stopWatch.lap("prepareData");

        doPreFunction(arg);
        stopWatch.lap("preAction");
    }

    protected void prepareData() {
        if (udefButton == null) {
            return;
        }

        Map<String, Object> args = getArgs();
        if (CollectionUtils.empty(args)) {
            return;
        }

        Set<IParamForm> paramForms = ParamForm.fromList(udefButton.getParamForm()).stream()
                .filter(it -> objectDescribe.getApiName().equals(it.getObjectApiName()))
                .filter(it -> objectDescribe.containsField(it.convertToFieldApiName()))
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(paramForms)) {
            return;
        }

        IObjectData argData = new ObjectData();
        argData.setTenantId(actionContext.getTenantId());
        argData.setDescribeApiName(objectDescribe.getApiName());
        paramForms.forEach(paramForm -> {
            String apiName = paramForm.getApiName();
            if (!args.containsKey(apiName)) {
                return;
            }
            String fieldApiName = paramForm.convertToFieldApiName();
            argData.set(fieldApiName, args.get(apiName));
        });
        serviceFacade.processData(objectDescribe, Lists.newArrayList(argData));
    }

    protected Map<String, Object> getUpdateFieldMapByButtonAction(IObjectData objectData, IUdefAction action,
                                                                  ButtonExecutor.Arg buttonArg, User user) {
        return getUpdateFieldAction()
                .getUpdateFieldMap(objectDescribe, objectData, udefButton, action, buttonArg, user);
    }

    private UpdateFieldAction getUpdateFieldAction() {
        if (updateFieldAction == null) {
            return updateFieldAction = serviceFacade.getBean(UpdateFieldAction.class);
        }
        return updateFieldAction;
    }

    private void doCheckParam() {
        if (Objects.isNull(udefButton) || CollectionUtils.empty(udefButton.getParamForm())) {
            return;
        }
        // 自定义按钮不需要在这里校验入参
        if (!ButtonExt.of(udefButton).isSystemButton()) {
            return;
        }
        infraServiceFacade.checkParam(udefButton, objectDescribe, getPreObjectData(), getArgs(), actionContext.getUser());
    }

    private void doPreFunction(A arg) {
        if (skipPreFunction()) {
            return;
        }
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.ofTriggerFunction(getPreObjectData(), getArgs(), getPreObjectDataDetails(), getActionParams(arg));
        if (!skipPreFunctionArgValidate() && Strings.isNullOrEmpty(executorArg.getObjectDataId()) && Objects.isNull(executorArg.getObjectData())) {
            return;
        }
        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .stage(UdefActionExt.PRE)
                .button(udefButton)
                .describe(objectDescribe)
                .user(actionContext.getUser())
                .ignoreFields(getIgnoreFields())
                .build();
        validatedFunctionResult = infraServiceFacade.triggerFunctionAction(executorArg, executorContext);
        processValidatedFunctionResult();
    }

    protected boolean skipPreFunctionArgValidate() {
        return false;
    }

    protected void processValidatedFunctionResult() {
        if (!validatedFunctionResult.isHasReturnValue()) {
            return;
        }

        if (!skipNonBlockingPreFunction() && RequestUtil.isCepRequest()) {
            throw new AcceptableValidateException(buildValidateResult());
        } else {
            String message = validatedFunctionResult.getReturnValue() == null ? "" : validatedFunctionResult.getReturnValue().toString();
            throw new ValidateException(message);
        }
    }

    protected Collection<String> getIgnoreFields() {
        return Collections.emptySet();
    }

    /**
     * 执行函数需要传入的主对象动作
     *
     * @return
     */
    protected Map<String, List<IObjectData>> getPreObjectDataDetails() {
        return null;
    }


    private void doCheckButtonConditions() {
        if (skipCheckButtonConditions()) {
            return;
        }
        IObjectData objectData = getOldObjectData();
        if (Objects.isNull(objectData)) {
            return;
        }

        List<Wheres> wheres = ButtonExt.of(udefButton).handleWheres(objectDescribe);
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        IFilter filter = FilterExt.of(Operator.EQ, IObjectData.ID, objectData.getId()).getFilter();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(wheres, filter);
        ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .queryExt(queryExt)
                .filterLabel(I18N.text(I18NKey.CUSTOM_BUTTON) + objectDescribe.getDisplayName() + "." + udefButton.getLabel())
                .build();
        List<IObjectData> dataList = dataFilter.doFilter(Lists.newArrayList(objectData));
        if (CollectionUtils.empty(dataList)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_SATISFY_BUTTON_CONDITION));
        }
    }

    protected boolean skipCheckButtonConditions() {
        return Strings.isNullOrEmpty(getButtonApiName()) || isBatchAction() || needSkipButtonConditions();
    }


    protected void initButton() {
        if (Strings.isNullOrEmpty(getButtonApiName())) {
            return;
        }
        if (Objects.nonNull(udefButton)) {
            return;
        }
        udefButton = serviceFacade.findButtonByApiName(actionContext.getUser(), getButtonApiName(), actionContext.getObjectApiName());
        if (Objects.isNull(udefButton)) {
            throw new ValidateException(I18N.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
    }

    /**
     * 跳过按钮的前置执行动作
     *
     * @return
     */
    protected boolean skipPreFunction() {
        // dataList为不存在或者size大于1的时候不执行前置函数
        return Strings.isNullOrEmpty(getButtonApiName()) || isBatchAction() || needSkipFunctionAction();
    }

    @Override
    protected final void postAction(A arg, R result) {
        stopWatch.lap("after");
        try {
            doPostFunction(arg, result);
            stopWatch.lap("postAction");
        } catch (AppBusinessException e) {
            //调用层级限制不拼接"后动作执行失败"
            if (e.getErrorCode() == AppFrameworkErrorCode.REQUEST_FORBIDDEN.getCode()) {
                throw e;
            }
            throw new ValidateException(I18N.text(I18NKey.POST_ACTION_FAILED) + ": " + e.getMessage(), e.getErrorCode());
        } catch (Exception e) {
            log.warn("post execute function error ", e);
            throw new ValidateException(I18N.text(I18NKey.POST_ACTION_FAILED));
        }


        try {
            doRedirectAction(arg, result);
            stopWatch.lap("redirectAction");
        } catch (AppBusinessException e) {
            //调用层级限制不拼接"后动作执行失败"
            if (e.getErrorCode() == AppFrameworkErrorCode.REQUEST_FORBIDDEN.getCode()) {
                throw e;
            }
            throw new ValidateException(I18N.text(I18NKey.POST_ACTION_FAILED) + ": " + e.getMessage(), e.getErrorCode());
        } catch (Exception e) {
            log.warn("post execute function error ", e);
            throw new ValidateException(I18N.text(I18NKey.POST_ACTION_FAILED));
        }

    }

    private void doPostFunction(A arg, R result) {
        if (skipPostFunction()) {
            return;
        }
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                .objectData(getPostObjectData())
                .detailDataSupplier(this::getPostObjectDetails)
                .relatedDataList(getPostObjectRelatedData())
                .actionParams(getActionParams(arg))
                .args(getArgs())
                .build();
        if (Strings.isNullOrEmpty(executorArg.getObjectDataId()) && Objects.isNull(executorArg.getObjectData())) {
            return;
        }
        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(udefButton)
                .stage(UdefActionExt.POST)
                .build();

        infraServiceFacade.triggerFunctionAction(executorArg, executorContext);
    }


    private void doRedirectAction(A arg, R result) {
        if (skipRedirectAction()) {
            return;
        }

        ButtonExecutor.Arg redirectExecutorArg = ButtonExecutor.Arg.ofTriggerFunction(getRedirectObjectData(),
                getRedirectArgs(),
                getRedirectObjectDetails(),
                getRedirectActionParams());
        if (Strings.isNullOrEmpty(redirectExecutorArg.getObjectDataId()) && Objects.isNull(redirectExecutorArg.getObjectData())) {
            return;
        }

        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(udefButton)
                .stage(UdefActionExt.REDIRECT)
                .build();

        ButtonExecutor.Result redirectFunctionResult = infraServiceFacade.triggerFunctionAction(redirectExecutorArg, executorContext);
        if (ObjectUtils.isNotEmpty(redirectFunctionResult)) {
            buildRedirectResult(result, redirectFunctionResult);
        }
    }

    protected void buildRedirectResult(R result, ButtonExecutor.Result redirectFunctionResult) {

    }

    /**
     * 函数后动作触发的从对象数据。
     *
     * @return
     */
    protected Map<String, List<IObjectData>> getPostObjectDetails() {
        return null;
    }

    /**
     * 函数后动作触发的相关对象数据。
     *
     * @return
     */
    protected Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> getPostObjectRelatedData() {
        return null;
    }

    /**
     * 跳过按钮后动作
     *
     * @return
     */
    protected boolean skipPostFunction() {
        // dataList为不存在或者size大于1的时候不执行后置动作
        return Strings.isNullOrEmpty(getButtonApiName()) || isBatchAction() || needSkipFunctionAction();
    }

    /**
     * @return 本次请求是否需要创建批量任务
     */
    @Override
    protected boolean isBatchAction() {
        return CollectionUtils.notEmpty(dataList) && dataList.size() > 1;
    }

    /**
     * 是否支持非阻塞的前验证返回结果
     *
     * @return
     */
    protected boolean skipNonBlockingPreFunction() {
        return true;
    }


    /**
     * 是否跳过跳转动作函数
     *
     * @return
     */
    protected boolean skipRedirectAction() {
        return true;
    }

    protected IObjectData getRedirectObjectData() {
        return null;
    }

    protected Map<String, List<IObjectData>> getRedirectObjectDetails() {
        return null;
    }


    protected Map<String, Object> getRedirectArgs() {
        return new HashMap<>();
    }

    protected Map<String, Object> getRedirectActionParams() {
        return new HashMap<>();
    }

}
