package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.IButtonDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.SceneDocument;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.SceneLogicService;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.layout.ButtonRender;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.metadata.layout.resource.module.LayoutView;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;

/**
 * create by zhaoju on 2021/09/27
 */
public abstract class AbstractDesignerListLayoutResourceController<A> extends PreDefineController<A, AbstractDesignerListLayoutResourceController.Result> {
    protected IObjectDescribe describe;
    private ButtonRender buttonRender;

    private LayoutResourceService resourceService;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    protected void before(A arg) {
        resourceService = serviceFacade.getBean(LayoutResourceService.class);
        super.before(arg);
        describe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
    }

    @Override
    protected Result doService(A arg) {
        List<IScene> scenes = findScenes();
        stopWatch.lap("findScenes");

        List<IButton> normalButtons = getNormalButtons();
        stopWatch.lap("getNormalButtons");

        List<IButton> batchButtons = getBatchButtons();
        stopWatch.lap("getBatchButtons");

        List<IButton> singleButtons = getSingleButtons();
        stopWatch.lap("getSingleButtons");

        List<LayoutView> views = getViews();
        return buildResult(scenes, normalButtons, batchButtons, singleButtons, views);
    }

    private Result buildResult(List<IScene> scenes, List<IButton> normalButtons, List<IButton> batchButtons, List<IButton> singleButtons, List<LayoutView> views) {
        return Result.builder()
                .scenes(SceneDocument.ofList(scenes))
                .normalButtons(normalButtons(normalButtons))
                .batchButtons(batchButtons(batchButtons))
                .singleButtons(singleButtons(singleButtons))
                .views(views)
                .build();
    }

    protected List<IButtonDocument> singleButtons(List<IButton> singleButtons) {
        return IButtonDocument.ofList(singleButtons);
    }

    protected List<IButtonDocument> batchButtons(List<IButton> batchButtons) {
        return IButtonDocument.ofList(batchButtons);
    }

    protected List<IButtonDocument> normalButtons(List<IButton> normalButtons) {
        return IButtonDocument.ofList(normalButtons);
    }

    protected abstract List<IButton> getSingleButtons();

    protected List<IButton> getBatchButtons() {
        List<IButton> batchButtons = findButtonsByUsePage(ButtonUsePageType.ListBatch);
        String tenantId = controllerContext.getUser().getTenantId();
        return LayoutButtonExt.handleTerminal(batchButtons, tenantId);
    }

    protected abstract List<IButton> getNormalButtons();

    protected List<IScene> findScenes() {
        List<IScene> scenes = infraServiceFacade.findScenes(controllerContext.getObjectApiName(), controllerContext.getUser(), null);
        return scenes.stream().filter(IScene::isActive).collect(Collectors.toList());
    }

    protected List<IButton> findButtonsByUsePage(ButtonUsePageType usePageType) {
        return getButtonRender().getButtonsByUsePage(usePageType);
    }

    protected List<LayoutView> getViews() {
        return resourceService.findLayoutViewResource(controllerContext.getUser(), describe, LayoutTypes.LIST_LAYOUT);
    }

    private ButtonRender getButtonRender() {
        if (buttonRender != null) {
            return buttonRender;
        }
        buttonRender = ButtonRender.builder()
                .customButtonService(serviceFacade)
                .layoutType(LayoutTypes.LIST_LAYOUT)
                .licenseService(serviceFacade)
                .buttonLogicService(serviceFacade)
                .optionalFeaturesService(infraServiceFacade)
                .user(controllerContext.getUser())
                .describe(describe)
                .build()
                .render();
        return buttonRender;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<SceneDocument> scenes;
        private List<IButtonDocument> normalButtons;
        private List<IButtonDocument> batchButtons;
        private List<IButtonDocument> singleButtons;
        private List<LayoutView> views;
    }
}
