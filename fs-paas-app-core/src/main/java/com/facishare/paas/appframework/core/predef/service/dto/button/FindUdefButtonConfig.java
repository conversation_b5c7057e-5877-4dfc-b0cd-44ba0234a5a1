package com.facishare.paas.appframework.core.predef.service.dto.button;

import com.facishare.paas.appframework.core.model.ButtonConfigDocument;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * create by z<PERSON><PERSON> on 2019/09/18
 */
public interface FindUdefButtonConfig {
    @Data
    class Arg {
        private String describeApiName;
        private String buttonApiName;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private ButtonConfigDocument buttonConfig;
    }
}
