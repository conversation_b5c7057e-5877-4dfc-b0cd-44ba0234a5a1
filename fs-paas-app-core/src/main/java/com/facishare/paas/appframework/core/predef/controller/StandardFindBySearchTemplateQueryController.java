package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.facishare.paas.appframework.core.predef.controller.StandardFindBySearchTemplateQueryController.Arg;

/**
 * create by z<PERSON><PERSON> on 2021/04/06
 */
public class StandardFindBySearchTemplateQueryController extends AbstractStandardFindBySearchTemplateQueryController<Arg> {

    @Override
    protected List<String> parseProjectFields() {
        return arg.getFieldProjection();
    }

    @Data
    public static class Arg {
        @JSONField(name = "search_query_info")
        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        private String searchQueryInfo;

        @JSONField(name = "object_describe_api_name")
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        private String objectDescribeApiName;

        @JSONField(name = "search_template_id")
        @JsonProperty("search_template_id")
        @SerializedName("search_template_id")
        private String searchTemplateId;

        @JSONField(name = "search_template_type")
        @JsonProperty("search_template_type")
        @SerializedName("search_template_type")
        private String searchTemplateType;

        /**
         * true  会返回count
         * false 不会返回count，totalNum给0
         * null 会返回count
         */
        @JSONField(name = "need_return_count_num")
        @JsonProperty("need_return_count_num")
        @SerializedName("need_return_count_num")
        private boolean needReturnCountNum = false;

        /**
         * true  只返回精确总数，不返回数据
         * false 返回预估的总数，返回数据，列表页用
         * null 返回精确总数，精确数据
         */
        @JSONField(name = "find_explicit_total_num")
        @JsonProperty("find_explicit_total_num")
        @SerializedName("find_explicit_total_num")
        private Boolean findExplicitTotalNum;

        /**
         * true  会返回引用字段
         * false 不会返回引用字段
         * null 会返回引用字段
         */
        @JSONField(name = "need_return_quote")
        @JsonProperty("need_return_quote")
        @SerializedName("need_return_quote")
        private boolean needReturnQuote = false;

        /**
         * true 补充拓展字段
         * false 不需要补充拓展字段
         */
        @JSONField(name = "fill_field_info")
        @JsonProperty("fill_field_info")
        @SerializedName("fill_field_info")
        private boolean fillFieldInfo;

        /**
         * 指定筛选的字段
         */
        @JSONField(name = "field_projection")
        @JsonProperty("field_projection")
        @SerializedName("field_projection")
        List<String> fieldProjection;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        private List<ObjectDataDocument> dataList;
        @JSONField(name = "M4")
        private Integer total;
    }
}
