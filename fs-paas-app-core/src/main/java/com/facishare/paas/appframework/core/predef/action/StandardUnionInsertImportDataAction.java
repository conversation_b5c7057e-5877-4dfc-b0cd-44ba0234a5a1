package com.facishare.paas.appframework.core.predef.action;


import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.importobject.ImportMarkInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-04-08 10:26
 */
@Slf4j
public class StandardUnionInsertImportDataAction extends StandardInsertImportDataAction {
    protected List<String> unionApiNameList;
    protected String jobId;
    public final String MARK_LABEL = I18NExt.text(I18NKey.RELATED_MARK) + I18NExt.text(I18NKey.MUST_FILL_IN);
    /**
     * 非常重要: 将关联标识虚拟fieldDescribe创建数据时传入元数据，保存成功的数据元数据再返回，存入redis时使用
     */
    private static final String MARK_LABEL_VIRTUAL_API = "mark_label_virtual_api";

    @Override
    protected void before(Arg arg) {
        unionApiNameList = arg.getUnionApiNameList();
        jobId = arg.getJobId();
        super.before(arg);
    }

    @Override
    protected void initImportReferenceMapping() {
        importReferenceMapping = infraServiceFacade.findImportReferenceMapping(actionContext.getUser(), getMasterApiName());
        importReferenceFieldMappingSwitch = BooleanUtils.isTrue(importReferenceMapping.getReferenceFieldMappingSwitch());
    }

    /**
     * 联合导入，补充相关字段
     */
    @Override
    protected void convertFields(List<ImportData> dataList) {
        if (!isMasterObject() && !arg.getImportPreProcessing()) {
            //1.补充从对象的主从关系字段或者指定关联关系字段
            IFieldDescribe fieldDescribe = assignValidIDField();
            //2.从objectDataDocument和redis中取出masterId
            Map<Integer, String> rowNoMasterIdMap = createRowNoMasterIdMap();
            //3.给dataList增加主从关系字段，并将masterID的值，填入主从关系字段中
            addFieldDescribeAndFillValue(dataList, rowNoMasterIdMap, fieldDescribe);
        }
        // 依赖联合导入处理关联字段
        super.convertFields(dataList);
    }

    /**
     * 从对象专用
     */
    @Override
    protected void convertReferenceNameToID(List<ImportData> dataList) {
        ImportExportExt.convertReferenceNameToID(dataList, arg.getRows(), objectDescribeExt, assignValidIDField(), importReferenceFieldMappingSwitch);
    }

    /**
     * 将关联标识字段补充入数据
     */
    @Override
    protected boolean addMarkLabelField(IObjectData result, Map.Entry<String, Object> entry) {
        String label = entry.getKey();
        if (getMarkLabel().equals(label)) {
            result.set(MARK_LABEL_VIRTUAL_API, entry.getValue());
            return true;
        }

        return false;
    }

    /**
     * 【关联标识（必填）】
     *
     * @return 关联标识（必填）或者 关联标识（必填）所在的列
     */
    protected String getMarkLabel() {
        if (isSupportFieldMapping()) {
            Optional<FieldMapping> relateMark = fieldMappings.get(objectDescribe.getApiName()).stream()
                    .filter(x -> Objects.equals(ImportExportExt.EXT_INFO_RELATED_MARK, x.getImportFieldMark()))
                    .findFirst();
            if (relateMark.isPresent()) {
                return relateMark.get().getColIndex();
            }
        }
        return MARK_LABEL;
    }

    /**
     * 从对象 【主对象ID(请勿编辑此列)】
     *
     * @return 【主对象ID(请勿编辑此列)】或者【主对象ID(请勿编辑此列)】所在的列
     */
    protected String getAssociateObjectId() {
        if (isSupportFieldMapping()) {
            Optional<FieldMapping> unionIdMark = fieldMappings.get(objectDescribe.getApiName()).stream()
                    .filter(x -> Objects.equals(ImportExportExt.UNION_IMPORT_ID_MARK, x.getImportFieldMark()))
                    .findFirst();
            if (unionIdMark.isPresent()) {
                return unionIdMark.get().getColIndex();
            }
        }
        return I18NExt.text(I18NKey.ASSOCIATE_OBJECT_ID);
    }

    /**
     * 给data添加主从关系字段（或者指定字段），并填值
     *
     * @param dataList         准备导入的data列表
     * @param rowNoMasterIdMap 行号对应的主属性ID
     * @param fieldDescribe    需要添加的字段
     */
    protected void addFieldDescribeAndFillValue(List<ImportData> dataList, Map<Integer, String> rowNoMasterIdMap, IFieldDescribe fieldDescribe) {
        dataList.forEach(d -> {
            String masterId = rowNoMasterIdMap.get(d.getRowNo());
            IObjectData data = d.getData();
            data.set(fieldDescribe.getApiName(), masterId);
        });
    }

    /**
     * 构造从对象行号对应的主对象ID值
     * 从redis中
     * dataLoader传过来
     *
     * @return key:行号,value:主对象的id
     */
    private Map<Integer, String> createRowNoMasterIdMap() {
        Map<Integer, String> result = Maps.newHashMap();
        List<ObjectDataDocument> objectDataDocuments = arg.getRows();
        Map<Integer, String> rowNoMarkMap = Maps.newHashMap();  //行号对应的关联标识
        objectDataDocuments.forEach(data -> {
            //从对象 【主对象ID(请勿编辑此列)】的 value --直接用于导入的主对象id，不需要转换
            Object objectId = data.get(getAssociateObjectId());
            if (Objects.nonNull(objectId) && Strings.isNotBlank((String) objectId)) {
                result.put(Integer.valueOf(String.valueOf(data.get(ROW_NO))), String.valueOf(objectId));
            }
            //从对象 【关联标识（必填）】的value -- 需要构建和主对象的关联关系
            Object mark = data.get(getMarkLabel());
            if (Objects.nonNull(mark)) {
                rowNoMarkMap.put(Integer.valueOf(String.valueOf(data.get(ROW_NO))), String.valueOf(mark));
            }
        });

        //从redis中取masterId列表
        if (CollectionUtils.notEmpty(rowNoMarkMap)) {
            Map<String, String> importMark = infraServiceFacade.findUnionInsertImportMark(actionContext.getUser(), jobId,
                    arg.getObjectCode(), rowNoMarkMap.values());
            rowNoMarkMap.forEach((r, m) -> {
                String id = importMark.get(m);
                if (Objects.nonNull(id)) {
                    result.put(r, id);
                }
            });
        }
        return result;
    }

    @Override
    protected Result doAct(Arg arg) {
        log.info("start to validate and import dataList:{}", dataList);
        //校验，关联标识不允许为空
        validateMarkerNotEmpty();
        //校验：导入主对象，重复的关联标识不允许导入
        validateMasterDataMarkerUnique();
        return super.doAct(arg);
    }

    @Override
    protected Boolean isUnionImport() {
        return true;
    }

    @Override
    protected IObjectData fillCustomInfo2FunctionData(ImportData importData) {
        IObjectData objectData = super.fillCustomInfo2FunctionData(importData);
        Object mark = importData.getData().get(MARK_LABEL_VIRTUAL_API);
        objectData.set("_UnionMark", mark);
        return objectData;
    }

    /**
     * 联合导入主从和关联字段校验逻辑
     */
    @Override
    protected void validateRelatedObject(Integer matchingType) {
        List<ImportError> errorList = Lists.newArrayList();
        Map<IFieldDescribe, List<String>> defObjMap = getFieldDefObjMap(objectDescribeExt, dataList);
        if (isMasterObject()) {
            //主对象校验,调用name转id服务
            //batchHandleObjectReference(errorList, defObjMap);
            convertReferences(defObjMap, errorList);
        } else {
            //联合导入中主从关系或者指定关联关系字段只校验ID是否存在
            IFieldDescribe customField = assignValidIDField();
            if (Objects.isNull(customField)) {
                log.error("union import detail error : tenantId:{}, objectApiName:{}, no associate field found!",
                        actionContext.getTenantId(), objectDescribeExt.getApiName());
                //没有找到需要校验ID的字段，本批次数据都无法导入
                dataList.forEach(data -> {
                    errorList.add(new ImportError(data.getRowNo(),
                            I18NExt.text(I18NKey.IMPORT_NO_ASSOCIATE_FIELD)));
                });
            }

            // 只有在非预处理模式、或者使用ID匹配、或者不是主从关系字段时才需要验证引用ID是否存在
            if (!arg.getImportPreProcessing() || MATCHING_TYPE_ID == arg.getMatchingType()
                    || !FieldDescribeExt.of(customField).isMasterDetailField()) {
                validReferenceIDExist(errorList, customField);
            }


            //其它关联字段，调用name转id服务
            IFieldDescribe tmpField = null;
            if (Objects.nonNull(customField)) {
                for (Map.Entry<IFieldDescribe, List<String>> entry : defObjMap.entrySet()) {
                    if (entry.getKey().getApiName().equals(customField.getApiName())) {
                        tmpField = entry.getKey();
                        break;
                    }
                }
            }

            Map<IFieldDescribe, List<String>> defObjMapToValidate = Maps.newHashMap();
            Map<IFieldDescribe, List<String>> masterFieldDefObjMap = Maps.newHashMap();
            String tmpApiName = Objects.nonNull(tmpField) ? tmpField.getApiName() : null;
            defObjMap.forEach((field, valueList) -> {
                if (!field.getApiName().equals(tmpApiName)) {
                    defObjMapToValidate.put(field, valueList);
                } else {
                    masterFieldDefObjMap.put(field, valueList);
                }
            });
            convertReferences(defObjMapToValidate, errorList);
            // 同步主对象信息
            batchHandleObjectReferenceById(errorList, masterFieldDefObjMap, actionContext.getTenantId());
        }
        mergeErrorList(errorList);
    }

    /*
     * 支持使用唯一性ID或者name导入
     */
    private void convertReferences(Map<IFieldDescribe, List<String>> defObjMap, List<ImportError> errorList) {
        if (importReferenceFieldMappingSwitch) {
            batchHandleObjectReference(errorList, defObjMap);
        } else {
            if (MATCHING_TYPE_ID == arg.getMatchingType()) {
                // 按照ID匹配时
                // 关联字段和主从字段必填时，匹配ID必须填写ID
                validReferenceID(errorList, defObjMap);
                // 使用ID匹配，无需调用转id服务，只需要检验该id是否存在
                batchHandleObjectReferenceById(errorList, defObjMap, actionContext.getTenantId());
            } else {
                // 按照Name或者其它方式导入
                // 调用name转id服务
                batchHandleObjectReference(errorList, defObjMap);
            }
        }
    }

    /**
     * 从对象专用
     * 指定需要校验ID的字段
     * 各业务方需要自己覆盖此方法
     *
     * @return 字段描述
     */
    protected IFieldDescribe assignValidIDField() {
        List<IFieldDescribe> fields = objectDescribeExt.getFieldDescribes();
        Optional<IFieldDescribe> masterDetailField = fields.stream()
                .filter(x -> IFieldType.MASTER_DETAIL.equals(x.getType()))
                .findFirst();
        if (masterDetailField.isPresent()) {
            return masterDetailField.get();
        }
        String masterApiName = getMasterApiName();
        Optional<ObjectReferenceFieldDescribe> objectReferenceField = fields.stream()
                .filter(x -> IFieldType.OBJECT_REFERENCE.equals(x.getType()))
                .map(x -> (ObjectReferenceFieldDescribe) x)
                .filter(x -> Objects.equals(x.getTargetApiName(), masterApiName))
                .findFirst();
        if (objectReferenceField.isPresent()) {
            return objectReferenceField.get();
        }
        Optional<ObjectReferenceManyFieldDescribe> objectReferenceManyField = fields.stream()
                .filter(x -> IFieldType.OBJECT_REFERENCE_MANY.equals(x.getType()))
                .map(x -> (ObjectReferenceManyFieldDescribe) x)
                .filter(x -> Objects.equals(x.getTargetApiName(), masterApiName))
                .findFirst();
        return objectReferenceManyField.orElse(null);
    }

    private String getMasterApiName() {
        if (isSupportFieldMapping()) {
            return arg.getMasterInfo().getApiName();
        }
        return unionApiNameList.get(0);
    }

    /**
     * 校验指定的字段，ID是否存在
     *
     * @param errorList 错误信息列表
     * @param field     指定要校验的字段
     */
    protected void validReferenceIDExist(List<ImportError> errorList, IFieldDescribe field) {
        dataList.forEach(data -> {
            String val = getStringValue(data.getData(), field.getApiName());
            if (StringUtils.isBlank(val)) {
                //没有填写关联对象的数据ID
                errorList.add(new ImportError(data.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.IMPORT_MASTER_NOT_EXIST, I18NKey.IMPORT_MASTER_NOT_EXIST, field.getLabel())));
            }
        });
    }

    /**
     * 关联标识不允许为空
     */
    private void validateMarkerNotEmpty() {
        List<ImportError> errorList = Lists.newArrayList();
        List<ObjectDataDocument> objectDataDocuments = arg.getRows();
        for (ObjectDataDocument objectDataDocument : objectDataDocuments) {
            if (Objects.isNull(objectDataDocument.get(getMarkLabel())) || Strings.isBlank((String) objectDataDocument.get(getMarkLabel()))) {
                errorList.add(new ImportError((int) objectDataDocument.get(ROW_NO), I18NExt.text(I18NKey.IMPORT_MARK_EMPTY)));
            }
        }
        mergeErrorList(errorList);
    }

    /**
     * 导入主对象时，关联标识不允许重复
     */
    protected void validateMasterDataMarkerUnique() {
        if (isMasterObject()) {
            //主对象导入数据的关联标识不允许重复
            List<ImportError> errorList = Lists.newArrayList();
            List<ObjectDataDocument> objectDataDocuments = arg.getRows();
            int rowNo;
            Object obj;
            Set<Integer> indexSet = Sets.newHashSet();
            for (int i = 0; i < objectDataDocuments.size(); i++) {
                if (CollectionUtils.notEmpty(indexSet) && indexSet.contains(i)) {
                    continue;
                }
                rowNo = (int) objectDataDocuments.get(i).get(ROW_NO);
                obj = objectDataDocuments.get(i).get(getMarkLabel());
                if (Objects.isNull(obj) || "".equals((String) obj)) {
                    continue;
                }
                Set<Integer> numberSet = Sets.newHashSet();
                for (int j = i + 1; j < objectDataDocuments.size(); j++) {
                    int rowNo2 = (int) objectDataDocuments.get(j).get(ROW_NO);
                    Object obj2 = objectDataDocuments.get(j).get(getMarkLabel());
                    if (Objects.isNull(rowNo2) || "".equals((String) obj2)) {
                        continue;
                    }
                    if (((String) obj).equals((String) obj2)) {
                        numberSet.add(rowNo);
                        numberSet.add(rowNo2);
                        indexSet.add(j);
                    }
                }
                if (CollectionUtils.notEmpty(numberSet)) {
                    StringBuilder messageBuilder = new StringBuilder();
                    for (int row : numberSet) {
                        messageBuilder.append(row);
                        messageBuilder.append(",");
                    }
                    if (messageBuilder.length() > 0) {
                        messageBuilder.deleteCharAt(messageBuilder.length() - 1);
                    }
                    for (int row : numberSet) {
                        errorList.add(new ImportError(row, I18NExt.getOrDefault(I18NKey.ASSOCIATE_DUPLICATE, I18NKey.ASSOCIATE_DUPLICATE,
                                messageBuilder.toString())));
                    }
                }
            }
            mergeErrorList(errorList);
        }
    }

    /**
     * 联合导入生成结果
     */
    @Override
    protected void generateResult(Result result) {
        ImportResultValue resultValue = new ImportResultValue();
        if (CollectionUtils.notEmpty(actualList)) {
            log.info("Import data tenantId:{}, unionApiNameList:{}, size:{}", actionContext.getTenantId(),
                    unionApiNameList, actualList.size());
            resultValue.setImportSucceedCount(actualList.size());
        }
        //将所有导入成功的主对象ID存入redis
        if (isMasterObject()) {
            // key: mark, value: id
            Map<String, String> markIdMap = getMarkIdMap();

            List<ImportMarkInfo> importMarkInfos = getImportMarkInfos();

            try {
                //主对象的mark不允许重复
                boolean success = infraServiceFacade.saveUnionInsertImportMark(actionContext.getUser(), jobId, arg.getObjectCode(), importMarkInfos);
                if (!success) {
                    mergeMasterErrorList(markIdMap);
                }
            } catch (Exception e) {
                log.error("saveUnionInsertImportMark error, tenantId:{},jobId:{},objectApiName:{},data:{}", actionContext.getTenantId(),
                        jobId, objectDescribeExt.getApiName(), markIdMap, e);
                mergeMasterErrorList(markIdMap);

            }
        } else if (!isMasterObject()) {
            //从对象导入失败数据， 将主对象id返回
            mergeDetailErrorList();
        }
        //所有的错误的数据结果
        resultValue.setRowErrorList(allErrorList);
        result.setValue(resultValue);
    }

    protected List<ImportMarkInfo> getImportMarkInfos() {
        Map<String, String> markIdMap = getMarkIdMap();
        List<ImportMarkInfo> importMarkInfos = Lists.newArrayList();
        markIdMap.forEach((mark, id) -> {
            ImportMarkInfo importMarkInfo = new ImportMarkInfo();
            importMarkInfo.setMarkLabel(mark);
            importMarkInfo.setDataId(id);
            importMarkInfo.setTriggerApprovalAfterImport(true);
            importMarkInfo.setTriggerWorkFlowAfterImport(UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UNION_INSERT_IMPORT_COMPLETE_TRIGGER_WORK_FLOW_GRAY, actionContext.getTenantId()));
            importMarkInfos.add(importMarkInfo);
        });
        return importMarkInfos;
    }

    protected Map<String, String> getMarkIdMap() {
        Map<String, String> markIdMap = Maps.newHashMap();
        actualList.forEach(data -> {
            markIdMap.put(data.get(MARK_LABEL_VIRTUAL_API, String.class), data.getId());
        });
        return markIdMap;
    }

    /**
     * 将导入失败的从对象关联的主对象数据ID写入返回数据
     * 没有主对象数据ID的表示主对象也导入失败
     */
    private void mergeDetailErrorList() {
        //本批次所有从对象
        List<ObjectDataDocument> objectDataDocumentList = arg.getRows();
        //导入失败的从对象行号对应的主对象的关联标识
        Map<Integer, String> rowNoMarkMap = Maps.newHashMap();
        //{关联标识value:主对象数据id}
        Map<String, String> markMasterIdMap = Maps.newHashMap();
        for (ObjectDataDocument objectDataDocument : objectDataDocumentList) {
            int rowNo = (int) objectDataDocument.get(ROW_NO);
            //主对象的mark
            String mark = (String) objectDataDocument.get(getMarkLabel());
            if (StringUtils.isNotBlank(mark)) {
                rowNoMarkMap.put(rowNo, mark);
                Object associateObjectId = objectDataDocument.get(getAssociateObjectId());
                if (Objects.nonNull(associateObjectId) && Strings.isNotEmpty(String.valueOf(associateObjectId))) {
                    markMasterIdMap.put(mark, String.valueOf(associateObjectId));
                }
            }
        }
        Set<String> markLabel = rowNoMarkMap.values().stream()
                .filter(it -> !markMasterIdMap.containsKey(it))
                .collect(Collectors.toSet());
        if (CollectionUtils.notEmpty(markLabel)) {
            Map<String, String> importMark = infraServiceFacade.findUnionInsertImportMark(actionContext.getUser(), jobId,
                    arg.getObjectCode(), markLabel);
            markMasterIdMap.putAll(importMark);
        }
        for (ImportError error : allErrorList) {
            int rowNo = error.getRowNo();
            String mark = rowNoMarkMap.get(rowNo);
            String masterId = markMasterIdMap.get(mark);
            error.setMasterId(masterId);
        }
    }

    /**
     * 将存入redis失败的主对象数据ID写入错误结果中
     *
     * @param markIdMap 保存redis的主对象数据
     */
    private void mergeMasterErrorList(Map<String, String> markIdMap) {
        //存入redis失败，将所有ID和关联标识返回给dataloader
        Map<String, Integer> markRowNoMap = arg.getRows().stream().collect(Collectors.toMap(
                d -> String.valueOf(d.get(getMarkLabel())),
                d -> Integer.valueOf(String.valueOf(d.get(ROW_NO))),
                (k1, k2) -> k2   //关联标识如果重复，这里为了防止duplicate key 异常，只取一个
        ));
        markIdMap.forEach((mark, id) -> {
            Integer rowNo = markRowNoMap.get(mark);
            ImportError error = ImportError.builder()
                    .rowNo(rowNo)
                    .errorMessage(I18NExt.text(I18NKey.IMPORT_SUCCESS_REDIS_FAIL))
                    .masterId(id)
                    .associateMark(mark)
                    .build();
            allErrorList.add(error);
        });
    }

    protected boolean isMasterObject() {
        if (isSupportFieldMapping()) {
            return CollectionUtils.notEmpty(arg.getDetailInfo()) && !ObjectDescribeExt.of(objectDescribe).isSlaveObject();
        }
        return !CollectionUtils.empty(unionApiNameList) && unionApiNameList.get(0).equals(objectDescribeExt.getApiName());
    }

    /**
     * 联合导入这里不需要触发主对象的工作流，后续会通过mq消息触发
     * 从对象还需要在这里触发工作流
     *
     * @param actualList
     */
    @Override
    protected void startImportWorkFlow(List<IObjectData> actualList) {
        if (isMasterObject() && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UNION_INSERT_IMPORT_COMPLETE_TRIGGER_WORK_FLOW_GRAY, actionContext.getTenantId())) {
            return;
        }
        super.startImportWorkFlow(actualList);
    }

    @Override
    protected void startImportApprovalFlow() {
        // 联合导入需要等所有数据都导入完成、通过 mq 消息触发审批
    }

    @Override
    protected void validateOwner(List<ImportData> dataList) {
        if (arg.getImportPreProcessing() && !isMasterObject()) {
            return;
        }
        super.validateOwner(dataList);
    }

}
