package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.AcceptableValidateNoRollbackException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.AbstractActTCCActionHandler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.facade.ChangeOrderHandlerLogicService;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.dto.CreateChangeOrder;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/31.
 */
@Component
@HandlerProvider(name = "defaultUpdateDataEditActHandler")
public class DefaultUpdateDataEditActHandler extends AbstractActTCCActionHandler<EditActionHandler.Arg, EditActionHandler.Result> {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ChangeOrderHandlerLogicService changeOrderHandlerLogicService;
    @Autowired
    private SaveActionServiceFacade saveActionServiceFacade;

    @Override
    @TccTransactional(name = "handle", confirmMethod = "commit", cancelMethod = "rollback")
    public EditActionHandler.Result handle(HandlerContext context, EditActionHandler.Arg arg) {
        if (arg.triggerUpdateApprovalFlowSuccess()) {
            return new EditActionHandler.Result();
        }
        handleQuoteValue(context, arg);
        doUpdateData(context, arg);
        return buildResult(arg);
    }

    @Override
    public boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, EditActionHandler.Arg arg) {
        return super.commit(branchTransactionalContext, context, arg);
    }

    @Override
    public boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, EditActionHandler.Arg arg) {
        return super.rollback(branchTransactionalContext, context, arg);
    }

    private EditActionHandler.Result buildResult(EditActionHandler.Arg arg) {
        BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                .isDuplicate(false)
                .writeDB(true)
                .triggerApproval(false)
                .objectData(arg.getObjectData())
                .details(arg.getDetailObjectData())
                .relatedDataList(arg.getRelatedObjectData())
                .build();
        EditActionHandler.Result result = new EditActionHandler.Result();
        result.setInterfaceResult(actionResult);
        return result;
    }

    private EditActionHandler.Result buildChangeOrderResult(EditActionHandler.Arg arg, String changeOrderApiName, String changeOrderDataId) {
        BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                .isDuplicate(false)
                .writeDB(false)
                .triggerApproval(false)
                .objectData(arg.getObjectData())
                .details(arg.getDetailObjectData())
                .relatedDataList(arg.getRelatedObjectData())
                .changeOrderDataId(changeOrderDataId)
                .changeOrderApiName(changeOrderApiName)
                .build();
        EditActionHandler.Result result = new EditActionHandler.Result();
        result.setInterfaceResult(actionResult);
        return result;
    }

    private void handleQuoteValue(List<IObjectData> dataList, IObjectDescribe describe) {
        List<Quote> quotes = ObjectDescribeExt.of(describe).getQuoteFieldDescribes();
        if (CollectionUtils.empty(quotes) || CollectionUtils.empty(dataList)) {
            return;
        }
        Set<String> quoteFieldApiName = quotes.stream().map(Quote::getApiName).collect(Collectors.toSet());
        dataList.stream().map(ObjectDataExt::of).forEach(dataExt -> dataExt.remove(quoteFieldApiName));
    }

    private void handleQuoteValue(HandlerContext context, EditActionHandler.Arg arg) {
        IObjectData objectData = arg.objectData();
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        handleQuoteValue(Lists.newArrayList(objectData), objectDescribe);

        // 处理从对象
        Map<String, List<IObjectData>> detailObjectData = arg.detailObjectData();
        if (CollectionUtils.notEmpty(detailObjectData)) {
            detailObjectData.forEach((apiName, objectDataList) -> handleQuoteValue(objectDataList, arg.getDetailDescribe(apiName)));
        }

        // 处理相关对象
        if (CollectionUtils.notEmpty(arg.getRelatedObjectData())) {
            arg.relatedDataMap().forEach((apiName, objectDataList) -> handleQuoteValue(objectDataList, arg.getRelatedDescribe(apiName)));
        }
    }

    private void doUpdateData(HandlerContext context, EditActionHandler.Arg arg) {
        IObjectData objectData = arg.objectData();
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        // 变更单
        doCreateChangeOrder(context.getUser(), objectDescribe, objectData, arg);
        Map<String, Object> masterUpdateMap = ObjectDataExt.of(copyAndRemoveSpecialFields(arg, objectDescribe.getApiName(),
                ObjectDataExt.of(arg.getDataDiffResult().getUpdatedFieldMap()).getObjectData())).toMap();
        CreateChangeOrder.Arg changeArg = buildChangeArg(objectDescribe, arg, "");

        IObjectData objectDataCp = changeArg.getMasterData();
        Map<String, List<IObjectData>> detailToUpdateMap = changeArg.getDetailToUpdateMap();

        UpdateMasterAndDetailData.Arg updateArg = buildUpdateArg(context.getUser(), masterUpdateMap, objectDataCp,
                changeArg.getDetailToAddMap(), detailToUpdateMap, changeArg.getDetailToDeleteMap(), arg.getConvertRuleDataContainer(),
                arg.getInterfaceArg().realTimeCalculateDetailAuth());
        updateMasterAndDetail(context.getUser(), updateArg);
        // 更新变更单的快照数据
        modifyOriginalDataByChangeOrder(context.getUser(), updateArg, arg.getObjectDescribe(), arg.getInterfaceArg(), arg.detailsToUpdate());

        //如果新增了从对象，则往redis里加一把锁，防止用户继续保存出现错误数据
        if (CollectionUtils.notEmpty(changeArg.getDetailToAddMap())) {
            saveActionServiceFacade.tryLockWithUpdateKey(arg.getInterfaceArg(), arg.getObjectApiName(), context.getUser());
        }

        //更新数据中的version
        objectData.setVersion(objectDataCp.getVersion());
        ObjectDataExt.of(objectData).handleMultiLangField(objectDescribe);
        //设置已更新标记，后续发送mq使用
        ObjectDataExt.of(objectData).setUpdatedTag();
        arg.detailsToUpdate().stream().filter(x -> detailToUpdateMap.containsKey(x.getDescribeApiName())).forEach(x -> {
            detailToUpdateMap.get(x.getDescribeApiName()).stream().filter(y -> y.getId().equals(x.getId()))
                    .findFirst().ifPresent(y -> {
                        x.setVersion(y.getVersion());
                        //设置已更新标记，后续发送mq使用
                        ObjectDataExt.of(x).setUpdatedTag();
                    });
        });
    }

    private Map<String, List<IObjectData>> getDetailToUpdateMap(String tenantId, EditActionHandler.Arg arg, String actionType) {
        if (isChangeOrderAction(actionType)) {
            return ObjectDataExt.groupByDescribeApiName(arg.detailsToUpdate().stream()
                    .map(x -> copyAndRemoveSpecialFields(arg, x.getDescribeApiName(), x))
                    .collect(Collectors.toList()));
        }
        Map<String, List<IObjectData>> detailToUpdateMap;
        if (isIncrementUpdate(tenantId, arg.getObjectApiName())) {
            //灰度了增量更新的只更新diff的字段
            detailToUpdateMap = getDetailUpdateMapForIncrementUpdate(tenantId, arg);
        } else if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SKIP_NO_CHANGE_DETAILS_IN_EDIT_ACTION_EI, tenantId)) {
            //灰度企业只更新有变化的从对象
            detailToUpdateMap = getUpdateMapFromDetailChangeMap(arg);
        } else {
            detailToUpdateMap = ObjectDataExt.groupByDescribeApiName(arg.detailsToUpdate().stream()
                    .map(x -> copyAndRemoveSpecialFields(arg, x.getDescribeApiName(), x))
                    .collect(Collectors.toList()));
        }
        return detailToUpdateMap;
    }

    private void doCreateChangeOrder(User user, IObjectDescribe objectDescribe, IObjectData objectData, EditActionHandler.Arg arg) {
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        BaseObjectSaveAction.Arg interfaceArg = arg.getInterfaceArg();
        String actionType = interfaceArg.getActionType();
        if (!isChangeOrderAction(actionType)) {
            return;
        }
        if (!ObjectDescribeExt.of(objectDescribe).enabledChangeOrder()) {
            return;
        }
        CreateChangeOrder.Arg changeArg = buildChangeArg(objectDescribe, arg, actionType);

        CreateChangeOrder.Result changeResult = changeOrderHandlerLogicService(user, changeArg, actionType);
        if (!changeResult.isSuccess()) {
            throw new ValidateException(changeResult.getMessage());
        }
        throw new AcceptableValidateNoRollbackException(buildChangeOrderResult(arg, changeResult.getChangeOrderApiName(), changeResult.getChangeOrderDataId()));
    }

    private CreateChangeOrder.Result changeOrderHandlerLogicService(User user, CreateChangeOrder.Arg changeArg, String actionType) {
        if ("change_order".equals(actionType)) {
            return changeOrderHandlerLogicService.doCreateChangeOrder(user, changeArg);
        }
        return changeOrderHandlerLogicService.doModifyChangeOrder(user, changeArg);
    }

    private boolean isChangeOrderAction(String actionType) {
        return "change_order".equals(actionType) || "re_change_order".equals(actionType);
    }

    private CreateChangeOrder.Arg buildChangeArg(IObjectDescribe objectDescribe, EditActionHandler.Arg arg, String actionType) {
        IObjectData objectData = ObjectDataExt.of(arg.getObjectData());
        IObjectData objectDataCp = copyAndRemoveSpecialFields(arg, objectDescribe.getApiName(), objectData);
        Map<String, List<IObjectData>> detailToAddMap = ObjectDataExt.groupByDescribeApiName(arg.detailsToAdd());
        Map<String, List<IObjectData>> detailToUpdateMap = getDetailToUpdateMap(objectDescribe.getTenantId(), arg, actionType);
        Map<String, List<IObjectData>> detailToDeleteMap = ObjectDataExt.groupByDescribeApiName(arg.detailsToDelete());

        CreateChangeOrder.Arg changeArg = new CreateChangeOrder.Arg();
        changeArg.setDescribe(objectDescribe);
        changeArg.setMasterData(objectDataCp);
        changeArg.setDetailToAddMap(detailToAddMap);
        changeArg.setDetailToUpdateMap(detailToUpdateMap);
        changeArg.setDetailToDeleteMap(detailToDeleteMap);
        if (isChangeOrderAction(actionType)) {
            changeArg.setOriginalData(arg.getDbMasterData().toObjectData());
            changeArg.setOriginalDetails(ObjectDataDocument.ofDataMap(arg.getDbDetailDataMap()));
        }
        Map<String, Object> changeOrderCallbackData = JacksonUtils.fromJson(arg.getContextData("change_order_callback_data"), Map.class);
        changeArg.setCallBackData(CollectionUtils.nullToEmpty(changeOrderCallbackData));
        return changeArg;
    }

    private void updateMasterAndDetail(User user, UpdateMasterAndDetailData.Arg updateArg) {
        serviceFacade.updateMasterAndDetailData(user, updateArg);
    }

    private UpdateMasterAndDetailData.Arg buildUpdateArg(User user, Map<String, Object> masterUpdateMap,
                                                         IObjectData objectData, Map<String, List<IObjectData>> detailToAddMap,
                                                         Map<String, List<IObjectData>> detailToUpdateMap,
                                                         Map<String, List<IObjectData>> detailToDeleteMap,
                                                         ConvertRuleDataContainer convertRuleDataContainer,
                                                         boolean realTimeCalculateDetailAuth) {
        return UpdateMasterAndDetailData.Arg.builder()
                .incrementUpdate(isIncrementUpdate(user.getTenantId(), objectData.getDescribeApiName()))
                .incrementUpdateDetail(isIncrementUpdate(user.getTenantId(), objectData.getDescribeApiName()))
                .toUpdateMap(masterUpdateMap)
                .masterObjectData(objectData)
                .detailsToAdd(detailToAddMap)
                .detailsToUpdate(detailToUpdateMap)
                .detailsToDelete(detailToDeleteMap)
                .convertRuleDataContainer(convertRuleDataContainer)
                .actionType("editHandler")
                .realTimeCalculateDetailAuth(realTimeCalculateDetailAuth)
                .build();
    }

    private void modifyOriginalDataByChangeOrder(User user, UpdateMasterAndDetailData.Arg updateArg,
                                                 IObjectDescribe objectDescribe,
                                                 BaseObjectSaveAction.Arg interfaceArg,
                                                 List<IObjectData> detailsToUpdate) {
        if (!ObjectDescribeExt.of(objectDescribe).isChangeOrderObject() || !BooleanUtils.isTrue(interfaceArg.getFromChangeOrder())) {
            return;
        }
        ObjectDataDocument originalData = interfaceArg.getOriginalData();
        if (Objects.isNull(originalData)) {
            return;
        }
        if (CollectionUtils.notEmpty(detailsToUpdate) && CollectionUtils.notEmpty(updateArg.getDetailsToUpdate())) {
            Map<String, List<IObjectData>> updateDataMap = ObjectDataExt.groupByDescribeApiName(detailsToUpdate);
            updateArg.getDetailsToUpdate().forEach((describeApiName, dataList) -> {
                Map<String, String> id2OriginalDetailDataMap = Maps.newHashMap();
                for (IObjectData data : CollectionUtils.nullToEmpty(updateDataMap.get(describeApiName))) {
                    ObjectDataExt dataExt = ObjectDataExt.of(data);
                    String id = dataExt.getId();
                    String originalDetailDataId = dataExt.getOriginalDetailDataId();
                    if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(originalDetailDataId)) {
                        id2OriginalDetailDataMap.put(id, originalDetailDataId);
                    }
                }
                for (IObjectData data : dataList) {
                    String originalDetailDataId = id2OriginalDetailDataMap.get(data.getId());
                    if (StringUtils.isNotBlank(originalDetailDataId)) {
                        ObjectDataExt.of(data).setOriginalDetailDataId(originalDetailDataId);
                    }
                }
            });
        }
        changeOrderHandlerLogicService.modifyChangeOrderOriginalData(user, updateArg, originalData.toObjectData(),
                ObjectDataDocument.ofDataMap(interfaceArg.getOriginalDetails()));
    }

    private IObjectData copyAndRemoveSpecialFields(EditActionHandler.Arg arg, String objectApiName, IObjectData objectData) {
        Set<String> fieldApiNamesToRemove = arg.getRemoveFieldMap().getOrDefault(objectApiName, Sets.newHashSet());
        return ObjectDataExt.of(ObjectDataExt.of(objectData).copy()).remove(fieldApiNamesToRemove).getObjectData();
    }


    private boolean isIncrementUpdate(String tenantId, String objectApiName) {
        return AppFrameworkConfig.isIncrementUpdateInEditAction(tenantId, objectApiName);
    }

    private Map<String, List<IObjectData>> getDetailUpdateMapForIncrementUpdate(String tenantId, EditActionHandler.Arg arg) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        arg.getDataDiffResult().getDetailChangeMap().forEach((apiName, changeMap) -> {
            //先获取从对象的diff结果
            Map<String, Map<String, Object>> updateDataMap = (Map<String, Map<String, Object>>) changeMap.get(ObjectAction.UPDATE.getActionCode());
            if (CollectionUtils.empty(updateDataMap)) {
                return;
            }
            //将diff结果拷贝一份用于更新
            List<IObjectData> toUpdateList = Lists.newArrayList();
            updateDataMap.forEach((id, fieldMap) -> {
                IObjectData data = ObjectDataExt.of(fieldMap).getObjectData();
                IObjectData cpData = copyAndRemoveSpecialFields(arg, apiName, data);
                if (CollectionUtils.notEmpty(ObjectDataExt.of(cpData).toMap())) {
                    cpData.setTenantId(tenantId);
                    cpData.setDescribeApiName(apiName);
                    cpData.setId(id);
                    toUpdateList.add(cpData);
                }
            });
            if (CollectionUtils.notEmpty(toUpdateList)) {
                result.put(apiName, toUpdateList);
            }
        });
        return result;
    }

    private Map<String, List<IObjectData>> getUpdateMapFromDetailChangeMap(EditActionHandler.Arg arg) {
        Map<String, List<IObjectData>> detailToUpdateMap = Maps.newHashMap();
        arg.getDataDiffResult().getDetailChangeMap().forEach((apiName, changeMap) -> {
            Map<String, Object> updateDataMap = (Map<String, Object>) changeMap.get(ObjectAction.UPDATE.getActionCode());
            if (CollectionUtils.empty(updateDataMap)) {
                return;
            }
            List<IObjectData> toUpdateList = arg.detailsToUpdate().stream()
                    .filter(x -> updateDataMap.containsKey(x.getId()))
                    .map(x -> copyAndRemoveSpecialFields(arg, apiName, x))
                    .collect(Collectors.toList());
            detailToUpdateMap.put(apiName, toUpdateList);
        });
        return detailToUpdateMap;
    }

}
