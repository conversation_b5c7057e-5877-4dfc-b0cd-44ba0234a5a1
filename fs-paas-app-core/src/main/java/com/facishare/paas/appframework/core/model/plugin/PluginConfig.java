package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by zhouwr on 2022/4/14.
 */
@Slf4j
@UtilityClass
public class PluginConfig {

    private Map<RequestType, List<PluginInfo>> pluginMap = Maps.newHashMap();

    static {
        ConfigFactory.getConfig("fs-paas-plugin-config", config -> {
            String content = config.getString();
            log.info("reload {},content:{}", config.getName(), content);
            reloadPluginInfo(content);
        });
    }

    private void reloadPluginInfo(String content) {
        if (Strings.isNullOrEmpty(content)) {
            return;
        }
        Map<RequestType, List<PluginInfo>> pluginInfoMap = JacksonUtils.fromJson(content, new TypeReference<Map<RequestType, List<PluginInfo>>>() {
        });
        if (CollectionUtils.empty(pluginInfoMap)) {
            return;
        }
        pluginMap = pluginInfoMap;
    }

    public List<String> getPluginApiNames(RequestType type, String objectApiName, String code, String method, String tenantId, String agentType) {
        List<PluginInfo> pluginInfos = pluginMap.get(type);
        if (CollectionUtils.empty(pluginInfos)) {
            return Lists.newArrayList();
        }
        Set<String> result = Sets.newLinkedHashSet();
        PluginSearchKey searchItem = new PluginSearchKey(type, objectApiName, code, method, tenantId, agentType);
        for (PluginInfo pluginInfo : pluginInfos) {
            if (pluginInfo.match(searchItem)) {
                result.add(pluginInfo.getPluginApiName());
            }
        }
        return Lists.newArrayList(result);
    }

    @Getter
    @ToString
    @EqualsAndHashCode
    private static class PluginInfo {
        private final GrayRule objectApiNameGray;
        private final String code;
        private final List<String> methods;
        private final GrayRule tenantGray;
        private final String pluginApiName;
        /**
         * 请求的客户端
         * mobile/web
         */
        private final String agentType;

        @JsonCreator
        public PluginInfo(@JsonProperty("objectApiNameGray") String objectApiName, @JsonProperty("code") String code,
                          @JsonProperty("methods") List<String> methods, @JsonProperty("tenantGray") String tenantId,
                          @JsonProperty("pluginApiName") String pluginApiName, @JsonProperty("agentType") String agentType) {
            this.objectApiNameGray = new GrayRule(objectApiName);
            this.code = code;
            this.methods = methods;
            this.tenantGray = new GrayRule(tenantId);
            this.pluginApiName = pluginApiName;
            this.agentType = agentType;
        }

        public boolean match(PluginSearchKey pluginSearchKey) {
            return Objects.equals(code, pluginSearchKey.getCode())
                    && (CollectionUtils.empty(methods) || methods.contains(pluginSearchKey.getMethod()))
                    && tenantGray.isAllow(pluginSearchKey.getTenantId())
                    && allowObjectApiName(pluginSearchKey.getObjectApiName())
                    && matchAgentType(pluginSearchKey.getAgentType());
        }

        private boolean matchAgentType(String agentType) {
            if (Strings.isNullOrEmpty(this.agentType)) {
                return true;
            }
            return Objects.equals(this.agentType, agentType);
        }

        private boolean allowObjectApiName(String thatObjectApiName) {
            if (objectApiNameGray.isAllow(thatObjectApiName)) {
                return true;
            }
            if (ObjectDescribeExt.isCustomObject(thatObjectApiName)) {
                return objectApiNameGray.isAllow("udobj");
            }
            return false;
        }
    }

    @Data
    private static class PluginSearchKey {
        private final RequestType type;
        private final String objectApiName;
        private final String code;
        private final String method;
        private final String tenantId;
        private final String agentType;

        public PluginSearchKey(RequestType type, String objectApiName, String code, String method, String tenantId, String agentType) {
            this.type = type;
            this.objectApiName = objectApiName;
            this.code = code;
            this.method = method;
            this.tenantId = tenantId;
            this.agentType = agentType;
        }
    }

}
