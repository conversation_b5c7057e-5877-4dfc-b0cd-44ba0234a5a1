package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ImportConfig;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.prm.model.IPartnerData;
import com.facishare.paas.appframework.prm.model.OutResources;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class StandardInsertImportDataAction extends BaseImportDataAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.InsertImportData.getFunPrivilegeCodes();
    }

    @Override
    protected void customInit(List<ImportData> dataList) {
        //业务类型
        setDefaultRecordType(dataList, objectDescribe);
        importLogMessageBuilder.start(ImportLogMessage.VALID_UNIQUENESS_RULE);
        // 唯一性规则校验
        stopWatch.lap("validUniquenessRule");
        validUniquenessRule();
        stopWatch.lap("validUniquenessRule end");
        importLogMessageBuilder.end(ImportLogMessage.VALID_UNIQUENESS_RULE);
        //同步负责人
        syncOwner(dataList);
        //公共对象2个预制字段赋值
        initPublicDataFlag(dataList);
        //校验查重逻辑
        importLogMessageBuilder.start(ImportLogMessage.VALID_DUPLICATE_SEARCH);
        validDuplicateSearch();
        importLogMessageBuilder.end(ImportLogMessage.VALID_DUPLICATE_SEARCH);
    }

    private void initPublicDataFlag(List<ImportData> dataList) {
        if (!objectDescribeExt.isPublicObject()) {
            return;
        }
        for (ImportData importData : dataList) {
            IObjectData dataData = importData.getData();
            ObjectDataExt.of(dataData).initPublicDataFlagBeforeCreate(actionContext.getUser());
        }
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        //校验多货币字段
        validateCurrency(dataList);
        //过滤没有负责人的客户  770负责人非必输去除过滤校验
        objectDescribeExt.getOwnerField().ifPresent(ownerField -> {
            if (ownerField.isRequired()) {
                validateOwner(dataList);
            }
        });
    }

    private void validateCurrency(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList) || !ObjectDescribeExt.of(objectDescribe).containsMultiCurrencyField()) {
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            String currency = ObjectDataExt.of(data.getData()).getCurrency();
            Object exchangeRate = ObjectDataExt.of(data.getData()).getExchangeRate();
            if (Strings.isNullOrEmpty(currency) && !ObjectDataExt.isValueEmpty(exchangeRate)) {
                errorList.add(new ImportError(data.getRowNo(), I18NExt.text(I18NKey.CURRENCY_CANNOT_BE_EMPTY)));
            }
        });
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        fillERInfoWhenOutUser(actionContext.getUser(), validList);

        validList.forEach(data -> {
            addOwnerIntoTeamMember(data);
            data.setTenantId(objectDescribeExt.getTenantId());
            data.setDescribeApiName(objectDescribeExt.getApiName());
            data.setDescribeId(objectDescribeExt.getId());
            if (ObjectDataExt.of(data).getLifeStatusText() == null) {
                ObjectDataExt.of(data).setLifeStatus(arg.getIsApprovalFlowEnabled() ? ObjectLifeStatus.INEFFECTIVE :
                        ObjectLifeStatus.NORMAL);
            }
            if (ObjectDataExt.of(data).getLockStatus() == null) {
                ObjectDataExt.of(data).setLockStatus(ObjectLockStatus.UNLOCK.getStatus());
            }
        });

        Optional<SignInFieldDescribe> signInFieldDescribe = objectDescribeExt.getSignInFieldDescribe();
        signInFieldDescribe.ifPresent(signInField -> {
            validList.forEach(data -> {
                data.set(signInField.getVisitStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
                data.set(signInField.getSignOutStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
                data.set(signInField.getSignInStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            });
        });

        Optional<PaymentFieldDescribe> paymentFieldDescribe = objectDescribeExt.getPaymentFieldDescribe();
        paymentFieldDescribe.ifPresent(paymentField -> {
            validList.forEach(data -> {
                data.set(paymentField.getPayStatusFieldApiName(), Payment.PAY_STATUS_INCOMPLETE);
                if (Objects.isNull(data.get(paymentField.getPayAmountFieldApiName()))) {
                    data.set(paymentField.getPayAmountFieldApiName(), 0);
                }
            });
        });

        //补充多货币相关字段的值
        serviceFacade.fillMultiCurrencyFields(validList, objectDescribe);

    }

    private void fillERInfoWhenOutUser(User user, List<IObjectData> dataList) {
        if (!user.isOutUser()) {
            return;
        }

        if (!isSyncOwnerAndOuterOwner()) {
            return;
        }

        String partnerId = getPartnerId(user);
        // 每条数据回填合作伙伴id、外部负责人、外部相关团队负责人、负责人
        dataList.stream().map(ObjectDataExt::of).forEach(dataExt -> {
            // 下游灰度导入支持外部负责人，如果外部负责人不为空，则不能设置当前登录用户为外部负责人
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY, user.getTenantId())) {
                String outUserId = dataExt.getOutOwnerId().orElse(null);
                if (StringUtils.isNotBlank(outUserId)) {
                    dataExt.setCreatedBy(user.getOutUserId());
                    dataExt.setOutTenantId(user.getOutTenantId());
                    dataExt.setPartnerId(partnerId);
                    dataExt.synchronizeOutTeamMemberOwner(user.getOutTenantId(), outUserId);
                    return;
                }
            }
            dataExt.setOutOwner(Lists.newArrayList(user.getOutUserId()));
            dataExt.setCreatedBy(user.getOutUserId());
            dataExt.setOutTenantId(user.getOutTenantId());
            dataExt.setPartnerId(partnerId);

            // 同步外部相关团队负责人
            dataExt.synchronizeOutTeamMemberOwner(user.getOutTenantId(), user.getOutUserId());
        });

    }

    private String getPartnerId(User user) {
        String partnerId = null;
        if (AppIdMapping.isPRM(actionContext.getAppId())) {
            partnerId = infraServiceFacade.getUpstreamMapperObjectId(user, "PartnerObj");
        }
        return partnerId;
    }

    @Override
    protected void fillPartnerInfo(List<IObjectData> validList) {
    }

    @Override
    protected void fillOutResource(List<IObjectData> validList) {
        if (CollectionUtils.empty(validList)) {
            return;
        }
        if (AppIdMapping.isPRM(actionContext.getAppId())) {
            for (IObjectData objectData : validList) {
                objectData.set(IPartnerData.OUT_RESOURCES, OutResources.PARTNER.getValue());
            }
        } else if (AppIdMapping.isFXT(actionContext.getAppId())) {
            for (IObjectData objectData : validList) {
                objectData.set(IPartnerData.OUT_RESOURCES, OutResources.FXT.getValue());
            }
        }
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        if (arg.getIsWorkFlowEnabled() != null) {
            RequestContext requestContext = RequestContextManager.getContext();
            requestContext.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, arg.getIsWorkFlowEnabled());
        }
        ObjectDataExt.correctValue(actionContext.getUser(), validList, objectDescribe);
        MetaDataActionService.CreateAttributes attributes = MetaDataActionService.CreateAttributes.builder()
                .handleContext(context -> ActionContextExt.of(context).setObjectDataSource("import").setActionType("insertImport").getContext())
                .useAutoNumber(false)
                .synchronizeMasterStatus(false)
                .calculateDefaultValue(AppFrameworkConfig.isGrayCalculateDefaultValueForInsertImport(actionContext.getTenantId()))
                .calculateFormula(AppFrameworkConfig.calculateInImportEI(actionContext.getTenantId()))
                .build();
        return serviceFacade.bulkSaveObjectData(validList, actionContext.getUser(), attributes);
    }

    @Override
    protected IUdefFunction findFunction() {
        String value = ImportConfig.getFuncRelationValue(objectDescribeExt.getApiName(), ImportConfig.INSERT, getImportPreProcessing());
        return serviceFacade.getFunctionLogicService().findFunctionByFuncRelationKey(actionContext.getUser(), ImportConfig.IMPORT_RELATION_TYPE,
                value, objectDescribeExt.getApiName()).orElse(null);
    }

    @Override
    protected void fillDataOwnDeptAndOrganization() {
        // 从对象不支持导入归属部门
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        Set<Integer> errorRowNoSet = allErrorList.stream().map(ImportError::getRowNo).collect(Collectors.toSet());

        // 灰度企业，下游新建数据。
        // 归属部门、归属组织使用下游企业对应的 客户、合作伙伴数据上的部门和组织
        User user = actionContext.getUser();
        List<IObjectData> objectDataList = dataList.stream()
                .filter(importData -> !errorRowNoSet.contains(importData.getRowNo()))
                .map(ImportData::getData)
                .collect(Collectors.toList());
        boolean fillData = serviceFacade.fillDataOwnDeptAndOrgByOutUser(user, objectDescribeExt, Lists.newArrayList(objectDataList));
        if (fillData) {
            validateDataOwnDeptAndOrg();
            // 新建导入需要根据数据归属部门设置相关团队
            setupTeamInterconnectedDepartments();
            return;
        }

        Set<String> ownerIds = objectDataList.stream()
                .flatMap(data -> data.getOwner().stream())
                .collect(Collectors.toSet());

        OrganizationInfo organizationInfo = serviceFacade.findMainOrgAndDeptByUserId(actionContext.getTenantId(), user.getUserId(), Lists.newArrayList(ownerIds));
        fillDataOwnDept(errorRowNoSet, organizationInfo);
        fillDataOwnOrg(errorRowNoSet, organizationInfo);
        // 新建导入需要根据数据归属部门设置相关团队
        setupTeamInterconnectedDepartments();
    }

    /**
     * 设置相关团队
     */
    private void setupTeamInterconnectedDepartments() {
        Set<Integer> errorRowNoSet = allErrorList.stream()
                .map(ImportError::getRowNo)
                .collect(Collectors.toSet());

        List<IObjectData> objectDataList = dataList.stream()
                .filter(importData -> !errorRowNoSet.contains(importData.getRowNo()))
                .map(ImportData::getData)
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(objectDataList)) {
            serviceFacade.setupTeamInterconnectedDepartments(actionContext.getUser(), objectDescribe, objectDataList);
        }
    }

    private void validateDataOwnDeptAndOrg() {
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            if (CollectionUtils.empty(objectData.getDataOwnDepartment())) {
                String fieldLabel = objectDescribeExt.getFieldLabelByName(IObjectData.DATA_OWN_DEPARTMENT);
                ImportError importError = new ImportError(importData.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.AREA_NOT_NULL, I18NKey.AREA_NOT_NULL, fieldLabel));
                errorList.add(importError);
            }
            if (objectDescribeExt.isOpenOrganization() && CollectionUtils.empty(objectData.getDataOwnOrganization())) {
                String fieldLabel = objectDescribeExt.getFieldLabelByName(IObjectData.DATA_OWN_ORGANIZATION);
                ImportError importError = new ImportError(importData.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.AREA_NOT_NULL, I18NKey.AREA_NOT_NULL, fieldLabel));
                errorList.add(importError);
            }
        }
        mergeErrorList(errorList);
    }

    @Override
    protected void fillDataOutOwnDeptAndOrganization() {
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        Set<Integer> errorRowNoSet = allErrorList.stream().map(ImportError::getRowNo).collect(Collectors.toSet());
        List<IObjectData> objectDataList = dataList.stream()
                .filter(importData -> !errorRowNoSet.contains(importData.getRowNo()))
                .map(ImportData::getData)
                .collect(Collectors.toList());
        serviceFacade.fillOutDataOwnDeptAndOrgByOutOwner(actionContext.getUser(), objectDescribe, objectDataList);
    }

    @Override
    protected final void calculateFormula(List<IObjectData> validDataList) {
        //灰度了计算默认值，则不单独处理计算字段了
        if (AppFrameworkConfig.isGrayCalculateDefaultValueForInsertImport(actionContext.getTenantId())) {
            return;
        }
        try {
            CalculateFields calculateFields = infraServiceFacade.computeCalculateFieldsForAddAction(objectDescribe, null, true);
            serviceFacade.batchCalculateBySortFields(actionContext.getUser(), validDataList, calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap());
        } catch (Exception e) {
            log.error("calculateFormula failed", e);
        }
    }

    @Override
    protected void fillOutTeamMember(List<ImportData> dataList) {
        handleOutTeamMember(dataList);
    }

    @Override
    protected boolean removeOutTeamMember() {
        return false;
    }

    private void fillDataOwnOrg(Set<Integer> errorRowNoSet, OrganizationInfo organizationInfo) {
        if (!objectDescribeExt.isOpenOrganization()) {
            return;
        }

        for (ImportData importData : dataList) {
            if (errorRowNoSet.contains(importData.getRowNo())) {
                continue;
            }
            ObjectDataExt dataExt = ObjectDataExt.of(importData.getData());
            if (!Strings.isNullOrEmpty(getStringValue(dataExt, ObjectDataExt.DATA_OWN_ORGANIZATION))) {
                continue;
            }
            dataExt.getOwnerId()
                    .map(organizationInfo::getMainOrg)
                    .ifPresent(dataExt::setDataOwnOrgByDeptInfo);
        }
    }

    /**
     * @param errorRowNoSet
     * @param organizationInfo 返回用户的主属部门或者主属租值，且有如果主属部门的类型为组织则返回主属部门为空
     */
    private void fillDataOwnDept(Set<Integer> errorRowNoSet, OrganizationInfo organizationInfo) {
        for (ImportData importData : dataList) {
            if (errorRowNoSet.contains(importData.getRowNo())) {
                continue;
            }
            ObjectDataExt dataExt = ObjectDataExt.of(importData.getData());
            // todo: 判断是否部门是否是组织
            if (!Strings.isNullOrEmpty(getStringValue(dataExt, ObjectDataExt.DATA_OWN_DEPARTMENT))) {
                continue;
            }
            // todo: 判断主部门是否是组织
            if (CollectionUtils.notEmpty(dataExt.getOwner())) {
                dataExt.getOwnerId()
                        .map(organizationInfo::getMainDeptId)
                        .ifPresent(dataExt::setDataOwnDepartmentId);
            }
        }
    }

    @Override
    protected IObjectData prepareData(ImportData importData) {
        return importData.getData();
    }

    @Override
    protected void validUniquenessRuleInDB() {
        List<String> fieldNames = getRuleFieldName(uniqueRule);
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        try {
            Map<String, List<String>> duplicateIDMap = findDuplicateDataMapByUniquenessRule();
            List<ImportError> errorList = dataList.stream()
                    .filter(x -> duplicateIDMap.containsKey(x.getData().getId()))
                    .map(importData -> new ImportError(importData.getRowNo(), I18NExt.getOrDefault(I18NKey.VALID_UNIQUENESS_MESSAGE_DB, I18NKey.VALID_UNIQUENESS_MESSAGE_DB,
                            UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribeExt))))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(errorList)) {
                log.warn("validUniquenessRuleInDB duplicate, tenant=>{}, describeApiName=>{}, user=>{}, duplicateIDMap=>{}",
                        actionContext.getTenantId(), arg.getApiName(), JSON.toJSONString(actionContext.getUser()), JSON.toJSONString(duplicateIDMap));
            }
            mergeErrorList(errorList);
        } finally {
            if (CollectionUtils.notEmpty(dataList)) {
                dataList.stream()
                        .filter(Objects::nonNull)
                        .map(ImportData::getData)
                        .filter(Objects::nonNull)
                        .map(ObjectDataExt::of)
                        .forEach(objectDataExt -> objectDataExt.remove(IObjectData.ID));
            }
        }
    }

    @Override
    protected void convertFields(List<StandardInsertImportDataAction.ImportData> dataList) {
        if (importReferenceFieldMappingSwitch) {
            if (!isSupportFieldMapping()) {
                convertReferenceNameToID(dataList);
            }
            super.convertFields(dataList);
            return;
        }
        if (MATCHING_TYPE_ID != arg.getMatchingType()) {
            super.convertFields(dataList);
            return;
        }
        if (!isSupportFieldMapping()) {
            convertReferenceNameToID(dataList);
        }
        super.convertFields(dataList);
    }

    /**
     * 联合导入需要重写转换id的方式
     */
    protected void convertReferenceNameToID(List<ImportData> dataList) {
        ImportExportExt.convertReferenceNameToID(dataList, arg.getRows(), objectDescribeExt, null, importReferenceFieldMappingSwitch);
    }

    @Override
    protected void startImportWorkFlow(List<IObjectData> actualList) {
        if (CollectionUtils.empty(actualList)) {
            return;
        }

        User user = actionContext.getUser();
        String apiName = objectDescribe.getApiName();
        actualList.stream().filter(data -> ObjectDataExt.of(data).isNormal()).forEach(data -> {
            String dataId = data.getId();
            infraServiceFacade.startWorkFlow(dataId, apiName, WorkflowProducer.TRIGGER_START, user, null, true);
        });
    }

    @Override
    protected void startImportApprovalFlow() {
        if (arg.getIsApprovalFlowEnabled()) {
            // 触发审批流
            Map<String, Map<String, Object>> callbackDataMap = actualList.stream()
                    .map(IObjectData::getId)
                    .collect(Collectors.toMap(it -> it,
                            it -> buildFlowCallbackData()));
            serviceFacade.batchStartApprovalAsynchronous(ApprovalFlowTriggerType.CREATE, actionContext.getUser(), actualList, Maps.newHashMap(), callbackDataMap);

        }
    }

    private Map<String, Object> buildFlowCallbackData() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ExtraDataKeys.TRIGGER_WORK_FLOW, arg.getIsWorkFlowEnabled());
        map.put(ExtraDataKeys.TRIGGER_FROM, DefaultObjectImportProvider.INSERT_IMPORT);
        return map;
    }

    @Override
    protected void recordImportDataLog(List<IObjectData> actualList) {
        if (CollectionUtils.empty(actualList)) {
            return;
        }
        // 记录新建数据
        serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Import, objectDescribe, actualList, getLogExtendsInfo());

        // 记录添加相关团队
        if (!objectDescribeExt.isSlaveObject()) {
            List<IObjectData> objectDataList = ObjectDataExt.copyList(actualList);
            objectDataList.forEach(x -> ObjectDataExt.of(x).setTeamMembers(Lists.newArrayList()));
            serviceFacade.logByActionType(actionContext.getUser(), EventType.ADD, ActionType.AddEmployee, objectDataList, actualList, objectDescribe);
        }
    }

    @Override
    protected boolean checkUniqueInDB(IObjectData data, IObjectDescribe describe) {
        return infraServiceFacade.isUniqueCheck(data, describe, false);
    }

    @Override
    protected void validateUniqueDataInDB() {
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            Set<Map.Entry<String, Object>> entries = ObjectDataDocument.of(importData.getData()).entrySet();
            entries.forEach(entry -> {
                Optional<IFieldDescribe> fieldDescribeSilently = objectDescribeExt.getFieldDescribeSilently(entry.getKey());
                fieldDescribeSilently.ifPresent(fieldDescribe -> {
                    String valueStr = getStringValue(importData.getData(), fieldDescribe);
                    if (fieldDescribe.isUnique() && !Strings.isNullOrEmpty(valueStr)) {
                        IObjectData validateUniqueData = buildValidateUniqueData(fieldDescribe, importData.getData());
                        if (!checkUniqueInDB(validateUniqueData, objectDescribeExt)) {
                            log.warn("data is not unique name:{}", importData.getData().getName());
                            errorList.add(new ImportError(importData.getRowNo(),
                                    I18NExt.getOrDefault(I18NKey.CONTENT_NOT_UNIQUE, I18NKey.CONTENT_NOT_UNIQUE, fieldDescribe.getLabel())));
                            // 因为元数据是根据整条数据进行唯一性校验的，并非根据某个字段
                            // 因此，只要校验出此条数据重复，就进行下一条数据的校验
                        }
                    }
                });
            });
        }

        mergeErrorList(errorList);
    }

    /**
     * 构建需要校验的数据data
     * 只保留必要的字段和需要校验的字段
     * 防止一个唯一字段重复,导致别的没有重复的唯一字段也提示重复
     *
     * @param fieldDescribe
     * @param data
     * @return 待校验的数据
     */
    private IObjectData buildValidateUniqueData(IFieldDescribe fieldDescribe, IObjectData data) {
        IObjectData result = new ObjectData();
        result.setDescribeApiName(data.getDescribeApiName());
        result.setId(data.getId());
        result.setTenantId(data.getTenantId());
        Object fieldValue = data.get(fieldDescribe.getApiName());
        result.set(fieldDescribe.getApiName(), fieldValue);
        // 开启多语的字段,需要拷贝多语的配置
        if (BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
            String multiLangExtraField = FieldDescribeExt.getMultiLangFieldFromExtraField(fieldDescribe.getApiName());
            Object value = data.get(multiLangExtraField);
            if (!ObjectDataExt.isValueEmpty(value)) {
                result.set(multiLangExtraField, value);
            }
        }
        // 单选\多选的其他选项,需要拷贝 __o 的数据
        if (FieldDescribeExt.isSelectField(fieldDescribe.getType()) && ObjectDataExt.hasOtherValue(fieldValue)) {
            String selectOtherField = FieldDescribeExt.getSelectOther(fieldDescribe.getApiName());
            Object value = data.get(selectOtherField);
            if (!ObjectDataExt.isValueEmpty(value)) {
                result.set(selectOtherField, value);
            }
        }
        return result;
    }

    @Override
    protected List<IObjectData> filterValidDataList(List<ImportData> dataList, List<ImportError> allErrorList) {
        List<ImportData> validImportList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            boolean hasError = false;
            for (ImportError importError : allErrorList) {
                if (Objects.equals(importData.getRowNo(), importError.getRowNo())) {
                    hasError = true;
                    break;
                }
            }
            if (!hasError) {
                validImportList.add(importData);
            }
        }
        return filterAutoNumberUnique(validImportList, allErrorList);
    }

    private List<IObjectData> filterAutoNumberUnique(List<ImportData> validImportList, List<ImportError> allErrorList) {
        if (CollectionUtils.empty(validImportList)) {
            return Lists.newArrayList();
        }
        // 生成自增编号，并校验自增编号是否重复
        List<IObjectData> objectDataList = validImportList.stream().map(ImportData::getData).collect(Collectors.toList());
        infraServiceFacade.generateAutoNumber(objectDescribe, objectDataList);
        Map<String, String> failCheckMap = infraServiceFacade.filterDataListByAutoNumberUniqueCheck(objectDataList, objectDescribe);
        if (CollectionUtils.empty(failCheckMap)) {
            return objectDataList;
        }

        // 将自增编号重复的行号记录到allErrorList中
        Map<String, List<ImportData>> importDataGroupByName = validImportList.stream()
                .collect(Collectors.groupingBy(data -> data.getData().getName()));

        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        failCheckMap.forEach((name, fieldName) -> {
            String fieldLabelByName = objectDescribeExt.getFieldLabelByName(fieldName);
            for (ImportData importData : CollectionUtils.nullToEmpty(importDataGroupByName.get(name))) {
                Object value = importData.getData().get(fieldName);
                allErrorList.add(new ImportError(importData.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.AUTO_NUMBER_ALREADY_EXIST_PLEASE_IMPORT_AGAIN, I18NKey.NUMBER_ALREADY_EXIST_PLEASE_IMPORT_AGAIN, fieldLabelByName, value)));
                objectDataList.remove(importData.getData());
            }
        });

        return objectDataList;
    }

    /**
     * 同步负责人
     */
    private void syncOwner(List<ImportData> dataList) {
        //同步主对象的负责人
        Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe = objectDescribeExt.getMasterDetailFieldDescribe();
        masterDetailFieldDescribe.ifPresent(field -> {
            Map<String, List<IObjectData>> detailMap = Maps.newHashMap();
            for (ImportData importData : dataList) {
                IObjectData data = importData.getData();
                String stringValue = getStringValue(data, field);
                if (Strings.isNullOrEmpty(stringValue)) {
                    continue;
                }

                if (!detailMap.containsKey(stringValue)) {
                    detailMap.put(stringValue, Lists.newArrayList());
                }
                detailMap.get(stringValue).add(data);
            }
            syncDetailOwnerStuff(field, detailMap);
        });

        // 下游人员导入，同步负责人为上游对接人
        if (!masterDetailFieldDescribe.isPresent() && actionContext.getUser().isOutUser() && isSyncOwnerAndOuterOwner()) {
            syncOwnerFromUpstreamOwnerWhenOutUser(dataList);
        }
    }

    /**
     * 下游导入时，是否需要补充负责人和外部负责人
     *
     * @return true 补充， false 不补充
     */
    protected boolean isSyncOwnerAndOuterOwner() {
        return true;
    }

    protected void syncOwnerFromUpstreamOwnerWhenOutUser(List<ImportData> dataList) {
        dataList.forEach(a -> ObjectDataExt.of(a.getData()).setDataOwner(actionContext.getUser()));
    }

    /**
     * 根据主从字段查主对象数据，回填从对象负责人和外部负责人
     *
     * @param field
     * @param detailData
     */
    private void syncDetailOwnerStuff(MasterDetail field, Map<String, List<IObjectData>> detailData) {
        if (Objects.isNull(field) || CollectionUtils.empty(detailData)) {
            return;
        }

        List<IObjectData> masterDataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(detailData.keySet()), field.getTargetApiName());
        if (CollectionUtils.empty(masterDataList)) {
            return;
        }

        for (IObjectData master : masterDataList) {
            List<IObjectData> detailList = detailData.get(master.getId());
            if (CollectionUtils.empty(detailList)) {
                continue;
            }

            for (IObjectData detail : detailList) {
                detail.set(ObjectDataExt.OWNER, master.get(ObjectDataExt.OWNER));
            }
        }
        // 从对象同步主对象的外部负责人
        syncOutOwner(detailData, masterDataList);
    }

    private void syncOutOwner(Map<String, List<IObjectData>> detailData, List<IObjectData> masterDataList) {
        for (IObjectData master : masterDataList) {
            ObjectDataExt.of(master).getOutOwnerId().ifPresent(outOwnerId -> {
                List<IObjectData> detailList = detailData.get(master.getId());
                if (CollectionUtils.empty(detailList)) {
                    return;
                }
                for (IObjectData detail : detailList) {
                    ObjectDataExt.of(detail).setOutTenantAndOutOwner(master.getOutTenantId(), outOwnerId);
                }
            });

        }
    }

    /**
     * 过滤掉没有负责人的客户
     */
    protected void validateOwner(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            if (CollectionUtils.empty(data.getData().getOwner())) {
                errorList.add(new ImportError(data.getRowNo(), I18NExt.text(I18NKey.CANNOT_ADD_IF_NO_OWNER)));
            }
        });
        mergeErrorList(errorList);
    }


    protected void addOwnerIntoTeamMember(IObjectData objectData) {
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        Optional<String> ownerId = dataExt.getOwnerId();
        ownerId.ifPresent(a -> {
            TeamMember teamMember = new TeamMember(a, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
            dataExt.addTeamMembers(Lists.newArrayList(teamMember));
        });
        dataExt.getOutOwnerId().ifPresent(outOwnerId -> {
            dataExt.synchronizeOutTeamMemberOwner(dataExt.getOutTenantId(), outOwnerId);
        });
    }

    @Override
    protected String getParentSelectOneValue(IObjectData data, String parentApiName) {
        return String.valueOf(data.get(parentApiName));
    }

    @Override
    protected boolean customFilterHeader(String apiName) {
        if (Strings.isNullOrEmpty(apiName)) {
            return true;
        }
        return getValidImportFields().stream().noneMatch(f -> apiName.equals(f.getApiName()));
    }

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        if (Objects.isNull(validFieldDescribeList)) {
            List<IFieldDescribe> updateImportTemplateField = infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe);
            return validFieldDescribeList = CollectionUtils.empty(updateImportTemplateField) ? Collections.emptyList() : updateImportTemplateField;
        }
        return validFieldDescribeList;
    }


    //获取号码归属地
    @Override
    protected void getPhoneNumberInfo(List<ImportData> dataList) {
    }
}
