package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.metadata.config.ObjectConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindDescribeList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private boolean isDraft = false;

        @JSONField(name = "M2")
        @SerializedName("isIncludeSystemObj")
        @JsonProperty("isIncludeSystemObj")
        private boolean isIncludeSystemObj = true;

        @JSONField(name = "M3")
        @SerializedName("isIncludeFieldDescribe")
        @JsonProperty("isIncludeFieldDescribe")
        private boolean isIncludeFieldDescribe = false;

        @JSONField(name = "M4")
        private String packageName;

        @JSONField(name = "M5")
        private boolean isAsc = false;

        @JSONField(name = "M6")
        @SerializedName("isIncludeUnActived")
        @JsonProperty("isIncludeUnActived")
        private boolean isIncludeUnActived = false;

        @JSONField(name = "M7")
        @SerializedName("describeDefineType")
        @JsonProperty("describeDefineType")
        private String describeDefineType;

        private String actionCode;

        @SerializedName("sourceInfo")
        @JsonProperty("sourceInfo")
        private String sourceInfo;

        @SerializedName("includeAttributes")
        @JsonProperty("includeAttributes")
        private Set<String> includeAttributes;

        private boolean includeBigObject;
        private boolean includeSocialObject;
        private boolean onlyVisibleScope;
        private Boolean includeChangeOrderObject;

        private boolean includeControlLevel;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDescribeDocument> objectDescribeList;

        @JSONField(name = "M2")
        private List<String> iconPathList;

        @JSONField(name = "M3")
        private int useableDescribeCount;

        private ManageGroupDTO manageGroup;

        private Map<String, ObjectConfig> objectConfigs;
    }
}
