package com.facishare.paas.appframework.core.predef.service.dto.license;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;
import java.util.Set;

public interface QueryLicenseFeature {
    @Data
    class Arg {
        @JSONField(name = "items")
        @JsonProperty(value = "items")
        private Set<String> items;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "feature")
        @JsonProperty(value = "feature")
        private Map<String, Boolean> feature;
    }
}
