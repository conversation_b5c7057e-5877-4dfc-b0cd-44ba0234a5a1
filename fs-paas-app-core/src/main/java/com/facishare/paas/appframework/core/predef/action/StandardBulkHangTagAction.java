package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 批量数据添加标签
 *
 */
public class StandardBulkHangTagAction extends AbstractStandardAsyncBulkAction<StandardBulkHangTagAction.Arg, StandardBatchUpdateTagAction.Arg>{
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected String getDataIdByParam(StandardBatchUpdateTagAction.Arg param) {
        return param.getDataId();
    }

    @Override
    protected List<StandardBatchUpdateTagAction.Arg> getButtonParams() {
        return arg.getDataIds().stream().map(id ->
                        StandardBatchUpdateTagAction.Arg.builder().dataId(id).tagIds(arg.getTagIds()).append(arg.getAppend()).build())
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        //CRM 提醒 可以显示按钮名
        return ObjectAction.BULK_HANG_TAG.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        //MQ ConsumerGroup 用它找Action
        return "BatchUpdateTag";
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateArg(arg);

    }

    private void validateArg(Arg arg) {
        if (Objects.isNull(arg) || CollectionUtils.empty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        // 过滤掉dataIds中的null值
        arg.setDataIds(arg.getDataIds().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        
        // 再次检查过滤后的dataIds是否为空
        if (CollectionUtils.empty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class Arg {
        @JsonProperty("data_ids")
        List<String> dataIds;
        @JsonProperty("tag_ids")
        List<String> tagIds;
        @JsonProperty("append")
        Boolean append;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class Result {
        boolean success;
    }

}
