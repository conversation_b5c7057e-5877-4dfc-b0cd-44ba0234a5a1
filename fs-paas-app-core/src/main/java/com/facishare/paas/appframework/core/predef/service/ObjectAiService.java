package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.CheckAiUserLicense;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.GenerateFieldApiName;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.GenerateObjectApiName;
import com.facishare.paas.appframework.metadata.ai.AIService;
import com.facishare.paas.appframework.metadata.ai.AiLogicService;
import com.facishare.paas.appframework.metadata.dto.ChatComplete;
import com.facishare.paas.appframework.metadata.dto.ai.FormulaGenerate;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/5/13
 */
@Service
@Slf4j
@ServiceModule("objectAi")
public class ObjectAiService {

    @Autowired
    private AiLogicService aiLogicService;
    @Autowired
    protected ServiceFacade serviceFacade;
    @Resource(name = "aiService")
    private AIService aiService;

    @ServiceMethod("generateFieldApiName")
    public GenerateFieldApiName.Result generateFieldApiName(GenerateFieldApiName.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getName())) {
            log.warn("generateFieldApiName error missing arg:{}", arg);
            return new GenerateFieldApiName.Result();
        }
        ChatComplete.Result chatCompleteResult = aiLogicService.chatComplete(context.getTenantId(),
                context.getUser().getUserId(),
                "FieldAgent",
                arg.getName());
        GenerateFieldApiName.Result result = new GenerateFieldApiName.Result();
        result.setSuccess(chatCompleteResult.getErrCode() == 0);
        result.setErrorMessage(chatCompleteResult.getErrMessage());
        result.setApiName((chatCompleteResult.getResult() != null) ? chatCompleteResult.getResult().getMessage() : null);
        return result;
    }

    @ServiceMethod("chatTranslate")
    public ChatComplete.TranslateResult chatTranslate(ChatComplete.AITransDTO arg, ServiceContext context) throws ValidateException {
        ChatComplete.TranslateResult chatCompleteResult = aiLogicService.chatTranslate(context.getTenantId(),
                context.getUser().getUserId(),
                arg);
        return chatCompleteResult;
    }

    @ServiceMethod("generateObjectApiName")
    public GenerateObjectApiName.Result generateObjectApiName(GenerateObjectApiName.Arg arg, ServiceContext context) {
        if (StringUtils.isBlank(arg.getName())) {
            log.warn("generateObjectApiName error missing arg:{}", arg);
            return new GenerateObjectApiName.Result();
        }
        ChatComplete.Result chatCompleteResult = aiLogicService.chatComplete(context.getTenantId(),
                context.getUser().getUserId(),
                "ObjectAgent",
                arg.getName());
        GenerateObjectApiName.Result result = new GenerateObjectApiName.Result();
        result.setSuccess(chatCompleteResult.getErrCode() == 0);
        result.setErrorMessage(chatCompleteResult.getErrMessage());
        result.setApiName((chatCompleteResult.getResult() != null) ? chatCompleteResult.getResult().getMessage() : null);
        return result;
    }

    @ServiceMethod("checkAiUserLicense")
    public CheckAiUserLicense.Result checkAiUserLicense(CheckAiUserLicense.Arg arg, ServiceContext context) {
        Map<String, Boolean> funcResult = serviceFacade.checkFuncPrivilege(context.getUser(), Lists.newArrayList(PrivilegeConstants.AI_OBJECT_USER_LICENSE));
        CheckAiUserLicense.Result result = new CheckAiUserLicense.Result();
        result.setHasAiUserLicense(funcResult.getOrDefault(PrivilegeConstants.AI_OBJECT_USER_LICENSE, false));
        return result;
    }

    /**
     * AI聊天生成计算公式
     */
    @ServiceMethod("generateFormula")
    public FormulaGenerate.ExpressionResult chatComplete(FormulaGenerate.Argument arg, ServiceContext context) {
        return aiService.chatComplete(context.getUser(), arg);
    }

}
