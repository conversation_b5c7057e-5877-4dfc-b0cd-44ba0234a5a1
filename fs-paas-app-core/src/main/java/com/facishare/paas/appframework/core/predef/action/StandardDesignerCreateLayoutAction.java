package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.DesignerCreateLayoutActionDomainPlugin;
import com.facishare.paas.appframework.metadata.dto.LayoutResult;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;
import java.util.Objects;

/**
 * Created by z<PERSON><PERSON>ju on 2023/11/9
 */
public class StandardDesignerCreateLayoutAction extends AbstractDesignerSaveLayoutAction {
    @Override
    protected DesignerCreateLayoutActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return DesignerCreateLayoutActionDomainPlugin.Arg.builder()
                .layoutData(LayoutDocument.of(newLayout))
                .describeData(ObjectDescribeDocument.of(newDescribe))
                .describeExtra(describeExtra)
                .build();
    }

    @Override
    protected void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        if (!(pluginResult instanceof DesignerCreateLayoutActionDomainPlugin.Result)) {
            return;
        }
        DesignerCreateLayoutActionDomainPlugin.Result result = (DesignerCreateLayoutActionDomainPlugin.Result) pluginResult;
        if (Objects.nonNull(result.getLayout())) {
            newLayout = result.getLayout().toLayout();
        }
        if (Objects.nonNull(result.getObjectDescribe())) {
            newDescribe = result.getObjectDescribe().toObjectDescribe();
        }
        if (Objects.nonNull(result.getDescribeExtra())) {
            describeExtra = result.getDescribeExtra();
        }
    }

    @Override
    protected Result saveLayout() {
        LayoutResult layoutResult = serviceFacade.createLayoutAndUpdateDescribe(buildLayoutContext(), newLayout, newDescribe, arg.getPersistentDataCalc());
        ObjectDescribeDocument describeExtraResult = updateDescribeExtra(actionContext.getUser(), newDescribe.getApiName(), describeExtra);
        infraServiceFacade.bulkUpsertFieldsExtra(actionContext.getUser(), newDescribe.getApiName(), arg.getFieldsExtra());
        return buildResult(layoutResult, describeExtraResult, arg.getFieldsExtra());
    }

    @Override
    protected Result saveMobileListLayout() {
        ILayout layoutResult = serviceFacade.createAbstractLayout(buildLayoutContext(), newLayout);
        return buildResult(layoutResult);
    }

    @Override
    protected Result saveListLayout() {
        ILayout layoutResult = serviceFacade.createListLayout(buildLayoutContext(), newLayout);
        return buildResult(layoutResult);
    }
}
