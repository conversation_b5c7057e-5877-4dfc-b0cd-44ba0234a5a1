package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.metadata.ui.layout.ILayout.DETAIL_LAYOUT_TYPE;

/**
 * <AUTHOR>
 * @date 2019/3/8 14:20
 * @instruction
 */
public class StandardTreeRelatedListController extends BaseListController<StandardTreeRelatedListController.Arg> {

    protected ObjectDescribeExt parentObjectDescribe;
    protected ObjectDescribeExt childObjectDescribe;

    @Override
    protected void init() {
        super.init();
        childObjectDescribe = objectDescribe;
        parentObjectDescribe = findObject(arg.getTargetParentObjDescApiName());
    }

    protected SearchTemplateQuery buildSearchTemplateQuery(ObjectDescribeExt objectDescribeExt, String relatedListName, String searchQueryInfo) {
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQuery(controllerContext.getUser(), objectDescribeExt,
                getSearchTemplateId(), searchQueryInfo, true);
        String refFieldName = objectDescribeExt.getRefFieldName(arg.getSourceObjDescApiName(), relatedListName)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, refFieldName, arg.getSourceDataId());
        return query;
    }


    @Override
    protected String getSearchQueryInfo() {
        return arg.getTargetChildSearchQueryInfo();
    }

    @Override
    protected String getSearchTemplateId() {
        return null;
    }

    @Override
    protected String getSearchTemplateApiName() {
        return null;
    }

    @Override
    protected String getSearchTemplateType() {
        return null;
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return null;
    }

    @Override
    protected String getUsePageType() {
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected BaseListController.Result doService(Arg arg) {
        List<ILayout> mobildLayouts = findMobileLayouts();
        SearchTemplateQuery queryChild = buildSearchTemplateQuery(childObjectDescribe, arg.getAssociatedChildObjectFieldRelatedListName(), arg.getTargetChildSearchQueryInfo());
        QueryResult<IObjectData> queryChildResult = getQueryResult(queryChild);
        objectDescribe = parentObjectDescribe;
        SearchTemplateQuery queryParent = buildSearchTemplateQuery(parentObjectDescribe, arg.getAssociatedParentObjectFieldRelatedListName(), arg.getTargetParentSearchQueryInfo());
        QueryResult<IObjectData> queryParentResult = getQueryResult(queryParent);
        mobildLayouts.addAll(findMobileLayouts());


        return buildResult(mobildLayouts, getDetailLayout(), queryChildResult, queryParentResult);
    }


    protected Result buildResult(List<ILayout> childLayouts, List<ILayout> parentLayouts, QueryResult<IObjectData> queryChildResult, QueryResult<IObjectData> queryParentResult) {
        ObjectData childObjectData = new ObjectData(childObjectDescribe.getApiName(), false, ObjectDataDocument.ofList(queryChildResult.getData()));
        ObjectData parentObjectData = new ObjectData(parentObjectDescribe.getApiName(), true, ObjectDataDocument.ofList(queryParentResult.getData()));

        Result result = new Result(Lists.newArrayList(ObjectDescribeDocument.ofList(Lists.newArrayList(childObjectDescribe, parentObjectDescribe))),
                LayoutDocument.ofList(childLayouts),
                LayoutDocument.ofList(parentLayouts),
                Lists.newArrayList(childObjectData, parentObjectData)
        );
        return result;
    }


    protected ILayout getChildLayout() {
        return serviceFacade.findDefaultLayout(buildLayoutContext(), DETAIL_LAYOUT_TYPE, arg.getTargetChildObjDescApiName());
    }

    protected ILayout getParentLayout() {
        return serviceFacade.findDefaultLayout(buildLayoutContext(), DETAIL_LAYOUT_TYPE, arg.getTargetParentObjDescApiName());
    }

    protected List<ILayout> getDetailLayout() {
        return Lists.newArrayList(getChildLayout(), getParentLayout());
    }


    @Data
    public static class Arg {
        @JSONField(name = "source_data_id")
        @JsonProperty("source_data_id")
        @SerializedName("source_data_id")
        private String sourceDataId;

        @JSONField(name = "source_obj_desc_api_name")
        @JsonProperty("source_obj_desc_api_name")
        @SerializedName("source_obj_desc_api_name")
        private String sourceObjDescApiName;

        @JSONField(name = "target_parent_obj_desc_api_name")
        @JsonProperty("target_parent_obj_desc_api_name")
        @SerializedName("target_parent_obj_desc_api_name")
        private String targetParentObjDescApiName;

        @JSONField(name = "target_child_obj_desc_api_name")
        @JsonProperty("target_child_obj_desc_api_name")
        @SerializedName("target_child_obj_desc_api_name")
        private String targetChildObjDescApiName;

        @JSONField(name = "associated_child_object_field_related_list_name")
        @JsonProperty("associated_child_object_field_related_list_name")
        @SerializedName("associated_child_object_field_related_list_name")
        private String associatedChildObjectFieldRelatedListName;

        @JSONField(name = "associated_parent_object_field_related_list_name")
        @JsonProperty("associated_parent_object_field_related_list_name")
        @SerializedName("associated_parent_object_field_related_list_name")
        private String associatedParentObjectFieldRelatedListName;

        @JSONField(name = "target_parent_search_query_info")
        @JsonProperty("target_parent_search_query_info")
        @SerializedName("target_parent_search_query_info")
        private String targetParentSearchQueryInfo;

        @JSONField(name = "target_child_search_query_info")
        @JsonProperty("target_child_search_query_info")
        @SerializedName("target_child_search_query_info")
        private String targetChildSearchQueryInfo;

        @JSONField(name = "price_book_id")
        @JsonProperty("price_book_id")
        @SerializedName("price_book_id")
        private String priceBookId;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BaseListController.Result {
        private List<ObjectDescribeDocument> objectDescribes;

        private List<LayoutDocument> listLayoutList;

        private List<LayoutDocument> detailLayoutList;

        private List<ObjectData> dataMapList;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ObjectData {
        private String describeApiName;
        private Boolean isParent;
        private List<ObjectDataDocument> dataList;

    }

}
