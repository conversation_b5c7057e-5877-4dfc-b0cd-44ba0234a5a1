package com.facishare.paas.appframework.prm;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.prm.enums.PrmPageType;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * prm layout渲染类
 *
 * <AUTHOR>
 */
@Slf4j
@Builder
public class PrmLayoutRender {

    private PrmPageType pageType;
    private LayoutExt layoutExt;
    private IObjectDescribe describe;

    public void render() {
        switch (pageType) {
            case AddOrEdit:
                renderAddOrEdit();
                break;
            case Detail:
                renderDetail();
                break;
            case NewDetail:
                renderNewDetail();
                break;
            default:
                break;
        }
    }

    private void renderAddOrEdit() {
        if (layoutExt.getLayout() == null) {
            return;
        }
        filterField();
    }

    private void renderDetail() {
        if (layoutExt.getLayout() == null) {
            return;
        }
        filterField();
        filterRelatedList();
        filterOtherInfoComponent();
        handleTopInfoComponent();
        filterButton();
    }

    private void renderNewDetail() {
        if (layoutExt.getLayout() == null) {
            return;
        }
        handleNewComponent();
        filterButton();
    }

    private void filterField() {
        Set<String> removeFields = getRemoveFields();
        if (CollectionUtils.notEmpty(removeFields)) {
            layoutExt.removeFields(removeFields);
        }
    }

    private Set<String> getRemoveFields() {
        Map<String, IFieldDescribe> fieldDescribeMap = describe.getFieldDescribeMap();
        String objectApiName = describe.getApiName();
        Set<String> removeFields = Sets.newHashSet();
        fieldDescribeMap.forEach((apiName, fieldDescribe) -> {
            //屏蔽不支持的lookup字段
            if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())
                    && fieldDescribe instanceof ObjectReferenceFieldDescribe) {
                if (PrmConstant.unSupportLookupApiNames.contains(((ObjectReferenceFieldDescribe) fieldDescribe)
                        .getTargetApiName())) {
                    if ("PartnerObj".equals(((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName())
                            && !"partner_id".equals(fieldDescribe.getApiName())) {
                        return;
                    }
                    removeFields.add(apiName);
                }
            }
            //屏蔽自定义人员字段
            if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType())
                    && IFieldDescribe.DEFINE_TYPE_CUSTOM.equals(fieldDescribe.getDefineType())) {
                removeFields.add(apiName);
            }
            //屏蔽自定义部门字段
            if (Objects.equals(fieldDescribe.getType(), IFieldType.DEPARTMENT)) {
                removeFields.add(apiName);
            }

            //屏蔽自定义部门字段
            if (Objects.equals(fieldDescribe.getType(), IFieldType.DEPARTMENT_MANY)) {
                removeFields.add(apiName);
            }
            String tenantId = RequestContextManager.getContext().getTenantId();
            //屏蔽人员多选
            if (Objects.equals(fieldDescribe.getType(), IFieldType.EMPLOYEE_MANY) &&
                    !PrmConstant.isSupportEmployeeMany(objectApiName, tenantId)) {
                removeFields.add(apiName);
            }
        });
        return removeFields;
    }

    private void filterRelatedList() {
        layoutExt.getRelatedComponent().ifPresent(relatedObjectComponent -> {
            List<IComponent> childComponents = GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently();
            removeRelatedItem(childComponents);
            relatedObjectComponent.setChildComponents(childComponents);
        });
    }

    private void filterOtherInfoComponent() {
        layoutExt.getOtherInfoComponent().ifPresent(otherInfoComponent -> {
            List<IComponent> childComponents = GroupComponentExt.of(otherInfoComponent).getChildComponentsSilently();
            childComponents.removeIf(f -> !f.getName().equals("operation_log"));
            otherInfoComponent.setChildComponents(childComponents);
        });
    }

    private void handleTopInfoComponent() {
        //移除顶部企信群按钮
        layoutExt.getTopInfoComponent().ifPresent(topInfoComponent -> {
            List<IButton> topButtons = topInfoComponent.getButtons();
            if (CollectionUtils.notEmpty(topButtons)) {
                topButtons.removeIf(button -> ButtonExt.QIXIN_CHAT_GROUP_ACTION.equals(button.getAction()));
                topInfoComponent.setButtons(topButtons);
            }
        });
    }

    /**
     * 下游详情不支持企信群、附件、邮箱等相关的对象
     */
    private void handleNewComponent() {
        // todo 目前下游没有完全实现 v3 布局，所以暂时处理两种布局做兼容
        boolean isWeb;
        Set<String> toRemoveFields = getRemoveFields();
        List<IComponent> componentList = layoutExt.getComponentsSilently();
        if (!CollectionUtils.notEmpty(componentList)) {
            return;
        }
        isWeb = mobileDetailRender(componentList, toRemoveFields);
        if (isWeb) {
            webDetailRender(componentList, toRemoveFields);
        }
        layoutExt.setComponents(componentList);
        log.info("handleNewComponent {}", layoutExt.getComponentsSilently());
    }

    private boolean mobileDetailRender(List<IComponent> componentList, Set<String> toRemoveFields) {
        boolean isWeb = true;
        for (IComponent component : componentList) {
            if (component instanceof IGroupComponent) {
                isWeb = false;
                List<IComponent> childComponents = GroupComponentExt.of((IGroupComponent) component).getChildComponentsSilently();
                if (!CollectionUtils.notEmpty(childComponents)) {
                    continue;
                }
                if (Objects.equals(ComponentExt.TOP_COMPONENT_NAME, component.getName())) {
                    childComponents.forEach(child -> {
                        if (Objects.equals(ComponentExt.TEAM_COMPONENT_NAME, child.getName())) {
                            removeQXOfTeam(child);
                        }
                    });
                }
                for (IComponent childComponent : childComponents) {
                    removeFields(childComponent, toRemoveFields);
                }
                removeRelatedItem(childComponents);
                ((IGroupComponent) component).setChildComponents(childComponents);
            } else {
                break;
            }
        }
        return isWeb;
    }

    private void webDetailRender(List<IComponent> componentList, Set<String> toRemoveFields) {
        log.info("begin webDetailRender");
        for (IComponent component : componentList) {
            removeFields(component, toRemoveFields);
            if (Objects.equals(ComponentExt.TEAM_COMPONENT_NAME, component.getName())) {
                removeQXOfTeam(component);
            }
            if (IComponent.TYPE_TABS.equals(component.getType())) {
                removeItemOfTabs(component);
            }
        }
        removeRelatedItem(componentList);
        log.info("end webDetailRender {}", componentList);
    }

    private void removeQXOfTeam(IComponent component) {
        List<IButton> topButtons = component.getButtons();
        if (CollectionUtils.notEmpty(topButtons)) {
            topButtons.removeIf(button -> ButtonExt.QIXIN_CHAT_GROUP_ACTION.equals(button.getAction()));
            component.setButtons(topButtons);
        }
    }

    private void removeFields(IComponent component, Set<String> toRemoveFields) {
        if (component instanceof IFormComponent && !Objects.equals("summary_card_component", component.getName())) {
            {
                FormComponentExt formComponent = FormComponentExt.of((IFormComponent) component);
                formComponent.removeFields(toRemoveFields);
            }
        }
    }

    private void removeItemOfTabs(IComponent component) {
        List<List<String>> components = ((TabsComponent) component).getComponents();
        if (CollectionUtils.notEmpty(components)) {
            components.removeIf(c -> "attach_component".equals(c.get(0)));
        }
        List<TabSection> tabs = ((TabsComponent) component).getTabs();
        if (CollectionUtils.notEmpty(components)) {
            // TODO: 2024/9/20 I18n
            tabs.removeIf(t -> "tab_attach_component".equals(t.getApiName()) || "附件".equals(t.getHeader())); // ignoreI18n
        }
        ((TabsComponent) component).setTabs(tabs);
    }

    /**
     * These codes are not clear, because nobody knows why
     */
    private void removeRelatedItem(List<IComponent> childComponents) {
        childComponents.removeIf(iComponent -> {
            if (IComponent.TYPE_RELATEDLIST.equals(iComponent.getType())
                    && PrmConstant.unSupportRelatedApiNames.contains(iComponent.get(ILayout.REF_OBJECT_API_NAME, String.class))) {
                if ("PartnerObj".equals(iComponent.get(ILayout.REF_OBJECT_API_NAME, String.class)) && "parent_id".equals(iComponent.get("field_api_name"))) {
                    return false;
                }
                return true;
            }
            return false;
        });
    }

    private void filterButton() {
        List<IButton> buttons = layoutExt.getButtonOrder();
        if (CollectionUtils.notEmpty(buttons)) {
            //移除转发按钮
            // buttons.removeIf(button -> ObjectAction.DISCUSS.getActionCode().equals(button.getAction()));

            String apiName = describe.getApiName();
            if (!PrmConstant.supportBtns.containsKey(apiName)) {
                apiName = "UDOBJ";
            }
            if (PrmConstant.supportBtns.containsKey(apiName)) {
                List<String> supports = PrmConstant.supportBtns.get(apiName);
                buttons.removeIf(btn -> !supports.contains(btn.getAction()) && !IButton.ACTION_TYPE_CUSTOM.equals(btn.getActionType()));
            } else {
                buttons.clear();
            }

            layoutExt.setButtonOrder(buttons);
        }
    }
}
