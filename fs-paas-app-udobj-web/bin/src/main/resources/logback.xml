<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息 -->
    <property name="defaultPattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n"/>

    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>

    <appender name="udobj" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/udobj.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/udobj.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="Error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${catalina.home}/logs/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="OSS_Trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/perf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>


    <appender name="RequestLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/request.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/request.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${defaultPattern}</pattern>
        </encoder>
    </appender>

    <appender name="UDOBJ_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="udobj"/>
    </appender>

    <appender name="OSS_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="OSS_Trace"/>
    </appender>

    <appender name="PERF_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="PerfLog"/>
    </appender>

    <logger name="com.facishare.paas.expression.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.appframework.common.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.metadata.util.MetadataStopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.fxiaoke.common.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="OSS_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.appframework" level="INFO" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.metadata.cache.DescribeCache" level="WARN" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.github.autoconf" level="WARN" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.common" level="INFO" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.appframework.jaxrs.provider.RequestLogFilter" level="INFO" additivity="false">
        <appender-ref ref="RequestLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.jaxrs.provider.PathTraversalFilter" level="INFO" additivity="false">
        <appender-ref ref="RequestLog"/>
    </logger>

    <logger name="com.fxiaoke.transaction.tcc.servlets.TccTransactionalServlet" level="INFO" additivity="false">
        <appender-ref ref="RequestLog"/>
    </logger>

    <logger name="com.facishare.paas.appframework.fcp.model.AppFrameworkPreProcessFilter" level="INFO"
            additivity="false">
        <appender-ref ref="RequestLog"/>
    </logger>

    <logger name="com.fxiaoke.es.service.DataService" level="ERROR" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.common.sentinel.core.init.FsDataSourceInitFunc" level="INFO" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
    </logger>

    <root level="WARN">
        <appender-ref ref="UDOBJ_ASYNC"/>
    </root>

    <!-- 指定 context.path，value 为项目子模块名，如果一个应用只有一个模块时才可以不指定 -->
    <!--    <property name="context.path" value="ROOT"/>-->
    <include resource="fs-sentinel-logging.xml"/>
    <!--   本地 DEBUG 时可引入以下这个文件，将日志打印到 console-->
    <!--  <include resource="fs-sentinel-logging-console.xml"/>-->
</configuration>
