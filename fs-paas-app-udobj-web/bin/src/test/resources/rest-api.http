# For a quick start check out our HTTP Requests collection (Tools|HTTP Client|Open HTTP Requests Collection).
#
# Following HTTP Request Live Templates are available:
# * 'gtrp' and 'gtr' create a GET request with or without query parameters;
# * 'ptr' and 'ptrp' create a POST request with a simple or parameter-like body;
# * 'mptr' and 'fptr' create a POST request to submit a form with a text or file field (multipart/form-data);

### 列表页
POST {{baseUrl}}/object_1g18n__c/controller/List
x-fs-userInfo: 1000
x-fs-ei: 74255
Content-Type: application/json

{
  "object_describe_api_name": "object_1g18n__c",
  "search_template_id": "5d0c806a7cfed91f3e95ba8e",
  "search_template_type": "default",
  "ignore_scene_record_type": true,
  "search_query_info": "{\"limit\":50,\"offset\":0,\"filters\":[{\"field_name\":\"name\",\"field_values\":[\"导入\"],\"operator\":\"LIKE\",\"filterGroup\":\"1\"},{\"field_name\":\"record_type\",\"field_values\":[\"record_1l2rF__c\"],\"operator\":\"EQ\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
}

### 新建导入
POST {{baseUrl}}/object_Ds15d__c/action/InsertImportData
x-fs-userInfo: 1000
x-fs-ei: 78057
Content-Type: application/json

{
  "ImportType": 0,
  "IsApprovalFlowEnabled": false,
  "IsEmptyValueToUpdate": false,
  "IsUnionDuplicateChecking": false,
  "IsWorkFlowEnabled": false,
  "MatchingType": 2,
  "apiName": "object_Ds15d__c",
  "jobId": "5f0702324baad900015e8157",
  "locale": "zh-CN",
  "objectCode": "object_Ds15d__c",
  "parameter": {
    "ImportType": 0,
    "IsApprovalFlowEnabled": false,
    "IsEmptyValueToUpdate": false,
    "IsUnionDuplicateChecking": false,
    "IsWorkFlowEnabled": false,
    "MatchingType": 2,
    "isVerify": false,
    "jobId": "5f0702324baad900015e8157",
    "objectCode": "object_Ds15d__c",
    "operationType": "INVOKE",
    "unionApiNames": []
  },
  "rows": [
    {
      "rowNo": 2,
      "单行文本": "",
      "手机": "16768888912",
      "关联hh-name重复": "",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "测试幂等",
      "主属性（必填）": "导入测试720-0071",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    }
  ],
  "unionApiNames": [],
  "user": {
    "ea": "obj0509",
    "locale": "zh-CN",
    "outAppId": "",
    "tenantId": "78057",
    "upstreamOwnerId": "0",
    "userId": "1000"
  }
}

### 筛选支持或(场景字段被删除)
POST {{baseUrl}}/object_3oc19__c/controller/List
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 78057

{
  "object_describe_api_name": "object_3oc19__c",
  "search_template_id": "5ef06e9901d69e0001efeaf3",
  "search_template_type": "tenant",
  "ignore_scene_filter": false,
  "ignore_scene_record_type": true,
  "search_query_info": "{\"limit\":50,\"offset\":0,\"filters\":[{\"field_name\":\"record_type\",\"field_values\":[\"record_quH1y__c\"],\"operator\":\"EQ\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
}

### 筛选支持或（场景中的字段被禁用）
POST {{baseUrl}}/object_3oc19__c/controller/List
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 78057

{
  "object_describe_api_name": "object_3oc19__c",
  "search_template_id": "5d652277a5083d506370f1ba",
  "search_template_type": "tenant",
  "ignore_scene_filter": false,
  "ignore_scene_record_type": false,
  "search_query_info": "{\"limit\":50,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"create_time\",\"isAsc\":false}]}"
}

### 自定义按钮

POST {{baseUrl}}/object_2mmga__c/action/button_4w2x8__c
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 74255

{
  "objectDataId": "5f0c45bb01dcb90001db40dd",
  "args": {
    "form_field_m2o3F__c": [],
    "form_field_tG2gw__c": [],
    "form_field_0Nsmw__c": "sxasaad",
    "form_field_xn00S__c": null
  }
}

### 相关列表
POST {{baseUrl}}/object_9yXAw__c/controller/RelatedList
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 78057

{
  "associate_object_data_id": "5f0c111beb189b000185b961",
  "associate_object_describe_api_name": "object_50Cjk__c",
  "associated_object_describe_api_name": "object_9yXAw__c",
  "associated_object_field_related_list_name": "target_related_list_gqT79__c",
  "include_associated": true,
  "search_query_info": "{\"limit\":2000,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"name\",\"isAsc\":false}]}",
  "is_ordered": true,
  "object_describe_api_name": "object_9yXAw__c",
  "search_template_id": "5eb25d71d6ee820001dba02c",
  "search_template_type": "tenant",
  "ignore_scene_filter": false,
  "ignore_scene_record_type": false
}

### 筛选支持或（场景中的字段被禁用）
POST {{baseUrl}}/object_tJbHx__c/controller/List
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 78057

{
  "object_describe_api_name": "object_tJbHx__c",
  "search_template_id": "5f02a73fe249870001a1d30e",
  "search_template_type": "custom",
  "ignore_scene_filter": true,
  "ignore_scene_record_type": true,
  "search_query_info": "{\"limit\":50,\"offset\":0,\"filters\":[{\"field_name\":\"name\",\"field_values\":[\"测试\"],\"operator\":\"LIKE\",\"filterGroup\":\"1\"},{\"field_name\":\"field_Tw22r__c\",\"field_values\":[\"100\"],\"operator\":\"GT\",\"filterGroup\":\"1\"},{\"field_name\":\"field_Oj79z__c\",\"field_values\":[\"1593532800000\"],\"operator\":\"LT\",\"filterGroup\":\"1\"},{\"field_name\":\"field_b3626__c\",\"field_values\":[\"nsSp3X2cp\"],\"operator\":\"EQ\",\"filterGroup\":\"2\"},{\"field_name\":\"field_U0Vo0__c\",\"field_values\":[\"option1\"],\"operator\":\"NHASANYOF\",\"filterGroup\":\"2\"},{\"field_name\":\"field_480Yr__c\",\"field_values\":[\"true\"],\"operator\":\"EQ\",\"filterGroup\":\"2\"},{\"field_name\":\"field_h16gn__c\",\"field_values\":[\"\"],\"operator\":\"IS\",\"filterGroup\":\"2\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}"
}

### 获取场景
POST {{baseUrl}}/tenant_scene/service/find_scene_list
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 74255

{
  "describe_api_name": "object_2mmga__c"
}

### 校验按钮个数
POST {{baseUrl}}/button/service/validateButtonCount
Content-Type: application/json
x-fs-userInfo: 1000
x-fs-ei: 74255

{
  "describe_api_name": "object_2mmga__c",
  "use_page": [
    "create",
    "edit"
  ],
  "button_api_name": "button_ct5i2__c"
}


###