### 新建导入
POST {{baseUrl}}/object_Ds15d__c/action/InsertImportData
x-fs-userInfo: 1000
x-fs-ei: 78057
Content-Type: application/json

{
  "ImportType": 0,
  "IsApprovalFlowEnabled": false,
  "IsEmptyValueToUpdate": false,
  "IsUnionDuplicateChecking": false,
  "IsWorkFlowEnabled": false,
  "MatchingType": 2,
  "apiName": "object_Ds15d__c",
  "jobId": "5f102423d24afa00018a1fe4",
  "locale": "zh-CN",
  "objectCode": "object_Ds15d__c",
  "parameter": {
    "ImportType": 0,
    "IsApprovalFlowEnabled": false,
    "IsEmptyValueToUpdate": false,
    "IsUnionDuplicateChecking": false,
    "IsWorkFlowEnabled": false,
    "MatchingType": 2,
    "isVerify": false,
    "jobId": "5f102423d24afa00018a1fe4",
    "objectCode": "object_Ds15d__c",
    "operationType": "INVOKE",
    "unionApiNames": []
  },
  "rows": [
    {
      "rowNo": 2,
      "单行文本": "优雅哈哈",
      "手机": "16768888909",
      "关联hh-name重复": "测试1",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "",
      "主属性（必填）": "导入测试720-001",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 3,
      "单行文本": "审批",
      "手机": "16768888909",
      "关联hh-name重复": "测试1",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "",
      "主属性（必填）": "导入测试720-002",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 4,
      "单行文本": "审批",
      "手机": "16768888909",
      "关联hh-name重复": "测试2",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "",
      "主属性（必填）": "导入测试720-003",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 5,
      "单行文本": "天气晴蓝",
      "手机": "16768888909",
      "关联hh-name重复": "测试4",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "",
      "主属性（必填）": "导入测试720-004",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 6,
      "单行文本": "",
      "手机": "16768888910",
      "关联hh-name重复": "",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "中华老字号烤鸭店",
      "主属性（必填）": "导入测试720-005",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 7,
      "单行文本": "",
      "手机": "16768888911",
      "关联hh-name重复": "",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "2020062402",
      "主属性（必填）": "导入测试720-006",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 8,
      "单行文本": "",
      "手机": "16768888912",
      "关联hh-name重复": "",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "测试幂等",
      "主属性（必填）": "导入测试720-007",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    },
    {
      "rowNo": 9,
      "单行文本": "",
      "手机": "16768888913",
      "关联hh-name重复": "",
      "负责人（必填）": "lihh_obj0509",
      "业务类型（必填）": "预设业务类型",
      "关联客户": "测试代理通",
      "主属性（必填）": "导入测试720-008",
      "归属部门": "",
      "普通成员-读写": "",
      "单选": "",
      "普通成员-只读": ""
    }
  ],
  "unionApiNames": [],
  "user": {
    "ea": "obj0509",
    "locale": "zh-CN",
    "outAppId": "",
    "tenantId": "78057",
    "upstreamOwnerId": "0",
    "userId": "1005"
  }
}

### 新建导入,多选级联关系
POST {{baseUrl}}/object_PbY23__c/action/InsertImportData
x-fs-userInfo: 1000
x-fs-ei: 78057
Content-Type: application/json

{
  "ImportType": 0,
  "IsApprovalFlowEnabled": false,
  "IsEmptyValueToUpdate": false,
  "IsUnionDuplicateChecking": false,
  "IsWorkFlowEnabled": false,
  "MatchingType": 2,
  "apiName": "object_PbY23__c",
  "jobId": "5f1653b0d24afa00018a2036",
  "locale": "zh-CN",
  "objectCode": "object_PbY23__c",
  "parameter": {
    "ImportType": 0,
    "IsApprovalFlowEnabled": false,
    "IsEmptyValueToUpdate": false,
    "IsUnionDuplicateChecking": false,
    "IsWorkFlowEnabled": false,
    "MatchingType": 2,
    "isVerify": false,
    "jobId": "5f1653b0d24afa00018a2036",
    "objectCode": "object_PbY23__c",
    "operationType": "INVOKE",
    "unionApiNames": []
  },
  "rows": [
    {
      "网址": "www.fxiaoke.com",
      "rowNo": 2,
      "省": "北京市",
      "市": "北京市",
      "手机": "13900000000",
      "图片": "",
      "单选-父": "",
      "查找关联-订单": "",
      "负责人（必填）": "admin01_obj0509",
      "布尔值": "是",
      "主属性（必填）": "依赖导入1",
      "查找关联-开票申请": "",
      "详细地址": "",
      "普通成员-只读": "",
      "邮箱": "<EMAIL>",
      "收款金额": "123",
      "部门": "",
      "国家": "中国",
      "业务类型（必填）": "预设业务类型",
      "日期": "2020-6-29(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入)",
      "人员": "",
      "查找关联-客户": "",
      "单选": "示例选项",
      "客户": "",
      "附件": "",
      "多选": "多选2",
      "zm-通用1": "",
      "单选-子": "",
      "数字": "123",
      "单行文本": "",
      "时间": "10:21",
      "百分数（%）": "12.66",
      "查找关联-商机": "",
      "定位": "",
      "日期时间": "2020-6-29 10:21(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入)",
      "区": "海淀区",
      "普通成员-读写": ""
    },
    {
      "网址": "www.fxiaoke.com",
      "rowNo": 3,
      "省": "北京市",
      "市": "北京市",
      "手机": "13900000000",
      "图片": "",
      "单选-父": "",
      "查找关联-订单": "",
      "负责人（必填）": "admin01_obj0509",
      "布尔值": "是",
      "主属性（必填）": "依赖导入2",
      "查找关联-开票申请": "",
      "详细地址": "",
      "普通成员-只读": "",
      "邮箱": "<EMAIL>",
      "收款金额": "123",
      "部门": "",
      "国家": "中国",
      "业务类型（必填）": "预设业务类型",
      "日期": "2020-6-29(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入)",
      "人员": "",
      "查找关联-客户": "",
      "单选": "示例选项",
      "客户": "",
      "附件": "",
      "多选": "多选1",
      "zm-通用1": "",
      "单选-子": "",
      "数字": "123",
      "单行文本": "",
      "时间": "10:21",
      "百分数（%）": "12.66",
      "查找关联-商机": "",
      "定位": "",
      "日期时间": "2020-6-29 10:21(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入)",
      "区": "海淀区",
      "普通成员-读写": ""
    }
  ],
  "unionApiNames": [],
  "user": {
    "ea": "obj0509",
    "locale": "zh-CN",
    "outAppId": "",
    "tenantId": "78057",
    "upstreamOwnerId": "0",
    "userId": "1000"
  }
}

###