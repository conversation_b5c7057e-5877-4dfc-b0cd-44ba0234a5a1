package com.facishare.paas.appframework.core.util;

import com.facishare.organization.adapter.api.config.service.EnterpriseLanguageClient;
import com.fxiaoke.i18n.client.api.Language;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LanguageClientUtil {
    private static EnterpriseLanguageClient client;

    @Autowired
    public void setEnterpriseLanguageClient(EnterpriseLanguageClient client) {
        LanguageClientUtil.client = client;
    }

    public static List<Language> getLanguages(Long eid){
        return client.getLanguagesInLicenseAndSettings(String.valueOf(eid));
    }

    public static List<Language> getLanguages(String tenantId){
        return client.getLanguagesInLicenseAndSettings(tenantId);
    }



}
