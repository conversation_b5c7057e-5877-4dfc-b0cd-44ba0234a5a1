package com.facishare.paas.appframework.core.i18n


import spock.lang.Specification

import java.text.MessageFormat

/**
 * create by <PERSON><PERSON><PERSON> on 2020/09/24
 */
class I18NExtTest extends Specification {
    static {
        System.setProperty("spring.profiles.active", "fstest")
    }

    def "test_i18n_text"() {
        when:
        def result = MessageFormat.format(key, args.toArray(new String[0]))
        then:
        result == except
        where:
        key                  | args         || except
        """没有{0}这个{1}"""     | ["张三", "李四"] || "没有张三这个李四"
        """No {0}'s {1}"""   | ["张三", "李四"] || "No 张三s {1}"
        """No {0}''s {1}""" | ["张三", "李四"] || "No 张三's 李四"
    }
}
