package com.facishare.paas.appframework.core.model

import spock.lang.Specification

/**
 * Created by liyiguang on 2018/4/10.
 */
class RequestContextTest extends Specification {
    def "test getUser"() {

    }

    def "test toString"() {
        given:
            def requestContext = RequestContext.builder().user(User.systemUser("1")).build()
        when:
            println requestContext.toString()
        then:
            noExceptionThrown()
    }
}
