package com.facishare.paas.appframework.metadata.util;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;
import org.junit.Before;
import org.junit.Test;

import java.io.StringWriter;
import java.util.Properties;

/**
 * Created by liwei on 2020/2/4
 */
public class VelocityTest {
    private static Properties props = new Properties();

    @Before
    public void init() {
        props.setProperty(Velocity.INPUT_ENCODING, "UTF-8");
        props.setProperty(Velocity.RESOURCE_LOADER, "class");
        props.setProperty("class.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
    }

    @Test
    public void testStringReplace() {
        // 字符串模版
        String template = "https://xx.com/abc?mail=${email}";
        // 初始化并取得Velocity引擎
        VelocityEngine engine = new VelocityEngine(props);
        // 取得velocity的上下文context
        VelocityContext context = new VelocityContext();
        // 把数据填入上下文
        context.put("email", "<EMAIL>");
        StringWriter writer = new StringWriter();
        engine.evaluate(context, writer, "", template);
        System.out.println(writer.toString());
    }
}
