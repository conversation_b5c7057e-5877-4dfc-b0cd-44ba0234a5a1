package com.facishare.paas.appframework.metadata.util;

import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/7 11:00 AM
 */
public class XmlUtilTest {
    @Test
    public void testCreateXml() {
        Map<String, String> map = Maps.newHashMap();
        map.put("temp1.jpeg", "N_2q13123112asd23123");
        map.put("temp2.jpeg", "N_2q131231asda3123123");
        map.put("temp3.jpeg", "N_2q13123asd123123");
    }

    @Test
    public void testCreateXml2() {
        Map<String, String> map1 = Maps.newHashMap();
        map1.put("tem//p11.jpeg", "N_111111");
        map1.put("te|mp12.jpeg", "N_111112");
        map1.put("temp13.jpeg", "N_111113");

        Map<String, String> map2 = Maps.newHashMap();
        map2.put("te\"mp21.jpeg", "N_222221");
        map2.put("tem?p22.jpeg", "N_222222");
        map2.put("temp23.jpeg", "N_222223");


        Map<String, Map<String, String>> map = Maps.newHashMap();
        map.put("数据1", map1);
        map.put("数据2", map2);

    }

    @Test
    public void testTrimSpecial() {
        String result = XmlUtil.filterName("tes.123");
        System.out.println(result);
    }

    @Test
    public void test() {
        String str = "116.332001#%$39.976601#%$北京市海淀区知春路";
        String result = str.replaceFirst("([\\d.]+#%\\$){2}", "");
        System.out.println(result);
        Assert.assertEquals(result, "北京市海淀区知春路");
    }

    @Test
    public void testUtf8(){
        System.out.println(StandardCharsets.UTF_8.name());
    }
}
