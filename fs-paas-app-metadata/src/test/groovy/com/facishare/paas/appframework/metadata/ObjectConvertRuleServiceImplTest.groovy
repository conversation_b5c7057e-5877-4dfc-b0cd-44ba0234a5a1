package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectMappingRuleService
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo
import com.facishare.paas.metadata.impl.ObjectMappingRuleEnumInfo
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Language
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification

class ObjectConvertRuleServiceImplTest extends Specification {
    ObjectConvertRuleService objectConvertRuleService
    IRepository<MtConvertRule> repository = Mock(IRepository)
    ObjectMappingService objectMappingService = Mock(ObjectMappingService)
    IObjectMappingRuleService mappingRuleService = Mock(IObjectMappingRuleService)
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    FunctionPrivilegeService functionPrivilegeService = Mock(FunctionPrivilegeService)
    MetaDataFindService metaDataFindService = Mock(MetaDataFindService)
    FunctionLogicService functionLogicService = Mock(FunctionLogicService)
    LicenseService licenseService = Mock(LicenseService)
    I18nSettingService i18nSettingService = Mock(I18nSettingService)

    @Shared
    def dataMap = ['record_type': 'default__c', 'select_one__c': '2', '_id': '1']
    @Shared
    def dataMap1 = ['record_type': 'defaultA__c', 'select_one__c': '2', '_id': '2']

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> [JSON.parseObject("{\"code\":\"zh-CN\",\"name\":\"简体中文\",\"displayName\":\"简体中文\",\"default\":true}", Language.class)]
    }

    def setup() {
        def requestContext = RequestContext.builder()
                .user(User.systemUser("74255"))
                .tenantId('74255')
                .lang(Lang.zh_CN)
                .build()
        RequestContextManager.setContext(requestContext)
        objectConvertRuleService = new ObjectConvertRuleServiceImpl('repository': repository, 'objectMappingService': objectMappingService,
                'mappingRuleService': mappingRuleService, 'describeLogicService': describeLogicService, 'functionPrivilegeService': functionPrivilegeService,
                'metaDataFindService': metaDataFindService, 'functionLogicService': functionLogicService, 'licenseService': licenseService,
                'i18nSettingService': i18nSettingService)
    }


    def 'test create'() {
        given:

        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')

        when:
        def convertRule = new MtConvertRule()
        convertRule.setApiName('rule_clP2T__c')
        repository.findBy(*_) >> []
        objectConvertRuleService.create(User.systemUser('1000'), [rule])
        then:
        noExceptionThrown()
        1 * objectMappingService.createRule(*_)
    }

    def 'test update'() {
        given:
        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.setMdType(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')

        when:
        def convertRule = new MtConvertRule()
        convertRule.setApiName('rule_clP2T__c')
        convertRule.setMdType(0)
        repository.findBy(*_) >> [convertRule]
        objectConvertRuleService.update(User.systemUser('1000'), [rule])
        then:
        noExceptionThrown()
//        1 * objectMappingService.updateRule(*_)
    }

    def "test findRuleList"() {
        given:
        def user = User.systemUser('1000')
        def convertRule = new MtConvertRule()
        convertRule.setSourceObjectDescribeApiName('object_3BbVb__c')
        convertRule.setTargetObjectDescribeApiName('object_ssu7t__c')
        when:
        describeLogicService.findDisplayNameByApiNames(*_) >> ['object_3BbVb__c': 'zxf-主', 'object_ssu7t__c': 'zj-主']
        repository.findBy(*_) >> [convertRule]
        def result = objectConvertRuleService.findRuleList(user, 'rule_test__c', '{}')
        then:
        noExceptionThrown()
        result.size() == 1

    }

    def "test updateStatus"() {
        given:
        def user = User.systemUser('1000')
        def convertRule = new MtConvertRule()
        convertRule.setSourceObjectDescribeApiName('object_3BbVb__c')
        convertRule.setTargetObjectDescribeApiName('object_ssu7t__c')
        convertRule.setMdType(2)
        def describe = new ObjectDescribe()
        when:
        repository.findBy(*_) >> [convertRule]
        describeLogicService.findObjectWithoutCopyIfGray(*_) >> describe
        objectConvertRuleService.updateStatus(user, 'rule_test__c', 0)
        then:
        1 * repository.bulkUpdateByFields(*_)
    }

    def "test findConvertRuleByApiName"() {
        given:
        def user = User.systemUser('1000')
        def convertRule = new MtConvertRule()
        convertRule.setSourceObjectDescribeApiName('object_3BbVb__c')
        convertRule.setTargetObjectDescribeApiName('object_ssu7t__c')
        convertRule.setApiName('rule_test__c')
        convertRule.setRuleApiName('rule_test__c')
        convertRule.setMdType(2)
        def describe = new ObjectDescribe()
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('object_3BbVb__c')
        when:
        repository.findBy(*_) >> [convertRule]
        describeLogicService.findObjectWithoutCopyIfGray(*_) >> describe
        mappingRuleService.getObjectMappingRuleByRuleApiNameAction(*_) >> [ruleInfo]
        def ruleInfos = objectConvertRuleService.findConvertRuleByApiName(user, 'rule_test__c')
        then:
        noExceptionThrown()
        ruleInfos.get(0).getMasterApiName() == 'object_3BbVb__c'
    }

    def "test find push order convert rules"() {
        given:
        def user = User.systemUser('1000')
        def convertRule = new MtConvertRule()
        convertRule.setSourceObjectDescribeApiName('object_3BbVb__c')
        convertRule.setTargetObjectDescribeApiName('object_ssu7t__c')
        convertRule.setApiName('rule_test__c')
        convertRule.setRuleApiName('rule_test__c')
        convertRule.setMdType(2)
        def describe = new ObjectDescribe()
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('object_3BbVb__c')
        ruleInfo.setFieldMapping()
        def detailInfo = new ObjectMappingRuleDetailInfo()
        detailInfo.setSourceFieldName('record_type')
        detailInfo.setTargetFieldName('record_type')
        detailInfo.setOptionMapping()
        def enumInfo = new ObjectMappingRuleEnumInfo()
        enumInfo.setSourceEnumCode('default__c')
        enumInfo.setTargetEnumCode('default__c')
        def data = new ObjectData()
        data.setId('1')
        data.setRecordType('default__c')
        when:
        metaDataFindService.findObjectDataByIds(*_) >> [data]
        repository.findBy(*_) >> [convertRule]
        describeLogicService.findObjectWithoutCopyIfGray(*_) >> describe
        mappingRuleService.getObjectMappingRuleByRuleApiNameAction(*_) >> [ruleInfo]
        def ruleInfos = objectConvertRuleService.findConvertRuleByDescribeApiName(user, 'rule_test__c', null, [])
        then:
        noExceptionThrown()
        ruleInfos.size() == 1
    }

    def "test find push order"() {
        given:
        def user = User.systemUser('1000')
        def convertRule = new MtConvertRule()
        convertRule.setSourceObjectDescribeApiName('object_3BbVb__c')
        convertRule.setTargetObjectDescribeApiName('object_ssu7t__c')
        convertRule.setApiName('rule_test__c')
        convertRule.setRuleApiName('rule_test__c')
        convertRule.setMdType(2)
        convertRule.setCloseFieldApiName('select_one__c')
        convertRule.setCloseLogic(true)
        convertRule.setBackWrite(true)
        convertRule.setCloseFieldValue('1')
        def convertRule1 = new MtConvertRule()
        convertRule1.setSourceObjectDescribeApiName('object_3BbVb__c')
        convertRule1.setTargetObjectDescribeApiName('object_ssu7t__c')
        convertRule1.setApiName('rule_test1__c')
        convertRule1.setRuleApiName('rule_test1__c')
        convertRule1.setMdType(2)
        convertRule1.setCloseFieldApiName('select_one__c')
        convertRule1.setCloseLogic(true)
        convertRule1.setBackWrite(true)
        convertRule1.setCloseFieldValue('1')
        when:
        metaDataFindService.findObjectDataByIds(*_) >> dataList
        repository.findBy(*_) >> [convertRule, convertRule1]
        metaDataFindService.findObjectDataByIds(*_) >> dataList
        describeLogicService.findObjectWithoutCopyIfGray(*_) >> masterDescribe
        describeLogicService.findObjectsWithoutCopy(*_) >> describes
        mappingRuleService.getObjectMappingRuleByRuleApiNameAction(*_) >> mappingRuleList
        mappingRuleService.getObjectMappingRuleByRuleNameSourceTargetAction(*_) >> mappingRuleList
        def ruleInfos = objectConvertRuleService.findConvertRuleByDescribeApiName(user, 'rule_test__c', null, sourceIds, recordType, isPushOrder)
        then:
        noExceptionThrown()
        ruleSize == ruleInfos.size()
        where:
        masterDescribe                      | describes        | mappingRuleList        | dataList                           | sourceIds  | recordType   | isPushOrder || ruleSize
        buildDescribes()['object_3BbVb__c'] | buildDescribes() | buildMappingRuleList() | buildObjectData(dataMap)           | ['1']      | 'default__c' | true        || 2
        buildDescribes()['object_3BbVb__c'] | buildDescribes() | buildMappingRuleList() | buildObjectData(dataMap, dataMap1) | ['1', '2'] | 'default__c' | true        || 1
    }

    private List buildObjectData(Map... maps) {
        maps.collect {
            def data = new ObjectData(it)
            data
        }
    }

    private Map<String, IObjectDescribe> buildDescribes() {
        def describes = []
        def describe = new ObjectDescribe()
        describe.setApiName('object_3BbVb__c')
        def describe1 = new ObjectDescribe()
        describe1.setApiName('object_ssu7t__c')
        describes << describe
        describes << describe1
        ['object_3BbVb__c': describe, 'object_ssu7t__c': describe1]
    }

    private List<IObjectMappingRuleInfo> buildMappingRuleList() {
        def enumInfo = new ObjectMappingRuleEnumInfo()
        enumInfo.setSourceEnumCode('default__c')
        enumInfo.setTargetEnumCode('default__c')
        def detailInfo = new ObjectMappingRuleDetailInfo()
        detailInfo.setSourceFieldName('record_type')
        detailInfo.setTargetFieldName('record_type')
        detailInfo.setOptionMapping([enumInfo])
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo()
        ruleInfo.setRuleApiName('rule_test__c')
        ruleInfo.setMasterApiName('object_3BbVb__c')
        ruleInfo.setFieldMapping([detailInfo])
        [ruleInfo]
    }
}
