package com.facishare.paas.appframework.metadata.duplicated

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.DuplicatedSearchService
import com.facishare.paas.appframework.metadata.MetaDataFindService
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.cache.RedisDao
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.release.FsGrayReleaseBiz
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.Ignore
import org.powermock.reflect.Whitebox
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Shared
import spock.lang.Specification

@Ignore
@ContextConfiguration(locations = "classpath:applicationContext.xml")
class DuplicatedSearchDataRedisStoreServiceTest extends Specification {

    static{
        System.setProperty("spring.profiles.active","fstest")
        System.getProperties().setProperty("process.profile", "fstest");
    }

    // 用户提供的实际查重规则JSON数据
    @Shared
    String duplicatedSearchJson = '''
    {"createTime":1744716716773,"createdBy":"1000","dataScope":"[]","describeApiName":"object_3xvwy__c","effective":true,"enable":true,"id":"67fe43ac6e7c2e2c4b9ff333","invalidNotDuplicateSearch":false,"lastModifiedBy":"1000","lastModifiedTime":1744716716773,"mark":"","name":"查重规则名称","operatorId":"1000","ruleApiName":"rule_hror4__c","supportImport":false,"tenantId":"74255","type":"NEW","useableRules":{"relatedDescribes":[],"rules":[{"conditions":[{"connector":"AND","emptyPolicy":"IGNORE_EMPTY","fieldName":"field_hK624__c","fieldValue":"DISTANCE","range":"300"}],"connector":"OR"}],"showFields":["field_hK624__c"],"showNum":5,"specialConfig":{"transforms":[]}},"version":0,"wheres":"[]"}
    '''

    // 用户提供的实际对象描述JSON数据
    @Shared
    String objectDescribeJson = '''
    {"bigObject":false,"containerDocument":{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1744601929661,"create_time":1744601905709,"description":"","last_modified_by":"1000","display_name":"查重支持范围","created_by":"1000","version":2,"is_open_display_name":false,"index_version":1,"icon_index":0,"is_deleted":false,"api_name":"object_3xvwy__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"paC","_id":"67fc8331193a5b00077dea9e","fields":{"tenant_id":{"describe_api_name":"object_3xvwy__c","is_index":false,"is_active":true,"create_time":1744601905709,"pattern":"","is_unique":false,"description":"tenant_id","label":"租户ID","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"field_hK624__c":{"describe_api_name":"object_3xvwy__c","auto_location":false,"auto_adapt_places":false,"is_unique":false,"type":"location","is_required":false,"enable_clone":true,"define_type":"custom","is_single":false,"index_name":"t_2","is_index":true,"is_active":true,"create_time":1744601928940,"is_encrypted":false,"label":"定位","is_geo_index":false,"field_num":10,"inherit_type":3,"api_name":"field_hK624__c","range_limit":false,"_id":"67fc8349193a5b00077deb54","radius_range":100,"is_index_field":false,"help_text":"","status":"new"}},"release_version":"6.4","actions":{}},"fieldDescribeMap":{"tenant_id":{"active":true,"apiName":"tenant_id","containerDocument":{"$ref":"$.containerDocument.fields.tenant_id"},"createTime":1744601905709,"defaultIsExpression":false,"defaultToZero":false,"defineType":"system","describeApiName":"object_3xvwy__c","description":"tenant_id","enableClone":true,"enableMultiLang":false,"encrypted":false,"index":false,"indexField":false,"indexName":"ei","isNeedConvert":false,"label":"租户ID","maxLength":200,"pattern":"","required":true,"status":"released","type":"text","unique":false},"field_hK624__c":{"active":true,"apiName":"field_hK624__c","autoLocation":false,"containerDocument":{"$ref":"$.containerDocument.fields.field_hK624__c"},"createTime":1744601928940,"defaultIsExpression":false,"defaultToZero":false,"defineType":"custom","describeApiName":"object_3xvwy__c","enableClone":true,"enableMultiLang":false,"encrypted":false,"fieldNum":10,"helpText":"","id":"67fc8349193a5b00077deb54","index":true,"indexField":false,"indexName":"t_2","inheritType":3,"isGeoIndex":false,"label":"定位","radiusRange":false,"rangeLimit":false,"required":false,"status":"new","type":"location","unique":false}},"openDisplayName":false,"publicObject":false,"releaseVersion":"6.4","shortName":"paC","socialObject":false,"udef":true,"version":2}
    '''

    @Shared
    String objectDataJson = "{\n" +
            "    \"tenant_id\": \"74255\",\n" +
            "    \"field_hK624__c\": \"116.331953#%\$39.977195#%\$北京市海淀区知春路甲63号(知春里地铁站B东北口步行310米)卫星大厦\"\n" +
            "}"

//    @Autowired
//    RedisDao redisDao;
//    @Autowired
//    DepartmentService departmentService;
    @Autowired
    DuplicatedSearchDataStoreService duplicatedSearchDataStoreService

    @Autowired
    DescribeLogicService describeLogicService;

    @Autowired
    MetaDataFindService metaDataFindService;

    @Autowired
    DuplicatedSearchService duplicatedSearchService;

    @Autowired
    private RedisDao redisDao;


//    private RedisDao redisDao = Mock(RedisDao)

//    private DepartmentService departmentService = Mock(DepartmentService)


    def setup() {
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> true
    }


    @Shared
    private duplicatedAndStr = "{\n" +
            "    \"id\": \"67fe43ac6e7c2e2c4b9ff333\",\n" +
            "    \"name\": \"查重规则名称\",\n" +
            "    \"rule_api_name\": \"rule_hror4__c\",\n" +
            "    \"wheres\": \"[]\",\n" +
            "    \"data_scope\": \"[]\",\n" +
            "    \"tenant_id\": \"74255\",\n" +
            "    \"describe_api_name\": \"object_3xvwy__c\",\n" +
            "    \"create_time\": 1744716716773,\n" +
            "    \"created_by\": \"1000\",\n" +
            "    \"last_modified_time\": 1744873442601,\n" +
            "    \"last_modified_by\": \"1000\",\n" +
            "    \"mark\": \"\",\n" +
            "    \"type\": \"NEW\",\n" +
            "    \"enable\": true,\n" +
            "    \"effective\": true,\n" +
            "    \"useable_rules\": {\n" +
            "        \"show_num\": 5,\n" +
            "        \"show_fields\": [\n" +
            "            \"field_hK624__c\"\n" +
            "        ],\n" +
            "        \"related_describes\": [],\n" +
            "        \"special_config\": {\n" +
            "            \"transforms\": []\n" +
            "        },\n" +
            "        \"rules\": [\n" +
            "            {\n" +
            "                \"connector\": \"OR\",\n" +
            "                \"conditions\": [\n" +
            "                    {\n" +
            "                        \"connector\": \"AND\",\n" +
            "                        \"field_name\": \"field_hK624__c\",\n" +
            "                        \"field_value\": \"DISTANCE\",\n" +
            "                        \"range\": \"300\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"connector\": \"AND\",\n" +
            "                        \"field_name\": \"field_j64hn__c\",\n" +
            "                        \"field_value\": \"PRECISE\",\n" +
            "                        \"empty_policy\": \"NON_MATCH_EMPTY\"\n" +
            "                    }\n" +
            "                ]\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    \"operator_id\": \"1000\",\n" +
            "    \"version\": 1,\n" +
            "    \"support_import\": false,\n" +
            "    \"invalid_not_duplicate_search\": false\n" +
            "}"


    @Shared
    private static String duplicateOrStr = "{\n" +
            "    \"id\": \"67fe43ac6e7c2e2c4b9ff333\",\n" +
            "    \"name\": \"查重规则名称\",\n" +
            "    \"rule_api_name\": \"rule_hror4__c\",\n" +
            "    \"wheres\": \"[]\",\n" +
            "    \"data_scope\": \"[]\",\n" +
            "    \"tenant_id\": \"74255\",\n" +
            "    \"describe_api_name\": \"object_3xvwy__c\",\n" +
            "    \"create_time\": 1744716716773,\n" +
            "    \"created_by\": \"1000\",\n" +
            "    \"last_modified_time\": 1744877081470,\n" +
            "    \"last_modified_by\": \"1000\",\n" +
            "    \"mark\": \"\",\n" +
            "    \"type\": \"NEW\",\n" +
            "    \"enable\": true,\n" +
            "    \"effective\": true,\n" +
            "    \"useable_rules\": {\n" +
            "        \"show_num\": 5,\n" +
            "        \"show_fields\": [\n" +
            "            \"field_hK624__c\"\n" +
            "        ],\n" +
            "        \"related_describes\": [],\n" +
            "        \"special_config\": {\n" +
            "            \"transforms\": []\n" +
            "        },\n" +
            "        \"rules\": [\n" +
            "            {\n" +
            "                \"connector\": \"OR\",\n" +
            "                \"conditions\": [\n" +
            "                    {\n" +
            "                        \"connector\": \"AND\",\n" +
            "                        \"field_name\": \"field_hK624__c\",\n" +
            "                        \"field_value\": \"DISTANCE\",\n" +
            "                        \"range\": \"300\"\n" +
            "                    }\n" +
            "                ]\n" +
            "            },\n" +
            "            {\n" +
            "                \"connector\": \"OR\",\n" +
            "                \"conditions\": [\n" +
            "                    {\n" +
            "                        \"connector\": \"AND\",\n" +
            "                        \"field_name\": \"field_j64hn__c\",\n" +
            "                        \"field_value\": \"PRECISE\",\n" +
            "                        \"empty_policy\": \"MATCH_EMPTY\"\n" +
            "                    }\n" +
            "                ]\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    \"operator_id\": \"1000\",\n" +
            "    \"version\": 2,\n" +
            "    \"support_import\": false,\n" +
            "    \"invalid_not_duplicate_search\": false\n" +
            "}"

    def "multiSaveAndDuplicateData and filter"() {
        given:
        IDuplicatedSearch duplicatedSearch = JSONObject.parseObject(duplicatedAndStr, IDuplicatedSearch.class);
        IObjectDescribe describe = describeLogicService.findObject("74255", "object_3xvwy__c")
        when:
        List<DuplicateSearchDataInfo> data = duplicatedSearchDataStoreService.multiSaveAndDuplicateData(new User("74255", "1000"), describe,
                [duplicatedSearch], [objectData1, objectData2], true)
        then:
        data.size() == res
        noExceptionThrown()

        where:
        objectData1                                  | objectData2                                  | res
        changeObjectData(["field_j64hn__c": "1111"]) | changeObjectData(["field_j64hn__c": "222"])  | 0
        changeObjectData(["field_j64hn__c": "1111"]) | changeObjectData(["field_j64hn__c": "1111"]) | 2

    }


    def "multiSaveAndDuplicateData or filter"() {
        given:
        IDuplicatedSearch duplicatedSearch = JSONObject.parseObject(duplicateOrStr, IDuplicatedSearch.class);
        IObjectDescribe describe = describeLogicService.findObject("74255", "object_3xvwy__c")
        when:
        List<DuplicateSearchDataInfo> data = duplicatedSearchDataStoreService.multiSaveAndDuplicateData(new User("74255", "1000"), describe,
                [duplicatedSearch], [objectData1, objectData2], true)
        then:
        data.size() == res
        noExceptionThrown()

        where:
        objectData1                                                                                        | objectData2                                                                                        | res
        changeObjectData(["field_hK624__c": "116.331953#%\$39.977195#%\$星大厦", "field_j64hn__c": "111"]) | changeObjectData(["field_hK624__c": "119.939959#%\$31.736549#%\$路劲", "field_j64hn__c": "111"])   | 2
        changeObjectData(["field_hK624__c": "116.331953#%\$39.977195#%\$星大厦", "field_j64hn__c": "222"]) | changeObjectData(["field_hK624__c": "119.939959#%\$31.736549#%\$路劲", "field_j64hn__c": "111"])   | 0
        changeObjectData(["field_hK624__c": "116.331953#%\$39.977195#%\$星大厦", "field_j64hn__c": "222"]) | changeObjectData(["field_hK624__c": "116.331953#%\$39.977195#%\$星大厦", "field_j64hn__c": "111"]) | 2

    }

    /**
     * 单个规则 ，50条数据，一个规则配置了8个或条件，每个或条件中包含10个且条件的情况
     * max cost 1223ms
     */
    def "saveDuplicateSearchLuaCost"() {
        given:
        //4000 代表 50 * 80 50条数据 * 80 个条件 = 4000
        def keys = buildKeys(4000);
        Map resultMap = buildResultMap(keys)
        when:
        def start = System.currentTimeMillis()
        redisDao.evalScript(DuplicatedSearchDataRedisStoreService.SAVE_LUA_SCRIPT_V2, Lists.newArrayList(keys), String.valueOf(1), JacksonUtils.toJson(resultMap));
        println("SAVE_LUA_SCRIPT_V2 cost test:" + (System.currentTimeMillis() - start))

        then:
        noExceptionThrown()
    }
    /**
     * 单个规则，1条数据，规则中包含8个或条件，且每个或条件包含10个且条件的情况(5个普通查询，5个范围查询)
     * max cost 35ms
     */
    def "searchDuplicateSearchLuaCost"() {
        given:
        List<String> resultMap = buildSearchKeys()
        when:
        def start = System.currentTimeMillis()
        Collection<String> dataIds = redisDao.evalScript(DuplicatedSearchDataRedisStoreService.SEARCH_LUA_SCRIPT_V2, resultMap, "redisTempKey");
        println("SEARCH_LUA_SCRIPT_V2 cost test:" + (System.currentTimeMillis() - start))
        println("res:" + dataIds)
        then:
        noExceptionThrown()
    }


    IObjectData changeObjectData(Map<String, Object> data) {
        ObjectDataExt dataExt = ObjectDataExt.of(JSONObject.parseObject(objectDataJson, Map.class))
        dataExt.setId(UUID.randomUUID().toString())
        dataExt.putAll(data)
        return dataExt.getObjectData();
    }

    List<String> buildKeys(int size) {
        List<String> res = []
        List<String> geo = []
        List<String> data = []
        for (int i; i < size / 2; i++) {
            geo.add(UUID.randomUUID().toString() + "_DUPLICATED:GEO:74255:object_3xvwy__c:2:field_hK624__c")
            data.add(UUID.randomUUID().toString() + "_DUPLICATED_SEARCH_DATA_74255_object_3xvwy__c_2_field_j64hn__c_111")
        }

        res.addAll(geo)
        res.addAll(data)
        return res
    }

    Map buildResultMap(List<String> keys) {
        Map res = [:]
        Map<String, Map<String, List<String>>> geo = Maps.newHashMap()
        Map<String, List<String>> data = Maps.newHashMap()
        for (final def key in keys) {
            if (key.contains("GEO")) {
                for (i in 0..<50) {
                    geo.put(key, [(UUID.randomUUID().toString()): [116.331953, 39.977195]])
                }
            }
            if (key.contains("DATA")) {
                def dataIds = []
                for (i in 0..<50) {
                    dataIds.add((UUID.randomUUID().toString()))
                }
                data.put(key, dataIds)
            }
        }

        res.put("dataDup", data)
        res.put("geoDup", geo)
        return res;
    }

    List<String> buildSearchKeys() {
        List<String> res = []

        for (i in 0..<8) {
            List<String> dataDup = []
            List<String> geoDup = []
            for (j in 0..<5) {
                def id = UUID.randomUUID().toString()
                dataDup.add(id + "_DUPLICATED_SEARCH_DATA_74255_object_3xvwy__c_2_field_j64hn__c_111")
                geoDup.add(id + "_DUPLICATED:GEO:74255:object_3xvwy__c:2:field_hK624__c")
            }
            Map map = ["dataDup": dataDup, "geoDup": geoDup]
            res.add(JacksonUtils.toJson(map))
        }
        return res
    }
}