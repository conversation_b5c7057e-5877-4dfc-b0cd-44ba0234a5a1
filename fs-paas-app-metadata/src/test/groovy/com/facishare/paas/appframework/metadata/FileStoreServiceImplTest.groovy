package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.facishare.fsi.proxy.exception.FsiClientException
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NCreateNoPermissionFile
import com.facishare.fsi.proxy.service.NFileStorageService
import com.facishare.netdisk.api.model.type.V5FileInfo
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.paas.service.model.ObjectDataDocument
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import com.fxiaoke.stone.commons.SystemPresetClient
import com.fxiaoke.stone.commons.domain.constant.AuthModel
import com.github.trace.TraceContext
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class FileStoreServiceImplTest extends Specification {
    FileStoreServiceImpl fileStoreService
    IActionContext ctx
    SystemPresetClient appFrameworkExclusiveClient
    NFileStorageService nFileStorageService = Mock(NFileStorageService)

    GDSHandler gdsHandler = Mock(GDSHandler)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    void setup() {
        appFrameworkExclusiveClient = Mock(SystemPresetClient)
        ctx = Mock(IActionContext)
        fileStoreService = new FileStoreServiceImpl(
                "nFileStorageService": nFileStorageService,
                "gdsHandler": gdsHandler,
                "appFrameworkExclusiveClient": appFrameworkExclusiveClient)
    }

    def "tes saveImageFromTempFilesAndNames"() {
        given:
        List<FileStoreService.PathOriginNames> pathOriginNames = []
        def names = FileStoreService.PathOriginNames.of("N_202209_05_a0ac5b19d8eb44b39bfc73748857cdf9.xlsx", "测试.xlsx")
        pathOriginNames << names
        when:
        nFileStorageService.nSaveImageFromTempFiles(_, _) >> { throw new FsiClientException(10007, "Fsi Error Code:s312010004 : 10007:null") }
        fileStoreService.saveImageFromTempFilesAndNames("74255", "1000", pathOriginNames, "xlsx")
        then:
        def e = thrown(ValidateException)
        e.getMessage().contains("pass.udobj.file_not_exist_or_expired")
    }

    def "test generateSignedUrl with image list"() {
        given:
        def imageList = [
                [path: "path1", signature: "sig1", filename: "file1", ext: "jpg"],
                [path: "path2", signature: "sig2", filename: "file2", ext: "png"]
        ]
        def objData = [images: imageList]
        def signedUrl = "https://example.com/signed"
        def traceId = "trace123"
        TraceContext.get().setTraceId(traceId)
        
        // 添加灰度配置
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> isGray

        when:
        fileStoreService.generateSignedUrl(objData, AuthModel.SIGN, ctx)

        then:
        ctx.getEnterpriseId() >> "ei1"
        ctx.getUserId() >> "uid1"
        ctx.getOutEnterpriseId() >> "oei1"
        ctx.getOutUserId() >> "ouid1"
        ctx.getUpstreamOwnerId() >> "uoid1"
        
        // 验证解密调用
        2 * appFrameworkExclusiveClient.decryptSignature("ei1", _) >> Optional.of("decrypted_sig")
        // 验证生成URL调用
        2 * appFrameworkExclusiveClient.generateUrl(*_) >> signedUrl
        // 验证加密调用
        encryptTimes * appFrameworkExclusiveClient.encryptSignature(*_) >> Optional.of("encrypted_sig")
        
        and:
        imageList[0]["signedUrl"] == signedUrl + "&linkId=" + traceId
        imageList[1]["signedUrl"] == signedUrl + "&linkId=" + traceId
        imageList[0]["signature"] == (isGray ? "encrypted_sig" : "sig1")
        imageList[1]["signature"] == (isGray ? "encrypted_sig" : "sig2")

        where:
        isGray | encryptTimes
        true   | 2
        false  | 0
    }

    @Unroll
    def "test generateNPathSignedUrlForEdit"() {
        given: "准备测试数据"
        def fieldTypes = fieldTypeSet as Set<String>
        def describe = Mock(IObjectDescribe)

        def signedUrl = "https://example.com/signed"
        def traceId = "trace123"
        TraceContext.get().setTraceId(traceId)

        // 添加灰度配置
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> isGray

        // 模拟字段描述
        def field = Mock(IFieldDescribe)
        field.getType() >> "image"
        field.getApiName() >> "images"

        when: "设置Mock行为"
        describe.getFieldDescribes() >> [field]
        describe.getApiName() >> "testObject"
        // 初始化从对象数据
        List<ObjectDataDocument> dataDocuments = JSONArray.parseArray(JSON.toJSONString(objectDataList), ObjectDataDocument)
        List<IObjectData> dataList = ObjectDataDocument.ofDataList(dataDocuments)

        and: "调用测试方法"
        fileStoreService.generateNPathSignedUrlForEdit(ctx, fieldTypes, describe, dataList)

        then: "验证基础交互"
        ctx.getEnterpriseId() >> "ei1"
        ctx.getUserId() >> "uid1"
        ctx.getOutEnterpriseId() >> "oei1"
        ctx.getOutUserId() >> "ouid1"
        ctx.getUpstreamOwnerId() >> "uoid1"

        and: "验证解密调用"
        decryptTimes * appFrameworkExclusiveClient.decryptSignature("ei1", _) >> Optional.of("decrypted_sig")
        
        and: "验证URL生成"
        urlTimes * appFrameworkExclusiveClient.generateUrl(*_) >> signedUrl
        
        and: "验证签名加密"
        encryptTimes * appFrameworkExclusiveClient.encryptSignature(_, _) >> Optional.of("encrypted_signature")

        where: "测试不同场景"
        fieldTypeSet      | objectDataList                                                                                                                                                                                | urlTimes | decryptTimes | isGray | encryptTimes
        null              | null                                                                                                                                                                                          | 0        | 0           | true   | 0
        []                | []                                                                                                                                                                                            | 0        | 0           | true   | 0
        ["image"]         | null                                                                                                                                                                                          | 0        | 0           | true   | 0
        null              | [[:]]                                                                                                                                                                                         | 0        | 0           | true   | 0
        ["image"]         | [[images: [[path: "path1", signature: "sig1", filename: "file1", ext: "jpg"]]]]                                                                                                              | 1        | 1           | true   | 1
        ["image"]         | [[images: [[path: "path1", signature: "sig1", filename: "file1", ext: "jpg"]]], [images: [[path: "path2", signature: "sig2", filename: "file2", ext: "png"]]]]                              | 2        | 2           | true   | 2
        ["attachment"]    | [[attachments: [[path: "path1", signature: "sig1", filename: "file1", ext: "jpg"]]]]                                                                                                         | 0        | 0           | true   | 0
        ["image", "file"] | [[images: [[path: "path1", signature: "sig1", filename: "file1", ext: "jpg"]]], [files: [[path: "path2", signature: "sig2", filename: "file2", ext: "png"]]]]                              | 1        | 1           | true   | 1
        ["image"]         | [[images: [[path: "path1", signature: "sig1", filename: "file1", ext: "jpg"]]]]                                                                                                              | 1        | 1           | false  | 0
        ["image"]         | [[images: [[path: "path1", signature: "sig1", filename: "file1", ext: "jpg"]]], [images: [[path: "path2", signature: "sig2", filename: "file2", ext: "png"]]]]                              | 2        | 2           | false  | 0
    }

    @Unroll
    def "test getNPathsWithoutPermission success"() {
        given: "准备测试数据"
        def ea = "ea_test"
        def userId = "123"
        def files = [
            new V5FileInfo(nPath: "path1"),
            new V5FileInfo(nPath: "path2")
        ]

        and: "mock nFileStorageService响应"
        def result1 = new NCreateNoPermissionFile.Result(nPath: "new_path1")
        def result2 = new NCreateNoPermissionFile.Result(nPath: "new_path2")
        
        nFileStorageService.nCreateNoPermissionFile({ NCreateNoPermissionFile.Arg arg ->
            arg.sourceUser == "E." + userId &&
            arg.ea == ea &&
            arg.nPath == "path1"
        }, ea) >> result1
        
        nFileStorageService.nCreateNoPermissionFile({ NCreateNoPermissionFile.Arg arg ->
            arg.sourceUser == "E." + userId &&
            arg.ea == ea &&
            arg.nPath == "path2"
        }, ea) >> result2

        when: "调用测试方法"
        def results = fileStoreService.getNPathsWithoutPermission(ea, userId, files)

        then: "验证结果"
        results.size() == 2
        results.collect { it.nPath }.sort() == ["new_path1", "new_path2"].sort()
        results.every { result ->
            result.nPath in ["new_path1", "new_path2"]
        }
    }

    @Unroll
    def "test getNPathsWithoutPermission with empty files"() {
        given: "准备测试数据"
        def ea = "ea_test"
        def userId = "123"
        def files = []

        when: "调用测试方法"
        def results = fileStoreService.getNPathsWithoutPermission(ea, userId, files)

        then: "验证结果"
        results.size() == 0
    }

    @Unroll
    def "test getNPathsWithoutPermission with service exception"() {
        given: "准备测试数据"
        def ea = "ea_test"
        def userId = "123"
        def files = [new V5FileInfo(nPath: "path1")]

        and: "mock service exception"
        nFileStorageService.nCreateNoPermissionFile(_, _) >> { throw new RuntimeException("Service error") }

        when: "调用测试方法"
        fileStoreService.getNPathsWithoutPermission(ea, userId, files)

        then: "验证异常"
        def e = thrown(ValidateException)
        e.message == I18NKey.ERROR_OBTAINING_NPATH
    }

    @Unroll
    def "test getNPathsWithoutPermission with interrupted exception"() {
        given: "准备测试数据"
        def ea = "ea_test"
        def userId = "123"
        def files = [new V5FileInfo(nPath: "path1")]

        and: "mock interrupted exception"
        nFileStorageService.nCreateNoPermissionFile(_, _) >> { args ->
            // 先设置中断状态
            Thread.currentThread().interrupt()
            // 抛出InterruptedException以确保中断被处理
            throw new InterruptedException("Test interruption")
        }

        when: "调用测试方法"
        fileStoreService.getNPathsWithoutPermission(ea, userId, files)

        then: "验证异常"
        def e = thrown(ValidateException)
        e.message == I18NKey.ERROR_OBTAINING_NPATH
        
        and: "验证中断状态已被清除"
        !Thread.currentThread().isInterrupted()
    }

    @Unroll
    def "test getNPathsWithoutPermission with duplicate results"() {
        given: "准备38个文件的测试数据"
        def ea = "ea_test"
        def userId = "123"
        def files = (1..38).collect { index ->
            new V5FileInfo(nPath: "path${index}")
        }

        and: "mock nFileStorageService响应"
        nFileStorageService.nCreateNoPermissionFile(_, _) >> { NCreateNoPermissionFile.Arg arg, String enterpriseAccount ->
            // 确保每次调用都返回一个有效的结果
            new NCreateNoPermissionFile.Result(nPath: arg.nPath == "path38" ? "new_path1" : "new_" + arg.nPath)
        }

        when: "调用测试方法"
        def results = fileStoreService.getNPathsWithoutPermission(ea, userId, files)

        then: "验证结果数量"
        results.size() == 38  // 总共38个结果
        
        and: "验证结果中包含所有预期的路径"
        def resultPaths = results.collect { it.nPath }
        resultPaths.count { it == "new_path1" } == 2  // new_path1应该出现两次
        resultPaths.count { it.startsWith("new_path") && it != "new_path1" } == 36  // 其他路径应该各出现一次
    }
}
