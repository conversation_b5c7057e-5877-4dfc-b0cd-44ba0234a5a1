package com.facishare.paas.appframework.metadata.expression

import org.powermock.reflect.internal.WhiteboxImpl
import spock.lang.Specification

class AbstractExpressionVariableTest extends Specification {
    AbstractExpressionVariable abstractExpressionVariable = Spy(AbstractExpressionVariable)

    def "测试getBindingValue方法"() {
        given:
        def objectFieldVar = new ExpressionVariableFactory.FormVariable('field__c', '字段')
        objectFieldVar.setDataType(dataType)
        objectFieldVar.setValue(value)

        when:
        def result = objectFieldVar.getBindingValue(nullAsZero, isFormula, returnType, useValue, tenantId)

        then:
        resultValue == result

        where:
        dataType                      | value | nullAsZero | isFormula | returnType                    | useValue | tenantId || resultValue
        null                          | null  | true       | true      | ExpressionDataType.String     | true     | '74255'  || ''
        null                          | null  | false      | false     | ExpressionDataType.SelectMany | false    | '74255'  || null
        ExpressionDataType.Decimal    | null  | false      | false     | ExpressionDataType.Decimal    | false    | '74255'  || null
        ExpressionDataType.Decimal    | null  | true       | false     | ExpressionDataType.Decimal    | false    | '74255'  || 0
        ExpressionDataType.RichText   | null  | false      | false     | ExpressionDataType.RichText   | false    | '74255'  || ''
        ExpressionDataType.SelectMany | null  | false      | false     | ExpressionDataType.String     | false    | '74255'  || ''
        ExpressionDataType.SelectMany | null  | false      | false     | ExpressionDataType.SelectMany | false    | '74255'  || []
        ExpressionDataType.Percentile | 20    | false      | false     | ExpressionDataType.SelectMany | false    | '74255'  || 0.2
    }

    def '单独测试私有方法isUseValue'() {
        given:
        abstractExpressionVariable.setDataType(dataType)
        abstractExpressionVariable.setName(name)
        when:
        def result = WhiteboxImpl.invokeMethod(abstractExpressionVariable, 'isUseValue')
        then:
        isUseValue == result
        where:
        name              | dataType                      || isUseValue
        'field__c._label' | null                          || false
        'field__c._value' | null                          || true
        'field__c'        | ExpressionDataType.RecordType || true
        'field__c'        | ExpressionDataType.SelectMany || true
        'field__c'        | ExpressionDataType.SelectOne  || false
    }
}
