package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.function.dto.IncrementNumberBatchExecute
import com.facishare.paas.metadata.api.describe.AutoNumber
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * create by z<PERSON><PERSON> on 2019/11/11
 */
class AutoNumberLogicServiceTest extends Specification {
    AutoNumberLogicService autoNumberLogicService
    IObjectDescribe describe
    IObjectDescribe personnelObj
    DescribeLogicService describeLogicService
    AutoNumberService autoNumberService


    String personneJson = '''{"fields":{"birthday":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251673,"description":"员工的生日","is_unique":false,"label":"生日","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_required":false,"api_name":"birthday","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d0dc03aa5083df3da572678","is_index_field":false,"is_single":false,"label_r":"生日","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"l_1","status":"released"},"date_of_first_ployment":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"description":"就业日期","is_unique":false,"label":"就业日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_required":false,"api_name":"date_of_first_ployment","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d0dc03aa5083df3da572686","is_index_field":false,"is_single":false,"label_r":"就业日期","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"l_3","status":"released"},"date_of_joining":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251933,"description":"入职日期","is_unique":false,"label":"入职日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_required":false,"api_name":"date_of_joining","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d0dc03aa5083df3da57269b","is_index_field":false,"is_single":false,"label_r":"入职日期","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"l_4","status":"released"},"description":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251675,"pattern":"","description":"基本描述","is_unique":false,"label":"基本信息描述","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"description","define_type":"package","_id":"5d0dc03aa5083df3da572681","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"基本信息描述","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_2","max_length":500,"status":"released"},"email":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"description":"员工的邮箱","is_unique":false,"label":"邮箱","type":"email","is_abstract":null,"field_num":null,"is_required":false,"api_name":"email","define_type":"package","_id":"5d0dc03aa5083df3da57268b","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"邮箱","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"w_1","max_length":64,"status":"released"},"employee_create_time":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1569045228367,"description":"employee_create_time","is_unique":false,"label":"员工创建时间","time_zone":"","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"employee_create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","_id":"5d85baec319d1998e71f35b3","is_index_field":false,"is_single":false,"label_r":null,"status":"released"},"employee_number":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251933,"pattern":"","description":"员工编号","is_unique":false,"label":"员工编号","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"employee_number","define_type":"package","_id":"5d0dc03aa5083df3da57269c","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"员工编号","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_11","max_length":64,"status":"released"},"extend_obj_data_id":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"连接通表的记录ID,扩展字段用","is_unique":true,"label":"扩展字段在mt_data中的记录ID","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"extend_obj_data_id","define_type":"system","_id":"5d0dc03aa5083df3da572685","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"扩展字段在mt_data中的记录ID","index_name":"t_5","max_length":64,"status":"released"},"extension_number":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251932,"pattern":"^[0-9+-;,]{0,100}$","description":"办公电话扩展分机号","is_unique":false,"label":"办公电话扩展分机号","type":"phone_number","is_abstract":null,"field_num":null,"is_required":false,"api_name":"extension_number","define_type":"package","_id":"5d0dc03aa5083df3da572698","is_index_field":false,"is_single":false,"label_r":"办公电话扩展分机号","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"p_3","status":"released"},"field_0tcKA__c":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1561182266463,"is_unique":false,"description":"","label":"国家","type":"country","is_abstract":null,"field_num":4,"used_in":"component","is_required":false,"api_name":"field_0tcKA__c","options":[],"define_type":"custom","_id":"5d0dc03aa5083df3da572695","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_9","help_text":"","status":"new"},"field_101sx__c":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1561182277319,"is_unique":false,"description":"","default_value":"","label":"单选","type":"select_one","is_abstract":null,"field_num":8,"is_required":false,"api_name":"field_101sx__c","options":[{"not_usable":false,"label":"示例选项","value":"option1","config":{"edit":1,"enable":1,"remove":1}},{"not_usable":true,"label":"其他","value":"other","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"custom","_id":"5d0dc045a5083df3da5727a8","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"options":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_12","help_text":"","status":"new"},"field_5n1bw__c":{"describe_api_name":"PersonnelObj","default_is_expression":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","is_extend":true,"is_single":false,"index_name":"t_14","max_length":100,"is_index":true,"is_active":true,"create_time":1561182275854,"default_value":"","label":"单行文本","is_abstract":null,"field_num":7,"api_name":"field_5n1bw__c","_id":"5d0dc043a5083df3da572716","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"help_text":"","status":"new"},"field_anyj8__c":{"describe_api_name":"PersonnelObj","is_index":false,"is_active":true,"create_time":1561182266240,"is_unique":false,"description":"","group_type":"area","label":"地区定位","type":"group","is_abstract":null,"field_num":null,"is_required":false,"api_name":"field_anyj8__c","define_type":"custom","_id":"5d0dc03aa5083df3da57267e","fields":{"area_country":"field_0tcKA__c","area_location":"field_dswyn__c","area_detail_address":"field_Hujhe__c","area_city":"field_m1tv1__c","area_province":"field_fIT02__c","area_district":"field_d2poo__c"},"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"header":1}},"index_name":"s_4","help_text":"","status":"new"},"field_d2poo__c":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1561182266463,"is_unique":false,"description":"","label":"区","type":"district","is_abstract":null,"field_num":5,"used_in":"component","is_required":false,"api_name":"field_d2poo__c","options":[],"define_type":"custom","_id":"5d0dc03aa5083df3da57269a","cascade_parent_api_name":"field_m1tv1__c","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_10","help_text":"","status":"new"},"field_dswyn__c":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1561182266463,"is_unique":false,"description":"","label":"定位","type":"location","is_abstract":null,"field_num":6,"used_in":"component","is_required":false,"api_name":"field_dswyn__c","define_type":"custom","_id":"5d0dc03aa5083df3da57269d","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"label":1,"help_text":1}},"index_name":"t_12","help_text":"","status":"new"},"field_fIT02__c":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1561182266303,"is_unique":false,"description":"","label":"省","type":"province","is_abstract":null,"field_num":1,"used_in":"component","is_required":false,"api_name":"field_fIT02__c","options":[],"define_type":"custom","_id":"5d0dc03aa5083df3da572679","cascade_parent_api_name":"field_0tcKA__c","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_1","help_text":"","status":"new"},"field_Hujhe__c":{"describe_api_name":"PersonnelObj","default_is_expression":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_extend":true,"is_single":false,"index_name":"t_7","max_length":100,"is_index":true,"is_active":true,"create_time":1561182266303,"default_value":"","label":"详细地址","is_abstract":null,"field_num":3,"api_name":"field_Hujhe__c","_id":"5d0dc03aa5083df3da572688","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"help_text":"","status":"new"},"field_m1tv1__c":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1561182266303,"is_unique":false,"description":"","label":"市","type":"city","is_abstract":null,"field_num":2,"used_in":"component","is_required":false,"api_name":"field_m1tv1__c","options":[],"define_type":"custom","_id":"5d0dc03aa5083df3da57267a","cascade_parent_api_name":"field_fIT02__c","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_2","help_text":"","status":"new"},"field_rZcle__c":{"describe_api_name":"PersonnelObj","default_is_expression":false,"is_index":true,"is_active":true,"create_time":1570588979984,"pattern":"^[0-9+-;,]{0,100}$","is_unique":false,"description":"","default_value":"","label":"手机1","type":"phone_number","is_abstract":null,"field_num":9,"is_required":false,"api_name":"field_rZcle__c","define_type":"custom","_id":"5d9d4934a5083df20e6ec933","is_extend":true,"is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"p_4","help_text":"","status":"new"},"full_name":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1567674055157,"pattern":"","description":"姓名","is_unique":false,"label":"姓名","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"full_name","define_type":"package","_id":"5d70cec7319d19ec406b052f","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"姓名","config":{"attrs":{"is_readonly":0,"is_required":0}},"max_length":64,"status":"released"},"is_active":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1569045228367,"description":"is_active","is_unique":false,"default_value":false,"label":"激活","type":"true_or_false","is_abstract":null,"field_num":null,"is_required":false,"api_name":"is_active","options":[{"label":"是","value":true,"config":{"edit":1,"enable":0,"remove":0}},{"label":"否","value":false,"config":{"edit":1,"enable":0,"remove":0}}],"define_type":null,"_id":"5d85baec319d1998e71f35b2","is_index_field":false,"is_single":false,"label_r":"激活","status":"new"},"is_pause_login":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1569045228376,"description":"","is_unique":false,"default_value":false,"label":"禁止登录","type":"true_or_false","is_abstract":null,"field_num":null,"is_required":false,"api_name":"is_pause_login","options":[{"label":"是","value":true,"config":{"edit":1,"enable":0,"remove":0}},{"label":"否","value":false,"config":{"edit":1,"enable":0,"remove":0}}],"define_type":null,"_id":"5d85baec319d1998e71f35b4","is_index_field":false,"is_single":false,"label_r":"禁止登录","status":"new"},"leader":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251673,"description":"员工的直属上级，即汇报对象","is_unique":false,"label":"汇报对象","type":"employee","is_abstract":null,"field_num":null,"is_required":false,"api_name":"leader","define_type":"package","_id":"5d0dc03aa5083df3da57267c","is_extend":false,"is_index_field":false,"is_single":true,"label_r":"汇报对象","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"a_1","max_length":64,"status":"released"},"life_status":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251896,"description":"生命状态","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","is_abstract":null,"field_num":null,"is_required":true,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"5d0dc03aa5083df3da57268f","is_index_field":false,"is_single":true,"label_r":"生命状态","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"s_6","status":"released"},"life_status_before_invalid":{"describe_api_name":"PersonnelObj","is_index":false,"is_active":true,"create_time":*************,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5d0dc03aa5083df3da572689","is_index_field":false,"is_single":false,"label_r":"作废前生命状态","index_name":"t_8","max_length":256,"status":"released"},"lock_rule":{"describe_api_name":"PersonnelObj","is_index":false,"is_active":true,"create_time":*************,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5d0dc03aa5083df3da57267b","is_index_field":false,"is_single":false,"label_r":"锁定规则","index_name":"s_3","status":"released"},"lock_status":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251878,"is_unique":false,"description":"","default_value":"0","label":"锁定状态","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"5d0dc03aa5083df3da57268e","is_index_field":false,"is_single":true,"label_r":"锁定状态","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"s_5","status":"released"},"lock_user":{"describe_api_name":"PersonnelObj","is_index":false,"is_active":true,"create_time":*************,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5d0dc03aa5083df3da572683","is_index_field":false,"is_single":true,"label_r":"加锁人","index_name":"a_2","status":"released"},"main_department":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251917,"description":"员工的主属部门","is_unique":false,"label":"主属部门","type":"department","is_abstract":null,"field_num":null,"is_required":false,"api_name":"main_department","define_type":"package","_id":"5d0dc03aa5083df3da572692","is_index_field":false,"is_single":true,"label_r":"主属部门","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"a_4","status":"released"},"msn":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251675,"pattern":"","description":"msn","is_unique":false,"label":"msn","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"msn","define_type":"package","_id":"5d0dc03aa5083df3da572682","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"msn","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_3","max_length":64,"status":"released"},"name":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1527155144118,"pattern":"","description":"员工昵称，只在企业内唯一","is_unique":true,"label":"员工昵称","type":"text","is_abstract":null,"field_num":null,"is_required":true,"api_name":"name","define_type":"package","_id":"5d0dc03aa5083df3da572699","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"系统名（昵称）","config":{"edit":0,"enable":0,"attrs":{"label":0}},"index_name":"name","max_length":64,"status":"released"},"owner":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251679,"is_unique":false,"description":"","label":"负责人","type":"employee","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d0dc03aa5083df3da57268d","is_extend":false,"is_index_field":false,"is_single":true,"label_r":"负责人","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"owner","status":"released"},"owner_department":{"describe_api_name":"PersonnelObj","default_is_expression":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","is_extend":false,"is_single":true,"label_r":"负责人所在部门","index_name":"owner_dept","max_length":100,"is_index":false,"is_active":true,"create_time":1542613251677,"default_value":"","label":"负责人所在部门","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"owner_department","_id":"5d0dc03aa5083df3da57268a","is_index_field":false,"config":{"attrs":{"is_readonly":0,"is_required":0}},"status":"released"},"password":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"密码","is_unique":false,"label":"密码","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"password","define_type":"package","_id":"5d9d993a319d1998e71f4775","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"密码","config":{"attrs":{"is_readonly":0,"is_required":0}},"max_length":100,"status":"released"},"phone":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"^[0-9+-;,]{0,100}$","description":"手机","is_unique":true,"label":"手机","type":"phone_number","is_abstract":null,"field_num":null,"is_required":false,"api_name":"phone","define_type":"package","_id":"5d0dc03aa5083df3da572697","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"手机","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"p_2","max_length":64,"status":"released"},"position":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251934,"pattern":"","description":"员工的职位，如销售经理，java工程师","is_unique":false,"label":"职位","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"position","define_type":"package","_id":"5d0dc03aa5083df3da57269e","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"职位","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_13","max_length":64,"status":"released"},"profile_image":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"头像","is_unique":false,"label":"头像","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"profile_image","define_type":"package","_id":"5d9d993a319d1998e71f4776","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"头像","config":{"attrs":{"is_readonly":0,"is_required":0}},"max_length":500,"status":"released"},"qq_account":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"qq号码","is_unique":false,"label":"qq号码","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"qq_account","define_type":"package","_id":"5d0dc03aa5083df3da572684","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"qq号码","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_4","max_length":64,"status":"released"},"record_type":{"describe_api_name":"PersonnelObj","is_index":false,"is_active":true,"create_time":*************,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"5d0dc03aa5083df3da572693","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"业务类型","index_name":"r_type","config":{},"status":"released"},"relevant_team":{"describe_api_name":"PersonnelObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5d0dc03aa5083df3da572694","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"相关团队","index_name":"s_8","help_text":"相关团队","status":"new"},"sex":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"description":"","label":"性别","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"sex","options":[{"not_usable":false,"label":"男","value":"M"},{"not_usable":false,"label":"女","value":"F"}],"define_type":"package","_id":"5d0dc03aa5083df3da572690","is_index_field":false,"is_single":false,"label_r":"性别","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"s_7","status":"released"},"status":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":1542613251944,"description":"表示员工状态，启用或停用","is_unique":false,"label":"员工状态","type":"select_one","is_abstract":null,"field_num":null,"is_required":true,"api_name":"status","options":[{"not_usable":false,"label":"启用","value":"0"},{"not_usable":false,"label":"停用","value":"1"}],"define_type":"package","_id":"5d0dc03aa5083df3da57269f","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"员工状态","index_name":"s_11","config":{},"status":"released"},"stop_time":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"description":"停用时间","is_unique":false,"label":"停用时间","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_required":false,"api_name":"stop_time","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d0dc03aa5083df3da57267d","is_index_field":false,"is_single":false,"label_r":"停用时间","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"l_2","status":"released"},"user_id":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"员工ID，只在企业内唯一","is_unique":true,"label":"员工ID","type":"text","is_abstract":null,"field_num":null,"is_required":true,"api_name":"user_id","define_type":"package","_id":"5d0dc03aa5083df3da572696","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"员工ID","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_10","max_length":64,"status":"released"},"user_name":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"员工登录账号","is_unique":true,"label":"登录账号","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"user_name","define_type":"package","_id":"5d0dc03aa5083df3da57267f","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"登录账号","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_1","max_length":64,"status":"released"},"vice_departments":{"describe_api_name":"PersonnelObj","is_index":false,"is_active":true,"create_time":*************,"description":"员工的附属部门","is_unique":false,"label":"附属部门","type":"department","is_abstract":null,"field_num":null,"is_required":false,"api_name":"vice_departments","define_type":"package","_id":"5d0dc03aa5083df3da57268c","is_index_field":false,"is_single":false,"label_r":"附属部门","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"a_3","status":"released"},"weixin_account":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"微信号码","is_unique":false,"label":"微信号码","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"weixin_account","define_type":"package","_id":"5d0dc03aa5083df3da572687","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"微信号码","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_6","max_length":64,"status":"released"},"working_states":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"心情","is_unique":false,"label":"心情","type":"text","is_abstract":null,"field_num":null,"is_required":false,"api_name":"working_states","define_type":"package","_id":"5d0dc03aa5083df3da572691","is_extend":false,"is_index_field":false,"is_single":false,"label_r":"心情","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"t_9","max_length":64,"status":"released"},"work_phone":{"describe_api_name":"PersonnelObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"^[0-9+-;,]{0,100}$","description":"办公电话","is_unique":false,"label":"办公电话","type":"phone_number","is_abstract":null,"field_num":null,"is_required":false,"api_name":"work_phone","define_type":"package","_id":"5d0dc03aa5083df3da572680","is_index_field":false,"is_single":false,"label_r":"办公电话","config":{"attrs":{"is_readonly":0,"is_required":0}},"index_name":"p_1","status":"released"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":1561182266303},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":1561182266303,"description":""},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":1561182266303,"description":""},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":1561182266303,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":1561182266303,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":1561182266303},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":1561182266303},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":1561182266303},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":1561182266303},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":1561182266303},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":1561182266303},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":1561182266303,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":1561182266303,"description":""},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":1561182266303,"description":""},"order_by":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"order_by","api_name":"order_by","description":"order_by","status":"released","index_name":"o_by","create_time":1561182266303}},"actions":{},"index_version":1,"_id":"5d0dc03aa5083df3da572677","tenant_id":"78057","is_udef":true,"api_name":"PersonnelObj","created_by":"-1000","last_modified_by":"1005","display_name":"人员","package":"CRM","record_type":null,"is_active":true,"icon_path":null,"version":4,"release_version":"6.4","plural_name":null,"define_type":"package","is_deleted":false,"config":{"layout":{"add":0},"record_type":{"add":0,"assign":0}},"last_modified_time":1570588980409,"create_time":1561182266303,"store_table_name":"org_employee_user","module":null,"icon_index":null,"description":"描述企业的员工基本信息","visible_scope":null,"display_name_r":"人员"}'''
    String describeJson = '''{"fields":{"field_15Sli__c":{"describe_api_name":"object_ryjRs__c","default_is_expression":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference","is_required":false,"wheres":[],"define_type":"custom","is_single":false,"index_name":"s_5","is_index":true,"is_active":true,"create_time":1566181338104,"label":"查找关联","target_api_name":"ProductObj","target_related_list_name":"target_related_list_Bzd4u__c","is_abstract":null,"field_num":6,"target_related_list_label":"zj-自定义对象","action_on_target_delete":"set_null","api_name":"field_15Sli__c","_id":"5d5a07daa5083dd185da5a22","is_index_field":true,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"target_related_list_label":1,"wheres":1,"api_name":1,"is_unique":1,"default_value":1,"label":1,"target_api_name":1,"target_related_list_name":1,"help_text":1}},"help_text":"","status":"new"},"field_22751__c":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1573460343296,"is_unique":false,"description":"","default_value":"","label":"单选1","type":"select_one","is_abstract":null,"field_num":13,"is_required":false,"api_name":"field_22751__c","options":[{"not_usable":false,"label":"示例选项","value":"option1","config":{"edit":1,"enable":1,"remove":1}},{"not_usable":true,"label":"其他","value":"other","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"custom","_id":"5dc91977a5083dc313523c58","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"options":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_8","help_text":"","status":"new"},"field_417gh__c":{"describe_api_name":"object_ryjRs__c","default_is_expression":false,"pattern":"","description":"","is_unique":true,"type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","is_single":false,"index_name":"t_3","max_length":100,"is_index":true,"is_active":true,"create_time":1566289368668,"default_value":"","label":"单行文本-不能重复","is_abstract":null,"field_num":11,"api_name":"field_417gh__c","_id":"5d5badd8a5083d83035f4c6e","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"help_text":"","status":"new"},"field_DSsA4__c":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181496021,"quote_field_type":"text","description":"","is_unique":false,"label":"引用产品名称","type":"quote","is_abstract":null,"field_num":7,"quote_field":"field_15Sli__c__r.name","is_required":false,"api_name":"field_DSsA4__c","define_type":"custom","_id":"5d5a0878a5083dd185da7e00","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"is_index":1,"api_name":1,"quote_field_type":1,"label":1,"help_text":1,"quote_field":1}},"index_name":"t_2","help_text":"","status":"new"},"field_E6U3r__c":{"describe_api_name":"object_ryjRs__c","is_index":false,"is_active":true,"create_time":1566182394311,"quote_field_type":"employee","description":"","is_unique":false,"label":"引用人员","type":"quote","is_abstract":null,"quote_field":"field_15Sli__c__r.owner","field_num":null,"is_required":false,"api_name":"field_E6U3r__c","define_type":"custom","_id":"5d5a0bfaa5083dd185db0104","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"is_index":1,"api_name":1,"quote_field_type":1,"label":1,"help_text":1,"quote_field":1}},"index_name":"a_2","help_text":"","status":"new"},"field_fveT1__c":{"describe_api_name":"object_ryjRs__c","prefix":"{yy}-{mm}","description":"","is_unique":true,"start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1573443574943,"default_value":"{yy}-{mm}0001","serial_number":4,"label":"自增编号","is_abstract":null,"field_num":12,"condition":"NONE","api_name":"field_fveT1__c","_id":"5dc8d7f6a5083dc31350ab6e","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"condition":1,"prefix":1,"api_name":1,"text_preview":1,"start_number":1,"serial_number":1,"label":1,"postfix":1,"help_text":1}},"help_text":"","status":"new"},"field_MkG5b__c":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566182034730,"quote_field_type":"currency","description":"","is_unique":false,"label":"引用价格","type":"quote","is_abstract":null,"field_num":10,"quote_field":"field_15Sli__c__r.price","is_required":false,"api_name":"field_MkG5b__c","define_type":"custom","_id":"5d5a0a92a5083dd185dac865","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"is_index":1,"api_name":1,"quote_field_type":1,"label":1,"help_text":1,"quote_field":1}},"index_name":"d_1","help_text":"","status":"new"},"field_nvs89__c":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1573460347242,"is_unique":false,"description":"","default_value":"","label":"单选2","type":"select_one","is_abstract":null,"field_num":14,"is_required":false,"api_name":"field_nvs89__c","options":[{"not_usable":false,"label":"示例选项","value":"option1","config":{"edit":1,"enable":1,"remove":1}},{"not_usable":true,"label":"其他","value":"other","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"custom","_id":"5dc9197ba5083dc313523c5a","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"options":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"index_name":"s_9","help_text":"","status":"new"},"field_W4bec__c":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181904486,"quote_field_type":"date_time","description":"","is_unique":false,"label":"引用上架时间","type":"quote","is_abstract":null,"field_num":8,"quote_field":"field_15Sli__c__r.off_shelves_time","is_required":false,"api_name":"field_W4bec__c","define_type":"custom","_id":"5d5a0a10a5083dd185dab3d7","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"is_index":1,"api_name":1,"quote_field_type":1,"label":1,"help_text":1,"quote_field":1}},"index_name":"l_1","help_text":"","status":"new"},"field_xYkm6__c":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181953852,"quote_field_type":"select_one","description":"","is_unique":false,"label":"引用上下架","type":"quote","is_abstract":null,"field_num":9,"quote_field":"field_15Sli__c__r.product_status","is_required":false,"api_name":"field_xYkm6__c","define_type":"custom","_id":"5d5a0a41a5083dd185dab901","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"is_index":1,"api_name":1,"quote_field_type":1,"label":1,"help_text":1,"quote_field":1}},"index_name":"s_6","help_text":"","status":"new"},"life_status":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181264664,"description":"","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","is_abstract":null,"field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective","config":{"edit":0,"enable":0,"remove":0}},{"label":"审核中","value":"under_review","config":{"edit":0,"enable":0,"remove":0}},{"label":"正常","value":"normal","config":{"edit":0,"enable":0,"remove":0}},{"label":"变更中","value":"in_change","config":{"edit":0,"enable":0,"remove":0}},{"label":"作废","value":"invalid","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a0790a5083dd185da4aac","is_index_field":false,"is_single":false,"label_r":null,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"is_readonly":0,"is_required":0,"api_name":0,"options":0,"is_unique":0,"default_value":0,"label":0,"help_text":0}},"index_name":"s_3","help_text":"","status":"new"},"life_status_before_invalid":{"describe_api_name":"object_ryjRs__c","is_index":false,"is_active":true,"create_time":1573443785359,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_abstract":null,"field_num":4,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5dc8d8c9a5083dc31350c2e5","is_index_field":false,"is_single":false,"label_r":null,"index_name":"t_1","max_length":256,"status":"new"},"lock_rule":{"describe_api_name":"object_ryjRs__c","is_index":false,"is_active":true,"create_time":1573443785359,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","is_abstract":null,"field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5dc8d8c9a5083dc31350c2e2","is_index_field":false,"is_single":false,"label_r":null,"index_name":"s_1","status":"new"},"lock_status":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181264642,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_abstract":null,"field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0","config":{"edit":0,"enable":0,"remove":0}},{"label":"锁定","value":"1","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a0790a5083dd185da4a9c","is_index_field":false,"is_single":false,"label_r":null,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"is_readonly":0,"is_required":0,"api_name":0,"options":0,"is_unique":0,"default_value":0,"label":0,"help_text":0}},"index_name":"s_2","help_text":"","status":"new"},"lock_user":{"describe_api_name":"object_ryjRs__c","is_index":false,"is_active":true,"create_time":1573443785359,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_abstract":null,"field_num":5,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5dc8d8c9a5083dc31350c2e3","is_index_field":false,"is_single":true,"label_r":null,"index_name":"a_1","status":"new"},"name":{"describe_api_name":"object_ryjRs__c","default_is_expression":false,"pattern":"","description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","is_single":false,"label_r":null,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1566181264715,"default_value":"","label":"主属性","is_abstract":null,"field_num":null,"api_name":"name","_id":"5d5a0790a5083dd185da4aae","is_index_field":false,"config":{"add":0,"edit":1,"enable":0,"display":1,"remove":0,"attrs":{"api_name":1,"is_unique":1,"label":1,"type":1,"help_text":1}},"help_text":"","status":"new"},"owner":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181264661,"description":"","is_unique":false,"label":"负责人","type":"employee","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d5a0790a5083dd185da4a8c","is_index_field":false,"is_single":true,"label_r":null,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"index_name":"owner","help_text":"","status":"new"},"owner_department":{"describe_api_name":"object_ryjRs__c","default_is_expression":false,"pattern":"","description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","is_single":true,"label_r":null,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1566181264662,"default_value":"","label":"负责人主属部门","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"owner_department","_id":"5d5a0790a5083dd185da4aaf","is_index_field":false,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"help_text":"","status":"new"},"record_type":{"describe_api_name":"object_ryjRs__c","is_index":true,"is_active":true,"create_time":1566181264663,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型","config":{"edit":1,"enable":0,"remove":0}}],"define_type":"package","_id":"5d5a0790a5083dd185da4ab1","is_index_field":false,"is_single":false,"label_r":null,"config":{"add":0,"edit":0,"enable":0,"display":1,"remove":0,"attrs":{"api_name":0,"label":0}},"index_name":"r_type","help_text":"","status":"released"},"relevant_team":{"describe_api_name":"object_ryjRs__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1573443785488,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5dc8d8c9a5083dc31350c2e4","is_index_field":false,"is_single":false,"label_r":null,"index_name":"a_team","help_text":"相关团队","status":"new"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":1566181264666},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":1566181264666,"description":""},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":1566181264666,"description":""},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":1566181264666,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":1566181264666,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":1566181264666},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":1566181264666},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":1566181264666},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":1566181264666},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":1566181264666},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":1566181264666},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":1566181264666,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":1566181264666,"description":""},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":1566181264666,"description":""},"order_by":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"order_by","api_name":"order_by","description":"order_by","status":"released","index_name":"o_by","create_time":1566181264666}},"actions":{},"index_version":1,"_id":"5d5a0790a5083dd185da4a8b","tenant_id":"78057","is_udef":true,"api_name":"object_ryjRs__c","created_by":"1000","last_modified_by":"1000","display_name":"zj-自定义对象lookup产品","package":"CRM","record_type":null,"is_active":true,"icon_path":"A_201907_04_25fcb9f2ca2147f6a41d36a52614341c.png","version":23,"release_version":"6.4","plural_name":null,"define_type":"custom","is_deleted":false,"config":{"button":{"add":1},"layout":{"add":1,"assign":1},"layout_rule":{"add":1},"edit":1,"cascade":{"add":1},"rule":{"add":1},"fields":{"add":1},"record_type":{"add":1,"assign":1}},"last_modified_time":1573460347405,"create_time":1566181264666,"store_table_name":null,"module":null,"icon_index":14,"description":"","visible_scope":null}'''

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        describeLogicService = Mock(DescribeLogicService)
        autoNumberService = Mock(AutoNumberService)
        autoNumberLogicService = new AutoNumberLogicService("describeLogicService": describeLogicService, "autoNumberService": autoNumberService)
        personnelObj = createDescribe(personneJson)
        describe = createDescribe(describeJson)
    }

    IObjectDescribe createDescribe(String jsonStr) {
        IObjectDescribe describe = new ObjectDescribe()
        describe.fromJsonString(jsonStr)
        return describe
    }

    def "test validateFixField is selectOne type field"() {
        given: "初始化对象描述和自增编号描述"
        def autoNumber = FieldDescribeFactory.newInstance(fieldJson)

        when: "校验字段"
        autoNumberLogicService.validateFixField(describe, autoNumber, personnelObj)
        then:
        noExceptionThrown()
        where:
        fieldJson                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | _
        '''{"describe_api_name":"object_ryjRs__c","prefix":"{yy}-{mm}","description":"","is_unique":true,"start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"$field_22751__c$","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1573443574943,"default_value":"{yy}-{mm}$owner__r.sex$0001$field_22751__c$","serial_number":4,"label":"自增编号","is_abstract":null,"field_num":12,"condition":"NONE","api_name":"field_fveT1__c","_id":"5dc8d7f6a5083dc31350ab6e","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"condition":1,"prefix":1,"api_name":1,"text_preview":1,"start_number":1,"serial_number":1,"label":1,"postfix":1,"help_text":1}},"help_text":"","status":"new"}'''                | _
        '''{"describe_api_name":"object_ryjRs__c","prefix":"{yy}-{mm}-$owner__r.sex$","description":"","is_unique":true,"start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"$field_22751__c$","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1573443574943,"default_value":"{yy}-{mm}$owner__r.sex$0001$field_22751__c$","serial_number":4,"label":"自增编号","is_abstract":null,"field_num":12,"condition":"NONE","api_name":"field_fveT1__c","_id":"5dc8d7f6a5083dc31350ab6e","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"condition":1,"prefix":1,"api_name":1,"text_preview":1,"start_number":1,"serial_number":1,"label":1,"postfix":1,"help_text":1}},"help_text":"","status":"new"}''' | _
    }

    def "test validateFixField not is selectOne type field"() {
        given: "初始化对象描述和自增编号描述"
        def autoNumber = FieldDescribeFactory.newInstance(fieldJson)
        when: "校验字段"
        autoNumberLogicService.validateFixField(describe, autoNumber, personnelObj)
        then:
        thrown(ValidateException)
        where:
        fieldJson                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | _
        '''{"describe_api_name":"object_ryjRs__c","prefix":"{yy}-{mm}-$field_417gh__c$","description":"","is_unique":true,
"start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"$field_22751__c$",
"is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1573443574943,
"default_value":"{yy}-{mm}$owner__r.sex$0001$field_22751__c$","serial_number":4,"label":"自增编号",
"is_abstract":null,"field_num":12,"condition":"NONE","api_name":"field_fveT1__c","_id":"5dc8d7f6a5083dc31350ab6e",
"is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"condition":1,"prefix":1,
"api_name":1,"text_preview":1,"start_number":1,"serial_number":1,"label":1,"postfix":1,"help_text":1}},
"help_text":"","status":"new"}'''                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | _
        '''{"describe_api_name":"object_ryjRs__c","prefix":"{yy}-{mm}-$field_E6U3R__c$","description":"",
"is_unique":true,"start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"$field_22751__c$","is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1573443574943,"default_value":"{yy}-{mm}$owner__r.sex$0001$field_22751__c$","serial_number":4,"label":"自增编号","is_abstract":null,"field_num":12,"condition":"NONE","api_name":"field_fveT1__c","_id":"5dc8d7f6a5083dc31350ab6e","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"condition":1,"prefix":1,"api_name":1,"text_preview":1,"start_number":1,"serial_number":1,"label":1,"postfix":1,"help_text":1}},"help_text":"","status":"new"}''' | _
        '''{"describe_api_name":"object_ryjRs__c","prefix":"{yy}-{mm}$owner__r.name$","description":"","is_unique":true,
"start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"$field_22751__c$",
"is_single":false,"index_name":"s_7","is_index":true,"is_active":true,"create_time":1573443574943,"default_value":"{yy}-{mm}$owner__r.sex$0001$field_22751__c$","serial_number":4,"label":"自增编号","is_abstract":null,"field_num":12,"condition":"NONE","api_name":"field_fveT1__c","_id":"5dc8d7f6a5083dc31350ab6e","is_index_field":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"condition":1,"prefix":1,"api_name":1,"text_preview":1,"start_number":1,"serial_number":1,"label":1,"postfix":1,"help_text":1}},"help_text":"","status":"new"}'''                                                                                                                                | _
    }

    def "test incrementNumber"() {
        given:
        def describe = Stub(IObjectDescribe)
        def autoNumber = Stub(AutoNumber)
        def incrementNumber = Stub(IncrementNumberBatchExecute.IncrementNumber)
        def user = Stub(User)

        def autoNumberService = Stub(AutoNumberService)
        def autoNumberLogicService = new AutoNumberLogicService(autoNumberService: autoNumberService)

        Map<IncrementNumberBatchExecute.IncrementNumber, Set<String>> incrementNumberSetMap = ImmutableMap.of(incrementNumber, Sets.newHashSet("1", "2"))

        incrementNumber.getCondition() >> condition
        incrementNumber.getCounter() >> counter
        incrementNumber.getSteppingNumber() >> steppingNumber
        incrementNumber.getInitialValue() >> initialValue

        autoNumberService.incrementNumber(describe, autoNumber, counter, initialValue, _, user, 2, steppingNumber) >> 2
        when:
        def result = autoNumberLogicService.incrementNumber(describe, autoNumber, incrementNumberSetMap, user)
        then:
        result.get(incrementNumber).get() == except

        where:
        condition | counter   | steppingNumber | initialValue || except
        "NONE"    | "counter" | 1              | 1            || 2
    }

    def "test calculateAutoNumberValue"() {
        given:
        describeLogicService.findObjectWithoutCopyIfGray(tenantId, "object_ryjRs__c") >> describe

        1 * autoNumberService.generateAutoNumber(describe, 1, _ as Long) >> ["field_fveT1__c": 10]
        1 * autoNumberService.replaceAutoNumber(_ as List, describe, ["field_fveT1__c": 10], _ as Long)

        when:
        def result = autoNumberLogicService.calculateAutoNumberValue(tenantId, dataMap)
        println(result)
        then:
        noExceptionThrown()

        where:
        tenantId | dataMap
        "12321"  | ["object_describe_api_name": "object_ryjRs__c"]
    }
}
