package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer
import com.facishare.paas.appframework.metadata.dto.DetailResource
import com.facishare.paas.appframework.util.JsonUtils
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Language
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import org.powermock.reflect.internal.WhiteboxImpl
import org.springframework.transaction.support.TransactionSynchronizationManager
import org.springframework.transaction.support.TransactionSynchronizationUtils
import spock.lang.Specification

import java.util.function.Supplier
import java.util.function.UnaryOperator
import java.util.stream.Collectors

class ConvertRuleLogicServiceTest extends Specification {
    MetaDataActionService metaDataActionService = Mock(MetaDataActionService)
    MetaDataFindService metaDataFindService = Mock(MetaDataFindService)
    LogService logService = Mock(LogService)
    ConvertRuleLogicService convertRuleLogicService
    ObjectConvertRuleService objectConvertRuleService = Mock(ObjectConvertRuleServiceImpl)
    ObjectConvertTrackerService objectConvertTrackerService = Mock(ObjectConvertTrackerServiceImpl)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> [JSON.parseObject("{\"code\":\"zh-CN\",\"name\":\"简体中文\",\"displayName\":\"简体中文\",\"default\":true}", Language.class)]
    }
    void setup() {
        convertRuleLogicService = new ConvertRuleLogicServiceImpl("metaDataActionService": metaDataActionService,
                "metaDataFindService": metaDataFindService, "logService": logService,
                'objectConvertTrackerService': objectConvertTrackerService, "objectConvertRuleService": objectConvertRuleService)
    }

    def "关闭所有从对象共6条数据后触发关闭主对象1条：共7条"() {
        given:
        def user = User.systemUser("74255")
        def (Object detailDataList, List<IObjectData> detailDataList2, ConvertRuleDataContainer convertRuleDataContainer, ObjectDataExt masterData, ObjectDescribeExt object_Nc1Ek__c, ObjectDescribeExt object_axkN2__c) = buildParameter()
        def detailDataListUpdated = Lists.newArrayList(detailDataList)
        detailDataListUpdated.each { it.set('field_hkfv8__c', true) }

        def detailDataListUpdated2 = Lists.newArrayList(detailDataList2)
        detailDataListUpdated2.each { it.set('field_hkfv8__c', true) }

        def targetDetailData = (JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).details.object_pHn1o__c.collect { ObjectDataExt.of(it as Map).getObjectData() }
                .groupBy { it.describeApiName }

        def targetDetailData2 = (JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).details.object_ptTZE__c.collect { ObjectDataExt.of(it as Map).getObjectData() }
                .groupBy { it.describeApiName }
        targetDetailData << targetDetailData2
        def targetData = ObjectDataExt.of((JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).object_data)


        when:
        metaDataFindService.findObjectDataByIds(_, convertRuleDataContainer.getFromMasterIds(), convertRuleDataContainer.getObjectDescribe().getApiName()) >> [masterData.getObjectData()]
        metaDataFindService.findDetailObjectDataList(_, object_Nc1Ek__c, [masterData.getObjectData()], _ as UnaryOperator) >> detailDataList
        metaDataFindService.findDetailObjectDataList(_, object_axkN2__c, [masterData.getObjectData()], _ as UnaryOperator) >> detailDataList2
        metaDataActionService.batchUpdateByFields(_, _, _) >> detailDataListUpdated >> Lists.newArrayList(detailDataList2)
        metaDataFindService.findObjectDataByIds(_ as String, _, 'object_Nc1Ek__c') >> detailDataList
        metaDataFindService.findObjectDataByIds(_ as String, _, 'object_axkN2__c') >> detailDataList2
        TransactionSynchronizationManager.initSynchronization();
        convertRuleLogicService.closeSourceOrder(user, convertRuleDataContainer, targetData, targetDetailData)
        TransactionSynchronizationUtils.triggerAfterCommit()
        TransactionSynchronizationManager.clearSynchronization()
        then:
        7 * logService.log(_, _, _, _, _, _, _, _, _, _)
    }

    private List buildParameter() {
        def convertRuleDataContainer = ConvertRuleDataContainer.builder().build()
        convertRuleDataContainer.sourceId = '620b7e0035aa3d00010a6a64'
        // 源对象
        def object_3BbVb__c = ObjectDescribeExt.of(JsonUtils.readFileFromResources('describe/object_3BbVb__c.json') as Map)
        def object_Nc1Ek__c = ObjectDescribeExt.of(JsonUtils.readFileFromResources('describe/object_Nc1Ek__c.json') as Map)
        def object_axkN2__c = ObjectDescribeExt.of(JsonUtils.readFileFromResources('describe/object_axkN2__c.json') as Map)
        def masterData = ObjectDataExt.of(JsonUtils.readFileFromResources('data/object_3BbVb__c-masterData.json') as Map)
        def detailDataList = (JsonUtils.readFileFromResources('data/object_Nc1Ek__c-sourceDetailData.json') as List).collect { ObjectDataExt.of(it as Map).getObjectData() }
        def detailDataList2 = (JsonUtils.readFileFromResources('data/object_axkN2__c-sourceDetailData.json') as List).collect { ObjectDataExt.of(it as Map).getObjectData() }

        def list = JsonUtils.readFileFromResources('convertrule/rule_clP2T__c.json') as List
        def convertRuleList = list.collect { new ObjectMappingRuleInfo(it as Map) }
                .collect { WhiteboxImpl.invokeMethod(objectConvertRuleService, 'convertConvertRule', it, 2) }
        convertRuleDataContainer.objectDescribe = object_3BbVb__c
        convertRuleDataContainer.detailDescribe = Lists.newArrayList(object_Nc1Ek__c, object_axkN2__c)
        convertRuleDataContainer.convertRuleList = convertRuleList
        convertRuleDataContainer.fromDetails = [buildDetailResource(masterData, ["object_Nc1Ek__c": detailDataList, "object_axkN2__c": detailDataList2])]
        convertRuleDataContainer.fromMasterIds = ['620b7e0035aa3d00010a6a64']
        [detailDataList, detailDataList2, convertRuleDataContainer, masterData, object_Nc1Ek__c, object_axkN2__c]
    }

    private DetailResource buildDetailResource(IObjectData sourceObjectData, Map<String, List<IObjectData>> sourceDetailObjectData) {
        List<DetailResource.Detail> modelDetail = Lists.newArrayList();
        sourceDetailObjectData.forEach({ apiName, detailDataList ->
            if (CollectionUtils.empty(detailDataList)) {
                return;
            }
            List<String> ids = detailDataList.stream().map({ x -> x.getId() }).collect(Collectors.toList());
            DetailResource.Detail detail = DetailResource.Detail.builder()
                    .apiName(apiName)
                    .ids(ids)
                    .build();
            modelDetail.add(detail);
        });
        return DetailResource.builder()
                .masterId(sourceObjectData.getId())
                .details(modelDetail)
                .build();
    }


    def "关闭其中一个从对象的2条数据"() {
        given:
        def user = User.systemUser("74255")
        def (List<IObjectData> detailDataList, List<IObjectData> detailDataList2, ConvertRuleDataContainer convertRuleDataContainer, ObjectDataExt masterData, ObjectDescribeExt object_Nc1Ek__c, ObjectDescribeExt object_axkN2__c) = buildParameter()
        detailDataList.eachWithIndex { item, index -> if (index >= 2) { item.set('field_20oz0__c', '0') } }
        detailDataList2.each { it.set('field_20oz0__c', '0') }

        def closeWheres = [["connector": "OR", "filters": [["value_type": 0, "operator": "GTE", "field_name": "field_20oz0__c", "field_values": ["30"]]]]]
        convertRuleDataContainer.getConvertRuleList().each { if (it.getId() == '64ae89744ccb540001c29b84') { it.setCloseWheres(closeWheres) } }
        def targetDetailData = (JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).details.object_pHn1o__c.collect { ObjectDataExt.of(it as Map).getObjectData() }
                .groupBy { it.describeApiName }
        def targetData = ObjectDataExt.of((JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).object_data)
        def targetDetailData2 = (JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).details.object_ptTZE__c.collect { ObjectDataExt.of(it as Map).getObjectData() }
                .groupBy { it.describeApiName }
        targetDetailData << targetDetailData2

        when:
        metaDataFindService.findObjectDataByIds(_, convertRuleDataContainer.getFromMasterIds(), convertRuleDataContainer.getObjectDescribe().getApiName()) >> [masterData.getObjectData()]
        metaDataFindService.findDetailObjectDataList(_, object_Nc1Ek__c, [masterData.getObjectData()], _ as UnaryOperator) >> detailDataList
        metaDataFindService.findDetailObjectDataList(_, object_axkN2__c, [masterData.getObjectData()], _ as UnaryOperator) >> detailDataList2
        metaDataActionService.batchUpdateByFields(_, _, _) >> Lists.newArrayList(detailDataList) >> Lists.newArrayList(detailDataList2)
        TransactionSynchronizationManager.initSynchronization();
        convertRuleLogicService.closeSourceOrder(user, convertRuleDataContainer, targetData, targetDetailData)
        TransactionSynchronizationUtils.triggerAfterCommit()
        TransactionSynchronizationManager.clearSynchronization()
        then:
        noExceptionThrown()
        2 * logService.log(_, _, _, _, _, _, _, _, _, _)
    }

    def "关闭其中一个从对象的全部4条数据"() {
        given:
        def user = User.systemUser("74255")
        def (List<IObjectData> detailDataList, List<IObjectData> detailDataList2, ConvertRuleDataContainer convertRuleDataContainer, ObjectDataExt masterData, ObjectDescribeExt object_Nc1Ek__c, ObjectDescribeExt object_axkN2__c) = buildParameter()
        detailDataList2.each { it.set('field_20oz0__c', '0') }

        def closeWheres = [["connector": "OR", "filters": [["value_type": 0, "operator": "GTE", "field_name": "field_20oz0__c", "field_values": ["30"]]]]]
        convertRuleDataContainer.getConvertRuleList().each { if (it.getId() == '64ae89744ccb540001c29b84') { it.setCloseWheres(closeWheres) } }
        def targetDetailData = (JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).details.object_pHn1o__c.collect { ObjectDataExt.of(it as Map).getObjectData() }
                .groupBy { it.describeApiName }
        def targetData = ObjectDataExt.of((JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).object_data)
        def targetDetailData2 = (JsonUtils.readFileFromResources('data/targetObjectData.json') as Map).details.object_ptTZE__c.collect { ObjectDataExt.of(it as Map).getObjectData() }
                .groupBy { it.describeApiName }
        targetDetailData << targetDetailData2

        when:
        metaDataFindService.findObjectDataByIds(_, convertRuleDataContainer.getFromMasterIds(), convertRuleDataContainer.getObjectDescribe().getApiName()) >> [masterData.getObjectData()]
        metaDataFindService.findDetailObjectDataList(_, object_Nc1Ek__c, [masterData.getObjectData()], _ as UnaryOperator) >> detailDataList
        metaDataFindService.findDetailObjectDataList(_, object_axkN2__c, [masterData.getObjectData()], _ as UnaryOperator) >> detailDataList2
        metaDataActionService.batchUpdateByFields(_, _, _) >> Lists.newArrayList(detailDataList) >> Lists.newArrayList(detailDataList2)
        TransactionSynchronizationManager.initSynchronization();
        convertRuleLogicService.closeSourceOrder(user, convertRuleDataContainer, targetData, targetDetailData)
        TransactionSynchronizationUtils.triggerAfterCommit()
        TransactionSynchronizationManager.clearSynchronization()
        then:
        noExceptionThrown()
        4 * logService.log(_, _, _, _, _, _, _, _, _, _)
    }

    def "筛选条件支持按转换规则过滤"() {
        given:
        def user = User.systemUser("74255")
        def (Object detailDataList, List<IObjectData> detailDataList2, ConvertRuleDataContainer convertRuleDataContainer, ObjectDataExt masterData, ObjectDescribeExt object_Nc1Ek__c, ObjectDescribeExt object_axkN2__c) = buildParameter()
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString("{\"limit\":20,\"offset\":0,\"filters\":[{\"value_type\":25,\"operator\":\"IN\",\"field_name\":\"id\",\"field_values\":[\"rule_clP2T__c\"]}],\"orders\":[{\"fieldName\":\"name\",\"isAsc\":false}]}");
        Supplier<Double> masterSalveDataSupplier = { Tuple.of(masterData.getObjectData(), detailDataList) } as Supplier
        when:
        def list = JsonUtils.readFileFromResources('convertrule/rule_clP2T__c.json') as List
        def mappingRuleList = list.collect { new ObjectMappingRuleInfo(it as Map) }
        objectConvertRuleService.findConvertRuleInInternalObjByApiName(user, "rule_clP2T__c") >> convertRuleDataContainer.getConvertRuleList()
        objectConvertRuleService.findObjectMappingRuleByApiName(user, "rule_clP2T__c") >> mappingRuleList
        convertRuleLogicService.processFilterByConvertRule(user, object_Nc1Ek__c.getObjectDescribe(), query, masterSalveDataSupplier, false)
        then:
        query.getFilters().size() == 3
        noExceptionThrown()
    }
}
