package com.facishare.paas.appframework.metadata.gdpr

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService
import com.facishare.paas.metadata.impl.describe.ObjectFieldExtra
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class GdprServiceImplTest extends Specification {

    GdprServiceImpl gdprServiceImpl
    ConfigService configService = Mock(ConfigService)
    SwitchCacheService switchCacheService = Mock(SwitchCacheService)
    IRepository<GdprCompliance> complianceRepository = Mock(IRepository)
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    FsGrayReleaseBiz fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
    
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        
        gdprServiceImpl = new GdprServiceImpl(
            configService: configService,
            switchCacheService: switchCacheService,
            complianceRepository: complianceRepository,
            describeLogicService: describeLogicService
        )
        Whitebox.setInternalState(AppFrameworkConfig, "switchCacheGrayEi", [] as Set)
    }

    def "test openGdprCompliance"() {
        given:
        GdprCompliance gdprCompliance = new GdprCompliance()
        gdprCompliance.setApiNames(apiNames)
        gdprCompliance.setOpenStatus(status)
        when:
        gdprServiceImpl.openGdprCompliance(User.systemUser(tenantId), gdprCompliance)
        then:
        noExceptionThrown()
        where:
        tenantId | apiNames                              | status
        "78057"  | Lists.newArrayList("object_axfew__c") | true
    }

    def "test closeGdprCompliance"() {
        given:
        String tenantId = "78057"
        when:
        gdprServiceImpl.closeGdprCompliance(User.systemUser(tenantId))
        then:
        noExceptionThrown()
    }
    
    def "test findGdprCompliance with blank apiName"() {
        given:
        def user = User.systemUser("74255")
        def apiName = ""
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        result.isEmpty()
        0 * switchCacheService.findSwitchStatus(_, _, _, _, _)
        0 * complianceRepository.findBy(_, _, _)
    }
    
    def "test findGdprCompliance when compliance is not enabled"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        
        and: "模拟不启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> false
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        result.isEmpty()
        0 * complianceRepository.findBy(_, _, _)
    }
    
    def "test findGdprCompliance with compliance enabled and data found"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        def sensitiveFields = ["field1", "field2"]
        def ordinaryFields = ["field3", "field4"]
        
        def compliance = new GdprCompliance()
        compliance.setApiName(apiName)
        compliance.setOpenStatus(true)
        compliance.setSensitiveFields(sensitiveFields)
        compliance.setOrdinaryFields(ordinaryFields)
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟未使用字段扩展属性（通过模拟UdobjGrayConfig.isAllow方法返回false）"
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        and: "模拟查询返回结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> [compliance]
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["LeadsObj", "PartnerObj", "CasesObj", "AccountObj", "object_mAuoz__c", "object_Uh1x4__c", "interaction_revert_object__c", "object_t8wJp__c", "object_dkiy3__c"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        result.size() == 1
        result[0].apiName == apiName
        result[0].sensitiveFields == sensitiveFields
        result[0].ordinaryFields == ordinaryFields
        result[0].openStatus
    }
    
    def "test findGdprCompliance with field compliance settings from field extras"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        
        // 模拟数据库中已存在的配置
        def existingCompliance = new GdprCompliance()
        existingCompliance.setApiName(apiName)
        existingCompliance.setOpenStatus(true)
        existingCompliance.setSensitiveFields(["oldField1"])
        existingCompliance.setOrdinaryFields(["oldField2"])
        
        // 模拟字段扩展属性
        def field1Extra = new ObjectFieldExtra()
        field1Extra.setFieldApiName("sensitiveField1")
        field1Extra.setComplianceSetting(["GDPR": "sensitive"])
        
        def field2Extra = new ObjectFieldExtra()
        field2Extra.setFieldApiName("ordinaryField1")
        field2Extra.setComplianceSetting(["GDPR": "ordinary"])
        
        def fieldExtras = [(apiName): [field1Extra, field2Extra]]
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟使用字段扩展属性（通过模拟UdobjGrayConfig.isAllow方法返回true）"
        fsGrayReleaseBiz.isAllow(*_) >> true
        
        and: "模拟查询返回结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> [existingCompliance]
        
        and: "模拟获取字段扩展属性"
        describeLogicService.findDescribeExtra(user, { set -> set.contains(apiName) }) >> fieldExtras
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["LeadsObj", "PartnerObj", "CasesObj", "AccountObj", "object_mAuoz__c", "object_Uh1x4__c", "interaction_revert_object__c", "object_t8wJp__c", "object_dkiy3__c"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        1 * describeLogicService.findDescribeExtra(_, _)
        
        result.size() == 1
        result[0].apiName == apiName
        result[0].openStatus
    }
    
    def "test findGdprCompliance with field compliance settings but no existing compliance"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        
        // 模拟字段扩展属性
        def field1Extra = new ObjectFieldExtra()
        field1Extra.setFieldApiName("sensitiveField1")
        field1Extra.setComplianceSetting(["GDPR": "sensitive"])
        
        def fieldExtras = [(apiName): [field1Extra]]
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟使用字段扩展属性（通过模拟UdobjGrayConfig.isAllow方法返回true）"
        fsGrayReleaseBiz.isAllow(*_) >> true
        
        and: "模拟查询返回空结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> []
        
        and: "模拟获取字段扩展属性"
        describeLogicService.findDescribeExtra(user, { set -> set.contains(apiName) }) >> fieldExtras
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["LeadsObj", "PartnerObj", "CasesObj", "AccountObj", "object_mAuoz__c", "object_Uh1x4__c", "interaction_revert_object__c", "object_t8wJp__c", "object_dkiy3__c"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        result.size() == 1
    }
    
    def "test findGdprCompliance with field compliance settings and need to create new configuration"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        
        // 模拟字段扩展属性
        def field1Extra = new ObjectFieldExtra()
        field1Extra.setFieldApiName("sensitiveField1")
        field1Extra.setComplianceSetting(["GDPR": "sensitive"])
        
        def field2Extra = new ObjectFieldExtra()
        field2Extra.setFieldApiName("ordinaryField1")
        field2Extra.setComplianceSetting(["GDPR": "ordinary"])
        
        def fieldExtras = [(apiName): [field1Extra, field2Extra]]
        
        // 创建一个新的合规配置（由updateGdprPersonalField方法返回）
        def newCompliance = new GdprCompliance()
        newCompliance.setApiName(apiName)
        newCompliance.setOpenStatus(true)
        newCompliance.setSensitiveFields(["sensitiveField1"])
        newCompliance.setOrdinaryFields(["ordinaryField1"])
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟使用字段扩展属性"
        fsGrayReleaseBiz.isAllow(*_) >> true
        
        and: "模拟查询返回空结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> []
        
        and: "模拟获取字段扩展属性"
        describeLogicService.findDescribeExtra(user, { set -> set.contains(apiName) }) >> fieldExtras
        
        and: "模拟configService.findTenantConfig返回值，注意这个新对象类型不在apiNames列表中"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["LeadsObj", "PartnerObj", "CasesObj", "AccountObj"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        and: "模拟updateGdprPersonalField方法，这是processComplianceConfiguration调用的关键方法"
        gdprServiceImpl.updateGdprPersonalField(user, apiName, _, _, _) >> newCompliance
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        result.size() == 1
        result[0].apiName == apiName
        result[0].sensitiveFields == ["sensitiveField1"]
        result[0].ordinaryFields == ["ordinaryField1"]
        result[0].openStatus
    }
    
    def "test findGdprCompliance with no field compliance settings"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "EmptyObject"
        
        // 空的字段扩展属性集合
        def fieldExtras = [(apiName): []]
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟使用字段扩展属性"
        fsGrayReleaseBiz.isAllow(*_) >> true
        
        and: "模拟查询返回空结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> []
        
        and: "模拟获取字段扩展属性"
        describeLogicService.findDescribeExtra(user, { set -> set.contains(apiName) }) >> fieldExtras
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["LeadsObj", "PartnerObj", "CasesObj", "AccountObj"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        when:
        def result = gdprServiceImpl.findGdprCompliance(user, apiName)
        
        then:
        result.size() == 0
    }
    
    def "test migrateObjectFieldsToExtra with both sensitive and ordinary fields"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        
        // 创建一个合规配置，包含敏感和普通字段
        def compliance = new GdprCompliance()
        compliance.setApiName(apiName)
        compliance.setSensitiveFields(["sensitiveField1", "sensitiveField2"])
        compliance.setOrdinaryFields(["ordinaryField1", "ordinaryField2"])
        
        // 模拟describeLogicService.findDescribeExtra返回一个空Map，以避免NPE
        describeLogicService.findDescribeExtra(_, _) >> [:]
        
        when:
        gdprServiceImpl.migrateObjectFieldsToExtra(user, compliance)
        
        then:
        1 * describeLogicService.upsertObjectFieldExtra(_, _, _)
        noExceptionThrown()
    }
    
    def "test migrateObjectFieldsToExtra with empty fields"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        
        // 创建一个没有字段的合规配置
        def compliance = new GdprCompliance()
        compliance.setApiName(apiName)
        compliance.setSensitiveFields([])
        compliance.setOrdinaryFields([])
        
        when:
        gdprServiceImpl.migrateObjectFieldsToExtra(user, compliance)
        
        then:
        // 验证没有调用upsertObjectFieldExtra方法
        0 * describeLogicService.upsertObjectFieldExtra(_, _, _)
        noExceptionThrown()
    }
    
    def "test findGdprComplianceListByApiNames success case"() {
        given:
        def user = User.systemUser("74255")
        def apiNames = ["AccountObj", "LeadsObj"]
        
        // 创建测试数据
        def compliance1 = new GdprCompliance()
        compliance1.setApiName("AccountObj")
        compliance1.setOpenStatus(true)
        compliance1.setDeleted(false)
        
        def compliance2 = new GdprCompliance()
        compliance2.setApiName("LeadsObj")
        compliance2.setOpenStatus(true)
        compliance2.setDeleted(false)
        
        // 模拟查询方法返回值
        def expectedResult = [compliance1, compliance2]
        
        and: "模拟repository.findBy方法返回结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> expectedResult
        
        when:
        def result = gdprServiceImpl.findGdprComplianceListByApiNames(user, apiNames)
        
        then:
        // 验证调用次数和结果
        result == expectedResult
        result.size() == 2
        result*.apiName.containsAll(["AccountObj", "LeadsObj"])
        result.every { it.openStatus }
    }
    
    def "test findGdprComplianceListByApiNames with empty apiNames"() {
        given:
        def user = User.systemUser("74255")
        def apiNames = []
        
        and: "模拟repository.findBy返回空列表"
        def emptyList = []
        complianceRepository.findBy(_, _, GdprCompliance.class) >> emptyList
        
        when:
        def result = gdprServiceImpl.findGdprComplianceListByApiNames(user, apiNames)
        
        then:
        // 验证调用次数和结果
        result == emptyList
        result.isEmpty()
    }
    
    def "test needFilterGdprFields when compliance is not enabled"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        def operation = "export"
        
        and: "模拟不启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> false
        
        when:
        def result = gdprServiceImpl.needFilterGdprFields(user, apiName, operation)
        
        then:
        0 * complianceRepository.findBy(_, _, _)
        0 * describeLogicService.findDescribeExtra(_, _)
        result.isEmpty()
    }
    
    def "test needFilterGdprFields with batch export operation and sensitive fields"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        def operation = "Export"  // 批量导出操作
        
        // 创建合规配置
        def compliance = new GdprCompliance()
        compliance.setApiName(apiName)
        compliance.setOpenStatus(true)
        compliance.setSensitiveFields(["field1", "field2"])
        compliance.setOrdinaryFields(["field3", "field4"])
        compliance.setForbidExport("sensitive")  // 禁止导出敏感字段
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟查询返回结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> [compliance]
        
        and: "模拟未使用字段扩展属性"
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["AccountObj"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        when:
        def result = gdprServiceImpl.needFilterGdprFields(user, apiName, operation)
        
        then:
        result.size() == 2
        result.containsAll(["field1", "field2"])
    }
    
    def "test needFilterGdprFields with batch export operation and both field types"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        def operation = "Export"  // 批量导出操作
        
        // 创建合规配置
        def compliance = new GdprCompliance()
        compliance.setApiName(apiName)
        compliance.setOpenStatus(true)
        compliance.setSensitiveFields(["field1", "field2"])
        compliance.setOrdinaryFields(["field3", "field4"])
        compliance.setForbidExport(GdprCompliance.ORDINARY_AND_SENSITIVE)  // 禁止导出敏感和普通字段
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟查询返回结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> [compliance]
        
        and: "模拟未使用字段扩展属性"
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["AccountObj"], "openStatus": true, "periodType": "day", "forbidExport": "sensitive_and_ordinary", "forbidOpenApi": "", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        

        when:
        def result = gdprServiceImpl.needFilterGdprFields(user, apiName, operation)
        
        then:
        result.size() == 4
        result.containsAll(["field1", "field2", "field3", "field4"])
    }
    
    def "test needFilterGdprFields with openapi operation"() {
        given:
        def user = User.systemUser("74255")
        def apiName = "AccountObj"
        def operation = "OpenAPI"  // OpenAPI操作
        
        // 创建合规配置
        def compliance = new GdprCompliance()
        compliance.setApiName(apiName)
        compliance.setOpenStatus(true)
        compliance.setSensitiveFields(["field1", "field2"])
        compliance.setOrdinaryFields(["field3", "field4"])
        compliance.setForbidOpenApi("sensitive")  // 禁止OpenAPI访问敏感字段
        
        and: "模拟启用合规性设置"
        switchCacheService.findSwitchStatus(*_) >> true
        
        and: "模拟查询返回结果"
        complianceRepository.findBy(_, _, GdprCompliance.class) >> [compliance]
        
        and: "模拟未使用字段扩展属性"
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        and: "模拟configService.findTenantConfig返回值"
        def gdprComplianceJson = '''{"period": 1, "deleted": false, "apiNames": ["AccountObj"], "openStatus": true, "periodType": "day", "forbidExport": "", "forbidOpenApi": "sensitive", "tenantOpenStatus": true, "unusableOperation": []}'''
        configService.findTenantConfig(user, "gdpr_compliance") >> gdprComplianceJson
        
        when:
        def result = gdprServiceImpl.needFilterGdprFields(user, apiName, operation)
        
        then:
        result.size() == 2
        result.containsAll(["field1", "field2"])
    }
}
