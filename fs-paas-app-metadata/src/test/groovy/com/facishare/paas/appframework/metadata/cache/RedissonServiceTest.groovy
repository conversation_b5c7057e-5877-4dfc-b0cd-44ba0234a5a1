package com.facishare.paas.appframework.metadata.cache

import com.facishare.paas.appframework.core.exception.ValidateException
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import org.redisson.RedissonLock
import org.redisson.api.RedissonClient
import spock.lang.Specification

import java.util.concurrent.TimeUnit

/**
 * create by z<PERSON><PERSON> on 2020/12/10
 */
//@ContextConfiguration(value = "classpath:applicationContext.xml")
class RedissonServiceTest extends Specification {

    RedissonService redissonService
    RedissonClient redissonClient = Mock(RedissonClient)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    def setup() {
        redissonService = new RedissonServiceImpl("redissonClient": redissonClient)
    }

    def "test tryLock" () {
        given:
        def rLock = Mock(RedissonLock)
        when:
        redissonClient.getLock(_) >> rLock
        rLock.tryLock(*_) >> true
        def result = redissonService.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, "key", "test", 910000)
        then:
        result == rLock
    }
    def "test tryLock throw exception" () {
        given:
        def rLock = Mock(RedissonLock)
        when:
        redissonClient.getLock(_) >> rLock
        rLock.tryLock(*_) >> false
        def result = redissonService.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, "key", "test", 910000)
        then:
        thrown(ValidateException)
    }

    /*def "GetLock"() {
        given:
        final String key = "key"
        def lock = redissonService.getLock(key)
        println "0-thread:" + Thread.currentThread().getId() + ",name:" + Thread.currentThread().getName()
        def tryLock = lock.tryLock(5, 10, TimeUnit.SECONDS)
        println "0-tryLock : " + tryLock
        when:

        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("test-%d").setDaemon(false).build();
        ExecutorService threadPool = Executors.newScheduledThreadPool(3, workerFactory)
        threadPool.submit(new Runnable() {
            @Override
            void run() {
                def lock1 = redissonService.getLock(key)
                try {
                    println "1-thread:" + Thread.currentThread().getId() + ",name:" + Thread.currentThread().getName()
                    def tryLock1 = lock1.tryLock(2, 10, TimeUnit.SECONDS)
                    println "1-tryLock : " + tryLock1
                    sleep(1300)
                } finally {
                    println "1-unlock"
                    try {
                        if (lock1.isHeldByCurrentThread()) {
                            lock1.unlock()
                        }
                    } catch (Exception e) {
                        log.error("unlock failed", e)
                    }
                    println "1-unlock SUCCESS"
                }
            }
        })
        threadPool.submit(new Runnable() {
            @Override
            void run() {
                def lock2 = redissonService.getLock(key)
                try {
                    println "2-thread:" + Thread.currentThread().getId() + ",name:" + Thread.currentThread().getName()
                    def tryLock2 = lock2.tryLock(2, 10, TimeUnit.SECONDS)
                    println "2-tryLock : " + tryLock2
                    sleep(1300)
                } finally {
                    println "2-unlock"
                    try {
                        if (lock2.isHeldByCurrentThread()) {
                            lock2.unlock()
                        }
                    } catch (Exception e) {
                        log.error("unlock failed", e)
                    }
                    println "2-unlock SUCCESS"
                }
            }
        })
        sleep(7000)
        then:
        lock.unlock()
    }*/
}
