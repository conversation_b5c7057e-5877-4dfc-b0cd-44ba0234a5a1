package com.facishare.paas.appframework.metadata.importobject.dataconvert

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import spock.lang.Specification

class SelectManyImportFieldDataConverterTest extends Specification {
    def "test select many option valid"() {
        given:
        def str = "乐蔓莓果啤酒20L K"
        when:
        String[] array = str.split("\\|");
        then:
        array.length == 1
        array[0] == "乐蔓莓果啤酒20L K"
    }

    def "test"() {
        given:
        def converter = new SelectManyImportFieldDataConverter()
        def data = new ObjectData(["a":"乐蔓莓果啤酒20L K"])
        def field = new SelectManyFieldDescribe()
        field.setApiName("a")
        def option = new SelectOption()
        option.setLabel("乐蔓莓果啤酒20L K")
        option.setValue("111")
        field.addSelectOption(option)
        def user = new User("1", "-10000")
        when:
        converter.convertFieldData(data, field, user)
        then:
        1==1
    }
}
