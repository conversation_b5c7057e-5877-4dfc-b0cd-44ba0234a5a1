package com.facishare.paas.appframework.metadata.bi

import cn.hutool.core.exceptions.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.dto.CrossFilterSupportedObjects
import com.facishare.paas.appframework.metadata.dto.CrossObjectFilter
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * BIServiceImpl单元测试
 * 
 * <AUTHOR>
 */
class BIServiceImplTest extends Specification {

    BIServiceImpl biService
    BIRestServiceProxy biRestServiceProxy = Mock(BIRestServiceProxy)
    BICRMRestServiceProxy biCrmRestServiceProxy = Mock(BICRMRestServiceProxy)
    def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
    
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        biService = new BIServiceImpl(
            biRestServiceProxy: biRestServiceProxy,
            biCrmRestServiceProxy: biCrmRestServiceProxy
        )
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
    }
    
    @Unroll
    def "测试getObjRelationResult方法正常场景"() {
        given:
        def user = User.systemUser(tenantId)
        def arg = CrossObjectFilter.ObjRelationArg.builder()
            .businessObjects(businessObjects)
            .build()
        def headers = RestUtils.buildHeaders(user)
        def expectedResult = CrossObjectFilter.ObjRelationResult.builder()
            .objectsAndFields(objectsAndFields)
            .build()
            
        when:
        def result = biService.getObjRelationResult(user, arg)
        
        then:
        1 * biRestServiceProxy.getObjRelationResult(headers, arg) >> expectedResult
        result == expectedResult
        
        where:
        tenantId | businessObjects                                | objectsAndFields
        "74255"  | [["objectApiName": "AccountObj", "id": "123"]] | [new CrossObjectFilter.ObjectAndField(fieldInfoList: [new CrossObjectFilter.FieldInfo(apiName: "name", crmObjName: "AccountObj")], nodeName: "账户", crmObjName: "AccountObj", nodeId: "1")]
        "74256"  | []                                             | []
    }
    
    def "测试getObjRelationResult方法参数为空场景"() {
        when:
        biService.getObjRelationResult(null, null)
        
        then:
        thrown(ValidateException)
    }
    
    def "测试getObjRelationResult方法返回null场景"() {
        given:
        def user = User.systemUser("74255")
        def arg = CrossObjectFilter.ObjRelationArg.builder().build()
        def headers = RestUtils.buildHeaders(user)
        
        when:
        def result = biService.getObjRelationResult(user, arg)
        
        then:
        1 * biRestServiceProxy.getObjRelationResult(headers, arg) >> null
        result.objectsAndFields == []
    }
    
    def "测试getObjRelationResult方法异常场景"() {
        given:
        def user = User.systemUser("74255")
        def arg = CrossObjectFilter.ObjRelationArg.builder().build()
        def headers = RestUtils.buildHeaders(user)
        
        when:
        def result = biService.getObjRelationResult(user, arg)
        
        then:
        1 * biRestServiceProxy.getObjRelationResult(headers, arg) >> { throw new RuntimeException("测试异常") }
        result.objectsAndFields == []
    }
    
    @Unroll
    def "测试queryReportData方法正常场景"() {
        given:
        def user = User.systemUser(tenantId)
        def arg = CrossObjectFilter.QueryReportArg.builder()
            .businessObjects(businessObjects)
            .build()
        def headers = RestUtils.buildHeaders(user)
        def expectedResult = CrossObjectFilter.QueryReportResult.builder()
            .id(ids)
            .totalNum(totalNum)
            .build()
            
        when:
        def result = biService.queryReportData(user, arg)
        
        then:
        1 * biRestServiceProxy.queryReportData(headers, arg) >> expectedResult
        result == expectedResult
        
        where:
        tenantId | businessObjects                                | ids       | totalNum
        "74255"  | [["objectApiName": "AccountObj", "id": "123"]] | ["1", "2"] | 2
        "74256"  | []                                             | []        | 0
    }
    
    def "测试queryReportData方法参数为空场景"() {
        when:
        biService.queryReportData(null, null)
        
        then:
        thrown(ValidateException)
    }
    
    def "测试queryReportData方法返回null场景"() {
        given:
        def user = User.systemUser("74255")
        def arg = CrossObjectFilter.QueryReportArg.builder().build()
        def headers = RestUtils.buildHeaders(user)
        
        when:
        def result = biService.queryReportData(user, arg)
        
        then:
        1 * biRestServiceProxy.queryReportData(headers, arg) >> null
        result.id == []
        result.totalNum == 0
    }
    
    def "测试queryReportData方法异常场景"() {
        given:
        def user = User.systemUser("74255")
        def arg = CrossObjectFilter.QueryReportArg.builder().build()
        def headers = RestUtils.buildHeaders(user)
        
        when:
        def result = biService.queryReportData(user, arg)
        
        then:
        1 * biRestServiceProxy.queryReportData(headers, arg) >> { throw new RuntimeException("测试异常") }
        result.id == []
        result.totalNum == 0
    }
    
    @Unroll
    def "测试getCrossFilterSupportedObjects方法正常场景"() {
        given:
        def user = User.systemUser(tenantId)
        def crmObjNames = ["AccountObj", "ContactObj"]
        def headers = RestUtils.buildHeaders(user)
        def arg = CrossFilterSupportedObjects.Arg.builder()
            .crmObjNames(crmObjNames)
            .build()
        def data = CrossFilterSupportedObjects.Result.builder()
            .crmObjNames(supportedObjNames)
            .build()
        def restResult = CrossFilterSupportedObjects.RestResult.builder()
            .code("200")
            .data(data)
            .build()
            
        when:
        def result = biService.getCrossFilterSupportedObjects(user, crmObjNames)
        
        then:
        1 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(headers, arg) >> restResult
        result == data
        
        where:
        tenantId | supportedObjNames
        "74255"  | ["AccountObj"]
        "74256"  | ["AccountObj", "ContactObj"]
    }
    
    def "测试getCrossFilterSupportedObjects方法参数为空场景"() {
        when:
        biService.getCrossFilterSupportedObjects(null, null)
        
        then:
        thrown(ValidateException)
    }
    
    def "测试getCrossFilterSupportedObjects方法参数crmObjNames为空场景"() {
        given:
        def user = User.systemUser("74255")
        
        when:
        biService.getCrossFilterSupportedObjects(user, [])
        
        then:
        thrown(ValidateException)
    }
    
    def "测试getCrossFilterSupportedObjects方法返回null场景"() {
        given:
        def user = User.systemUser("74255")
        def crmObjNames = ["AccountObj"]
        def headers = RestUtils.buildHeaders(user)
        def arg = CrossFilterSupportedObjects.Arg.builder()
            .crmObjNames(crmObjNames)
            .build()
        
        when:
        def result = biService.getCrossFilterSupportedObjects(user, crmObjNames)
        
        then:
        1 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(headers, arg) >> null
        result.crmObjNames == []
    }
    
    def "测试getCrossFilterSupportedObjects方法返回失败场景"() {
        given:
        def user = User.systemUser("74255")
        def crmObjNames = ["AccountObj"]
        def headers = RestUtils.buildHeaders(user)
        def arg = CrossFilterSupportedObjects.Arg.builder()
            .crmObjNames(crmObjNames)
            .build()
        def restResult = CrossFilterSupportedObjects.RestResult.builder()
            .code("500")
            .build()
        
        when:
        def result = biService.getCrossFilterSupportedObjects(user, crmObjNames)
        
        then:
        1 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(headers, arg) >> restResult
        result.crmObjNames == []
    }
    
    def "测试getCrossFilterSupportedObjects方法异常场景"() {
        given:
        def user = User.systemUser("74255")
        def crmObjNames = ["AccountObj"]
        def headers = RestUtils.buildHeaders(user)
        def arg = CrossFilterSupportedObjects.Arg.builder()
            .crmObjNames(crmObjNames)
            .build()
        
        when:
        def result = biService.getCrossFilterSupportedObjects(user, crmObjNames)
        
        then:
        1 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(headers, arg) >> { throw new RuntimeException("测试异常") }
        result.crmObjNames == []
    }
    
    @Unroll
    def "测试supportCrossObjectFilter方法正常场景"() {
        given:
        def user = User.systemUser(tenantId)
        def objectApiName = apiName
        
        when:
        def result = biService.supportCrossObjectFilter(user, objectApiName)
        
        then:
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_CROSS_OBJECT_GRAY, tenantId) >> grayEnabled
        
        if (grayEnabled && !apiName.endsWith("__c") && needCallCrossFilter) {
            1 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(_, _) >> {
                CrossFilterSupportedObjects.RestResult.builder()
                    .code("200")
                    .data(CrossFilterSupportedObjects.Result.builder()
                        .crmObjNames(supportedObjNames)
                        .build())
                    .build()
            }
        } else {
            0 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(_, _)
        }
        result == expected
        
        where:
        tenantId | apiName      | grayEnabled | needCallCrossFilter | supportedObjNames | expected
        "74255"  | "AccountObj" | false       | false               | []                | false
        "74256"  | "custom__c"  | true        | false               | []                | true
        "74257"  | "AccountObj" | true        | true                | ["AccountObj"]    | true
        "74258"  | "AccountObj" | true        | true                | ["ContactObj"]    | false
    }
    
    def "测试supportCrossObjectFilter方法参数为空场景"() {
        when:
        def result = biService.supportCrossObjectFilter(null, null)
        
        then:
        !result
    }
    
    def "测试supportCrossObjectFilter方法异常场景"() {
        given:
        def user = User.systemUser("74255")
        def objectApiName = "AccountObj"
        
        when:
        def result = biService.supportCrossObjectFilter(user, objectApiName)
        
        then:
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.SUPPORT_CROSS_OBJECT_GRAY, "74255") >> true
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.isCustomObject(objectApiName) >> false
        1 * biCrmRestServiceProxy.getCrossFilterSupportedObjects(_, _) >> { throw new RuntimeException("测试异常") }
        !result
    }
}