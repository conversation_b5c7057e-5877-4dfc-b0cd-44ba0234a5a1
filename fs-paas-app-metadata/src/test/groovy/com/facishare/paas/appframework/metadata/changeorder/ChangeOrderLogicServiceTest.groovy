package com.facishare.paas.appframework.metadata.changeorder

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.flow.ApprovalFlowService
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.LayoutExt
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.metadata.cache.RedissonService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.dao.pg.entity.metadata.DataExtra
import com.facishare.paas.metadata.dao.pg.mapper.metadata.DataExtraMapper
import com.facishare.paas.metadata.impl.ui.layout.FieldSection
import com.facishare.paas.metadata.impl.ui.layout.FormField
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringContextUtil
import com.fasterxml.jackson.core.type.TypeReference
import org.powermock.reflect.Whitebox
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Specification

import java.util.function.Function

class ChangeOrderLogicServiceTest extends Specification {

    ChangeOrderLogicService changeOrderLogicService
    DescribeLogicService describeLogicService = Mock(DescribeLogicService)
    ConfigService configService = Mock(ConfigService)
    LicenseService licenseService = Mock(LicenseService)
    RedissonService redissonService = Mock(RedissonService)
    DataExtraMapper dataExtraMapper = Mock(DataExtraMapper)
    ApprovalFlowService approvalFlowService = Mock(ApprovalFlowService)
    String describeJson = "{\"tenant_id\":\"74255\",\"original_describe_api_name\":\"object_au47D__c\",\"package\":\"CRM\",\"is_active\":true,\"last_modified_time\":1709799948117,\"create_time\":*************,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"查重工具\",\"created_by\":\"1000\",\"version\":95,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_au47D__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"short_name\":\"blJ\",\"_id\":\"641851c90a1cf80001d3258a\",\"fields\":{\"tenant_id\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"lock_rule\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1679315401173,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"field_num\":1,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"_id\":\"641851c90a1cf80001d32581\",\"is_single\":false,\"label_r\":\"锁定规则\",\"is_index_field\":false,\"index_name\":\"s_1\",\"status\":\"new\"},\"data_own_organization\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属组织\",\"index_name\":\"a_1\",\"is_index\":true,\"is_active\":true,\"create_time\":1679315401412,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属组织\",\"is_need_convert\":false,\"api_name\":\"data_own_organization\",\"_id\":\"641851c90a1cf80001d32589\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"},\"version_number\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709799573473,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":true,\"label\":\"版本号\",\"type\":\"text\",\"field_num\":13,\"is_required\":false,\"api_name\":\"version_number\",\"define_type\":\"package\",\"_id\":\"65e97895a938e8ddcc426a60\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_0\",\"max_length\":256,\"status\":\"released\",\"description\":\"\"},\"origin_source\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"数据来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"origin_source\",\"options\":[{\"label\":\"数据同步\",\"value\":\"0\"}],\"define_type\":\"system\",\"is_extend\":false,\"index_name\":\"s_os\",\"config\":{\"display\":0},\"status\":\"released\",\"description\":\"\"},\"lock_user\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1679315401174,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"加锁人\",\"is_unique\":false,\"label\":\"加锁人\",\"type\":\"employee\",\"field_num\":4,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"_id\":\"641851c90a1cf80001d32583\",\"is_single\":true,\"label_r\":\"加锁人\",\"is_index_field\":false,\"index_name\":\"a_2\",\"status\":\"new\"},\"changed_status\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709799573473,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"default_value\":\"normal\",\"label\":\"变更状态\",\"type\":\"select_one\",\"field_num\":17,\"is_required\":false,\"api_name\":\"changed_status\",\"options\":[{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"}],\"define_type\":\"package\",\"_id\":\"65e97895a938e8ddcc426a64\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"s_0\",\"config\":{},\"status\":\"released\",\"description\":\"\"},\"mc_exchange_rate\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":8,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"汇率\",\"index_name\":\"d_1\",\"max_length\":18,\"is_index\":true,\"is_active\":true,\"create_time\":1709554066250,\"is_encrypted\":false,\"length\":10,\"label\":\"汇率\",\"field_num\":6,\"api_name\":\"mc_exchange_rate\",\"_id\":\"65e5b992d78180000116b83c\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"is_deleted\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"create_time\":*************,\"is_unique\":false,\"description\":\"is_deleted\",\"default_value\":false,\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"options\":[],\"define_type\":\"system\",\"index_name\":\"is_del\",\"status\":\"released\"},\"life_status_before_invalid\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1679315401174,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"label\":\"作废前生命状态\",\"type\":\"text\",\"field_num\":7,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"_id\":\"641851c90a1cf80001d32584\",\"is_single\":false,\"label_r\":\"作废前生命状态\",\"is_index_field\":false,\"index_name\":\"t_1\",\"max_length\":256,\"status\":\"new\"},\"object_describe_api_name\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"index_name\":\"api_name\",\"max_length\":200,\"status\":\"released\"},\"field_9d59s__c\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_many\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"options\":[{\"label\":\"示例选项\",\"value\":\"option1\"},{\"not_usable\":true,\"label\":\"其他\",\"value\":\"other\"}],\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"a_3\",\"is_index\":true,\"is_active\":true,\"create_time\":1679482043932,\"is_encrypted\":false,\"default_value\":[],\"label\":\"多选\",\"field_num\":11,\"api_name\":\"field_9d59s__c\",\"_id\":\"641adcbc15761500010e32b9\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner_department\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"label_r\":\"负责人主属部门\",\"index_name\":\"owner_dept\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1679315401399,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"_id\":\"641851c90a1cf80001d32578\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"out_owner\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"o_owner\",\"config\":{\"display\":1},\"status\":\"released\",\"description\":\"\"},\"mc_functional_currency\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709554066250,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"本位币\",\"type\":\"select_one\",\"field_num\":9,\"is_required\":false,\"api_name\":\"mc_functional_currency\",\"options\":[{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"65e5b992d78180000116b83d\",\"is_single\":false,\"label_r\":\"本位币\",\"is_index_field\":false,\"index_name\":\"s_5\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"employee\",\"is_required\":true,\"wheres\":[],\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"负责人\",\"index_name\":\"owner\",\"is_index\":true,\"is_active\":true,\"create_time\":1679315401398,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人\",\"is_need_convert\":false,\"api_name\":\"owner\",\"_id\":\"641851c90a1cf80001d32577\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"field_9r1h9__c\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"enable_clone\":true,\"optional_type\":\"department\",\"define_type\":\"custom\",\"is_single\":true,\"index_name\":\"a_0\",\"is_index\":true,\"is_active\":true,\"create_time\":1703756290420,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"部门\",\"field_num\":12,\"api_name\":\"field_9r1h9__c\",\"_id\":\"658d42038cee4e0001a62a3a\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"changed_time\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709799573473,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"变更时间\",\"type\":\"date_time\",\"time_zone\":\"GMT+8\",\"field_num\":14,\"is_required\":false,\"api_name\":\"changed_time\",\"define_type\":\"package\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"_id\":\"65e97895a938e8ddcc426a61\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"l_0\",\"status\":\"released\",\"description\":\"\"},\"lock_status\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未锁定\",\"value\":\"0\"},{\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"锁定状态\",\"index_name\":\"s_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1679315401174,\"is_encrypted\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"field_num\":2,\"is_need_convert\":false,\"api_name\":\"lock_status\",\"_id\":\"641851c90a1cf80001d32582\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"package\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"package\",\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"index_name\":\"pkg\",\"max_length\":200,\"status\":\"released\"},\"last_modified_time\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"last_modified_time\",\"label\":\"最后修改时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"md_time\",\"status\":\"released\"},\"changed_reason\":{\"expression_type\":\"json\",\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709799573473,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"label\":\"变更原因\",\"type\":\"long_text\",\"field_num\":15,\"is_required\":false,\"api_name\":\"changed_reason\",\"define_type\":\"package\",\"_id\":\"65e97895a938e8ddcc426a62\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_4\",\"max_length\":100000,\"status\":\"released\",\"description\":\"\"},\"create_time\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"create_time\",\"label\":\"创建时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"crt_time\",\"status\":\"released\"},\"life_status\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未生效\",\"value\":\"ineffective\"},{\"label\":\"审核中\",\"value\":\"under_review\"},{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"},{\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"生命状态\",\"index_name\":\"s_3\",\"is_index\":true,\"is_active\":true,\"create_time\":1679315401406,\"is_encrypted\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"field_num\":3,\"is_need_convert\":false,\"api_name\":\"life_status\",\"_id\":\"641851c90a1cf80001d3257f\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"last_modified_by\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"md_by\",\"status\":\"released\",\"description\":\"\"},\"field_gW3w2__c\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_3\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1679315422034,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单行文本\",\"field_num\":10,\"api_name\":\"field_gW3w2__c\",\"_id\":\"641851de0a1cf80001d32636\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"out_tenant_id\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"out_tenant_id\",\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"index_name\":\"o_ei\",\"config\":{\"display\":0},\"max_length\":200,\"status\":\"released\"},\"mc_currency\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709554066250,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"币种\",\"type\":\"select_one\",\"field_num\":5,\"is_required\":false,\"api_name\":\"mc_currency\",\"options\":[{\"not_usable\":false,\"label\":\"BWP - 博茨瓦纳普拉\",\"value\":\"BWP\"},{\"not_usable\":false,\"label\":\"ALL - 阿尔巴尼亚列克\",\"value\":\"ALL\"},{\"not_usable\":false,\"label\":\"AOA - 安哥拉宽扎\",\"value\":\"AOA\"},{\"not_usable\":false,\"label\":\"ANG - 荷属安地列斯盾\",\"value\":\"ANG\"},{\"not_usable\":false,\"label\":\"ARS - 阿根廷比索\",\"value\":\"ARS\"},{\"not_usable\":false,\"label\":\"AMD - 亚美尼亚打兰\",\"value\":\"AMD\"},{\"not_usable\":false,\"label\":\"AUD - Australian Dollar\",\"value\":\"AUD\"},{\"not_usable\":false,\"label\":\"AFN - Afghanistan Afghani (New)\",\"value\":\"AFN\"},{\"not_usable\":false,\"label\":\"AZN - 阿塞拜疆马纳特\",\"value\":\"AZN\"},{\"not_usable\":false,\"label\":\"USD - U.S. Dollar\",\"value\":\"USD\"},{\"not_usable\":false,\"label\":\"BAM - 自由兑换马克\",\"value\":\"BAM\"},{\"not_usable\":false,\"label\":\"BBD - 巴巴多斯元\",\"value\":\"BBD\"},{\"not_usable\":false,\"label\":\"BDT - 孟加拉国塔卡\",\"value\":\"BDT\"},{\"not_usable\":false,\"label\":\"BGN - Bulgarian Lev\",\"value\":\"BGN\"},{\"not_usable\":false,\"label\":\"BHD - Bahraini Dinar\",\"value\":\"BHD\"},{\"not_usable\":false,\"label\":\"BND - Brunei Dollar\",\"value\":\"BND\"},{\"not_usable\":false,\"label\":\"BMD - 百慕大元\",\"value\":\"BMD\"},{\"not_usable\":false,\"label\":\"BIF - 布隆迪法郎\",\"value\":\"BIF\"},{\"not_usable\":false,\"label\":\"BRL - 巴西币\",\"value\":\"BRL\"},{\"not_usable\":false,\"label\":\"BOB - 玻利维亚的玻利维亚诺\",\"value\":\"BOB\"},{\"not_usable\":false,\"label\":\"BYN - 白俄罗斯卢布\",\"value\":\"BYN\"},{\"not_usable\":false,\"label\":\"BZD - 伯利兹元\",\"value\":\"BZD\"},{\"not_usable\":false,\"label\":\"BOV - 玻利维亚姆夫多尔\",\"value\":\"BOV\"},{\"not_usable\":false,\"label\":\"BRB - 巴西克鲁塞罗（旧）\",\"value\":\"BRB\"},{\"not_usable\":false,\"label\":\"BSD - 巴哈马元\",\"value\":\"BSD\"},{\"not_usable\":false,\"label\":\"自定义QWE\",\"value\":\"qwe\"},{\"not_usable\":false,\"label\":\"自定义zas\",\"value\":\"zas\"},{\"not_usable\":false,\"label\":\"翻译ASD\",\"value\":\"asd\"},{\"not_usable\":false,\"label\":\"ada名称\",\"value\":\"ada\"},{\"not_usable\":false,\"label\":\"BTN - 不丹卢比\",\"value\":\"BTN\"},{\"not_usable\":false,\"label\":\"AWG - 阿鲁巴岛弗罗林\",\"value\":\"AWG\"},{\"not_usable\":false,\"label\":\"AED - UAE Dirham\",\"value\":\"AED\"},{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"65e5b992d78180000116b83b\",\"is_single\":false,\"label_r\":\"币种\",\"is_index_field\":false,\"index_name\":\"s_4\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"version\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"create_time\":*************,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"index_name\":\"version\",\"round_mode\":4,\"status\":\"released\"},\"created_by\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"crt_by\",\"status\":\"released\",\"description\":\"\"},\"relevant_team\":{\"describe_api_name\":\"object_au47D__c\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":1679315401407,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"_id\":\"641851c90a1cf80001d32580\",\"is_single\":false,\"label_r\":\"相关团队\",\"is_index_field\":false,\"index_name\":\"a_team\",\"help_text\":\"相关团队\",\"status\":\"new\"},\"record_type\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1679315401405,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"record_type\",\"is_unique\":false,\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"}],\"define_type\":\"package\",\"_id\":\"641851c90a1cf80001d3257e\",\"is_single\":false,\"label_r\":\"业务类型\",\"is_index_field\":false,\"index_name\":\"r_type\",\"config\":{},\"help_text\":\"\",\"status\":\"released\"},\"data_own_department\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属部门\",\"index_name\":\"data_owner_dept_id\",\"is_index\":true,\"is_active\":true,\"create_time\":1679315401400,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属部门\",\"is_need_convert\":false,\"api_name\":\"data_own_department\",\"_id\":\"641851c90a1cf80001d32579\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"},\"changed_by\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709799573473,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"变更人\",\"type\":\"employee\",\"field_num\":16,\"is_required\":false,\"api_name\":\"changed_by\",\"define_type\":\"package\",\"_id\":\"65e97895a938e8ddcc426a63\",\"is_index_field\":false,\"is_single\":true,\"index_name\":\"a_4\",\"status\":\"released\",\"description\":\"\"},\"name\":{\"describe_api_name\":\"object_au47D__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"name\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"主属性\",\"index_name\":\"name\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1679315401830,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"主属性\",\"api_name\":\"name\",\"_id\":\"641851c90a1cf80001d32576\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate_version\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709554066250,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"label\":\"汇率版本\",\"type\":\"text\",\"field_num\":8,\"is_required\":false,\"api_name\":\"mc_exchange_rate_version\",\"define_type\":\"package\",\"_id\":\"65e5b992d78180000116b83e\",\"is_single\":false,\"label_r\":\"汇率版本\",\"is_index_field\":false,\"index_name\":\"t_2\",\"max_length\":256,\"help_text\":\"\",\"status\":\"new\"},\"_id\":{\"describe_api_name\":\"object_au47D__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"index_name\":\"_id\",\"max_length\":200,\"status\":\"released\"}},\"release_version\":\"6.4\",\"actions\":{}}\n"

    String detailDescribeJson = "{\"tenant_id\":\"74255\",\"package\":\"CRM\",\"original_describe_api_name\":\"AccountObj\",\"is_active\":true,\"last_modified_time\":*************,\"create_time\":*************,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"查重工具从\",\"created_by\":\"1000\",\"version\":7,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_f59kS__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"short_name\":\"Uo1\",\"_id\":\"65e98ec57b102d0001f98860\",\"fields\":{\"field_211rf__c\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"type\":\"long_text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"t_5\",\"max_length\":2000,\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"min_length\":0,\"default_value\":\"\",\"label\":\"多行文本\",\"field_num\":12,\"api_name\":\"field_211rf__c\",\"_id\":\"65e98ef67b102d0001f98bc8\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"tenant_id\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"lock_rule\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253288,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"field_num\":1,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98857\",\"is_single\":false,\"label_r\":\"锁定规则\",\"is_index_field\":false,\"index_name\":\"s_0\",\"status\":\"new\"},\"data_own_organization\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253335,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"归属组织\",\"type\":\"department\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"data_own_organization\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885f\",\"is_single\":true,\"label_r\":\"归属组织\",\"is_index_field\":false,\"index_name\":\"a_0\",\"status\":\"released\",\"description\":\"\"},\"field_6f23A__c\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_4\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1709805298902,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单行文本\",\"field_num\":11,\"api_name\":\"field_6f23A__c\",\"_id\":\"65e98ef37b102d0001f98a8b\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"version_number\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805288592,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":true,\"label\":\"版本号\",\"type\":\"text\",\"is_required\":false,\"api_name\":\"version_number\",\"define_type\":\"package\",\"_id\":\"65e98ee87b102d0001f989ee\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_3\",\"max_length\":256,\"status\":\"released\",\"description\":\"\"},\"origin_source\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"数据来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"origin_source\",\"options\":[{\"label\":\"数据同步\",\"value\":\"0\"}],\"define_type\":\"system\",\"is_extend\":false,\"index_name\":\"s_os\",\"config\":{\"display\":0},\"status\":\"released\",\"description\":\"\"},\"lock_user\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253289,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"加锁人\",\"is_unique\":false,\"label\":\"加锁人\",\"type\":\"employee\",\"field_num\":4,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98859\",\"is_single\":true,\"label_r\":\"加锁人\",\"is_index_field\":false,\"index_name\":\"a_1\",\"status\":\"new\"},\"changed_status\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805288593,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"default_value\":\"normal\",\"label\":\"变更状态\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"changed_status\",\"options\":[{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"}],\"define_type\":\"package\",\"_id\":\"65e98ee87b102d0001f989ef\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"s_5\",\"config\":{},\"status\":\"released\",\"description\":\"\"},\"mc_exchange_rate\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":8,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"汇率\",\"index_name\":\"d_0\",\"max_length\":18,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253332,\"is_encrypted\":false,\"length\":10,\"label\":\"汇率\",\"field_num\":6,\"api_name\":\"mc_exchange_rate\",\"_id\":\"65e98ec57b102d0001f9885c\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"field_3cdxe__c\":{\"describe_api_name\":\"object_f59kS__c\",\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"master_detail\",\"is_required\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"s_6\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805286476,\"is_encrypted\":false,\"label\":\"主从关系\",\"target_api_name\":\"object_au47D__c\",\"show_detail_button\":false,\"target_related_list_name\":\"target_related_list_L982t__c\",\"field_num\":10,\"target_related_list_label\":\"查重工具从\",\"api_name\":\"field_3cdxe__c\",\"is_create_when_master_create\":true,\"_id\":\"65e98ee87b102d0001f989ed\",\"is_index_field\":true,\"is_required_when_master_create\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"is_deleted\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"create_time\":*************,\"is_unique\":false,\"description\":\"is_deleted\",\"default_value\":false,\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"options\":[],\"define_type\":\"system\",\"index_name\":\"is_del\",\"status\":\"released\"},\"life_status_before_invalid\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253289,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"label\":\"作废前生命状态\",\"type\":\"text\",\"field_num\":7,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885a\",\"is_single\":false,\"label_r\":\"作废前生命状态\",\"is_index_field\":false,\"index_name\":\"t_0\",\"max_length\":256,\"status\":\"new\"},\"object_describe_api_name\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"index_name\":\"api_name\",\"max_length\":200,\"status\":\"released\"},\"owner_department\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"label_r\":\"负责人主属部门\",\"index_name\":\"owner_dept\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253322,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"_id\":\"65e98ec57b102d0001f9884e\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"out_owner\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"o_owner\",\"config\":{\"display\":1},\"status\":\"released\",\"description\":\"\"},\"mc_functional_currency\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253333,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"本位币\",\"type\":\"select_one\",\"field_num\":9,\"is_required\":false,\"api_name\":\"mc_functional_currency\",\"options\":[{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885d\",\"is_single\":false,\"label_r\":\"本位币\",\"is_index_field\":false,\"index_name\":\"s_4\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253321,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"default_value\":\"\",\"label\":\"负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":true,\"wheres\":[],\"api_name\":\"owner\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9884d\",\"is_single\":true,\"label_r\":\"负责人\",\"is_index_field\":false,\"index_name\":\"owner\",\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"changed_time\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805288593,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"变更时间\",\"type\":\"date_time\",\"time_zone\":\"GMT+8\",\"is_required\":false,\"api_name\":\"changed_time\",\"define_type\":\"package\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"_id\":\"65e98ee87b102d0001f989f0\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"l_0\",\"status\":\"released\",\"description\":\"\"},\"lock_status\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253289,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"type\":\"select_one\",\"field_num\":2,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_status\",\"options\":[{\"label\":\"未锁定\",\"value\":\"0\"},{\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98858\",\"is_single\":false,\"label_r\":\"锁定状态\",\"is_index_field\":false,\"index_name\":\"s_1\",\"config\":{},\"status\":\"new\"},\"package\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"package\",\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"index_name\":\"pkg\",\"max_length\":200,\"status\":\"released\"},\"last_modified_time\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"last_modified_time\",\"label\":\"最后修改时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"md_time\",\"status\":\"released\"},\"changed_reason\":{\"expression_type\":\"json\",\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805288593,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"label\":\"变更原因\",\"type\":\"long_text\",\"is_required\":false,\"api_name\":\"changed_reason\",\"define_type\":\"package\",\"_id\":\"65e98ee87b102d0001f989f1\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_2\",\"max_length\":100000,\"status\":\"released\",\"description\":\"\"},\"create_time\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"create_time\",\"label\":\"创建时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"index_name\":\"crt_time\",\"status\":\"released\"},\"life_status\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未生效\",\"value\":\"ineffective\"},{\"label\":\"审核中\",\"value\":\"under_review\"},{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"},{\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"生命状态\",\"index_name\":\"s_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253329,\"is_encrypted\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"field_num\":3,\"is_need_convert\":false,\"api_name\":\"life_status\",\"_id\":\"65e98ec57b102d0001f98855\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"last_modified_by\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"md_by\",\"status\":\"released\",\"description\":\"\"},\"out_tenant_id\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"out_tenant_id\",\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"index_name\":\"o_ei\",\"config\":{\"display\":0},\"max_length\":200,\"status\":\"released\"},\"mc_currency\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253331,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"币种\",\"type\":\"select_one\",\"field_num\":5,\"is_required\":false,\"api_name\":\"mc_currency\",\"options\":[{\"not_usable\":false,\"label\":\"BYR - 白俄罗斯卢布\",\"value\":\"BYR\"},{\"not_usable\":false,\"label\":\"AED - UAE Dirham\",\"value\":\"AED\"},{\"not_usable\":false,\"label\":\"BIF - 布隆迪法郎\",\"value\":\"BIF\"},{\"not_usable\":false,\"label\":\"BRL - 巴西币\",\"value\":\"BRL\"},{\"not_usable\":false,\"label\":\"BOB - 玻利维亚的玻利维亚诺\",\"value\":\"BOB\"},{\"not_usable\":false,\"label\":\"BYN - 白俄罗斯卢布\",\"value\":\"BYN\"},{\"not_usable\":false,\"label\":\"BZD - 伯利兹元\",\"value\":\"BZD\"},{\"not_usable\":false,\"label\":\"BOV - 玻利维亚姆夫多尔\",\"value\":\"BOV\"},{\"not_usable\":false,\"label\":\"BRB - 巴西克鲁塞罗（旧）\",\"value\":\"BRB\"},{\"not_usable\":false,\"label\":\"BSD - 巴哈马元\",\"value\":\"BSD\"},{\"not_usable\":false,\"label\":\"自定义QWE\",\"value\":\"qwe\"},{\"not_usable\":false,\"label\":\"自定义zas\",\"value\":\"zas\"},{\"not_usable\":false,\"label\":\"翻译ASD\",\"value\":\"asd\"},{\"not_usable\":false,\"label\":\"AWG - 阿鲁巴岛弗罗林\",\"value\":\"AWG\"},{\"not_usable\":false,\"label\":\"ada名称\",\"value\":\"ada\"},{\"not_usable\":false,\"label\":\"BTN - 不丹卢比\",\"value\":\"BTN\"},{\"not_usable\":false,\"label\":\"BGN - Bulgarian Lev\",\"value\":\"BGN\"},{\"not_usable\":false,\"label\":\"BHD - Bahraini Dinar\",\"value\":\"BHD\"},{\"not_usable\":false,\"label\":\"BND - Brunei Dollar\",\"value\":\"BND\"},{\"not_usable\":false,\"label\":\"BMD - 百慕大元\",\"value\":\"BMD\"},{\"not_usable\":false,\"label\":\"AOA - 安哥拉宽扎\",\"value\":\"AOA\"},{\"not_usable\":false,\"label\":\"ANG - 荷属安地列斯盾\",\"value\":\"ANG\"},{\"not_usable\":false,\"label\":\"ARS - 阿根廷比索\",\"value\":\"ARS\"},{\"not_usable\":false,\"label\":\"AMD - 亚美尼亚打兰\",\"value\":\"AMD\"},{\"not_usable\":false,\"label\":\"AUD - Australian Dollar\",\"value\":\"AUD\"},{\"not_usable\":false,\"label\":\"AFN - Afghanistan Afghani (New)\",\"value\":\"AFN\"},{\"not_usable\":false,\"label\":\"AZN - 阿塞拜疆马纳特\",\"value\":\"AZN\"},{\"not_usable\":false,\"label\":\"USD - U.S. Dollar\",\"value\":\"USD\"},{\"not_usable\":false,\"label\":\"BAM - 自由兑换马克\",\"value\":\"BAM\"},{\"not_usable\":false,\"label\":\"BBD - 巴巴多斯元\",\"value\":\"BBD\"},{\"not_usable\":false,\"label\":\"BDT - 孟加拉国塔卡\",\"value\":\"BDT\"},{\"not_usable\":false,\"label\":\"BWP - 博茨瓦纳普拉\",\"value\":\"BWP\"},{\"not_usable\":false,\"label\":\"ALL - 阿尔巴尼亚列克\",\"value\":\"ALL\"},{\"not_usable\":false,\"label\":\"CNY - China Yuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885b\",\"is_single\":false,\"label_r\":\"币种\",\"is_index_field\":false,\"index_name\":\"s_3\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"version\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"create_time\":*************,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"index_name\":\"version\",\"round_mode\":4,\"status\":\"released\"},\"created_by\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"crt_by\",\"status\":\"released\",\"description\":\"\"},\"relevant_team\":{\"describe_api_name\":\"object_f59kS__c\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":1709805253330,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98856\",\"is_single\":false,\"label_r\":\"相关团队\",\"is_index_field\":false,\"index_name\":\"a_team\",\"help_text\":\"相关团队\",\"status\":\"new\",\"description\":\"\"},\"record_type\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253328,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"record_type\",\"is_unique\":false,\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98854\",\"is_single\":false,\"label_r\":\"业务类型\",\"is_index_field\":false,\"index_name\":\"r_type\",\"config\":{},\"help_text\":\"\",\"status\":\"released\"},\"data_own_department\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属部门\",\"index_name\":\"data_owner_dept_id\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253323,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属部门\",\"is_need_convert\":false,\"api_name\":\"data_own_department\",\"_id\":\"65e98ec57b102d0001f9884f\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"changed_by\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805288593,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"变更人\",\"type\":\"employee\",\"is_required\":false,\"api_name\":\"changed_by\",\"define_type\":\"package\",\"_id\":\"65e98ee87b102d0001f989f2\",\"is_index_field\":false,\"is_single\":true,\"index_name\":\"a_2\",\"status\":\"released\",\"description\":\"\"},\"name\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"name\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"主属性\",\"index_name\":\"name\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253376,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"主属性\",\"api_name\":\"name\",\"_id\":\"65e98ec57b102d0001f9884c\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate_version\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253334,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"label\":\"汇率版本\",\"type\":\"text\",\"field_num\":8,\"is_required\":false,\"api_name\":\"mc_exchange_rate_version\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885e\",\"is_single\":false,\"label_r\":\"汇率版本\",\"is_index_field\":false,\"index_name\":\"t_1\",\"max_length\":256,\"help_text\":\"\",\"status\":\"new\"},\"_id\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"index_name\":\"_id\",\"max_length\":200,\"status\":\"released\"}},\"release_version\":\"6.4\",\"actions\":{}}\n";

    String detailOriginalDescribeJson = "{\"tenant_id\":\"74255\",\"package\":\"CRM\",\"is_active\":true,\"last_modified_time\":*************,\"create_time\":*************,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"查重工具从\",\"created_by\":\"1000\",\"version\":7,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_f59kS__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"short_name\":\"Uo1\",\"_id\":\"65e98ec57b102d0001f98860\",\"fields\":{\"field_211rf__c\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"type\":\"long_text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"t_5\",\"max_length\":2000,\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"min_length\":0,\"default_value\":\"\",\"label\":\"多行文本\",\"field_num\":12,\"api_name\":\"field_211rf__c\",\"_id\":\"65e98ef67b102d0001f98bc8\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"tenant_id\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"lock_rule\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253288,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"field_num\":1,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98857\",\"is_single\":false,\"label_r\":\"锁定规则\",\"is_index_field\":false,\"index_name\":\"s_0\",\"status\":\"new\"},\"data_own_organization\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253335,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"归属组织\",\"type\":\"department\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"data_own_organization\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885f\",\"is_single\":true,\"label_r\":\"归属组织\",\"is_index_field\":false,\"index_name\":\"a_0\",\"status\":\"released\",\"description\":\"\"},\"field_6f23A__c\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"input_mode\":\"\",\"is_single\":false,\"index_name\":\"t_4\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1709805298902,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"单行文本\",\"field_num\":11,\"api_name\":\"field_6f23A__c\",\"_id\":\"65e98ef37b102d0001f98a8b\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"version_number\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805288592,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":true,\"label\":\"版本号\",\"type\":\"text\",\"is_required\":false,\"api_name\":\"version_number\",\"define_type\":\"package\",\"_id\":\"65e98ee87b102d0001f989ee\",\"is_single\":false,\"is_index_field\":false,\"index_name\":\"t_3\",\"max_length\":256,\"status\":\"released\",\"description\":\"\"},\"origin_source\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"数据来源\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"origin_source\",\"options\":[{\"label\":\"数据同步\",\"value\":\"0\"}],\"define_type\":\"system\",\"is_extend\":false,\"index_name\":\"s_os\",\"config\":{\"display\":0},\"status\":\"released\",\"description\":\"\"},\"lock_user\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253289,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"加锁人\",\"is_unique\":false,\"label\":\"加锁人\",\"type\":\"employee\",\"field_num\":4,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98859\",\"is_single\":true,\"label_r\":\"加锁人\",\"is_index_field\":false,\"index_name\":\"a_1\",\"status\":\"new\"},\"mc_exchange_rate\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":8,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"汇率\",\"index_name\":\"d_0\",\"max_length\":18,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253332,\"is_encrypted\":false,\"length\":10,\"label\":\"汇率\",\"field_num\":6,\"api_name\":\"mc_exchange_rate\",\"_id\":\"65e98ec57b102d0001f9885c\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\"},\"field_3cdxe__c\":{\"describe_api_name\":\"object_f59kS__c\",\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"master_detail\",\"is_required\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"s_6\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805286476,\"is_encrypted\":false,\"label\":\"主从关系\",\"target_api_name\":\"object_au47D__c\",\"show_detail_button\":false,\"target_related_list_name\":\"target_related_list_L982t__c\",\"field_num\":10,\"target_related_list_label\":\"查重工具从\",\"api_name\":\"field_3cdxe__c\",\"is_create_when_master_create\":true,\"_id\":\"65e98ee87b102d0001f989ed\",\"is_index_field\":true,\"is_required_when_master_create\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"is_deleted\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"create_time\":*************,\"is_unique\":false,\"description\":\"is_deleted\",\"default_value\":false,\"label\":\"is_deleted\",\"type\":\"true_or_false\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"is_deleted\",\"options\":[],\"define_type\":\"system\",\"index_name\":\"is_del\",\"status\":\"released\"},\"life_status_before_invalid\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253289,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"label\":\"作废前生命状态\",\"type\":\"text\",\"field_num\":7,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885a\",\"is_single\":false,\"label_r\":\"作废前生命状态\",\"is_index_field\":false,\"index_name\":\"t_0\",\"max_length\":256,\"status\":\"new\"},\"object_describe_api_name\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"index_name\":\"api_name\",\"max_length\":200,\"status\":\"released\"},\"owner_department\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":true,\"label_r\":\"负责人主属部门\",\"index_name\":\"owner_dept\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253322,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"负责人主属部门\",\"is_need_convert\":false,\"api_name\":\"owner_department\",\"_id\":\"65e98ec57b102d0001f9884e\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"out_owner\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"外部负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_owner\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"o_owner\",\"config\":{\"display\":1},\"status\":\"released\",\"description\":\"\"},\"mc_functional_currency\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253333,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"label\":\"本位币\",\"type\":\"select_one\",\"field_num\":9,\"is_required\":false,\"api_name\":\"mc_functional_currency\",\"options\":[{\"not_usable\":false,\"label\":\"CNY-ChinaYuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885d\",\"is_single\":false,\"label_r\":\"本位币\",\"is_index_field\":false,\"index_name\":\"s_4\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253321,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"default_value\":\"\",\"label\":\"负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":true,\"wheres\":[],\"api_name\":\"owner\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9884d\",\"is_single\":true,\"label_r\":\"负责人\",\"is_index_field\":false,\"index_name\":\"owner\",\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"lock_status\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253289,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"type\":\"select_one\",\"field_num\":2,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_status\",\"options\":[{\"label\":\"未锁定\",\"value\":\"0\"},{\"label\":\"锁定\",\"value\":\"1\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98858\",\"is_single\":false,\"label_r\":\"锁定状态\",\"is_index_field\":false,\"index_name\":\"s_1\",\"config\":{},\"status\":\"new\"},\"package\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"package\",\"label\":\"package\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"package\",\"define_type\":\"system\",\"index_name\":\"pkg\",\"max_length\":200,\"status\":\"released\"},\"last_modified_time\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"last_modified_time\",\"label\":\"最后修改时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"last_modified_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-ddHH:mm:ss\",\"index_name\":\"md_time\",\"status\":\"released\"},\"create_time\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"create_time\",\"label\":\"创建时间\",\"type\":\"date_time\",\"time_zone\":\"\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"create_time\",\"define_type\":\"system\",\"date_format\":\"yyyy-MM-ddHH:mm:ss\",\"index_name\":\"crt_time\",\"status\":\"released\"},\"life_status\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"未生效\",\"value\":\"ineffective\"},{\"label\":\"审核中\",\"value\":\"under_review\"},{\"label\":\"正常\",\"value\":\"normal\"},{\"label\":\"变更中\",\"value\":\"in_change\"},{\"label\":\"作废\",\"value\":\"invalid\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"生命状态\",\"index_name\":\"s_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253329,\"is_encrypted\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"field_num\":3,\"is_need_convert\":false,\"api_name\":\"life_status\",\"_id\":\"65e98ec57b102d0001f98855\",\"is_index_field\":false,\"config\":{},\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"last_modified_by\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"最后修改人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"last_modified_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"md_by\",\"status\":\"released\",\"description\":\"\"},\"out_tenant_id\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"out_tenant_id\",\"label\":\"外部企业\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"out_tenant_id\",\"define_type\":\"system\",\"index_name\":\"o_ei\",\"config\":{\"display\":0},\"max_length\":200,\"status\":\"released\"},\"mc_currency\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253331,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"币种\",\"type\":\"select_one\",\"field_num\":5,\"is_required\":false,\"api_name\":\"mc_currency\",\"options\":[{\"not_usable\":false,\"label\":\"BYR-白俄罗斯卢布\",\"value\":\"BYR\"},{\"not_usable\":false,\"label\":\"AED-UAEDirham\",\"value\":\"AED\"},{\"not_usable\":false,\"label\":\"BIF-布隆迪法郎\",\"value\":\"BIF\"},{\"not_usable\":false,\"label\":\"BRL-巴西币\",\"value\":\"BRL\"},{\"not_usable\":false,\"label\":\"BOB-玻利维亚的玻利维亚诺\",\"value\":\"BOB\"},{\"not_usable\":false,\"label\":\"BYN-白俄罗斯卢布\",\"value\":\"BYN\"},{\"not_usable\":false,\"label\":\"BZD-伯利兹元\",\"value\":\"BZD\"},{\"not_usable\":false,\"label\":\"BOV-玻利维亚姆夫多尔\",\"value\":\"BOV\"},{\"not_usable\":false,\"label\":\"BRB-巴西克鲁塞罗（旧）\",\"value\":\"BRB\"},{\"not_usable\":false,\"label\":\"BSD-巴哈马元\",\"value\":\"BSD\"},{\"not_usable\":false,\"label\":\"自定义QWE\",\"value\":\"qwe\"},{\"not_usable\":false,\"label\":\"自定义zas\",\"value\":\"zas\"},{\"not_usable\":false,\"label\":\"翻译ASD\",\"value\":\"asd\"},{\"not_usable\":false,\"label\":\"AWG-阿鲁巴岛弗罗林\",\"value\":\"AWG\"},{\"not_usable\":false,\"label\":\"ada名称\",\"value\":\"ada\"},{\"not_usable\":false,\"label\":\"BTN-不丹卢比\",\"value\":\"BTN\"},{\"not_usable\":false,\"label\":\"BGN-BulgarianLev\",\"value\":\"BGN\"},{\"not_usable\":false,\"label\":\"BHD-BahrainiDinar\",\"value\":\"BHD\"},{\"not_usable\":false,\"label\":\"BND-BruneiDollar\",\"value\":\"BND\"},{\"not_usable\":false,\"label\":\"BMD-百慕大元\",\"value\":\"BMD\"},{\"not_usable\":false,\"label\":\"AOA-安哥拉宽扎\",\"value\":\"AOA\"},{\"not_usable\":false,\"label\":\"ANG-荷属安地列斯盾\",\"value\":\"ANG\"},{\"not_usable\":false,\"label\":\"ARS-阿根廷比索\",\"value\":\"ARS\"},{\"not_usable\":false,\"label\":\"AMD-亚美尼亚打兰\",\"value\":\"AMD\"},{\"not_usable\":false,\"label\":\"AUD-AustralianDollar\",\"value\":\"AUD\"},{\"not_usable\":false,\"label\":\"AFN-AfghanistanAfghani(New)\",\"value\":\"AFN\"},{\"not_usable\":false,\"label\":\"AZN-阿塞拜疆马纳特\",\"value\":\"AZN\"},{\"not_usable\":false,\"label\":\"USD-U.S.Dollar\",\"value\":\"USD\"},{\"not_usable\":false,\"label\":\"BAM-自由兑换马克\",\"value\":\"BAM\"},{\"not_usable\":false,\"label\":\"BBD-巴巴多斯元\",\"value\":\"BBD\"},{\"not_usable\":false,\"label\":\"BDT-孟加拉国塔卡\",\"value\":\"BDT\"},{\"not_usable\":false,\"label\":\"BWP-博茨瓦纳普拉\",\"value\":\"BWP\"},{\"not_usable\":false,\"label\":\"ALL-阿尔巴尼亚列克\",\"value\":\"ALL\"},{\"not_usable\":false,\"label\":\"CNY-ChinaYuan\",\"value\":\"CNY\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885b\",\"is_single\":false,\"label_r\":\"币种\",\"is_index_field\":false,\"index_name\":\"s_3\",\"config\":{},\"help_text\":\"\",\"status\":\"new\"},\"version\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"create_time\":*************,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"index_name\":\"version\",\"round_mode\":4,\"status\":\"released\"},\"created_by\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"创建人\",\"type\":\"employee\",\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"created_by\",\"define_type\":\"system\",\"is_single\":true,\"index_name\":\"crt_by\",\"status\":\"released\",\"description\":\"\"},\"relevant_team\":{\"describe_api_name\":\"object_f59kS__c\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"label\":\"负责人\",\"value\":\"1\"},{\"label\":\"普通成员\",\"value\":\"4\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"label\":\"只读\",\"value\":\"1\"},{\"label\":\"读写\",\"value\":\"2\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":1709805253330,\"is_encrypted\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98856\",\"is_single\":false,\"label_r\":\"相关团队\",\"is_index_field\":false,\"index_name\":\"a_team\",\"help_text\":\"相关团队\",\"status\":\"new\",\"description\":\"\"},\"record_type\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253328,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"record_type\",\"is_unique\":false,\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"api_name\":\"default__c\",\"description\":\"预设业务类型\",\"label\":\"预设业务类型\"}],\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f98854\",\"is_single\":false,\"label_r\":\"业务类型\",\"is_index_field\":false,\"index_name\":\"r_type\",\"config\":{},\"help_text\":\"\",\"status\":\"released\"},\"data_own_department\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"where_type\":\"field\",\"type\":\"department\",\"is_required\":false,\"wheres\":[],\"optional_type\":\"department\",\"define_type\":\"package\",\"is_single\":true,\"label_r\":\"归属部门\",\"index_name\":\"data_owner_dept_id\",\"is_index\":true,\"is_active\":true,\"create_time\":1709805253323,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"归属部门\",\"is_need_convert\":false,\"api_name\":\"data_own_department\",\"_id\":\"65e98ec57b102d0001f9884f\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\",\"description\":\"\"},\"name\":{\"describe_api_name\":\"object_f59kS__c\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"remove_mask_roles\":{},\"description\":\"name\",\"is_unique\":true,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":true,\"define_type\":\"system\",\"input_mode\":\"\",\"is_single\":false,\"label_r\":\"主属性\",\"index_name\":\"name\",\"max_length\":100,\"is_index\":true,\"is_active\":true,\"create_time\":1709805253376,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"主属性\",\"api_name\":\"name\",\"_id\":\"65e98ec57b102d0001f9884c\",\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"},\"mc_exchange_rate_version\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1709805253334,\"is_encrypted\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"label\":\"汇率版本\",\"type\":\"text\",\"field_num\":8,\"is_required\":false,\"api_name\":\"mc_exchange_rate_version\",\"define_type\":\"package\",\"_id\":\"65e98ec57b102d0001f9885e\",\"is_single\":false,\"label_r\":\"汇率版本\",\"is_index_field\":false,\"index_name\":\"t_1\",\"max_length\":256,\"help_text\":\"\",\"status\":\"new\"},\"_id\":{\"describe_api_name\":\"object_f59kS__c\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"index_name\":\"_id\",\"max_length\":200,\"status\":\"released\"}},\"release_version\":\"6.4\",\"actions\":{}}\n";

    String dataJson = "{\"name\":\"1245\",\"_id\":\"123234321232123212\",\"life_status\":\"normal\"}\n"


//    String dataExtraJson = "{\"id\":\"132232131231232\",\"tenantId\":\"74255\",\"describeApiName\":\"object_au47D__c\",\"fieldApiName\":\"__change_order_snapshot__\",\"extraInfo\":\"{\\\\\\\"name\\\\\\\":\\\\\\\"1245\\\\\\\",\\\\\\\"_id\\\\\\\":\\\\\\\"123234321232123212\\\\\\\",\\\\\\\"life_status\\\\\\\":\\\\\\\"normal\\\\\\\"}\",\"isDeleted\":false}\n"

    String changOrderOriginalDescribeGrayJson = "{\"udobj\":{\"describeApiName\":\"udobj\",\"ei\":\"white:74255\",\"changeOrderApiName\":\"udobj\",\"changeOrderStoreTableName\":\"mt_data\",\"supportFields\":\"black: unit|product_image|is_saleable|max_amount|min_amount|price_editable|price_mode|product_group_id|product_life_status|product_status|amount_editable|increment|is_package\",\"supportFieldTypes\":\"black:quote\",\"changeFields\":\"white:price_book_product_id|discount|product_price|sale_contract_line_id|subtotal|price_book_id|remark|quantity|price_book_price|sales_price|dynamic_amount|name\",\"changeFieldType\":\"black:auto_number|quote|signature|sign_in|payment|count|formula|master_detail\",\"originalFields\":\"white:price_book_product_id|discount|product_price|sale_contract_line_id|subtotal|price_book_id|remark|quantity|price_book_price|sales_price|dynamic_amount\",\"originalCount\":10,\"originalFieldType\":\"white:number|currency|percentile|text\"}}\n"


    @Shared
    IObjectDescribe describe

    @Shared
    IObjectDescribe detailDescribe

    @Shared
    IObjectDescribe detailOriginalDescribe

    @Shared
    IObjectData data;

    @Shared
    DataExtra dataExtra

    def setup() {
        changeOrderLogicService = new ChangeOrderLogicServiceImpl(
                describeLogicService: describeLogicService,
                configService: configService,
                licenseService: licenseService,
                redissonService: redissonService,
                dataExtraMapper: dataExtraMapper,
                approvalFlowService: approvalFlowService
        )
        ApplicationContext CONTEXT = Mock(ApplicationContext.class)

        Whitebox.setInternalState(SpringContextUtil, "CONTEXT", CONTEXT)
        describe = ObjectDescribeExt.of(JSON.parseObject(describeJson, Map.class))
        detailDescribe = ObjectDescribeExt.of(JSON.parseObject(detailDescribeJson, Map.class))
        detailOriginalDescribe = ObjectDescribeExt.of(JSON.parseObject(detailOriginalDescribeJson, Map.class))
        data = ObjectDataExt.of(JSON.parseObject(dataJson, Map.class))
//        dataExtra = JSON.parseObject(dataExtraJson,DataExtra.class)

        dataExtra = new DataExtra()
        dataExtra.setTenantId("74255")
        dataExtra.setIsDeleted(false)
        dataExtra.setFieldApiName("__change_order_snapshot__")
        dataExtra.setDescribeApiName("object_au47D__c")
        dataExtra.setExtraInfo("{\"name\":\"1245\",\"_id\":\"123234321232123212\",\"life_status\":\"normal\"}\n")
        dataExtra.setId("2332312321232123")
        ChangeOrderConfig.changeOrderOriginalDescribeGray = JacksonUtils.fromJson(changOrderOriginalDescribeGrayJson, new TypeReference<Map<String, ChangeOrderConfig.ChangeOrderConfigItem>>() {
        })
    }


    def "findSupportChangeOrderDescribes test"() {
        when:
        describeLogicService.findObjectsByTenantId(_) >> [describe]
        changeOrderLogicService.findSupportChangeOrderDescribes("74255")
        then:
        noExceptionThrown()
    }


    def "findDescribesByOriginalApiName test"() {
        when:
        describeLogicService.findObject(_, _) >> describe
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [describe]
        configService.findTenantConfig(_, _) >> "object_au47D__c__changeObj__c"
        def res = changeOrderLogicService.findDescribesByOriginalApiName(new User("74255", "100"), "object_au47D__c")
        then:
        noExceptionThrown()
    }


    def "findChangeOrderDescribeByOriginalApiName test"() {
        when:
        describeLogicService.findObject(_, _) >> describe
        configService.findTenantConfig(_, _) >> "object_au47D__c__changeObj__c"
        def res = changeOrderLogicService.findChangeOrderDescribeByOriginalApiName(new User("74255", "1000"), "object_au47D__c")
        then:
        res == Optional.of(describe)
        noExceptionThrown()
    }

//    def "openDetailChangeOrder test"() {
//        given:
//        RLock rLock = Mock(RedissonLock)
//        detailDescribe.removeFieldDescribeList(['version_number', 'changed_time', 'changed_reason', 'changed_by', 'changed_status'])
//        TransactionSynchronizationManager.initSynchronization()
//        ChangeOrderObjectFieldConvert changeOrderObjectFieldConvert = new ChangeOrderObjectFieldConvert()
//
//        ChangeOrderObjectFieldConvert convert = GroovyMock(ChangeOrderObjectFieldConvert)
//        Whitebox.setInternalState(ChangeOrderObjectFieldConvert.instance,"INSTANCE",changeOrderObjectFieldConvert)
//
//
//
//        when:
//        licenseService.isSupportChangeOrder(_) >> true
//        redissonService.tryLockWithErrorMsg(_, _, _, _, _, _) >> rLock
//        approvalFlowService.hasActiveApprovalFlowDefinitions(_,_,_) >> false
//        changeOrderLogicService.openDetailChangeOrder(new User("74255", "1000"), detailDescribe)
//        then:
//        noExceptionThrown()
//
//    }

    def "closeChangeOrder test"() {
        when:
        configService.findTenantConfig(_, _) >> "object_au47D__c__changeObj__c"
        describeLogicService.findObject(_, _) >> describe
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [detailDescribe]
        changeOrderLogicService.closeChangeOrder(new User("74255", "1000"), "object_au47D__c")
        then:
        noExceptionThrown()
    }

    def "syncChangeOrderDescribe test"() {
        when:
        describeLogicService.findObject(_, _) >> describe
        describeLogicService.findRelatedDescribesWithoutCopyIfGray(_, _) >> [detailDescribe]
        changeOrderLogicService.syncChangeOrderDescribe(new User(tenantId, "1000"), describeApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | res
        "74255"  | "AccountObj"      | _
        "74255"  | "object_au47D__c" | _
    }

    def "findChangeOrderOriginalData test"() {
        given:
        DataExtra dataExtra = new DataExtra()
        when:
        dataExtraMapper.findByFieldApiNameAndDataId(_, _, _) >> [dataExtra]
        def res = changeOrderLogicService.findChangeOrderOriginalData(new User("74255", "1000"), "AccountObj", ['**********'])

        then:
        noExceptionThrown()
//        where:

    }

    def "findAndMergeObjectDataWithOriginalData test"() {
        given:
        when:
        dataExtraMapper.setTenantId(_) >> dataExtraMapper
        dataExtraMapper.findByFieldApiNameAndDataId(_, _, _) >> [dataExtra]
        describeLogicService.findObject(_, _) >> describe
        def res = changeOrderLogicService.findAndMergeObjectDataWithOriginalData(new User("74255", "1000"), describe, [data])
        then:
        noExceptionThrown()
    }


    def "openChangeOrder test"() {
        given:
        describe.removeFieldDescribeList(["version_number", "changed_by", "changed_time", "changed_reason", "changed_status"])
        describe.setOriginalDescribeApiName("")
        describe.setFieldDescribes([])
        detailDescribe.setFieldDescribes([])
        when:
//        ChangeOrderObjectFieldConvert changeOrderObjectFieldConvert = Mockito.mock(ChangeOrderObjectFieldConvert.class)
//        Whitebox.setInternalState(ChangeOrderObjectFieldConvert.Helper,"INSTANCE",changeOrderObjectFieldConvert)
        licenseService.isSupportChangeOrder(_) >> true
        describeLogicService.findObject(_, _) >> describe
        describeLogicService.findDetailDescribes(_, _) >> [detailDescribe]
        changeOrderLogicService.openChangeOrder(User.systemUser("74255"), "AccountObj")
        then:
        noExceptionThrown()
    }

    def "openChangeOrder test2"() {
        given:
        describe.removeFieldDescribeList(["version_number", "changed_by", "changed_time", "changed_reason", "changed_status"])
        describe.setOriginalDescribeApiName("")
        describe.setFieldDescribes([])
//        detailDescribe.setFieldDescribes([])
        ObjectDescribeExt.of(detailDescribe).getMasterDetailField().ifPresent {x->x.setIsCreateWhenMasterCreate(true)}

        when:
//        ChangeOrderObjectFieldConvert changeOrderObjectFieldConvert = Mockito.mock(ChangeOrderObjectFieldConvert.class)
//        Whitebox.setInternalState(ChangeOrderObjectFieldConvert.Helper,"INSTANCE",changeOrderObjectFieldConvert)
        licenseService.isSupportChangeOrder(_) >> true
        describeLogicService.findObject(_, _) >> describe
        describeLogicService.findDetailDescribes(_, _) >> [detailDescribe]
        changeOrderLogicService.openChangeOrder(User.systemUser("74255"), "AccountObj")
        then:
        noExceptionThrown()
    }


    def "openDetailChangeOrder test"() {
        when:
        licenseService.isSupportChangeOrder(_) >> true
        changeOrderLogicService.openDetailChangeOrder(User.systemUser("74255"), detailOriginalDescribe)
        then:
        noExceptionThrown()
    }


    def "saveChangeOrderOriginalData test"() {
        when:
        describeLogicService.findObjectWithoutCopy(_, _) >> describe
        changeOrderLogicService.findDetailLayoutsWithChangeOrder(User.systemUser("74255"), describe, new Function<String, Map<String, Layout>>() {
            @Override
            Map<String, Layout> apply(String s) {
                return ["layout": new Layout()]
            }
        })
        then:
        noExceptionThrown()
    }

    def "isOpenChangeOrder test"() {
        when:
        configService.findTenantConfig(_, _) >> ""
        changeOrderLogicService.isOpenChangeOrder(User.systemUser("74255"), "AccountOBj",)
        then:
        noExceptionThrown()
    }

    def "addFieldForOriginalDescribe test"() {
        when:
        licenseService.isSupportChangeOrder(_) >> true
        detailDescribe.setOriginalDescribeApiName("")
        detailDescribe.removeFieldDescribeList(["version_number", "changed_by", "changed_time", "changed_reason", "changed_status"])
        changeOrderLogicService.addFieldForOriginalDescribe(User.systemUser("74255"), detailDescribe)
        then:
        noExceptionThrown()
    }

    def "findOriginalApiNameByChangeOrder test"() {
        when:
        describeLogicService.findObjectWithoutCopy(_, _) >> describe
        changeOrderLogicService.findOriginalApiNameByChangeOrder(User.systemUser("74255"), "AccountObj")
        then:
        noExceptionThrown()
    }

    def "batchFindOriginalApiNameByChangOrderList test"() {
        when:
        configService.queryTenantConfigs() >> [:]
        changeOrderLogicService.batchFindOriginalApiNameByChangOrderList(User.systemUser("74255"), ["AccountOBj"])
        then:
        noExceptionThrown()
    }


    def "findLayoutWithChangeOrder"() {
        given:
        Layout layout = new Layout()
        layout.setLayoutType("detail")
        def section = new FieldSection()
        section.setFields([new FormField()])
        def component = new FormComponent()
        component.setFieldSections([section])
        layout.setComponents([component])
        when:
        describeLogicService.findObjectWithoutCopy(_, _) >> describe

        changeOrderLogicService.findLayoutWithChangeOrder(User.systemUser("74255"),
                "AccountObj", layoutType, { describeApiName -> return layout })
        then:
        noExceptionThrown()
        where:
        layoutType                 || res
        ILayout.DETAIL_LAYOUT_TYPE || _


    }

    def "saveChangeOrderOriginalData"() {
        given:
        when:
        dataExtraMapper.setTenantId(_) >> Mock(DataExtraMapper)
        changeOrderLogicService.saveChangeOrderOriginalData(User.systemUser("74255"), "AccountObj", [data])
        then:
        noExceptionThrown()
    }


}

