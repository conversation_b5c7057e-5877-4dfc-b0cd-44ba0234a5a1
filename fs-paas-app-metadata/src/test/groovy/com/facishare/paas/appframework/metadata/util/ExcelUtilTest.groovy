package com.facishare.paas.appframework.metadata.util

import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * ExcelUtil工具类的单元测试
 * 主要测试Excel导出时的数据格式化和安全处理功能
 */
class ExcelUtilTest extends Specification {

    def setupSpec() {
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(*_) >> true
    }

    def "OrderSheets"() {
        when:
        orderSheets(sheets)
        then:
        println sheets

        List<Sheet> list = sheets.subList(0, (sheets.size() / 2).toInteger())
        println list
        list.findAll { it -> it.isHidden }.empty


        where:
        sheets                                                                         || _
        [Sheet.of(false, 1), Sheet.of(true, 4), Sheet.of(false, 2), Sheet.of(true, 5),
         Sheet.of(false, 3), Sheet.of(true, 6)]                                        || _
        [Sheet.of(false, 1), Sheet.of(true, 3), Sheet.of(false, 2), Sheet.of(true, 4)] || _
        [Sheet.of(false, 1), Sheet.of(true, 5), Sheet.of(false, 2), Sheet.of(true, 6),
         Sheet.of(false, 3), Sheet.of(true, 7), Sheet.of(false, 4), Sheet.of(true, 8)] || _
    }

    def orderSheets(List<Sheet> sheets) {
        int number = sheets.size()
        int i = 0
        int j = 1

        while (j < number) {
            if (i < number && sheets.get(i).isHidden) {
                while (j < number && sheets.get(j).isHidden) {
                    j++
                }
                if (j >= number) {
                    break
                }
                swap(sheets, i, j)
            } else {
                j++
            }
            i++
        }

    }

    def swap(List<Sheet> sheets, int i, int j) {
        def iSheet = sheets.get(i)
        def jSheet = sheets.get(j)
        sheets.set(i, jSheet)
        sheets.set(j, iSheet)
    }

    static class Sheet {
        boolean isHidden
        int index

        Sheet(boolean isHidden, int index) {
            this.isHidden = isHidden
            this.index = index
        }

        static Sheet of(boolean isHidden, int index) {
            return new Sheet(isHidden, index)
        }

        @Override
        public String toString() {
            return "Sheet{" +
                    "isHidden=" + isHidden +
                    ", index=" + index +
                    '}';
        }

        boolean equals(o) {
            if (this.is(o)) return true
            if (getClass() != o.class) return false

            Sheet sheet = (Sheet) o

            if (index != sheet.index) return false
            if (isHidden != sheet.isHidden) return false

            return true
        }

        int hashCode() {
            int result
            result = (isHidden ? 1 : 0)
            result = 31 * result + index
            return result
        }
    }

    @Unroll
    def "test getFormatValue with formula patterns"() {
        expect:
        ExcelUtil.getFormatValue(value) == expected

        where:
        value      | expected
        "=1+1"     | "'=1+1"      // 被识别为风险
        "@SUM(A1)" | "'@SUM(A1)"  // 应被识别为风险
        "+123"     | "+123"       // 纯数字不识别为风险
        "-456"     | "-456"       // 纯数字不识别为风险
        "*789"     | "*789"       // 星号开头不识别为风险
    }

    @Unroll
    def "test getFormatValue with CSV characters"() {
        expect:
        ExcelUtil.getFormatValue(value) == expected

        where:
        value                 | expected
        "Hello,World"         | "\"Hello,World\""       // 简单的双引号包围
        "Text with \"quote\"" | "\"Text with \"\"quote\"\"\""
        " Leading space"      | "\" Leading space\""
        "Trailing space "     | "\"Trailing space \""
    }

    @Unroll
    def "test getFormatValue with combined patterns"() {
        expect:
        ExcelUtil.getFormatValue(value) == expected

        where:
        value           | expected
        "=SUM(A1),test" | "'=SUM(A1),test"
        ",=formula"     | "',=formula"
        "=\"formula\""  | "'=\"formula\""
    }

    @Unroll
    def "test getFormatValue with complex injection patterns"() {
        expect:
        ExcelUtil.getFormatValue(value) == expected

        where:
        value                         | expected
        "=sum(1+1)*cmd|' /C calc'!A0" | "'=sum(1+1)*cmd|' /C calc'!A0"
        "=cmd|' /C calc'"             | "'=cmd|' /C calc'"
        "=javascript:alert(1)"        | "'=javascript:alert(1)"
        "=@macro.run()"               | "'=@macro.run()"
    }

    def "test getFormatValue with null or empty"() {
        expect:
        ExcelUtil.getFormatValue(value) == expected

        where:
        value | expected
        null  | null
        ""    | null
        " "   | null
    }

    def "test getFormatValue with long text"() {
        given:
        def longText = "a" * 40000

        when:
        def result = ExcelUtil.getFormatValue(longText)

        then:
        result.length() <= 32767
    }

    @Unroll
    def "test getFormatValue with common business text"() {
        expect:
        def actual = ExcelUtil.getFormatValue(value)
        println "实际结果: ${actual}"
        actual == expected

        where:
        value                         | expected
        // 常见日期格式
        "2023-10-15"                  | "2023-10-15"                    // 标准日期
        "2023年10月15日"              | "2023年10月15日"                // 中文日期
        "15/10/2023"                  | "15/10/2023"                    // 欧式日期

        // 电话号码和数字标识
        "+86-10-12345678"             | "+86-10-12345678"               // 国际电话格式
        "010-12345678"                | "010-12345678"                  // 国内电话
        "***********"                 | "***********"                   // 手机号

        // 数字范围和单位
        "范围是10-20kg"               | "范围是10-20kg"                 // 数字范围
        "比例为1:100"                 | "比例为1:100"                   // 比例表示
        "温度为-10°C至+30°C"          | "温度为-10°C至+30°C"            // 温度范围
        "增长了+15%"                  | "增长了+15%"                    // 百分比增长
        "降低了-5%"                   | "降低了-5%"                     // 百分比降低

        // 版本号和编码
        "软件版本-v1.0-正式版"        | "软件版本-v1.0-正式版"           // 版本号
        "产品编号A-001+B"             | "产品编号A-001+B"               // 产品编码
        "批次号20231015-A++"          | "批次号20231015-A++"            // 批次编号

        // 常见业务标记
        "测试环境-test-部署"          | "测试环境-test-部署"            // 测试标记
        "导入v2验证-嘿呀-5"           | "导入v2验证-嘿呀-5"             // 版本标识
        "-待审核-"                    | "-待审核-"                      // 状态标记

        // 数学和物理表达式
        "公式y=kx+b"                  | "公式y=kx+b"                    // 线性方程
        "速度v=60km/h"                | "速度v=60km/h"                  // 物理公式
        "面积S=πr²"                   | "面积S=πr²"                     // 数学公式

        // 价格和金额
        "价格¥100-200"                | "价格¥100-200"                  // 价格范围
        "折扣-30%"                    | "折扣-30%"                      // 折扣
        "成本+利润=售价"              | "成本+利润=售价"                // 价格计算

        // 电子邮件和网址
        "<EMAIL>"            | "<EMAIL>"              // 电子邮件
        "https://www.example.com"     | "https://www.example.com"       // 网址
        "访问www.example.com获取更多" | "访问www.example.com获取更多"    // 含网址的文本
    }

    @Unroll
    def "test getFormatValue with mixed content"() {
        expect:
        def actual = ExcelUtil.getFormatValue(value)
        println "实际结果: ${actual}"
        actual == expected

        where:
        value                                        | expected
        "2023-11-01，=HYPERLINK(\"http://evil.com\")" | "'2023-11-01，=HYPERLINK(\"http://evil.com\")"  // 使用英文逗号
        "价格表: 苹果=red(红色)"                     | "价格表: 苹果=red(红色)"          // 未被识别为风险，无前缀
        "y=sin(x)表示正弦函数"                       | "y=sin(x)表示正弦函数"            // 未被识别为风险，无前缀
    }

    @Unroll
    def "test getFormatValue with boundary cases"() {
        expect:
        def actual = ExcelUtil.getFormatValue(value)
        println "实际结果 ${actual}"
        actual == expected

        where:
        value                 | expected
        // 看起来像公式但实际是正常业务文本
        "产品型号A+B"         | "产品型号A+B"                    // 不需要转义
        "价格区间A-Z"         | "价格区间A-Z"                    // 不需要转义
        "等级评定A=优秀"      | "等级评定A=优秀"                 // 不需要转义
        "标记符号@2023"       | "标记符号@2023"                  // 不需要转义
        "参数*重要"           | "参数*重要"                      // 不需要转义

        // 特殊的数学和科学表达式
        "方程式f(x)=2x+1"     | "方程式f(x)=2x+1"                // 不需要转义
        "对数log(100)=2"      | "对数log(100)=2"                 // 不需要转义
        "二项式定理(a+b)^2"   | "二项式定理(a+b)^2"               // 不需要转义

        // 编程和代码片段
        "函数名add(x,y)"      | "\"函数名add(x,y)\""                 // 由于逗号可能需要转义
        "变量x+=1表示自增"    | "变量x+=1表示自增"                // 不需要转义
        "条件语句if(x>0)"     | "条件语句if(x>0)"                // 不需要转义

        // 特殊分隔和格式
        "选项A|选项B|=C"      | "选项A|选项B|=C"                 // 不需要转义
        "数据1,数据2,=值"     | "\"数据1,数据2,=值\""            // 包含逗号，需要转义
        "表头1\t表头2\t=合计" | "\"表头1\t表头2\t=合计\""        // 包含制表符，需要转义
    }
}
