package com.facishare.paas.appframework.metadata.options

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
/**
 * create by z<PERSON><PERSON> on 2021/12/13
 */
class OptionSetLogicServiceImplTest extends Specification {
    def tenantId = '74255'

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    @Unroll
    def "Create no exception"() {
        given:
        User user = User.systemUser(tenantId)
        MtOptionSet option = MtOptionSet.fromJson(json)
        and: "repository"
        IRepository<MtOptionSet> repository = Mock(IRepository)
        and: "optionSetMaxCountGray"
        Whitebox.setInternalState(AppFrameworkConfig, "optionSetMaxCountGray", [:])
        and: "licenseService"
        LicenseService licenseService = Mock(LicenseService)
        and: "optionSetLogicService"
        OptionSetLogicService optionSetLogicService = new OptionSetLogicServiceImpl("repository": repository, "licenseService": licenseService)

        when:
        def result = optionSetLogicService.create(user, option)

        then:
        notThrown()
        checkCount * repository.findCountOnly(*_) >> 1
        2 * licenseService.existModule(*_) >> [:]
        1 * repository.findBy(*_) >> []
        1 * repository.bulkCreate(*_) >> [option]
        result != null

        where:
        checkCount | json
        1          | '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"custom","options":[{"label":"示例选项","font_color":"#181C25","value":"option1"}]}'''
        0          | '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"package","options":[{"label":"示例选项","font_color":"#181C25","value":"option1"}]}'''
        1          | '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"","options":[{"label":"示例选项","value":"option1"}]}'''
        1          | '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"options":[{"label":"示例选项","value":"option1"}]}'''
    }

        @Unroll
    def "Create whit exception"() {
        given:
        User user = User.systemUser(tenantId)
        MtOptionSet option = MtOptionSet.fromJson(json)
        and: "repository"
        IRepository<MtOptionSet> repository = Mock(IRepository)
        and: "optionSetMaxCountGray"
        Whitebox.setInternalState(AppFrameworkConfig, "optionSetMaxCountGray", [:])
        and: "licenseService"
        LicenseService licenseService = Mock(LicenseService)
        and: "optionSetLogicService"
        OptionSetLogicService optionSetLogicService = new OptionSetLogicServiceImpl("repository": repository, "licenseService": licenseService)

        when:
        def result = optionSetLogicService.create(user, option)

        then:
        thrown(ValidateException)

        where:
        json = '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"invalid","options":[{"label":"示例选项","value":"option1"}]}'''
    }

    @Unroll
    def "Update"() {
        given:
        User user = User.systemUser(tenantId)
        MtOptionSet option = MtOptionSet.fromJson(json)
        and: "repository"
        IRepository<MtOptionSet> repository = Mock(IRepository)
        and: "licenseService"
        LicenseService licenseService = Mock(LicenseService)
        and: "optionSetLogicService"
        OptionSetLogicService optionSetLogicService = new OptionSetLogicServiceImpl("repository": repository, "licenseService": licenseService)

        when:
        def result = optionSetLogicService.update(user, option, false)
        
        then:
        notThrown()
        repository.findBy(*_) >> [option]
        1 * repository.update(*_) >> option
        licenseService.existModule(*_) >> [:]
        result != null

        where:
        json << [
        '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"custom","options":[{"label":"示例选项","value":"option1"}]}''',
        '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"custom","options":[{"label":"示例选项","value":"option1"}]}''',
        '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"","options":[{"label":"示例选项","value":"option1"}]}''',
        '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"options":[{"label":"示例选项","value":"option1"}]}'''
        ]
    }

    @Unroll
    def "Update with exception"() {
        given:
        User user = User.systemUser(tenantId)
        MtOptionSet option = MtOptionSet.fromJson(json)
        and: "repository"
        IRepository<MtOptionSet> repository = Mock(IRepository)
        and: "licenseService"
        LicenseService licenseService = Mock(LicenseService)
        and: "optionSetLogicService"
        OptionSetLogicService optionSetLogicService = new OptionSetLogicServiceImpl("repository": repository, "licenseService": licenseService)

        when:
        def result = optionSetLogicService.update(user, option, false)

        then:
        thrown(ValidateException)

        where:
        json = '''{"label":"通用选项集","api_name":"option__c","description":"","is_active":true,"tenant_id":"78057","version":99,"define_type":"invalid","options":[{"label":"示例选项","value":"option1"}]}'''
    }

    def "Find"() {
        given:
        User user = User.systemUser(tenantId)
        and: "repository"
        IRepository<MtOptionSet> repository = Mock(IRepository)
        and: "licenseService"
        LicenseService licenseService = Mock(LicenseService)
        and: "optionSetLogicService"
        OptionSetLogicService optionSetLogicService = new OptionSetLogicServiceImpl("repository": repository, "licenseService": licenseService)
        when:
        def result = optionSetLogicService.findAll(user)
        println(JacksonUtils.toJson(result))
        then:
        1 * repository.findBy(*_) >> []
        1 * licenseService.existModule(*_) >> [:]
        where:
        tenantId | _
        "74255"  | _

    }
}
