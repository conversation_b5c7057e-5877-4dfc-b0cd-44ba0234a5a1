package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.service.DepartmentService
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.cache.RedisDao
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl
import com.facishare.paas.appframework.metadata.dto.BatchMultiQueryDuplicateSearch
import com.facishare.paas.appframework.metadata.dto.BatchQueryDuplicateSearch
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult
import com.facishare.paas.appframework.metadata.dto.MultiDuplicateSearchResult
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchDataStoreService
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import spock.lang.Shared
import spock.lang.Specification

/**
 * create by zhaoju on 2020/10/23
 */
class DuplicatedSearchDataServiceImplTest extends Specification {

    DuplicatedSearchDataService duplicatedSearchDataService;
    DuplicatedSearchService duplicatedSearchService = Mock(DuplicatedSearchService)
    DuplicateSearchProxy duplicateSearchProxy = Mock(DuplicateSearchProxy);
    RedisDao redisDao = Mock(RedisDao);
    RedissonServiceImpl redissonService = Mock(RedissonServiceImpl);
    DuplicatedSearchDataStoreService searchDataStoreService = Mock(DuplicatedSearchDataStoreService);
    MetaDataFindService metaDataFindService = Mock(MetaDataFindService);
    DepartmentService departmentService = Mock(DepartmentService);
    @Shared
    User user
    @Shared
    IDuplicatedSearch duplicatedSearch
    @Shared
    IDuplicatedSearch duplicatedSearchIncludeScope;
    @Shared
    String duplicatedSearchStr = """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"tel","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""";

    @Shared
    String duplicatedSearchJson = "{\n" +
            "  \"id\": \"6697be275f578a0007c9d22d\",\n" +
            "  \"name\": \"查重规则名称\",\n" +
            "  \"rule_api_name\": \"rule_2d8Ug__c\",\n" +
            "  \"wheres\": \"[{\\\"filters\\\": [{\\\"operator\\\": \\\"EQ\\\", \\\"field_name\\\": \\\"name\\\", \\\"value_type\\\": 0, \\\"field_values\\\": [\\\"2\\\"]}], \\\"connector\\\": \\\"OR\\\"}]\",\n" +
            "  \"data_scope\": \"[{\\\"filters\\\": [{\\\"operator\\\": \\\"IN\\\", \\\"field_name\\\": \\\"data_own_department\\\", \\\"is_cascade\\\": false, \\\"value_type\\\": 0, \\\"field_values\\\": [\\\"1024\\\"]}], \\\"connector\\\": \\\"OR\\\"}]\",\n" +
            "  \"tenant_id\": \"74255\",\n" +
            "  \"describe_api_name\": \"object_93cYU__c\",\n" +
            "  \"create_time\": 1721220647606,\n" +
            "  \"created_by\": \"1000\",\n" +
            "  \"last_modified_time\": 1721738413399,\n" +
            "  \"last_modified_by\": \"1000\",\n" +
            "  \"mark\": \"\",\n" +
            "  \"type\": \"NEW\",\n" +
            "  \"enable\": true,\n" +
            "  \"effective\": false,\n" +
            "  \"useable_rules\": {\n" +
            "    \"show_num\": 5,\n" +
            "    \"show_fields\": [\n" +
            "      \"field_hKMap__c\",\n" +
            "      \"name\"\n" +
            "    ],\n" +
            "    \"related_describes\": [],\n" +
            "    \"special_config\": {\n" +
            "      \"transforms\": []\n" +
            "    },\n" +
            "    \"rules\": [\n" +
            "      {\n" +
            "        \"connector\": \"OR\",\n" +
            "        \"conditions\": [\n" +
            "          {\n" +
            "            \"connector\": \"AND\",\n" +
            "            \"field_name\": \"field_hKMap__c\",\n" +
            "            \"field_value\": \"PRECISE\",\n" +
            "            \"empty_policy\": \"IGNORE_EMPTY\"\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"operator_id\": \"1000\",\n" +
            "  \"version\": 2,\n" +
            "  \"support_import\": false,\n" +
            "  \"invalid_not_duplicate_search\": false\n" +
            "}"

    def setup() {
        duplicatedSearchDataService = new DuplicatedSearchDataServiceImpl(
                duplicatedSearchService: duplicatedSearchService,
                duplicateSearchProxy: duplicateSearchProxy,
                redisDao: redisDao,
                redissonService: redissonService,
                searchDataStoreService: searchDataStoreService,
                metaDataFindService: metaDataFindService,
                departmentService: departmentService
        )

        user = User.systemUser("74255")
        duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchStr, IDuplicatedSearch)
        duplicatedSearchIncludeScope = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
    }

//    def "test searchInRedis"() {
//        when:
//
//        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
//        RedisDao redisDao = Mock(RedisDao)
//
//        DuplicatedSearchEntry searchEntry1 = DuplicatedSearchEntry.of("name_123", "name")
//        DuplicatedSearchEntry searchEntry2 = DuplicatedSearchEntry.of("tel_13376890012", "tel")
//        DuplicatedSearchDataContainer.DuplicatedSearchContainer container = new DuplicatedSearchDataContainer.DuplicatedSearchContainer(
//                Sets.newHashSet(searchEntry1, searchEntry2), duplicatedSearch, "1")
//        DuplicatedSearchDataServiceImpl duplicatedSearchDataService = new DuplicatedSearchDataServiceImpl(redisDao: redisDao)
//
//        redisDao.sinter(Sets.newHashSet("name_123", "tel_13376890012")) >> ["111", "222"]
//        then:
//
//        def func = duplicatedSearchDataService.searchInRedis()
//        Set<String> result = func.apply(container)
//        println result
//        ListUtils.isEqualList(result, expect)
//
//        where:
//        duplicatedSearchJson                                                                                           || expect
//        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
//"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
//"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
//"field_name":"tel","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
//"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}"""                     || ["111", "222"]
//    }


    def "searchDuplicateDataByType test"() {
        given:

        when:
        duplicatedSearchService.findDuplicatedSearchByApiNameAndType(_, _, _, _) >> duplicatedSearchIncludeScope
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> new ObjectData()
        duplicateSearchProxy.getDuplicateSearchResult(_, _) >> DuplicateSearchResult.builder().code("200").duplicateDataList([DuplicateSearchResult.DuplicateData.builder().apiName("AccountObj").ruleApiName("rule_sdsad__c").dataIds(["234e32323232323323"]).build()]).build();
        searchDataStoreService.searchDuplicatedData(_, _, _) >> [DuplicateSearchDataInfo.builder().dataIds(new LinkedHashSet<String>(["234e32323232323323"])).ruleApiName("rule_wescacas__c").apiName("AccountObj").build()]
        duplicatedSearchDataService.searchDuplicateDataByType(user, new ObjectData(), IDuplicatedSearch.Type.NEW, new ObjectDescribe())
        then:
        noExceptionThrown()
    }


    def "searchDuplicateDataByType test1"() {
        given:
        def esResult = MultiDuplicateSearchResult.Result.builder()
                .result(new MultiDuplicateSearchResult.DataResult())
                .code("200")
                .build()
        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> new ObjectData()
        duplicateSearchProxy.getMultiDuplicateSearchResult(_, _) >> esResult
        redisDao.sunion(_) >> []
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.of(DuplicateSearchDataInfo.builder().build())
        duplicatedSearchDataService.searchDuplicateDataByType(user, new ObjectData(), IDuplicatedSearch.Type.NEW, new ObjectDescribe(), [duplicatedSearch])
        then:
        noExceptionThrown()
    }

    def "searchDuplicateDataListByType test"() {
        given:
        BatchMultiQueryDuplicateSearch.Result result = new BatchMultiQueryDuplicateSearch.Result()
        result.setCode("200")
        result.setDuplicateDataList([])
        when:
        redisDao.sunion(_) >> []
        duplicateSearchProxy.batchMultiQueryDuplicateSearch(_, _) >> result
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> [new ObjectData()]
        duplicatedSearchDataService.searchDuplicateDataListByType(user, [new ObjectData()], IDuplicatedSearch.Type.NEW, new ObjectDescribe(), [duplicatedSearch], true)

        then:
        noExceptionThrown()
    }


    def "searchDuplicateDataListByType test2"() {
        given:
        BatchQueryDuplicateSearch.Result result = new BatchQueryDuplicateSearch.Result()
        result.setCode("200")
        result.setDuplicateDataList([])
        AppFrameworkConfig.multiDuplicateRuleAndSupportFilterGrayEi = []
        BatchQueryDuplicateSearch.Arg arg = BatchQueryDuplicateSearch.Arg.builder()
                .describeApiName("AccountObj")
                .tenantId(user.getTenantId())
                .type(IDuplicatedSearch.Type.NEW.name)
                .dataList([])
                .isIdValue(false)
                .build();
        when:
        redisDao.evalScript(_, _, _) >> []
        duplicateSearchProxy.batchQueryDuplicateSearch(user.getTenantId(), arg) >> result
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> [new ObjectData()]
        duplicatedSearchDataService.searchDuplicateDataListByType(user, [new ObjectData()], IDuplicatedSearch.Type.NEW, new ObjectDescribe(), [duplicatedSearch], true)

        then:
        noExceptionThrown()
    }


    def "getDuplicateData"() {
        given:
        MultiDuplicateSearchResult.Result result = new MultiDuplicateSearchResult.Result()
        result.setCode("200")
        result.setResult(MultiDuplicateSearchResult.DataResult.builder().ruleApiName("").duplicateDataList([]).build())
        DuplicateSearchResult duplicateSearchResult = DuplicateSearchResult.builder().code("200").duplicateDataList([DuplicateSearchResult.DuplicateData.builder().apiName("AccountObj").ruleApiName("rule_sdsad__c").dataIds(["234e32323232323323"]).build()]).build();
        when:
        duplicatedSearchService.getEnableDuplicateSearchRuleList(_, _, _, _, _) >> [duplicatedSearch]
        duplicatedSearchService.processFieldValueForDuplicatedSearch(_, _, _, _) >> new ObjectData()
        redisDao.sunion(_) >> []
        duplicatedSearchService.findDuplicatedSearchByApiNameAndType(_, _, _, _) >> duplicatedSearch
        duplicateSearchProxy.getMultiDuplicateSearchResult(_, _) >> result
        duplicateSearchProxy.getDuplicateSearchResult(_, _) >> duplicateSearchResult
        searchDataStoreService.searchDuplicatedData(_, _, _) >> Optional.empty()
        duplicatedSearchDataService.getDuplicateData(user, new ObjectDescribe(), new ObjectData(), "rule_dsd__c", false, false)
        then:
        noExceptionThrown()
    }

    def "saveDuplicateDataResultInRedis"() {
        given:
        when:
        duplicatedSearchDataService.saveDuplicateDataResultInRedis([new ObjectData()], new ObjectDescribe(), duplicatedSearch, user)
        then:
        noExceptionThrown()
    }

    def "duplicateSearchLock"() {
        given:
        when:
        duplicatedSearchDataService.duplicateSearchLock(user, [duplicatedSearch], new ObjectDescribe(), new ObjectData())
        then:
        noExceptionThrown()
    }


}
