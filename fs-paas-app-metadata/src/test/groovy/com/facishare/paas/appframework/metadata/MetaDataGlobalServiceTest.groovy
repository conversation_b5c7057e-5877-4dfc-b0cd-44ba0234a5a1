package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.dto.TownInfo
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Language
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by zhangxf in 2021/1/27 17:36
 */
//@ContextConfiguration(value = "classpath:applicationContext.xml")
class MetaDataGlobalServiceTest extends Specification {

    MetaDataGlobalService metaDataGlobalService
    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> [JSON.parseObject("{\"code\":\"zh-CN\",\"name\":\"简体中文\",\"displayName\":\"简体中文\",\"default\":true}", Language.class)]
    }
    def setup() {
        metaDataGlobalService = new MetaDataGlobalServiceImpl()
    }

/*    def "getCountry"() {
        given:
        Map<String, Map> map = new HashMap<>()
        when:
        map = CountryAreaManager.getCountryArea("745255")
        then:
        println(map)

    }*/

/*    def "test getTownOptions"() {
        given:
        String code = "298"
        String type = "city"
        User user = new User("74255", "1031")

        when:
        List<TownInfo> list = metaDataGlobalService.getTownOptions(type, [code] as Set, user)
        then:
        println(list)
    }*/
}
