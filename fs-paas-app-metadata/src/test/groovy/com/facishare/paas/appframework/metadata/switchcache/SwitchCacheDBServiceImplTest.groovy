package com.facishare.paas.appframework.metadata.switchcache

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.appframework.metadata.repository.model.MtSwitch
import com.facishare.paas.appframework.metadata.search.Query
import com.facishare.paas.metadata.api.DELETE_STATUS
import com.facishare.paas.metadata.api.search.IFilter
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class SwitchCacheDBServiceImplTest extends Specification {
    
    SwitchCacheDBServiceImpl switchCacheDBService
    IRepository<MtSwitch> repository
    User user

    def setup() {
        switchCacheDBService = new SwitchCacheDBServiceImpl()
        repository = Mock(IRepository)
        user = User.systemUser("74255")
        Whitebox.setInternalState(switchCacheDBService, "repository", repository)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 querySwitch 方法，验证能够正确查询开关信息
     */
    @Unroll
    def "querySwitchTest"() {
        given:
        def switchType = "mt_unique_rule"
        def objectApiName = "testObj"
        def switchNames = ["switch1", "switch2"]
        def expectedSwitches = [new MtSwitch(switchName: "switch1"), new MtSwitch(switchName: "switch2")]

        when:
        def result = switchCacheDBService.querySwitch(user, switchType, objectApiName, switchNames)

        then:
        1 * repository.findBy(user, _, MtSwitch.class) >> expectedSwitches
        result == expectedSwitches
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 bulkUpsert 方法，验证能够正确批量更新开关信息
     */
    @Unroll
    def "bulkUpsertTest"() {
        given:
        def switches = [
            new MtSwitch(switchName: "switch1", switchType: "mt_unique_rule"),
            new MtSwitch(switchName: "switch2", switchType: "mt_unique_rule")
        ]

        when:
        def result = switchCacheDBService.bulkUpsert(user, switches)

        then:
        1 * repository.bulkUpsert(user, switches) >> switches
        result == switches
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 modifySwitch 方法，验证当开关不存在时能够正确创建新开关
     */
    @Unroll
    def "modifySwitchTest_Create"() {
        given:
        def switchInfos = [
            SwitchInfo.of(user.tenantId, "mt_unique_rule", "switch1", "testObj", true)
        ]
        def expectedSwitch = new MtSwitch(
            switchName: "switch1",
            switchType: "mt_unique_rule",
            bindingObjectApiName: "testObj",
            switchStatus: true,
            tenantId: user.tenantId,
            objectDescribeApiName: MtSwitch.MT_SWITCH_OBJ_API_NAME,
            deleted: false
        )

        when:
        switchCacheDBService.modifySwitch(user, switchInfos)

        then:
        1 * repository.findBy(user, _, MtSwitch.class) >> []
        1 * repository.bulkUpsert(user, [expectedSwitch]) >> [expectedSwitch]
        1 * repository.bulkInvalidAndDelete(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 modifySwitch 方法，验证当开关存在时能够正确更新开关状态
     */
    @Unroll
    def "modifySwitchTest_Update"() {
        given:
        def switchInfo = SwitchInfo.of(user.tenantId, "mt_unique_rule", "switch1", "testObj", true)
        def existingSwitch = new MtSwitch(
            id: "1",
            switchName: "switch1",
            switchType: "mt_unique_rule",
            bindingObjectApiName: "testObj",
            switchStatus: false,
            tenantId: user.tenantId,
            objectDescribeApiName: MtSwitch.MT_SWITCH_OBJ_API_NAME,
            deleted: false
        )
        def expectedSwitch = new MtSwitch(
            id: "1",
            switchName: "switch1",
            switchType: "mt_unique_rule",
            bindingObjectApiName: "testObj",
            switchStatus: true,
            tenantId: user.tenantId,
            objectDescribeApiName: MtSwitch.MT_SWITCH_OBJ_API_NAME,
            deleted: false
        )

        when:
        switchCacheDBService.modifySwitch(user, [switchInfo])

        then:
        1 * repository.findBy(user, _, MtSwitch.class) >> [existingSwitch]
        1 * repository.bulkUpsert(user, [expectedSwitch]) >> [expectedSwitch]
        1 * repository.bulkInvalidAndDelete(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 modifySwitch 方法，验证当设置开关状态为 false 时能够正确删除开关
     */
    @Unroll
    def "modifySwitchTest_Delete"() {
        given:
        def switchInfo = SwitchInfo.of(user.tenantId, "mt_unique_rule", "switch1", "testObj", false)
        def existingSwitch = new MtSwitch(
            id: "1",
            switchName: "switch1",
            switchType: "mt_unique_rule",
            bindingObjectApiName: "testObj",
            switchStatus: true,
            tenantId: user.tenantId,
            objectDescribeApiName: MtSwitch.MT_SWITCH_OBJ_API_NAME,
            deleted: false
        )
        def expectedDeleteSwitch = new MtSwitch(
            id: "1",
            switchName: "switch1",
            switchType: "mt_unique_rule",
            bindingObjectApiName: "testObj",
            switchStatus: true,
            tenantId: user.tenantId,
            objectDescribeApiName: MtSwitch.MT_SWITCH_OBJ_API_NAME,
            deleted: true
        )

        when:
        switchCacheDBService.modifySwitch(user, [switchInfo])

        then:
        1 * repository.findBy(user, _, MtSwitch.class) >> [existingSwitch]
        1 * repository.bulkUpsert(_, _)
        1 * repository.bulkInvalidAndDelete(user, [expectedDeleteSwitch])
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试 buildSearchQuery 方法，验证能够正确构建查询条件
     */
    @Unroll
    def "buildSearchQueryTest"() {
        given:
        def tenantId = "74255"
        def switchType = "mt_unique_rule"
        def objectApiName = "testObj"
        def switchNames = Sets.newHashSet("switch1", "switch2")

        when:
        Query query = Whitebox.invokeMethod(switchCacheDBService, "buildSearchQuery", tenantId, switchType, objectApiName, switchNames) as Query

        then:
        query != null
        query.limit == 2000
        List<IFilter> filters = query.getFilters()
        filters.find { it.getFieldName() == "tenant_id" && it.getFieldValues().contains(tenantId) }
        filters.find { it.getFieldName() == "is_deleted" && it.getFieldValues().contains(String.valueOf(DELETE_STATUS.NORMAL.value)) }
        filters.find { it.getFieldName() == "switch_type" && it.getFieldValues().contains(switchType) }
        filters.find { it.getFieldName() == "binding_object_api_name" && it.getFieldValues().contains(objectApiName) }
        filters.find { it.getFieldName() == "switch_name" && it.getFieldValues().containsAll(Lists.newArrayList(switchNames)) }
    }
} 