package com.facishare.paas.appframework.metadata.mask

import spock.lang.Specification

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/12/22
 */
class MaskFieldEncryptServiceImplTest extends Specification {

    def "test serialize"() {
        when:
        def encryptData = MaskFieldEncryptServiceImpl.EncryptData.of(value, fieldType)
        then:
        def str = encryptData.serialize()
        println(str)
        def data = MaskFieldEncryptServiceImpl.EncryptData.deserialize(str)

        def formatValue = data.formatValue()
        println(formatValue)
        println(formatValue.class)

        formatValue == value
        where:
        value         | fieldType
        "123.00"      | "number"
        1671679747917 | "date_time"
        ["1000"]      | "employee"
    }

    def "test encode and decode"() {
        when:
        MaskFieldEncryptService maskFieldEncryptService = new MaskFieldEncryptServiceImpl()
        then:
        def encodeValue = maskFieldEncryptService.encode(value)
        println(encodeValue)
        def decodeValue = maskFieldEncryptService.decode(encodeValue)
        println(decodeValue)
        value == decodeValue

        where:
        value << ["1111", "6e756d6265f2033132332e30b0", "646174655f74696de5099a9d85fea661", "656d706c6f7965e501006a6176612e7574696c2e41727261794c6973f4820300313030b0"]
    }
}
