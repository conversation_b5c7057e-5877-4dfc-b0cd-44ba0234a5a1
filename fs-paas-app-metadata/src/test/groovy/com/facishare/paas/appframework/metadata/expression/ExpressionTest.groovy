package com.facishare.paas.appframework.metadata.expression

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.metadata.ObjectDescribeExt
import com.facishare.paas.appframework.util.JsonUtils
import com.facishare.paas.expression.ExpressionService
import com.facishare.paas.expression.ExpressionServiceImpl
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

import static org.powermock.api.mockito.PowerMockito.when

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([AppFrameworkConfig])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
@SuppressStaticInitializationFor("com.facishare.crm.userdefobj.DefObjConstants")
class ExpressionTest extends Specification {

    ExpressionService expressionService = Mock(ExpressionServiceImpl)
    IObjectDescribe describe = Mock(ObjectDescribe)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    def "测试doCalculate币种分支逻辑"() {
        given:
        def expression = Expression.builder()
                .expression('AND(NOT(ISNULL(null)),null>100)')
                .fieldName(fieldName)
                .isDefaultValue(true)
                .describe(describe)
                .build()
        expression.setLatestExchangeRate('1.0')
        expression.setFunctionalCurrency('1.0')
        expression.setLatestExchangeRateVersion('8')
        when:
        expressionService.evaluate(_ as String, _ as Map) >> evaluateResult
        then:
        result == expression.doCalculate(expressionService)
        where:
        fieldName                  | evaluateResult || result
        'mc_exchange_rate'         | ''             || '1.0'
        'mc_exchange_rate_version' | ''             || '8'
        'mc_functional_currency'   | ''             || '1.0'
    }

    def "测试doCalculate: Decimal"() {
        given:
        def expression = Expression.builder()
                .expression('AND(NOT(ISNULL(null)),null>100)')
//                .fieldName(fieldName)
                .decimalPlaces(decimalPlaces)
                .describe(describe)
                .build()
        when:
        expressionService.evaluate(_ as String, _ as Map) >> evaluateResult
        then:
        result == expression.doCalculate(expressionService)
        where:
        decimalPlaces | evaluateResult || result
        2             | 1.276          || 1.28
        2             | 1.271          || 1.27
        3             | 1.271          || 1.271
    }

    def "测试doCalculate: 异常"() {
        given:
        def expression = Expression.builder()
                .expression('AND(NOT(ISNULL(null)),null>100)')
                .describe(describe)
                .build()
        when:
        expressionService.evaluate(_ as String, _ as Map) >> { throw new NullPointerException() }
        then:
        expression.doCalculate(expressionService) == null
    }

    def "测试doCalculate: 不同字段类型"() {
        given:
        def expression = Expression.builder()
                .expression('AND(NOT(ISNULL(null)),null>100)')
//                .fieldName(fieldName)
                .returnType(returnType)
                .describe(describe)
                .build()
        when:
        expressionService.evaluate(_ as String, _ as Map) >> evaluateResult
        then:
        result == expression.doCalculate(expressionService)
        where:
        returnType                 | evaluateResult || result
        IFieldType.ARRAY           | ['a', 'b']     || '[a, b]'
        IFieldType.EMPLOYEE        | ['a', 'b']     || ['a', 'b']
        IFieldType.EMPLOYEE_MANY   | ['a', 'b']     || ['a', 'b']
        IFieldType.DEPARTMENT      | ['a', 'b']     || ['a', 'b']
        IFieldType.DEPARTMENT_MANY | ['a', 'b']     || ['a', 'b']
    }

    def "compileCheck: 不同字段类型"() {
        given:
        def object_2I7ye__c = ObjectDescribeExt.of(JsonUtils.readFileFromResources('describe/object_2I7ye__c.json') as Map)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        def expression = Expression.builder()
                .describe(object_2I7ye__c)
                .expression(expressionValue)
                .fieldName('field_GQi6n__c')
                .returnType(IFieldType.TEXT)
                .build()
        expression.init()
        expression.getGlobalVariables().forEach { x -> x.setDataType(ExpressionDataType.DateTime) }
        when:
        when(AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant('74255')).thenReturn(true)
        expression.compileCheck(expressionService, '74255')
        then:
        def e = thrown(expectException)
        where:
        expressionValue                         || expectException
        '$field_nTe6N__c$+$currentDateTime__g$' || ExpressionCalculateException
        '$field_nTe6N__c$+DATETIMEVALUE(NOW())' || ExpressionCalculateException
    }
}
