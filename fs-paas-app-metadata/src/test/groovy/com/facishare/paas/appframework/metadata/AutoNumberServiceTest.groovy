package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.autonumber.UpsertAutoNumberInfo
import com.facishare.paas.appframework.metadata.cache.RedissonService
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.metadata.api.describe.AutoNumber
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IAutoNumberService
import com.facishare.paas.metadata.impl.AutoNumberInfo
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.service.impl.UdefFunctionService
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfigFactory
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Created by zhaooju on 2022/5/30
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([AutoNumberExt.ConditionEnum, ConfigFactory, UdobjGrayConfig, RequestContextManager, ActionContextExt])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*"])
class AutoNumberServiceTest extends Specification {
    @Shared
    def autoNumberJson = '''{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1631013280362,"is_encrypted":false,"default_value":"01","serial_number":2,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"","_id":"613749a0a9265600017c61ff","is_index_field":false,"status":"new","help_text":""}'''
    @Shared
    def newAutoNumberJson = '''{"type":"auto_number","define_type":"system","api_name":"name","label":"主属性","help_text":"","is_required":true,"is_unique":true,"is_active":true,"is_index":true,"status":"new","condition":"NONE","prefix":"","postfix":"","serial_number":2,"start_number":1,"warning":"编号的前缀和后缀可以插入日期和替代变量，每新建一条数据，序列号递增1","default_value":"01","auto_number_type":"function","func_api_name":"id_generate_test__c","is_readonly":true,"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","default_to_zero":false,"input_mode":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"create_time":1631013280362,"is_encrypted":false,"_id":"613749a0a9265600017c61ff","is_index_field":false}'''
    @Shared
    def nameFieldJson = '''{"type":"text","define_type":"system","api_name":"name","label":"主属性","help_text":"","is_required":true,"is_unique":true,"is_active":true,"is_index":true,"status":"new","default_value":"","default_is_expression":false,"default_to_zero":false,"max_length":100,"input_mode":"","describe_api_name":"object_zCM8z__c","prefix":"","auto_adapt_places":false,"pattern":"","description":"","start_number":1,"postfix":"","label_r":"主属性","is_single":false,"index_name":"name","auto_number_type":"function","create_time":1631013280362,"is_encrypted":false,"serial_number":2,"condition":"NONE","func_api_name":"","_id":"613749a0a9265600017c61ff","is_index_field":false}'''
    @Shared
    def describeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1659927673697,"create_time":1631013280256,"description":"","last_modified_by":"1000","display_name":"fj-自增编号函数","created_by":"1000","version":80,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_zCM8z__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"gxB","_id":"613749a0a9265600017c6212","fields":{"tenant_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279639,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"613749a0a9265600017c620a","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new"},"data_own_organization":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1647329873833,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属组织","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6230425204a720000162e4d5","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","help_text":""},"lock_user":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279764,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"613749a0a9265600017c620c","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1631013280007,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"613749a0a9265600017c620f","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_zCM8z__c","is_index":false,"create_time":1631013280256,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279764,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","label":"作废前生命状态","type":"text","field_num":7,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"613749a0a9265600017c620d","is_index_field":false,"label_r":"作废前生命状态","is_single":false,"index_name":"t_1","status":"new","max_length":256},
                "object_describe_api_name":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1631013279997,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"613749a0a9265600017c6201","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280008,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"613749a0a9265600017c6210","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"owner":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013279996,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"613749a0a9265600017c6200","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_zCM8z__c","is_index":true,"create_time":1631013280256,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_zCM8z__c","auto_adapt_places":false,"is_unique":false,"description":"锁定状态","type":"select_one","is_required":false,"options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","label_r":"锁定状态","is_single":false,"index_name":"s_2","is_index":true,"is_active":true,"create_time":1631013279763,"is_encrypted":false,"default_value":"0","label":"锁定状态","field_num":2,"is_need_convert":false,"api_name":"lock_status","_id":"613749a0a9265600017c620b","is_index_field":false,"config":{},"status":"new","help_text":""},"create_time":{"describe_api_name":"object_zCM8z__c","is_index":true,"create_time":1631013280256,"description":
                "create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_zCM8z__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"select_one","is_required":false,"options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","label_r":"生命状态","is_single":false,"index_name":"s_3","is_index":true,"is_active":true,"create_time":1631013280004,"is_encrypted":false,"default_value":"normal","label":"生命状态","field_num":3,"is_need_convert":false,"api_name":"life_status","_id":"613749a0a9265600017c6208","is_index_field":false,"config":{},"status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_zCM8z__c","is_index":false,"create_time":1631013280256,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280006,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"613749a0a9265600017c620e","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_dc1c8__c":{"describe_api_name":"object_zCM8z__c","prefix":"{yyyy}-{mm}-{dd}","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"","is_single":false,"index_name":"s_7","is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1659343776379,"is_encrypted":false,"default_value":"{yyyy}-{mm}-{dd}01","serial_number":2,"label":"自增编号","field_num":11,"condition":"NONE","api_name":"field_dc1c8__c","func_api_name":"","_id":"62e793a0ae36a20001a4195b","is_index_field":false,"status":"new","help_text":""},"record_type":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280003,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"613749a0a9265600017c6207","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_zCM8z__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1631013280005,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"613749a0a9265600017c6209","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"62df9efc11f2f20001ace35c","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"released","help_text":""},"name":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"function","is_active":true,"create_time":1631013280362,"is_encrypted":false,"serial_number":2,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"id_generate__c","_id":"613749a0a9265600017c61ff","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280009,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","label":"汇率版本","type":"text","field_num":8,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"613749a0a9265600017c6211","is_index_field":false,"label_r":"汇率版本","is_single":false,"index_name":"t_2","status":"new","help_text":"","max_length":256},"_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
    @Shared
    def describeNameChangedJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1659927673697,"create_time":1631013280256,"description":"","last_modified_by":"1000","display_name":"fj-自增编号函数","created_by":"1000","version":80,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_zCM8z__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"gxB","_id":"613749a0a9265600017c6212","fields":{"tenant_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279639,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"613749a0a9265600017c620a","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new"},"data_own_organization":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1647329873833,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属组织","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6230425204a720000162e4d5","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","help_text":""},"lock_user":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279764,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"613749a0a9265600017c620c","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1631013280007,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"613749a0a9265600017c620f","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_zCM8z__c","is_index":false,"create_time":1631013280256,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279764,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","label":"作废前生命状态","type":"text","field_num":7,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"613749a0a9265600017c620d","is_index_field":false,"label_r":"作废前生命状态","is_single":false,"index_name":"t_1","status":"new","max_length":256},
                "object_describe_api_name":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1631013279997,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"613749a0a9265600017c6201","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280008,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"613749a0a9265600017c6210","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"owner":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013279996,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"613749a0a9265600017c6200","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_zCM8z__c","is_index":true,"create_time":1631013280256,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_zCM8z__c","auto_adapt_places":false,"is_unique":false,"description":"锁定状态","type":"select_one","is_required":false,"options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","label_r":"锁定状态","is_single":false,"index_name":"s_2","is_index":true,"is_active":true,"create_time":1631013279763,"is_encrypted":false,"default_value":"0","label":"锁定状态","field_num":2,"is_need_convert":false,"api_name":"lock_status","_id":"613749a0a9265600017c620b","is_index_field":false,"config":{},"status":"new","help_text":""},"create_time":{"describe_api_name":"object_zCM8z__c","is_index":true,"create_time":1631013280256,"description":
                "create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_zCM8z__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"select_one","is_required":false,"options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","label_r":"生命状态","is_single":false,"index_name":"s_3","is_index":true,"is_active":true,"create_time":1631013280004,"is_encrypted":false,"default_value":"normal","label":"生命状态","field_num":3,"is_need_convert":false,"api_name":"life_status","_id":"613749a0a9265600017c6208","is_index_field":false,"config":{},"status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_zCM8z__c","is_index":false,"create_time":1631013280256,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280006,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"613749a0a9265600017c620e","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_dc1c8__c":{"describe_api_name":"object_zCM8z__c","prefix":"{yyyy}-{mm}-{dd}","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"","is_single":false,"index_name":"s_7","is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1659343776379,"is_encrypted":false,"default_value":"{yyyy}-{mm}-{dd}01","serial_number":2,"label":"自增编号","field_num":11,"condition":"NONE","api_name":"field_dc1c8__c","func_api_name":"","_id":"62e793a0ae36a20001a4195b","is_index_field":false,"status":"new","help_text":""},"record_type":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280003,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"613749a0a9265600017c6207","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_zCM8z__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1631013280005,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"613749a0a9265600017c6209","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"62df9efc11f2f20001ace35c","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"released","help_text":""},"name":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"function","is_active":true,"create_time":1631013280362,"is_encrypted":false,"serial_number":2,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"id_generate_test__c","_id":"613749a0a9265600017c61ff","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280009,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","label":"汇率版本","type":"text","field_num":8,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"613749a0a9265600017c6211","is_index_field":false,"label_r":"汇率版本","is_single":false,"index_name":"t_2","status":"new","help_text":"","max_length":256},"_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
    @Shared
    def newDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1659927673697,"create_time":1631013280256,"description":"","last_modified_by":"1000","display_name":"fj-自增编号函数","created_by":"1000","version":80,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_zCM8z__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"gxB","_id":"613749a0a9265600017c6212","fields":{"tenant_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"lock_rule":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279639,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"613749a0a9265600017c620a","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_1","status":"new"},"data_own_organization":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1647329873833,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属组织","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6230425204a720000162e4d5","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_2","status":"released","help_text":""},"lock_user":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279764,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"613749a0a9265600017c620c","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new"},"mc_exchange_rate":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1631013280007,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"613749a0a9265600017c620f","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_zCM8z__c","is_index":false,"create_time":1631013280256,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013279764,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","label":"作废前生命状态","type":"text","field_num":7,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"613749a0a9265600017c620d","is_index_field":false,"label_r":"作废前生命状态","is_single":false,"index_name":"t_1","status":"new","max_length":256},
                "object_describe_api_name":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1631013279997,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"613749a0a9265600017c6201","is_index_field":false,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280008,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"613749a0a9265600017c6210","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"owner":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013279996,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"613749a0a9265600017c6200","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_zCM8z__c","is_index":true,"create_time":1631013280256,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_zCM8z__c","auto_adapt_places":false,"is_unique":false,"description":"锁定状态","type":"select_one","is_required":false,"options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","label_r":"锁定状态","is_single":false,"index_name":"s_2","is_index":true,"is_active":true,"create_time":1631013279763,"is_encrypted":false,"default_value":"0","label":"锁定状态","field_num":2,"is_need_convert":false,"api_name":"lock_status","_id":"613749a0a9265600017c620b","is_index_field":false,"config":{},"status":"new","help_text":""},"create_time":{"describe_api_name":"object_zCM8z__c","is_index":true,"create_time":1631013280256,"description":
                "create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_zCM8z__c","auto_adapt_places":false,"is_unique":false,"description":"","type":"select_one","is_required":false,"options":[{"font_color":"#2a304d","label":"未生效","value":"ineffective"},{"font_color":"#2a304d","label":"审核中","value":"under_review"},{"font_color":"#2a304d","label":"正常","value":"normal"},{"font_color":"#2a304d","label":"变更中","value":"in_change"},{"font_color":"#2a304d","label":"作废","value":"invalid"}],"define_type":"package","label_r":"生命状态","is_single":false,"index_name":"s_3","is_index":true,"is_active":true,"create_time":1631013280004,"is_encrypted":false,"default_value":"normal","label":"生命状态","field_num":3,"is_need_convert":false,"api_name":"life_status","_id":"613749a0a9265600017c6208","is_index_field":false,"config":{},"status":"new","help_text":""},"last_modified_by":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"created_by":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_zCM8z__c","is_index":false,"create_time":1631013280256,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280006,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"613749a0a9265600017c620e","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""},"field_dc1c8__c":{"describe_api_name":"object_zCM8z__c","prefix":"{yyyy}-{mm}-{dd}","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"","is_single":false,"index_name":"s_7","is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1659343776379,"is_encrypted":false,"default_value":"{yyyy}-{mm}-{dd}01","serial_number":2,"label":"自增编号","field_num":11,"condition":"NONE","api_name":"field_dc1c8__c","func_api_name":"","_id":"62e793a0ae36a20001a4195b","is_index_field":false,"status":"new","help_text":""},"record_type":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280003,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"font_color":"#2a304d","api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"613749a0a9265600017c6207","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_zCM8z__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"成员员工","define_type":"package","is_unique":false,"label":"成员员工","is_single":true,"type":"employee","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"成员角色","define_type":"package","is_unique":false,"label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"成员权限类型","define_type":"package","is_unique":false,"label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1631013280005,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"613749a0a9265600017c6209","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"data_own_department":{"describe_api_name":"object_zCM8z__c","is_index":true,"is_active":true,"create_time":1631013280256,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"62df9efc11f2f20001ace35c","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"released","help_text":""},"name":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"function","is_active":true,"create_time":1631013280362,"is_encrypted":false,"default_value":"","serial_number":2,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"","_id":"613749a0a9265600017c61ff","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280009,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","label":"汇率版本","type":"text","field_num":8,"is_required":false,"api_name":"mc_exchange_rate_version","define_type":"package","_id":"613749a0a9265600017c6211","is_index_field":false,"label_r":"汇率版本","is_single":false,"index_name":"t_2","status":"new","help_text":"","max_length":256},"_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
    @Shared
    def changeDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1659927673697,"create_time":1631013280256,"description":"","last_modified_by":"1000","display_name":"fj-自增编号函数","created_by":"1000","version":80,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_zCM8z__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"gxB","_id":"613749a0a9265600017c6212","fields":{"field_dc1c8__c":{"describe_api_name":"object_zCM8z__c","prefix":"{yyyy}-{mm}-{dd}","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"Num-","is_single":false,"index_name":"s_7","is_index":true,"auto_number_type":"normal","is_active":true,"create_time":1659343776379,"is_encrypted":false,"default_value":"{yyyy}-{mm}-{dd}02","serial_number":2,"label":"自增编号","field_num":11,"condition":"NONE","api_name":"field_dc1c8__c","func_api_name":"","_id":"62e793a0ae36a20001a4195b","is_index_field":false,"status":"new","help_text":""},"name":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"function","is_active":true,"create_time":1631013280362,"is_encrypted":false,"serial_number":2,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"id_generate_test1__c","_id":"613749a0a9265600017c61ff","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''
    @Shared
    def twoFunctionAutoNumDescribeJson = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1659927673697,"create_time":1631013280256,"description":"","last_modified_by":"1000","display_name":"fj-自增编号函数","created_by":"1000","version":80,"is_open_display_name":false,"index_version":200,"icon_index":0,"is_deleted":false,"api_name":"object_zCM8z__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"gxB","_id":"613749a0a9265600017c6212","fields":{"field_dc1c8__c":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"s_7","max_length":100,"is_index":true,"auto_number_type":"function","is_active":true,"create_time":1631013280362,"is_encrypted":false,"serial_number":2,"label":"主属性","condition":"NONE","api_name":"field_dc1c8__c","func_api_name":"id_generate_test2__c","_id":"62e793a0ae36a20001a4195b","is_index_field":false,"status":"new","help_text":""},"name":{"describe_api_name":"object_zCM8z__c","default_is_expression":false,"prefix":"","auto_adapt_places":false,"pattern":"","is_unique":true,"description":"","start_number":1,"type":"auto_number","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","postfix":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"auto_number_type":"function","is_active":true,"create_time":1631013280362,"is_encrypted":false,"serial_number":2,"label":"主属性","condition":"NONE","api_name":"name","func_api_name":"id_generate_test1__c","_id":"613749a0a9265600017c61ff","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_zCM8z__c","is_index":false,"is_active":true,"create_time":1631013280256,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200}},"release_version":"6.4","actions":{}}'''

    def setup() {
        PowerMockito.mockStatic(ConfigFactory)
        def configFactory = Mock(IConfigFactory)
        PowerMockito.when(ConfigFactory.getInstance()).thenReturn(configFactory)
        PowerMockito.mockStatic(RequestContextManager)
        RequestContext requestContext = Mock(RequestContext)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        requestContext.getUser() >> User.systemUser("74255")
        PowerMockito.mockStatic(UdobjGrayConfig)
        PowerMockito.mockStatic(ActionContextExt)
    }

    def "test incrementNumber null2create"() {
        given:
        def describe = Stub(IObjectDescribe)
        def autoNumber = Stub(AutoNumber)
        def user = Stub(User)

        def numberService = Stub(IAutoNumberService)
        def redissonService = Mock(RedissonService)
        AutoNumberService autoNumberService = new AutoNumberService(autoNumberService: numberService, redissonService: redissonService)

        numberService.addNtoAutoNumber(tenantId, describeApiName, autoNumberApiName, _, _, _, counter) >> null
        user.getTenantId() >> tenantId
        describe.getApiName() >> describeApiName
        autoNumber.getApiName() >> autoNumberApiName

        def autoNumberInfo = Stub(AutoNumberInfo)
        autoNumberInfo.getCurrentNumber() >> 2
        UdobjGrayConfig.isAllow(UdobjGrayConfigKey.AUTO_NUMBER_LOCK_GRAY, _) >> false


        when:
        def result = autoNumberService.incrementNumber(describe, autoNumber, counter, startNumber, condition, user, count, steppingNumber)
        then:
        numberService.create(_) >> autoNumberInfo
        result == except
        where:
        counter | startNumber | condition                        | count | steppingNumber | tenantId | describeApiName   | autoNumberApiName   || except
        "123"   | 1           | AutoNumberExt.ConditionEnum.NONE | 2     | 1              | "7"      | "describeApiName" | "autoNumberApiName" || 2
    }

    def "test incrementNumber reset startNumber"() {
        given:
        def describe = Stub(IObjectDescribe)
        def autoNumber = Stub(AutoNumber)
        def user = Stub(User)
        def condition = PowerMockito.mock(AutoNumberExt.ConditionEnum)
        PowerMockito.when(condition.checkTime(Mockito.anyLong(), Mockito.anyLong())).thenReturn(false)

        def numberService = Stub(IAutoNumberService)
        AutoNumberService autoNumberService = new AutoNumberService(autoNumberService: numberService)

        def autoNumberInfo = Stub(AutoNumberInfo)
        user.getTenantId() >> tenantId
        describe.getApiName() >> describeApiName
        autoNumber.getApiName() >> autoNumberApiName
        numberService.addNtoAutoNumber(tenantId, describeApiName, autoNumberApiName, "", "", 10L, counter) >> autoNumberInfo


        def numberInfo = Stub(AutoNumberInfo)
        numberInfo.getCurrentNumber() >> 2
        when:
        def result = autoNumberService.incrementNumber(describe, autoNumber, counter, startNumber, condition, user, count, steppingNumber)
        then:
        numberService.setNtoAutoNumber(tenantId, describeApiName, autoNumberApiName, _, _, _, counter) >> numberInfo
        result == except
        where:
        counter | startNumber | count | steppingNumber | tenantId | describeApiName   | autoNumberApiName   || except
        "123"   | 1           | 2     | 1              | "7"      | "describeApiName" | "autoNumberApiName" || 2
    }

    def "test changeAutoNumberFunctionStatus"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe(JSON.parseObject(describeJson))
        def fieldDescribe = FieldDescribeFactory.newInstance(autoNumberFiledJson)
        def fieldList = []
        fieldList << fieldDescribe
        def numberService = Stub(IAutoNumberService)
        UdefFunctionService udefFunctionService = Mock(UdefFunctionService)
        FunctionLogicService functionLogicService = Mock(FunctionLogicService)
        LogService logService = Mock(LogService)
        AutoNumberService autoNumberService = new AutoNumberService(autoNumberService: numberService, "udefFunctionService": udefFunctionService,
                "functionLogicService": functionLogicService, "logService": logService)
        PowerMockito.when(ActionContextExt.of(Mockito.any(User.class), Mockito.any(RequestContext.class))).thenReturn(Mock(ActionContextExt))
        udefFunctionService.findFunctionByApiName(_, _, _) >> Mock(IUdefFunction)
        when:
        autoNumberService.changeAutoNumberFunctionStatus(objectDescribe, fieldList)
        then:
        delCount * functionLogicService.deleteRelation(_, _, _, _)
        saveCount * functionLogicService.saveRelation(_, _)
        where:
        autoNumberFiledJson | delCount || saveCount
        autoNumberJson      | 1        || 0
        newAutoNumberJson   | 1        || 1
        nameFieldJson       | 0        || 0
    }

    def "test diffAutoNumberField"() {
        given:
        def numberService = Stub(IAutoNumberService)
        UdefFunctionService udefFunctionService = Mock(UdefFunctionService)
        FunctionLogicService functionLogicService = Mock(FunctionLogicService)
        LogService logService = Mock(LogService)
        def redissonService = Mock(RedissonService)
        AutoNumberService autoNumberService = new AutoNumberService(autoNumberService: numberService, "udefFunctionService": udefFunctionService,
                "functionLogicService": functionLogicService, "logService": logService, "redissonService": redissonService)
        PowerMockito.when(ActionContextExt.of(Mockito.any(User.class), Mockito.any(RequestContext.class))).thenReturn(Mock(ActionContextExt))
        udefFunctionService.findFunctionByApiName(_, _, _) >> Mock(IUdefFunction)
        when:
        autoNumberService.diffAutoNumberField(new ObjectDescribe(JSON.parseObject(newDescribe)), new ObjectDescribe(JSON.parseObject(oldDescribe)))
        then:
        delCount * functionLogicService.deleteRelation(_, _, _, _)
        saveCount * functionLogicService.saveRelation(_, _)
        // describeJson name为text  newDescribeJson为function autoNumber
        where:
        newDescribe     | oldDescribe                    | delCount | saveCount
        describeJson    | newDescribeJson                | 0        | 1
        newDescribeJson | describeJson                   | 1        | 0
        describeJson    | describeNameChangedJson        | 1        | 1
        describeJson    | changeDescribeJson             | 1        | 1
        describeJson    | twoFunctionAutoNumDescribeJson | 2        | 1
    }

    @Unroll
    def "test upsertAutoNumberInfo updateAutoNumberInfo_1:#updateAutoNumberInfo_1, updateAutoNumberInfo_2:#updateAutoNumberInfo_2, createAutoNumberInfo:#createAutoNumberInfo"() {
        given: "A non-null AutoNumberInfo returned by doUpdate.upsert"
        def redissonService = Mock(RedissonService)
        AutoNumberService autoNumberService = new AutoNumberService("redissonService": redissonService)

        UpsertAutoNumberInfo doUpdate = Mock(UpsertAutoNumberInfo)
        UpsertAutoNumberInfo doCreate = Mock(UpsertAutoNumberInfo)
        AutoNumberInfo autoNumberInfo = Mock(AutoNumberInfo)

        when: "upsertAutoNumberInfo is called"
        doUpdate.upsert() >> updateAutoNumberInfo_1 >> updateAutoNumberInfo_2
        doCreate.upsert() >> createAutoNumberInfo

        def result = autoNumberService.upsertAutoNumberInfo(doUpdate, doCreate, "lockKey")

        then: "The result should indicate that creation was not needed and the returned AutoNumberInfo is the one from doUpdate.upsert"
        result.isCreate() == expectCreate

        where:
        updateAutoNumberInfo_1 | updateAutoNumberInfo_2 | createAutoNumberInfo || expectCreate
        Mock(AutoNumberInfo)   | _                      | _                    || false
        null                   | Mock(AutoNumberInfo)   | _                    || false
        null                   | null                   | Mock(AutoNumberInfo) || true
    }
}
