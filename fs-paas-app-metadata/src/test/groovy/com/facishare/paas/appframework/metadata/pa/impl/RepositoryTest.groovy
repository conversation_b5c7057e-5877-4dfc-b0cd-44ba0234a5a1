package com.facishare.paas.appframework.metadata.pa.impl

import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.ActionContextExt
import com.facishare.paas.appframework.metadata.FilterExt
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl
import com.facishare.paas.appframework.metadata.ObjectConvertRuleServiceImpl
import com.facishare.paas.appframework.metadata.OrderByExt
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule
import com.facishare.paas.appframework.metadata.search.Query
import com.facishare.paas.appframework.metadata.search.SearchQuery
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl
import com.facishare.paas.metadata.api.DELETE_STATUS
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.search.Operator
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox
import spock.lang.Specification

import static com.facishare.paas.appframework.metadata.repository.model.MtConvertRule.DETAIL_OBJECT
import static com.facishare.paas.appframework.metadata.repository.model.MtConvertRule.IS_ACTIVE
import static com.facishare.paas.appframework.metadata.repository.model.MtConvertRule.MD_TYPE

class RepositoryTest extends Specification {
    Repository repository

    MetaDataServiceImpl metaDataService = Mock()
    ObjectConvertRuleServiceImpl objectConvertRuleService = Mock()

    def setup() {
        repository = new Repository("metaDataService": metaDataService)
    }


    def "test bulkUpsert"() {
        given:
        def user = User.systemUser("74255")
        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setTenantId('74255')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')
        rule.setDeleted(false)

        IObjectData objectData = new ObjectData()
        objectData.fromJsonString(rule.toJsonString())
        objectData.set('is_active', true)
        def rules = [Whitebox.invokeMethod(objectConvertRuleService, 'convertConvertRule', rule) as MtConvertRule]
        when:
        metaDataService.bulkUpsertObjectData(*_) >> [objectData]
        def list = repository.bulkUpsert(user, rules) as List<MtConvertRule>
        then:
        list.size() == rules.size()
        list.get(0).getId() == rules.get(0).getId()
    }

    def "test bulkUpdate"() {
        given:
        def user = User.systemUser("74255")
        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setTenantId('74255')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')
        rule.setDeleted(false)

        IObjectData objectData = new ObjectData()
        objectData.fromJsonString(rule.toJsonString())
        objectData.set('is_active', true)
        def rules = [Whitebox.invokeMethod(objectConvertRuleService, 'convertConvertRule', rule) as MtConvertRule]

        IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                .setIsSpecifyTime(Boolean.TRUE)
                .setSkipRemoveNotChangeData(Boolean.TRUE)
                .getContext();
        when:
        metaDataService.batchUpdate(*_) >> [objectData]
        def list = repository.bulkUpdate(context, user, rules) as List<MtConvertRule>
        then:
        list.size() == rules.size()
        list.get(0).getId() == rules.get(0).getId()
    }

    def "test bulkDelete"() {
        given:
        def user = User.systemUser("74255")
        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setTenantId('74255')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')
        rule.setDeleted(false)

        IObjectData objectData = new ObjectData()
        objectData.fromJsonString(rule.toJsonString())
        objectData.set('is_active', true)
        def rules = [Whitebox.invokeMethod(objectConvertRuleService, 'convertConvertRule', rule) as MtConvertRule]
        when:
        metaDataService.bulkDeleteDirect(*_) >> [objectData]
        def list = repository.bulkDelete(user, rules) as List<MtConvertRule>
        then:
        list.size() == rules.size()
        list.get(0).getId() == rules.get(0).getId()
    }

    def "test findBy"() {
        given:
        def user = User.systemUser("74255")
        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setTenantId('74255')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')
        rule.setDeleted(false)

        IObjectData objectData = new ObjectData()
        objectData.fromJsonString(rule.toJsonString())
        objectData.set('is_active', true)
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter(),
                FilterExt.of(Operator.NEQ, MD_TYPE, String.valueOf(DETAIL_OBJECT)).getFilter())
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        def query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, true)))
                .limit(2000)
                .offset(0)
                .build()
        def queryResult = new QueryResult()
        queryResult.setData([objectData])
        when:
        metaDataService.findBySearchQuery(*_) >> queryResult
        def list = repository.findBy(user, query, MtConvertRule.class)
        then:
        list.size() == 1
    }

    def "test bulkUpdateByFields"() {
        given:
        def user = User.systemUser("74255")
        def rule = new ObjectMappingRuleInfo()
        rule.setId('64ae89744ccb540001c29b83')
        rule.setTenantId('74255')
        rule.setRuleName('主从转换-『可用』')
        rule.setRuleApiName('rule_clP2T__c')
        rule.setTargetApiName('object_ssu7t__c')
        rule.setSourceApiName('object_3BbVb__c')
        rule.setTargetDisplayName('zj-主')
        rule.setSourceDisplayName('zxf-主')
        rule.setDefineType('custom')
        rule.setLastModifiedBy('1000')
        rule.setLastModifiedTime(1709954277914)
        rule.setCreatedBy('1000')
        rule.setCreateTime(1689160052061)
        rule.setStatus(0)
        rule.set('close_field_api_name', 'true')
        rule.set('associated_field_api_name', 'field_O1lbo__c')
        rule.set('where_type', 'function')
        rule.setDeleted(false)

        IObjectData objectData = new ObjectData()
        objectData.fromJsonString(rule.toJsonString())
        objectData.set('is_active', true)
        when:
        def rules = [Whitebox.invokeMethod(objectConvertRuleService, 'convertConvertRule', rule) as MtConvertRule]
        metaDataService.batchUpdateByFields(*_) >> {
            objectData.set('is_active', false)
            return [objectData]
        }
        def list = repository.bulkUpdateByFields(user, rules, Lists.newArrayList(IS_ACTIVE))
        then:
        list.size() == 1
        !(list.get(0) as MtConvertRule).isActive()
    }

    def "test findCountOnly"() {
        given:
        def user = User.systemUser("74255")
        when:
        metaDataService.findBySearchQuery(*_) >> {
            def queryResult = new QueryResult()
            queryResult.setData([])
            queryResult.setTotalNumber(1)
            return queryResult
        }
        def query = Query.builder()
                .searchQuery(SearchQueryImpl.filters(Lists.newArrayList()))
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, true)))
                .limit(2000)
                .offset(0)
                .build()
        def count = repository.findCountOnly(user, query, MtConvertRule.class)
        then:
        count == 1
    }
}
