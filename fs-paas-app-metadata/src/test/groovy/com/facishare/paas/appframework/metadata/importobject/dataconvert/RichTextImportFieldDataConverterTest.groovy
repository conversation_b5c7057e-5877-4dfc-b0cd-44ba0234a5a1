package com.facishare.paas.appframework.metadata.importobject.dataconvert

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.RichTextFieldDescribe
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class RichTextImportFieldDataConverterTest extends Specification {

    RichTextImportFieldDataConverter richTextImportFieldDataConverter

    def setup() {
        richTextImportFieldDataConverter = new RichTextImportFieldDataConverter()
    }

    def "test ConvertFieldData"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("object_test__c")
        IObjectData objectData = new ObjectData()
        objectData.set('field_rich_text__c', '你说："你好，世界！"')
        IFieldDescribe fieldDescribe = new RichTextFieldDescribe()
        fieldDescribe.setApiName('field_rich_text__c')
        def user = User.systemUser('74255')
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(_, _) >> true
        when:
        def result = richTextImportFieldDataConverter.convertFieldData(objectData, objectDescribe, fieldDescribe, user)
        then:
        noExceptionThrown()
        println(JSON.toJSONString(result.getValue()).contains('你说："你好，世界！"'))
        JSON.toJSONString(result.getValue()) == "{\"__xt\":{\"__json\":{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"text\":\"你说：\\\"你好，世界！\\\"\",\"type\":\"text\"}]}]}},\"text\":\"你说：\\\"你好，世界！\\\"\"}"
    }
}
