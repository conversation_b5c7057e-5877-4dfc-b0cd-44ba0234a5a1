package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt
import com.facishare.paas.appframework.metadata.DuplicatedSearchService
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import org.powermock.reflect.Whitebox

import com.facishare.paas.I18N
import com.facishare.paas.I18NClientResource
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.common.service.PlatServiceProxy
import com.facishare.paas.appframework.common.service.dto.OrganizationStatus
import com.facishare.paas.appframework.common.util.ObjectAction
import com.facishare.paas.appframework.common.util.TenantUtil
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.config.OptionalFeaturesService
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.flow.ApprovalFlowService
import com.facishare.paas.appframework.flow.StageThrusterProxy
import com.facishare.paas.appframework.flow.dto.HasStageInstance
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.license.dto.ModuleParaLicense
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.cache.RedissonService
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig
import com.facishare.paas.appframework.metadata.config.util.ProductCategoryConfigUtils
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraService
import com.facishare.paas.appframework.metadata.objects.DescribeChangeEvent
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectEnableJobVerify
import com.facishare.paas.appframework.metadata.publicobject.verify.VerifyResult
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService
import com.facishare.paas.appframework.metadata.util.DefObjUtil
import com.facishare.paas.appframework.metadata.util.ProductUtil
import com.facishare.paas.appframework.privilege.DataPrivilegeService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.common.util.UdobjConstants
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.checker.CheckerResult
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.describe.*
import com.facishare.paas.metadata.api.service.*
import com.facishare.paas.metadata.dao.pg.entity.metadata.DuplicatedSearch
import com.facishare.paas.metadata.exception.ErrorCode
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.*
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringUtil
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.redisson.api.RLock
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Specification
import spock.lang.Unroll

/**
 * https://github.com/powermock/powermock/issues/643
 * <AUTHOR>
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([AppFrameworkConfig, ConfigFactory, ActionContextExt, RequestContextManager, FsGrayRelease, RequestUtil,
        UdobjGrayConfig, ProductCategoryConfigUtils, ProductUtil, SpringUtil, I18N, TenantUtil, I18nClient,
        I18NClientResource, ChangeOrderConfig])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
class DescribeLogicServiceImplTest extends Specification {

    DescribeLogicServiceImpl describeLogicServiceImpl

    IObjectDescribeService objectDescribeService
    FieldRelationCalculateService fieldRelationCalculateService
    OptionSetLogicService optionSetLogicService
    DescribeChangeEvent describeChangeEvent
    JobScheduleService jobScheduleService
    LayoutLogicService layoutLogicService
    LogService logService
    IFieldService fieldService
    AutoNumberService autoNumberService
    ObjectMappingService objectMappingService
    FunctionLogicService functionLogicService
    LicenseService licenseService
    RedissonService redissonService
    FunctionPrivilegeService functionPrivilegeService
    DefObjUtil defObjUtil = new DefObjUtil()
    AutoNumberLogicService autoNumberLogicService
    RecordTypeLogicService recordTypeLogicService
    MultiCurrencyLogicService multiCurrencyLogicService
    IMetadataMultiCurrencyService metadataMultiCurrencyService
    DataPrivilegeService dataPrivilegeService
    DefObjLifeStatusService defObjLifeStatusService
    ConfigService configService
    IObjectFieldDescribeExtService fieldDescribeExtService
    DescribeLogicService describeLogicService
    MetaDataService metaDataService
    PlatServiceProxy platServiceProxy
    IObjectDataProxyService dataProxyService
    StageThrusterProxy proxy
    CustomButtonService customButtonService
    ButtonLogicService buttonLogicService
    SelectFieldDependenceLogicService selectFieldDependenceLogicService
    ISearchTemplateService searchTemplateService
    ApprovalFlowService approvalFlowService
    OptionalFeaturesService optionalFeaturesService
    MetadataServiceException metadataServiceException
    PublicObjectEnableJobVerify publicObjectEnableJobVerify
    FieldBackgroundExtraService fieldBackgroundExtraService
    ApplicationLayeredGrayService applicationLayeredGrayService
    DuplicatedSearchService duplicatedSearchService
    OrgService orgService
    def tenantId = "78057"
    def objectApiName = "object_zxf__c"


    def setup() {
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.mockStatic(FsGrayRelease.class)
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(ProductCategoryConfigUtils.class)
        PowerMockito.mockStatic(SpringUtil.class)
        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.spy(ActionContextExt.class)
        def grayReleaseBiz = PowerMockito.mock(FsGrayReleaseBiz.class)
        def requestContext = PowerMockito.mock(RequestContext.class)
        def applicationContext = PowerMockito.mock(ApplicationContext.class)
        PowerMockito.mock(ConfigService.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(FsGrayRelease.getInstance(Mockito.anyString())).thenReturn(grayReleaseBiz)
        PowerMockito.when(UdobjGrayConfig.isAllow(Mockito.anyString(), Mockito.anyString())).thenReturn(true)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)

        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(I18NClientResource.class)
        PowerMockito.when(I18N.text(Mockito.anyString())).thenReturn("mocked text")
        PowerMockito.mockStatic(ProductUtil.class)
        PowerMockito.mockStatic(TenantUtil.class)
        PowerMockito.mockStatic(RequestUtil.class)

        // 修复doNothing调用
        PowerMockito.doNothing().when(ProductCategoryConfigUtils.class, "productCategoryGrayHandle", Mockito.anyString(), Mockito.anyList());

        PowerMockito.mockStatic(ChangeOrderConfig.class)
        metadataServiceException = new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "测试异常情况")

        objectDescribeService = PowerMockito.mock(IObjectDescribeService.class)
        fieldRelationCalculateService = Mock(FieldRelationCalculateService)
        optionSetLogicService = Mock(OptionSetLogicService)
        describeChangeEvent = Mock(DescribeChangeEvent)
        jobScheduleService = Mock(JobScheduleService)
        layoutLogicService = Mock(LayoutLogicService)
        logService = Mock(LogService)
        fieldService = Mock(IFieldService)
        autoNumberService = PowerMockito.mock(AutoNumberService.class)
        objectMappingService = Mock(ObjectMappingService)
        functionLogicService = Mock(FunctionLogicService)
        licenseService = Mock(LicenseService)
        redissonService = Mock(RedissonService)
        autoNumberLogicService = PowerMockito.mock(AutoNumberLogicService.class)
        functionPrivilegeService = Mock(FunctionPrivilegeService)
        recordTypeLogicService = Mock(RecordTypeLogicService)
        multiCurrencyLogicService = Mock(MultiCurrencyLogicService)
        dataPrivilegeService = Mock(DataPrivilegeService)
        defObjLifeStatusService = PowerMockito.mock(DefObjLifeStatusService.class)
        metadataMultiCurrencyService = Mock(IMetadataMultiCurrencyService)
        configService = Mock(ConfigService)
        fieldDescribeExtService = Mock(IObjectFieldDescribeExtService)
        describeLogicService = Mock(DescribeLogicService)
        metaDataService = Mock(MetaDataService)
        platServiceProxy = Mock(PlatServiceProxy)
        dataProxyService = Mock(IObjectDataProxyService)
        proxy = Mock(StageThrusterProxy)
        customButtonService = Mock(CustomButtonService)
        buttonLogicService = Mock(ButtonLogicService)
        selectFieldDependenceLogicService = Mock(SelectFieldDependenceLogicService)
        searchTemplateService = Mock(ISearchTemplateService)
        approvalFlowService = Mock(ApprovalFlowService)
        optionalFeaturesService = Mock(OptionalFeaturesService)
        publicObjectEnableJobVerify = PowerMockito.mock(PublicObjectEnableJobVerify.class)
        fieldBackgroundExtraService = Mock(FieldBackgroundExtraService)
        orgService = Mock(OrgService)
        duplicatedSearchService = Mock(DuplicatedSearchService)
        applicationLayeredGrayService = Mock(ApplicationLayeredGrayService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(
                objectDescribeService: objectDescribeService,
                fieldRelationCalculateService: fieldRelationCalculateService,
                optionSetLogicService: optionSetLogicService,
                describeChangeEvent: describeChangeEvent,
                jobScheduleService: jobScheduleService,
                layoutLogicService: layoutLogicService,
                logService: logService,
                fieldService: fieldService,
                objectMappingService: objectMappingService,
                autoNumberService: autoNumberService,
                functionLogicService: functionLogicService,
                licenseService: licenseService,
                redissonService: redissonService,
                functionPrivilegeService: functionPrivilegeService,
                defObjUtil: defObjUtil,
                autoNumberLogicService: autoNumberLogicService,
                recordTypeLogicService: recordTypeLogicService,
                multiCurrencyLogicService: multiCurrencyLogicService,
                dataPrivilegeService: dataPrivilegeService,
                defObjLifeStatusService: defObjLifeStatusService,
                metadataMultiCurrencyService: metadataMultiCurrencyService,
                configService: configService,
                fieldDescribeExtService: fieldDescribeExtService,
                describeLogicService: describeLogicService,
                metaDataService: metaDataService,
                platServiceProxy: platServiceProxy,
                dataProxyService: dataProxyService,
                proxy: proxy,
                customButtonService: customButtonService,
                buttonLogicService: buttonLogicService,
                selectFieldDependenceLogicService: selectFieldDependenceLogicService,
                searchTemplateService: searchTemplateService,
                approvalFlowService: approvalFlowService,
                optionalFeaturesService: optionalFeaturesService,
                publicObjectEnableJobVerify: publicObjectEnableJobVerify,
                orgService: orgService,
                fieldBackgroundExtraService: fieldBackgroundExtraService,
                applicationLayeredGrayService: applicationLayeredGrayService,
                duplicatedSearchService: duplicatedSearchService
        )
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)


    }

    def "test findByExample exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findByExample(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findByExample(tenantId, Maps.newHashMap())
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test queryAllSlave2Master"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.queryAllSlave2Master(Mockito.anyString(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.queryAllSlave2Master(User.systemUser(tenantId))
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test fillRefObjFieldSupportDisplayName"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("object_L2k2I__c")
        objectDescribe.setTenantId(tenantId)
        objectDescribe.setIsOpenDisplayName(true)
        IFieldDescribe fieldDescribe = new ObjectReferenceFieldDescribe()
        fieldDescribe.setApiName("field_jdhze__c")
        fieldDescribe.setTargetApiName("object_L2k2I__c")

        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
        when:
        PowerMockito.when(AppFrameworkConfig.isSupportDisplayNameFieldEnterprise(Mockito.anyString())).thenReturn(true)
        PowerMockito.when(AppFrameworkConfig.isSupportDisplayNameField(Mockito.anyString())).thenReturn(true)
        PowerMockito.when(objectDescribeService.findDescribeListByApiNamesWithoutFields(Mockito.anyString(), Mockito.anyList(), Mockito.any())).thenReturn(Lists.newArrayList(objectDescribe))
        describeLogicServiceImpl.fillRefObjFieldSupportDisplayName(User.systemUser(tenantId), objectDescribe)
        then:
        noExceptionThrown()
    }

    def "test touchDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)
        when:
        describeLogicServiceImpl.touchDescribe(objectDescribe)
        then:
        noExceptionThrown()
    }


    def "test updateLookupRoles"() {
        given:
        when:
        describeLogicServiceImpl.updateLookupRoles(tenantId, objectApiName, "", true, "")
        then:
        noExceptionThrown()
    }

    def "test updateLookupRolesList"() {
        given:

        when:
        describeLogicServiceImpl.updateLookupRolesList(tenantId, Lists.newArrayList(objectApiName), "", true, "")
        then:
        noExceptionThrown()
    }

    def "test checkIfHasApprovalFlowDefinition exception"() {
        given:
        when:
        PowerMockito.when(AppFrameworkConfig.isInMasterDetailApprovalWhiteList(Mockito.anyString())).thenReturn(isGray)
        approvalFlowService.hasApprovalFlowDefinitions(_, _) >> hasApprovalFlowDefinitions
        describeLogicServiceImpl.checkIfHasApprovalFlowDefinition(User.systemUser(tenantId), objectApiName)
        then:
        thrown(ValidateException.class)
        where:
        tenantId | objectApiName     | isGray | hasApprovalFlowDefinitions
        "78057"  | "object_L2k2I__c" | false  | true
    }

    def "test enableDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        def checkerResult = new CheckerResult()
        checkerResult.setPass(true)
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService.enableDescribe(Mockito.any(), Mockito.any())).thenReturn(checkerResult)
        describeLogicServiceImpl.enableDescribe(User.systemUser(tenantId), objectApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | objectApiName     | isPass
        "78057"  | "object_L2k2I__c" | true
    }

    def "test findDescribeListWithoutFields 2arg"() {
        given:

        when:
        describeLogicServiceImpl.findDescribeListWithoutFields(tenantId, Lists.newArrayList(objectApiName));
        then:
        noExceptionThrown()
    }

    def "test isMasterObject"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.checkReferenceExist(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.isMasterObject(tenantId, objectApiName)
        then:
        noExceptionThrown()
    }

    def "test updateSfaDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeApiName)
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        def layoutArg = layout
        layoutArg.setLayoutType(layoutType)
        layoutArg.setId(IdGenerator.get())
        def listLayout = layout
        listLayout.setId(IdGenerator.get())
        listLayout.setLayoutType(layoutType)
        when:
        PowerMockito.when(objectDescribeService.replace(Mockito.any(), Mockito.anyBoolean(), Mockito.any())).thenReturn(objectDescribe)
        layoutLogicService.findLayoutByObjectApiName(_, _) >> layoutResult
        PowerMockito.when(defObjLifeStatusService.generateDefaultFieldDescribe(Mockito.anyString())).thenReturn(new TextFieldDescribe())
        describeLogicServiceImpl.updateSfaDescribe(User.systemUser(tenantId), objectDescribe.toJsonString(), layoutArg.toJsonString(), listLayout.toJsonString(), detailApiName, functionsMapping)
        then:
        1 == 1
        where:
        tenantId | describeApiName   | fieldApiName     | layoutType | fieldDescribe             | layout       | layoutResult | detailApiName | functionsMapping
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "detail"   | new TextFieldDescribe()   | new Layout() | [layout]     | ""            | [:]
        "78057"  | "object_L2k2I__c" | "life_status"    | "detail"   | new TextFieldDescribe()   | new Layout() | []           | ""            | [:]
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | "list"     | new SignInFieldDescribe() | new Layout() | [layout]     | ""            | [:]
    }

    def "test findDescribeExtraByRenderType"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setTenantId(tenantId)
        when:
        PowerMockito.when(UdobjGrayConfig.isAllow(Mockito.any(), Mockito.anyString())).thenReturn(isGray)
        describeLogicService.findDescribeExtra(_, _) >> [:]
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> OptionalFeaturesSwitchDTO.builder().build()
        describeLogicServiceImpl.findDescribeExtraByRenderType(User.systemUser(tenantId), objectDescribe, detailDescribeList, renderType, describeCacheable)
        then:
        noExceptionThrown()
        where:
        isGray | tenantId | detailDescribeList | renderType                                        | describeCacheable
        true   | "78057"  | []                 | DescribeExpansionRender.RenderType.Designer       | true
        false  | "78057"  | []                 | DescribeExpansionRender.RenderType.Detail         | true
        false  | "78057"  | []                 | DescribeExpansionRender.RenderType.DescribeLayout | true
    }

    def "test findDescribeExtra"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)
        objectDescribe.setTenantId(tenantId)
        when:
        PowerMockito.when(UdobjGrayConfig.isAllow(Mockito.any(), Mockito.anyString())).thenReturn(isGray)
        describeLogicService.findDescribeExtra(_, _) >> [:]
        optionalFeaturesService.findOptionalFeaturesSwitch(_, _) >> OptionalFeaturesSwitchDTO.builder().build()
        describeLogicServiceImpl.findDescribeExtra(User.systemUser(tenantId), objectDescribe)
        then:
        noExceptionThrown()
        where:
        isGray | tenantId
        true   | "78057"
        false  | "78057"
    }

    def "test disableDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        def checkerResult = new CheckerResult()
        checkerResult.setPass(true)

        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService.disableDescribe(Mockito.any(), Mockito.any())).thenReturn(checkerResult)
        describeLogicServiceImpl.disableDescribe(User.systemUser(tenantId), objectApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | objectApiName     | isPass
        "78057"  | "object_L2k2I__c" | true
    }

    def "test deleteDescribeDirect"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        def checkerResult = new CheckerResult()
        checkerResult.setPass(true)

        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService.deleteDirect(Mockito.any())).thenReturn(checkerResult)
        describeLogicServiceImpl.deleteDescribeDirect(User.systemUser(tenantId), objectApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | objectApiName     | isPass
        "78057"  | "object_L2k2I__c" | true
    }

    def "test deleteDescribeDirect exception"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        def checkerResult = new CheckerResult()
        checkerResult.setPass(isPass)

        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService.deleteDirect(Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.deleteDescribeDirect(User.systemUser(tenantId), objectApiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | objectApiName     | isPass
        "78057"  | "object_L2k2I__c" | true
        "78057"  | "object_L2k2I__c" | false
    }

    def "test deleteDescribeDirect paas"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        def checkerResult = new CheckerResult()
        checkerResult.setPass(isPass)

        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService.deleteDirect(Mockito.any())).thenReturn(checkerResult)
        describeLogicServiceImpl.deleteDescribeDirect(User.systemUser(tenantId), objectApiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | objectApiName     | isPass
        "78057"  | "object_L2k2I__c" | false
    }

    def "test findObjectWithoutCopyUseThreadLocalCache"() {
        given:
        when:
        describeLogicServiceImpl.findObjectWithoutCopyUseThreadLocalCache(tenantId, objectApiName)
        then:
        noExceptionThrown()
    }

    def "test findDetailDescribesCreateWithMasterWithoutCopyIfGray"() {
        given:
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(Mockito.anyString())).thenReturn(isGray)
        describeLogicServiceImpl.findDetailDescribesCreateWithMasterWithoutCopyIfGray(tenantId, objectApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | objectApiName     | isGray
        "78057"  | "object_L2k2I__c" | true
        "78057"  | "object_L2k2I__c" | false
    }

    def "test checkFieldsForCalc"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeAPIName)

        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setApiName(fieldApiName)
        iFieldDescribe.setActive(isActive)
        iFieldDescribe.set("decimal_places", 0)
        def fieldDescribes = []
        fieldDescribes << iFieldDescribe
        objectDescribe.setFieldDescribes(fieldDescribes)

        IFieldDescribe iFieldDescribeArg = fieldDescribeArg
        iFieldDescribeArg.setApiName(fieldApiNameAfter)
        iFieldDescribeArg.set("decimal_places", countValue)
        def fieldDescribeList = []
        fieldDescribeList << iFieldDescribeArg

        when:
        PowerMockito.when(TenantUtil.isCalcCriteria(Mockito.any())).thenReturn(isGary)
        PowerMockito.when(TenantUtil.isMaxCount(Mockito.any())).thenReturn(isGary)
        PowerMockito.when(TenantUtil.needCalcCriteria(Mockito.anyString())).thenReturn(isGary)
        PowerMockito.when(AppFrameworkConfig.getMaxCountObjectData()).thenReturn((long) count)
        PowerMockito.when(AppFrameworkConfig.getMaxCountHistoryObjectData()).thenReturn((long) count)
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        metaDataService.countObjectDataFromDB(_, _, _) >> countValue
        fieldRelationCalculateService.notEndJobForEach(_, _, _) >> fieldDescribeList
        describeLogicServiceImpl.checkFieldsForCalc(User.systemUser(tenantId), describeAPIName, factor, fieldInfo, fieldDescribeList)

        then:
        noExceptionThrown()

        where:
        tenantId | describeAPIName   | fieldApiName     | fieldApiNameAfter | countValue | factor                                                    | fieldInfo | fieldDescribe              | fieldDescribeArg           | isGary | count | isActive
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 1          | Lists.newArrayList()                                      | true      | new FormulaFieldDescribe() | new FormulaFieldDescribe() | false  | 0     | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 2          | Lists.newArrayList("calc_job", "count_max", "open_modal") | true      | new FormulaFieldDescribe() | new FormulaFieldDescribe() | false  | 10    | false
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 2          | Lists.newArrayList("calc_job", "count_max", "open_modal") | true      | new FormulaFieldDescribe() | new FormulaFieldDescribe() | false  | 10    | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 3          | Lists.newArrayList("calc_job", "count_max", "open_modal") | true      | new FormulaFieldDescribe() | new FormulaFieldDescribe() | false  | 10    | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_2dhze__c"  | 3          | Lists.newArrayList("calc_job", "count_max", "open_modal") | true      | new FormulaFieldDescribe() | new FormulaFieldDescribe() | true   | 10    | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 1          | Lists.newArrayList()                                      | true      | new CountFieldDescribe()   | new CountFieldDescribe()   | false  | 0     | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 2          | Lists.newArrayList("calc_job", "count_max", "open_modal") | true      | new CountFieldDescribe()   | new CountFieldDescribe()   | false  | 10    | false
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 2          | Lists.newArrayList("calc_job", "count_max", "open_modal") | true      | new CountFieldDescribe()   | new CountFieldDescribe()   | false  | 10    | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_jdhze__c"  | 3          | Lists.newArrayList("calc_job", "count_max", "open_modal") | false     | new CountFieldDescribe()   | new CountFieldDescribe()   | true   | 10    | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "field_2dhze__c"  | 3          | Lists.newArrayList("calc_job", "count_max", "open_modal") | false     | new CountFieldDescribe()   | new CountFieldDescribe()   | true   | 10    | true
    }


    def "test queryDisplayNameByApiNames"() {
        given:
        when:
        describeLogicServiceImpl.queryDisplayNameByApiNames(tenantId, objectList)
        then:
        noExceptionThrown()
        where:
        tenantId | objectList
        "78057"  | Lists.newArrayList("object_L2k2I__c")
        "78057"  | Lists.newArrayList()
    }


    def "test findDescribeExtraList"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        IObjectFieldExtra objectFieldExtra = new ObjectFieldExtra()
        objectFieldExtra.setFieldApiName(fieldApiName)
        objectFieldExtra.setShowTag(isShowTag)


        when:
        PowerMockito.when(UdobjGrayConfig.isAllow(Mockito.anyString(), Mockito.anyString())).thenReturn(isGray)
        fieldDescribeExtService.findDescribeExtByDescribeApiName(_, _) >> [objectApiName: [objectFieldExtra]]
        describeLogicServiceImpl.findDescribeExtra(User.systemUser(tenantId), Lists.newArrayList(objectDescribe))
        // describeLogicService.findDescribeExtra(User.systemUser(tenantId),objectDescribe)
        then:
        noExceptionThrown()

        where:
        tenantId | objectApiName     | fieldApiName     | isShowTag | isGray
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | true      | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | false     | false
    }


    def "test updateDescribeExtra"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(objectApiName)

        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setApiName(fieldApiName)

        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))

        IObjectFieldExtra objectFieldExtra = new ObjectFieldExtra()
        objectFieldExtra.setFieldApiName(fieldApiName)
        objectFieldExtra.setShowTag(isShowTag)

        def describeExtra = DescribeExtra.of(objectDescribe, Lists.newArrayList(objectFieldExtra))

        when:
        PowerMockito.when(UdobjGrayConfig.isAllow(Mockito.anyString(), Mockito.anyString())).thenReturn(isGary)
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        fieldDescribeExtService.upsert(_, _, _) >> [objectFieldExtra]
        describeLogicServiceImpl.updateDescribeExtra(User.systemUser(tenantId), describeExtra)

        then:
        noExceptionThrown()

        where:
        tenantId | objectApiName     | fieldApiName     | isShowTag | isGary
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | true      | false
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | false     | true
    }


    def "test findObjectsWithoutCopyIfGray"() {
        given:
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(isGray)
        describeLogicServiceImpl.findObjectsWithoutCopyIfGray(tenantId, apiNames)
        then:
        noExceptionThrown()
        where:
        tenantId | apiNames            | isGray
        "78057"  | ["object_L2k2I__c"] | true
        "78057"  | []                  | true
        "78057"  | ["object_L2k2I__c"] | false
    }

    def "test findObjectsWithoutCopyIfGray exception"() {
        given:
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(true)
        PowerMockito.when(objectDescribeService.findDescribeListByApiNames(Mockito.anyString(), Mockito.anyList(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObjectsWithoutCopyIfGray(tenantId, apiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiName
        "78057"  | ["object_L2k2I__c"]
    }

    def "test isRelatedListFormSupportObject"() {
        given:
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName(apiName)

        IFieldDescribe ifieldDescribe = fieldDescribe
        ifieldDescribe.setApiName(relatedFieldName)
        if (ifieldDescribe instanceof IObjectReferenceField) {
            ifieldDescribe.setTargetApiName(targetApiName)
        }

        def fieldDescribeList = []
        fieldDescribeList << ifieldDescribe
        describe.setFieldDescribes(fieldDescribeList)
        when:

        describeLogicServiceImpl.isRelatedListFormSupportObject(User.systemUser(tenantId), targetApiName, describe, relatedFieldName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName           | isGray | targetApiName     | relatedFieldName | fieldDescribe
        "78057"  | "object_L2k2I__c" | true   | "object_L2k2I__c" | "field_jdhze__c" | new ObjectReferenceFieldDescribe()
        "78057"  | "object_L2k2I__c" | false  | "object_L2k2I__c" | "field_jdhze__c" | new MasterDetailFieldDescribe()
    }

    def "test findAssociationDescribes"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(apiName)
        when:
        describeLogicServiceImpl.findAssociationDescribes(tenantId, objectDescribe)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findAssociationDescribesWithoutCopyIfGray"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(apiName)
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(isGray)
        describeLogicServiceImpl.findAssociationDescribesWithoutCopyIfGray(tenantId, objectDescribe)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName           | isGray
        "78057"  | "object_L2k2I__c" | true
        "78057"  | "object_L2k2I__c" | false
    }

    def "test findObject"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        when:
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        describeLogicServiceImpl.findObject(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObject notFind"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null)
        describeLogicServiceImpl.findObject(tenantId, apiName)
        then:
        thrown(ObjectDefNotFoundError.class)
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObject exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObject(tenantId, apiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }


    def "test findObjectWithoutCopyIfGray"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(isGray)
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        describeLogicServiceImpl.findObjectWithoutCopyIfGray(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName           | isGray
        "78057"  | "object_L2k2I__c" | true
        "78057"  | "object_L2k2I__c" | false
    }


    def "test findObjectWithoutCopyIfGray notFind"() {
        given:
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(true)
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null)
        describeLogicServiceImpl.findObjectWithoutCopyIfGray(tenantId, apiName)
        then:
        thrown(ObjectDefNotFoundError.class)
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObjectWithoutCopyIfGray exception"() {
        given:
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(true)
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObjectWithoutCopyIfGray(tenantId, apiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }


    def "test findObjectUseThreadLocalCache"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        when:
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        describeLogicServiceImpl.findObjectUseThreadLocalCache(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObjectById"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        when:
        objectDescribeService.findById(_, _, _) >> objectDescribe
        describeLogicServiceImpl.findObjectById(tenantId, id)
        then:
        noExceptionThrown()
        where:
        tenantId | id
        "78057"  | "604212f21ba81700019281e1"
    }

    def "test findObjectById notFind"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findById(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null)
        describeLogicServiceImpl.findObjectById(tenantId, id)
        then:
        thrown(ObjectDefNotFoundError.class)
        where:
        tenantId | id
        "78057"  | "604212f21ba81700019281e1"
    }

    def "test findObjectById exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findById(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObjectById(tenantId, id)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | id
        "78057"  | "604212f21ba81700019281e1"
    }

    def "test findObjectIncludeDeleted"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        when:
        objectDescribeService.findByTenantIdAndObjectDescribeApiNameIncludedDeletedData(_, _, _) >> objectDescribe
        describeLogicServiceImpl.findObjectIncludeDeleted(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObjectIncludeDeleted notFind"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndObjectDescribeApiNameIncludedDeletedData(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null)
        describeLogicServiceImpl.findObjectIncludeDeleted(tenantId, apiName)
        then:
        thrown(ObjectDefNotFoundError.class)
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObjectIncludeDeleted exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndObjectDescribeApiNameIncludedDeletedData(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObjectIncludeDeleted(tenantId, apiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findObjectByRevision"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        when:
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        describeLogicServiceImpl.findObjectByRevision(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findAllObjectsByTenantId"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        LicenseService licenseService = Mock(LicenseService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService, licenseService: licenseService)
        def describeList = []
        describeList << objectDescribe
        when:
        licenseService.queryAvailableObject(_) >> []
        objectDescribeService.findByExample(_, _, _) >> describeList
        objectDescribeService.findDescribeListByApiNames(_, _, _) >> describeList
        describeLogicServiceImpl.findAllObjectsByTenantId(tenantId, describeDefineType, isOnlyActivate, isExcludeDetailObj, isExcludeDetailWithMasterCreated, isAsc, sourceInfo)
        then:
        noExceptionThrown()
        where:
        tenantId | describeDefineType | isOnlyActivate | isExcludeDetailObj | isExcludeDetailWithMasterCreated | isAsc | sourceInfo
        "78057"  | "package"          | true           | true               | true                             | true  | "object_management"
        "78057"  | "package"          | true           | true               | true                             | true  | "object_field_api"
        "78057"  | "package"          | true           | true               | true                             | true  | ""
    }


    def "test findObjectsByTenantId"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        def describeList = []
        describeList << objectDescribe
        PowerMockito.when(ProductUtil.isSpuOpen(Mockito.anyString())).thenReturn(false)
        PowerMockito.when(ProductUtil.findUnusableProductApiNames(Mockito.any())).thenReturn(["test"])
        when:
        licenseService.queryAvailableObject(_) >> []
        objectDescribeService.findByExample(_, _, _) >> describeList
        objectDescribeService.findDescribeListByApiNames(_, _, _) >> describeList
        configService.queryTenantConfigs(_, _) >> [:]
        describeLogicServiceImpl.findObjectsByTenantId(tenantId, describeDefineType, isOnlyActivate, isExcludeDetailObj, isExcludeDetailWithMasterCreated, isAsc, sourceInfo)
        then:
        noExceptionThrown()
        where:
        tenantId | describeDefineType | isOnlyActivate | isExcludeDetailObj | isExcludeDetailWithMasterCreated | isAsc | sourceInfo
        "78057"  | "package"          | true           | true               | true                             | true  | "object_management"
        "78057"  | "package"          | true           | true               | true                             | true  | "object_field_api"
        "78057"  | "package"          | true           | true               | true                             | true  | ""
    }

    def "test findObjectsByTenantIdIgnoreDefineTypeAndSourceType"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findByExample(_, _, _) >> describeList
        describeLogicServiceImpl.findObjectsByTenantId(tenantId, isOnlyActivate, isExcludeDetailObj, isExcludeDetailWithMasterCreated, isAsc)
        then:
        noExceptionThrown()
        where:
        tenantId | isOnlyActivate | isExcludeDetailObj | isExcludeDetailWithMasterCreated | isAsc
        "78057"  | true           | true               | true                             | true
    }

    def "test findDescribeList"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findByExample(_, _, _) >> describeList
        describeLogicServiceImpl.findDescribeList(tenantId, isOnlyUdObj, packageName, includeUnActived, isExcludeDetail, isExcludeDetailWithMasterCreated, sourceInfo)
        then:
        noExceptionThrown()
        where:
        tenantId | isOnlyUdObj | packageName | includeUnActived | isExcludeDetail | isExcludeDetailWithMasterCreated | sourceInfo
        "78057"  | true        | ""          | true             | true            | true                             | ""
    }

    def "test findObjects"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findDescribeListByApiNames(_, _, _) >> describeList
        describeLogicServiceImpl.findObjects(tenantId, apiNames)
        then:
        noExceptionThrown()
        where:
        tenantId | apiNames
        "78057"  | ["object_L2k2I__c"]
    }

    def "test findObjects exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findDescribeListByApiNames(Mockito.anyString(), Mockito.anyList(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObjects(tenantId, apiNames)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiNames
        "78057"  | ["object_L2k2I__c"]
    }


    def "test findObjectList"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findDescribeListByApiNames(_, _, _) >> describeList
        describeLogicServiceImpl.findObjectList(tenantId, apiNames)
        then:
        noExceptionThrown()
        where:
        tenantId | apiNames
        "78057"  | ["object_L2k2I__c"]
        "78057"  | []
    }

    def "test findObjectList exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findDescribeListByApiNames(Mockito.anyString(), Mockito.anyList(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findObjectList(tenantId, apiNames)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiNames
        "78057"  | ["object_L2k2I__c"]
    }

    def "test findDisplayNameByApiNames"() {
        given:
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def displayNameByApiNames = [:]
        when:
        objectDescribeService.queryDisplayNameByApiNames(_, _, _) >> displayNameByApiNames
        describeLogicServiceImpl.findDisplayNameByApiNames(tenantId, apiNames)
        then:
        noExceptionThrown()
        where:
        tenantId | apiNames
        "78057"  | ["object_L2k2I__c"]
        "78057"  | []
    }

    def "test useableDescribeCount"() {
        given:
        def count = 10
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        LicenseService licenseService = Mock(LicenseService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService, licenseService: licenseService)
        when:
        objectDescribeService.getCountByExample(_, _) >> count
        licenseService.batchGetModuleLicenses(_, _) >> [:]
        describeLogicServiceImpl.useableDescribeCount(User.systemUser("10000"))
        then:
        noExceptionThrown()
    }

    def "test findDetailDescribes"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        LicenseService licenseService = Mock(LicenseService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService, licenseService: licenseService)
        def describeList = []
        describeList << objectDescribe
        when:
        licenseService.queryAvailableObject(_) >> []
        objectDescribeService.findDetailDescribeList(_, _, _, _) >> describeList
        describeLogicServiceImpl.findDetailDescribes(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findSimpleDetailDescribeList"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        LicenseService licenseService = Mock(LicenseService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService, licenseService: licenseService)
        def describeList = []
        describeList << objectDescribe
        when:
        licenseService.queryAvailableObject(_) >> []
        objectDescribeService.findSimpleDetailDescribeList(_, _, _, _) >> describeList
        describeLogicServiceImpl.findSimpleDetailDescribes(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }


    def "test findDetailDescribesCreateWithMaster"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        LicenseService licenseService = Mock(LicenseService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService, licenseService: licenseService)
        def describeList = []
        describeList << objectDescribe
        when:
        licenseService.queryAvailableObject(_) >> []
        objectDescribeService.findDetailDescribeList(_, _, _, _) >> describeList
        describeLogicServiceImpl.findDetailDescribesCreateWithMaster(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findDescribeListWithoutFields"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findDescribeListByApiNamesWithoutFields(_, _, _) >> describeList
        objectDescribeService.findDetailDescribeApiNamesWithMasterCreated(_, _, _) >> []
        describeLogicServiceImpl.findDescribeListWithoutFields(tenantId, apiNames, checkDetailObjectButton)
        then:
        noExceptionThrown()
        where:
        tenantId | apiNames            | checkDetailObjectButton
        "78057"  | ["object_L2k2I__c"] | true
        "78057"  | ["object_L2k2I__c"] | false
        "78057"  | []                  | false
    }

    def "test findDescribeListWithoutFields exception"() {
        given:
        when:
        PowerMockito.when(objectDescribeService.findDescribeListByApiNamesWithoutFields(Mockito.anyString(), Mockito.anyList(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findDescribeListWithoutFields(tenantId, apiNames, checkDetailObjectButton)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        tenantId | apiNames            | checkDetailObjectButton
        "78057"  | ["object_L2k2I__c"] | true
    }

    def "test findRelatedDescribesByListPrivilege"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        Mock(FunctionPrivilegeService)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findReferenceList(_, _, _) >> describeList
        describeLogicServiceImpl.findRelatedDescribesByListPrivilege(User.systemUser(tenantId), apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }

    def "test findRelatedDescribesWithoutCopy exception"() {
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(Mockito.anyString())).thenReturn(isGray)
        PowerMockito.when(objectDescribeService.findReferenceList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenThrow(metadataServiceException)
        describeLogicServiceImpl.findRelatedDescribesWithoutCopyIfGray(tenantId, objectApiName)
        then:
        thrown(MetaDataBusinessException.class)
        where:
        isGray << [true, false]
    }

    def "test findRelatedDescribesWithoutCopyIfGray or findDetailDescribesWithoutCopyIfGray"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        Mock(FunctionPrivilegeService)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        PowerMockito.when(AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)).thenReturn(isGray)
        licenseService.queryAvailableObject(_) >> []
        objectDescribeService.findReferenceList(_, _, _) >> describeList
        describeLogicServiceImpl.findRelatedDescribesWithoutCopyIfGray(tenantId, apiName)
        describeLogicServiceImpl.findDetailDescribesWithoutCopyIfGray(tenantId, apiName)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName           | isGray
        "78057"  | "object_L2k2I__c" | true
        "78057"  | "object_L2k2I__c" | false
    }

    def "test findLookupDescribes"() {
        given:
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        Mock(FunctionPrivilegeService)
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        def describeList = []
        describeList << objectDescribe
        when:
        objectDescribeService.findLookupDescribeList(_, _, _, _, _) >> describeList
        describeLogicServiceImpl.findLookupDescribes(tenantId, apiName, true)
        then:
        noExceptionThrown()
        where:
        tenantId | apiName
        "78057"  | "object_L2k2I__c"
    }


    def "test findRelatedFieldsByListPrivilege"() {
        given:
        IFieldDescribe fieldDescribeArg = fieldDescribe
        fieldDescribeArg.setApiName(fieldApiName)
        fieldDescribeArg.setDescribeApiName(targetApiName)
        when:
        functionPrivilegeService.funPrivilegeCheck(_, _, _) >> []
        PowerMockito.when(objectDescribeService.findRelatedFields(Mockito.anyString(), Mockito.anyString())).thenReturn(fieldList)
        licenseService.queryAvailableObject(_) >> [targetApiName]
        describeLogicServiceImpl.findRelatedFieldsByListPrivilege(User.systemUser(tenantId), targetApiName)
        then:
        1 == 1
        where:
        tenantId | targetApiName     | fieldApiName     | fieldDescribe             | fieldList
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new TextFieldDescribe()   | Lists.newArrayList(fieldDescribe)
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | new SignInFieldDescribe() | []
    }


    def "test findDescribeByPrivilegeAndModule"() {
        given:
        IObjectDescribe objectDescribeArg = objectDescribe
        objectDescribeArg.setDefineType(IObjectDescribe.DEFINE_TYPE_CUSTOM)
        objectDescribeArg.setApiName(objetApiName)
        objectDescribeArg.setTenantId(tenantId)
        when:
        PowerMockito.when(ProductUtil.isSpuOpen(Mockito.anyString())).thenReturn(false)
        licenseService.queryAvailableObject(_) >> []
        PowerMockito.doReturn(describeList).when(objectDescribeService).findByExample(Mockito.anyString(), Mockito.anyMap(), Mockito.any())
        functionPrivilegeService.funPrivilegeCheck(_, _, _) >> []
        describeLogicServiceImpl.findDescribeByPrivilegeAndModule(User.systemUser(tenantId), actionCode, true, true, true, true)
        then:
        1 == 1
        where:
        tenantId | objetApiName      | fieldApiName     | fieldDescribe             | actionCode                          | objectDescribe       | describeList
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new TextFieldDescribe()   | ObjectAction.CREATE.getActionCode() | new ObjectDescribe() | [objectDescribe]
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | new SignInFieldDescribe() | ""                                  | new ObjectDescribe() | [objectDescribe]
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | new SignInFieldDescribe() | ObjectAction.CREATE.getActionCode() | new ObjectDescribe() | []
    }

    def "test findDescribeByApiName"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setId(IdGenerator.get())
        objectDescribe.setTenantId(tenantId)
        objectDescribe.setApiName(describeApiName)
        objectDescribe.setDeleted(!isActive)

        IFieldDescribe referenceFieldDescribe = new ObjectReferenceFieldDescribe()
        referenceFieldDescribe.setApiName(fieldApiName)
        referenceFieldDescribe.setTargetApiName(targetApiName)

        IFieldDescribe masterDetailFieldDescribe = new MasterDetailFieldDescribe()
        masterDetailFieldDescribe.setApiName(fieldApiName)
        masterDetailFieldDescribe.setTargetApiName(targetApiName)

        objectDescribe.setFieldDescribes(Lists.newArrayList(masterDetailFieldDescribe, referenceFieldDescribe))

        IObjectData objectData = new ObjectData()
        objectData.setId(dataId)
        RequestContext requestContextArg = RequestContext.builder().user(User.systemUser(tenantId)).build()
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndObjectDescribeApiNameIncludedDeletedData(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        functionPrivilegeService.getUnauthorizedFields(_, _) >> []
        layoutLogicService.findObjectLayoutWithTypeIncludeAllComponent(_, _, _, _, _) >> new Layout()
        proxy.hasStageInstance(_, _) >> new HasStageInstance.Result()
        PowerMockito.doReturn(Lists.newArrayList(objectDescribe)).when(objectDescribeService).findDetailDescribeList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())
        layoutLogicService.findListLayoutByDescribeApiNames(_, _) >> [:]
        recordTypeLogicService.findValidRecordTypeListMap(_, _) >> [:]
        recordTypeLogicService.findRecordTypes(_, _) >> [:]
        layoutLogicService.findObjectLayoutWithType(_, _, _, _, _) >> new Layout()
        PowerMockito.doReturn(Lists.newArrayList()).when(objectDescribeService).findDescribeListByApiNames(Mockito.anyString(), Mockito.anyList(), Mockito.any())
        customButtonService.findButtonsForCreate(_, _) >> []
        customButtonService.findButtonsForEdit(_, _) >> []
        buttonLogicService.getButtonByComponentActions(_, _, _, _, _) >> []
        dataProxyService.findById(_, _, _, _) >> objectData
        licenseService.queryAvailableObject(_) >> [describeApiName]
        functionPrivilegeService.funPrivilegeCheck(_, _, _) >> [describeApiName]
        PowerMockito.when(RequestUtil.isMobileRequestBeforeVersion(Mockito.anyString())).thenReturn(isGray)
        describeLogicServiceImpl.findDescribeByApiName(requestContextArg, describeApiName, isIncludeLayout, layoutType, recordType, isIncludeRefDescribeList, isIncludeDetailDescribeList, isIncludeDeleted, dataId)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | targetApiName     | fieldApiName     | isIncludeLayout | layoutType                      | recordType | isIncludeRefDescribeList | isIncludeDetailDescribeList | isIncludeDeleted | dataId                     | isActive | isGray
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_eDimT__c" | true            | UdobjConstants.LAYOUT_TYPE_EDIT | null       | true                     | true                        | true             | "63760b5eae03c70001f77653" | true     | true
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_eDimT__c" | true            | UdobjConstants.LAYOUT_TYPE_EDIT | null       | true                     | true                        | true             | "63760b5eae03c70001f77653" | true     | false
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_eDimT__c" | false           | UdobjConstants.LAYOUT_TYPE_EDIT | null       | true                     | true                        | true             | "63760b5eae03c70001f77653" | true     | false
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_eDimT__c" | true            | null                            | null       | true                     | true                        | true             | "63760b5eae03c70001f77653" | false    | true
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_jdhze__c" | true            | UdobjConstants.LAYOUT_TYPE_ADD  | null       | true                     | true                        | true             | ""                         | true     | true
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_jdhze__c" | true            | UdobjConstants.LAYOUT_TYPE_ADD  | null       | true                     | true                        | true             | ""                         | false    | true
        "78057"  | "object_L2k2I__c" | "object_ro7V9__c" | "field_eDimT__c" | true            | null                            | null       | true                     | true                        | true             | "63760b5eae03c70001f77653" | true     | true
    }

    def "test findCustomFieldDescribe"() {
        given:
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        LayoutLogicService layoutLogicService = Mock(LayoutLogicService)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        def layoutList = []
        def layout = new Layout()
        layout.setLayoutType(layoutType)
        layoutList << layout
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService,
                layoutLogicService: layoutLogicService)
        when:
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        layoutLogicService.findLayoutByObjectApiName(_, _) >> layoutList
        layoutLogicService.findByTypesIncludeFlowLayout(_, _, _) >> layoutList
        describeLogicServiceImpl.findCustomFieldDescribe(tenantId, describeApiName, fieldApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldApiName     | layoutType | fieldDescribe
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "detail"   | new TextFieldDescribe()
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | "detail"   | new SignInFieldDescribe()
    }

    def "test createDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeApiName)
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        def layoutList = []
        def layout = new Layout()
        def listLayout = new Layout()
        layout.setLayoutType(layoutType)
        layoutList << layout
        def currencyList = []
        def currency = new MtCurrency()
        currency.setCurrencyCode("YNC")
        currency.setStatus(0)
        currency.setIsFunctional(isFunctional)
        currencyList << currency

        when:
        PowerMockito.when(objectDescribeService.findDetailDescribeList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(Lists.newArrayList())
        layoutLogicService.findLayoutByApiName(_, _, _) >> null
        multiCurrencyLogicService.findMultiCurrencyStatus(_) >> 1
        multiCurrencyLogicService.findCurrencyList(_) >> currencyList
        metadataMultiCurrencyService.generateMultiCurrencyFields(_, _, _) >> []
        licenseService.existModule(_, _) >> [_: true]
        layoutLogicService.createLayout(_, _) >> layout
        PowerMockito.when(objectDescribeService.getCountByExample(Mockito.anyString(), Mockito.anyMap())).thenReturn(0)
        PowerMockito.when(defObjLifeStatusService.generateDefaultFieldDescribe(Mockito.anyString())).thenReturn(PowerMockito.mock(IFieldDescribe))
        def license = new ModuleParaLicense()
        license.setModuleCode("custom_object")
        license.setParaKey("custom_objects_limit")
        license.setParaValue("100")
        def moduleMap = ["custom_object": [license]]
        licenseService.batchGetModuleLicenses(_, _) >> moduleMap
        def result = new OrganizationStatus.Result()
        result.setOpenOrganization(true)
        platServiceProxy.openOrganization(_) >> result
        PowerMockito.when(objectDescribeService.create(Mockito.any(), Mockito.anyBoolean(), Mockito.any())).thenReturn(objectDescribe)
        functionPrivilegeService.initFunctionPrivilege(_, _) >> null
        dataPrivilegeService.addCommonPrivilegeListResult(_, _) >> null
        recordTypeLogicService.recordTypeInit(_, _, _, _) >> null
        logService.log(_, _, _, _, _) >> null
        describeLogicServiceImpl.createDescribe(User.systemUser(tenantId), objectDescribe.toJsonString(), layout.toJsonString(), listLayout.toJsonString(), true, isIncludeLayout)
        describeLogicServiceImpl.createDescribe(User.systemUser(tenantId), objectDescribe, layout, listLayout, isIncludeLayout)

        then:
        1 == 1
        where:
        tenantId | describeApiName   | fieldApiName     | layoutType | fieldDescribe             | isIncludeLayout | isFunctional | key
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "detail"   | new TextFieldDescribe()   | false           | true         | "custom_object"
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | "detail"   | new SignInFieldDescribe() | false           | true         | "custom_object"
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | "detail"   | new SignInFieldDescribe() | true            | true         | "custom_object"
    }


    def "test initializeDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeApiName)
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        def layoutArg = layout
        layoutArg.setLayoutType(layoutType)
        layoutArg.setId(IdGenerator.get())
        def listLayout = layout
        listLayout.setId(IdGenerator.get())
        listLayout.setLayoutType(layoutType)
        when:
        PowerMockito.when(objectDescribeService.replace(Mockito.any(), Mockito.anyBoolean(), Mockito.any())).thenReturn(objectDescribe)
        layoutLogicService.findLayoutByObjectApiName(_, _) >> layoutResult
        PowerMockito.when(defObjLifeStatusService.generateDefaultFieldDescribe(Mockito.anyString())).thenReturn(new TextFieldDescribe())
        describeLogicServiceImpl.initializeDescribe(User.systemUser(tenantId), objectDescribe.toJsonString(), layoutArg.toJsonString(), listLayout.toJsonString())
        then:
        1 == 1
        where:
        tenantId | describeApiName   | fieldApiName     | layoutType | fieldDescribe             | layout       | layoutResult
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | "detail"   | new TextFieldDescribe()   | new Layout() | [layout]
        "78057"  | "object_L2k2I__c" | "life_status"    | "detail"   | new TextFieldDescribe()   | new Layout() | []
        "78057"  | "object_L2k2I__c" | "field_eDimT__c" | "list"     | new SignInFieldDescribe() | new Layout() | [layout]
    }

    def "test updateDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()

        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setLabel("test")
        iFieldDescribe.setApiName(fieldApiName)

        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        objectDescribe.setDisplayName("test")
        objectDescribe.setApiName(describeApiName)

        ILayout layout = new Layout()

        when:
        fieldRelationCalculateService.validateByObjectDescribe(_, _) >> []
        fieldRelationCalculateService.checkQuoteFieldsByObjectDescribe(_, _) >> []
        licenseService.batchGetModuleLicenses(_, _) >> [:]
        redissonService.tryLock(_, _, _, _) >> Mock(RLock)
        PowerMockito.when(objectDescribeService.update(Mockito.any(), Mockito.any())).thenReturn(objectDescribe)
        logService.log(_, _, _, _, _) >> null
        describeLogicServiceImpl.updateDescribe(User.systemUser(tenantId), objectDescribe.toJsonString(), layout.toJsonString(), isActive, isIncludeLayout)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldApiName     | fieldDescribe                | isActive | isIncludeLayout
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new AreaFieldDescribe()      | true     | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new TextFieldDescribe()      | true     | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new SelectOneFieldDescribe() | true     | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new WhatListFieldDescribe()  | true     | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new WhatFieldDescribe()      | true     | true
    }


    def "test deleteDescribe"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()

        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setLabel("test")
        iFieldDescribe.setApiName(fieldApiName)
        if (iFieldDescribe instanceof SelectOne) {
            iFieldDescribe.set("option_type", "general")
        }

        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        objectDescribe.setDisplayName("test")
        objectDescribe.setApiName(describeApiName)

        def checkerResult = new CheckerResult()
        checkerResult.setPass(true)
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(objectDescribeService.delete(Mockito.any())).thenReturn(checkerResult)
        fieldRelationCalculateService.buildReferenceFieldRelation(_, _) >> []
        describeLogicServiceImpl.deleteDescribe(User.systemUser(tenantId), describeApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldApiName               | fieldDescribe
        "78057"  | "object_L2k2I__c" | "field_jdhze__c"           | new TextFieldDescribe()
        "78058"  | "object_L2k2I__c" | "field_eDimT__c"           | new MasterDetailFieldDescribe()
        "78059"  | "object_L2k2I__c" | "field_selectOne__c"       | new SelectOneFieldDescribe()
        "78060"  | "object_L2k2I__c" | "field_whatList__c"        | new WhatListFieldDescribe()
        "78061"  | "object_L2k2I__c" | "field_objectReference__c" | new ObjectReferenceFieldDescribe()
        "78062"  | "object_L2k2I__c" | "field_area__c"            | new AreaFieldDescribe()
        "78063"  | "object_L2k2I__c" | "field_jdhze__c"           | new AutoNumberFieldDescribe()
        "78064"  | "object_L2k2I__c" | "field_jdhze__c"           | new SelectManyFieldDescribe()
    }


    def "test updateCustomFieldDescribe"() {
        given:
        IObjectDescribeService objectDescribeService = Mock(IObjectDescribeService)
        FieldRelationCalculateService fieldRelationCalculateService = Mock(FieldRelationCalculateService)
        OptionSetLogicService optionSetLogicService = Mock(OptionSetLogicService)
        DescribeChangeEvent describeChangeEvent = Mock(DescribeChangeEvent)
        JobScheduleService jobScheduleService = Mock(JobScheduleService)
        LayoutLogicService layoutLogicService = Mock(LayoutLogicService)
        LicenseService licenseService = Mock(LicenseService)
        LogService logService = Mock(LogService)
        describeLogicServiceImpl = new DescribeLogicServiceImpl(objectDescribeService: objectDescribeService,
                fieldRelationCalculateService: fieldRelationCalculateService,
                optionSetLogicService: optionSetLogicService,
                describeChangeEvent: describeChangeEvent,
                jobScheduleService: jobScheduleService,
                layoutLogicService: layoutLogicService,
                licenseService: licenseService,
                logService: logService,
                publicObjectEnableJobVerify: publicObjectEnableJobVerify,
                orgService: orgService)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setLabel("test")
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        objectDescribe.setDisplayName("test")

        def layoutPojoList = []
        def fieldLayoutPojo = new FieldLayoutPojo()
        layoutPojoList << fieldLayoutPojo

        def fieldList = []
        fieldList << fieldDescribe

        if (iFieldDescribe instanceof Area) {
            iFieldDescribe.setIsSupportTown(true)
            def townFieldDescribe = new TownFieldDescribe()
            townFieldDescribe.setApiName("town")
            fieldList.add(townFieldDescribe)
        }
        def newUser = User.systemUser(tenantId)
        newUser.setUserName('admin01')
        when:
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> objectDescribe
        fieldRelationCalculateService.validateByField(_, _) >> []
        fieldRelationCalculateService.checkQuoteField(_, _) >> []
        layoutLogicService.findLayoutByApiNames(_, _, _) >> [:]
        licenseService.batchGetModuleLicenses(_, _) >> [:]
        objectDescribeService.addCustomFieldDescribe(_, _, _) >> objectDescribe
        objectDescribeService.updateFieldDescribe(_, _, _) >> objectDescribe
        publicObjectEnableJobVerify.verifyWithDescribe(_, _, _) >> VerifyResult.buildEmpty()
        User.systemUser(tenantId)
        orgService.fillUserName(_) >> newUser
        logService.log(_, _, _, _, _) >> null
        describeLogicServiceImpl.updateCustomFieldDescribe(User.systemUser(tenantId), describeApiName, iFieldDescribe.toJsonString(), null, layoutPojoList, fieldList)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldApiName     | fieldDescribe
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new AreaFieldDescribe()
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new TextFieldDescribe()
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new SelectOneFieldDescribe()
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new WhatListFieldDescribe()
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new WhatFieldDescribe()
    }

    def "test deleteCustomField"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setLabel("test")
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        objectDescribe.setDisplayName("test")

        def checkerResult = new CheckerResult()
        checkerResult.setPass(true)
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        PowerMockito.when(ChangeOrderConfig.getOriginalDescribeFields(Mockito.any())).thenReturn(Lists.newArrayList())
        fieldRelationCalculateService.validateByField(_, _) >> []
        fieldRelationCalculateService.checkQuoteField(_, _) >> []
        layoutLogicService.findLayoutByApiNames(_, _, _) >> [:]
        objectDescribeService.updateFieldDescribe(_, _, _) >> objectDescribe
        fieldService.deleteField(_, _) >> checkerResult
        logService.log(_, _, _, _, _) >> null
        objectMappingService.deleteDetailDescribeRule(_, _) >> null
        autoNumberService.changeFunctionStatus(_, _, _) >> null
        layoutLogicService.removeFieldInLayout(_, _, _) >> null
        jobScheduleService.cancelCalculateJob(_, _, _) >> null
        fieldRelationCalculateService.cleanReferenceFieldRelation(_, _, _) >> null
        functionLogicService.processFunctionReference(_, _, _, _, _) >> null
        fieldService.disableField(_, _) >> checkerResult
        describeLogicServiceImpl.deleteCustomField(User.systemUser(tenantId), describeApiName, fieldApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldApiName               | fieldDescribe
        "78057"  | "object_L2k2I__c" | "field_jdhze__c"           | new TextFieldDescribe()
        "78058"  | "object_L2k2I__c" | "field_eDimT__c"           | new MasterDetailFieldDescribe()
        "78059"  | "object_L2k2I__c" | "field_selectOne__c"       | new SelectOneFieldDescribe()
        "78060"  | "object_L2k2I__c" | "field_whatList__c"        | new WhatListFieldDescribe()
        "78061"  | "object_L2k2I__c" | "field_objectReference__c" | new ObjectReferenceFieldDescribe()
        "78062"  | "object_L2k2I__c" | "field_area__c"            | new AreaFieldDescribe()
        "78063"  | "object_L2k2I__c" | "field_jdhze__c"           | new AutoNumberFieldDescribe()
        "78064"  | "object_L2k2I__c" | "field_jdhze__c"           | new SelectManyFieldDescribe()
    }


    def "test enableField"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe iFieldDescribe = fieldDescribe
        iFieldDescribe.setLabel("test")
        iFieldDescribe.setApiName(fieldApiName)
        objectDescribe.setFieldDescribes(Lists.newArrayList(iFieldDescribe))
        objectDescribe.setDisplayName("test")

        def checkerResult = new CheckerResult()
        checkerResult.setPass(isPass)
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        fieldRelationCalculateService.validateByField(_, _) >> []
        jobScheduleService.submitCalculateJob(_, _, _, _, _) >> null
        logService.log(_, _, _, _, _) >> null
        fieldService.enableField(_, _) >> checkerResult
        describeLogicServiceImpl.enableField(User.systemUser(tenantId), describeApiName, fieldApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | fieldApiName     | fieldDescribe                      | isPass
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new WhatListFieldDescribe()        | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new TextFieldDescribe()            | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new MasterDetailFieldDescribe()    | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new SelectOneFieldDescribe()       | true
        "78057"  | "object_L2k2I__c" | "field_jdhze__c" | new ObjectReferenceFieldDescribe() | true
        "78057"  | "object_L2k2I__c" | "field_area__c"  | new AreaFieldDescribe()            | true
    }

    def "test findIconList"() {
        given:
        when:
        describeLogicServiceImpl.findIconList()
        then:
        noExceptionThrown()
    }


    def "test findDescribeAndLayout"() {
        given:
        when:
        objectDescribeService.findByTenantIdAndDescribeApiName(_, _, _) >> new ObjectDescribe()
        layoutLogicService.findLayoutByApiName(_, _, _) >> new Layout()
        layoutLogicService.findDefaultLayout(_, _, _) >> new Layout()
        describeLogicServiceImpl.findDescribeAndLayout(User.systemUser(tenantId), describeApiName, isIncludeLayout, layoutApiName)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName   | isIncludeLayout | layoutApiName
        "78057"  | "object_L2k2I__c" | true            | "layout_241zl__c"
        "78057"  | "object_L2k2I__c" | true            | ""
    }

    def "test findDescribeByFieldTypes"() {
        given:

        when:
        objectDescribeService.findDescribeByFieldTypes(_, _, _, _) >> []
        licenseService.queryAvailableObject(_) >> []
        describeLogicServiceImpl.findDescribeByFieldTypes(tenantId, fieldTypes, groupTypes)
        then:
        noExceptionThrown()
        where:
        tenantId | fieldTypes                  | groupTypes
        "78057"  | FieldDescribeExt.GROUP_TYPE | FieldDescribeExt.WHAT_GROUP_TYPES
        "78057"  | []                          | FieldDescribeExt.WHAT_GROUP_TYPES
    }

    def "test updateDescribeWithSubmitCalculateJob"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("object_L2k2I__c")
        objectDescribe.setTenantId("78057")
        RLock rLock = Mock(RLock)
        when:
        PowerMockito.when(objectDescribeService.findByTenantIdAndDescribeApiName(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(objectDescribe)
        redissonService.tryLock(_, _, _, _) >> rLock
        fieldRelationCalculateService.validateByObjectDescribe(_, _) >> []
        fieldRelationCalculateService.checkQuoteFieldsByObjectDescribe(_, _) >> []
        describeLogicServiceImpl.updateDescribeWithSubmitCalculateJob(objectDescribe, User.systemUser("78057"))
        then:
        noExceptionThrown()
    }

    def "test getWhatCountDescribes"() {
        given:
        when:
        describeLogicServiceImpl.getWhatCountDescribes(tenantId, describeApiNames)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiNames
        "78057"  | ["object_L2k2I__c"]
        "78057"  | []
    }

    /**
     * GenerateByAI
     * 测试checkLocalizationFileReferenceDupRule方法检查位置字段的重复规则引用
     */
    @Unroll
    def "checkLocalizationFileReferenceDupRule测试_#caseName"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def describeAPIName = "testObj__c"

        // 模拟AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray方法

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.when(AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(Mockito.anyString(), Mockito.anyString())).thenReturn(true)

        // 创建Location字段
        def locationField = FieldDescribeFactory.newInstance([
                "api_name": "testLocation",
                "type"    : "location"
        ])
        // 为Location字段设置IsGeoIndex属性
        locationField.set("is_geo_index", isGeoIndex)

        // 创建对象描述并设置字段
        def objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName(describeAPIName)
        objectDescribe.setTenantId(tenantId)
        if (oldFieldExists) {
            def oldLocationField = FieldDescribeFactory.newInstance([
                    "api_name": "testLocation",
                    "type"    : "location"
            ])
            oldLocationField.set("is_geo_index", oldIsGeoIndex)
            objectDescribe.setFieldDescribes([oldLocationField])
        }


        // 模拟DuplicatedSearchExt.of().getGeoFields()方法
        if (hasDupRule) {
            def duplicatedSearch = Mock(IDuplicatedSearch)
            duplicatedSearchService.getDuplicateSearchRuleList(describeAPIName, null, tenantId, true, DuplicateSearchOrderByType.ORDER_BY_SORT) >> [duplicatedSearch]

            // 模拟DuplicatedSearchExt.of()静态方法
            def dupSearchExt = Mock(DuplicatedSearchExt)
            dupSearchExt.getGeoFields() >> (containsAllGeoFields ? ["testLocation"] as Set : [] as Set)

            // 使用GroovyMock模拟静态方法调用
            DuplicatedSearchExt.metaClass.static.of = { IDuplicatedSearch ds ->
                return dupSearchExt
            }
        } else {
            duplicatedSearchService.getDuplicateSearchRuleList(_, _, _, _, _) >> []
        }

        when: "调用测试方法"
        def exception = null
        try {
            describeLogicServiceImpl.checkLocalizationFileReferenceDupRule(tenantId, describeAPIName, locationField, [locationField], objectDescribe)
        } catch (Exception e) {
            exception = e
        }

        then: "验证结果"
        if (shouldThrowException) {
            exception != null
            exception instanceof ValidateException
        } else {
            exception == null
        }

        cleanup: "清理元编程"
        // 恢复原始的of方法
        if (hasDupRule) {
            DuplicatedSearchExt.metaClass = null
        }

        where: "测试用例"
        caseName                                   | isGeoIndex | oldFieldExists | oldIsGeoIndex | hasDupRule | containsAllGeoFields | shouldThrowException
//        "灰度功能未开启"                           | true       | true           | false         | true       | true                 | false // AppFrameworkConfig已配置了灰度开启，此处只是case描述
//        "没有位置字段"                             | false      | false          | false         | true       | true                 | false
//        "所有位置字段都开启了地理索引"             | true       | true           | true          | true       | true                 | false
//        "没有地理索引状态变更"                     | true       | true           | true          | true       | true                 | false
//        "没有重复搜索规则"                         | true       | true           | false         | false      | true                 | false
//        "规则中地理字段不包含所有变更字段"         | true       | true           | false         | true       | false                | false
//        "规则中地理字段包含所有变更字段"           | true       | true           | false         | true       | true                 | true
        "规则中包含地理字段并且将字段中的属性关闭" | false      | true           | true          | true       | true                 | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldsDocMap方法在比较新旧字段列表时的处理（有新增、删除和修改字段）
     */
    def "getFieldsDocMapTestFieldsComparison"() {
        given:
        // 新建测试字段 - 当前字段列表
        TextFieldDescribe field1 = new TextFieldDescribe()
        field1.setApiName("field1")
        field1.setLabel("Field 1 Updated")
        field1.setMaxLength(150)

        TextFieldDescribe field2 = new TextFieldDescribe()
        field2.setApiName("field2")
        field2.setLabel("Field 2")

        TextFieldDescribe field3 = new TextFieldDescribe()
        field3.setApiName("field3")
        field3.setLabel("Field 3 New")

        List<IFieldDescribe> allList = Lists.newArrayList(field1, field2, field3)

        // 数据库中的字段列表
        TextFieldDescribe oldField1 = new TextFieldDescribe()
        oldField1.setApiName("field1")
        oldField1.setLabel("Field 1")
        oldField1.setMaxLength(100)

        TextFieldDescribe oldField2 = new TextFieldDescribe()
        oldField2.setApiName("field2")
        oldField2.setLabel("Field 2")

        TextFieldDescribe oldField4 = new TextFieldDescribe()
        oldField4.setApiName("field4")
        oldField4.setLabel("Field 4 Deleted")

        List<IFieldDescribe> fieldsInDB = Lists.newArrayList(oldField1, oldField2, oldField4)

        when:
        Map<String, Object> result = DescribeLogicServiceImpl.getFieldsDocMap(allList, fieldsInDB)

        then:
        result != null
        result.size() == 3
        result.containsKey("added_fields")
        result.containsKey("deleted_fields")
        result.containsKey("changed_fields")

        // 验证新增的字段
        (result.get("added_fields") as List).size() == 1
        ((result.get("added_fields") as List)[0] as Map).get("api_name") == "field3"

        // 验证删除的字段
        (result.get("deleted_fields") as List).size() == 1
        ((result.get("deleted_fields") as List)[0] as Map).get("api_name") == "field4"

        // 验证修改的字段
        (result.get("changed_fields") as List).size() == 1
        ((result.get("changed_fields") as List)[0] as Map).get("api_name") == "field1"

    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldsDocMap方法在没有变化的字段时的处理
     */
    def "getFieldsDocMapTestNoChanges"() { // 新建测试字段 - 当前字段列表
        TextFieldDescribe field1 = new TextFieldDescribe()
        field1.setApiName("field1")
        field1.setLabel("Field 1")

        TextFieldDescribe field2 = new TextFieldDescribe()
        field2.setApiName("field2")
        field2.setLabel("Field 2")

        List<IFieldDescribe> allList = Lists.newArrayList(field1, field2)

        // 数据库中的字段列表（相同）
        TextFieldDescribe oldField1 = new TextFieldDescribe()
        oldField1.setApiName("field1")
        oldField1.setLabel("Field 1")

        TextFieldDescribe oldField2 = new TextFieldDescribe()
        oldField2.setApiName("field2")
        oldField2.setLabel("Field 2")

        List<IFieldDescribe> fieldsInDB = Lists.newArrayList(oldField1, oldField2)

        when:
        Map<String, Object> result = DescribeLogicServiceImpl.getFieldsDocMap(allList, fieldsInDB)

        then:
        result != null
        result.size() == 3
        result.containsKey("added_fields")
        result.containsKey("deleted_fields")
        result.containsKey("changed_fields")

        // 验证没有新增的字段
        (result.get("added_fields") as List).isEmpty()

        // 验证没有删除的字段
        (result.get("deleted_fields") as List).isEmpty()

        // 验证没有修改的字段
        (result.get("changed_fields") as List).isEmpty()

    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldsDocMap方法处理不同类型字段时的行为
     */
    def "getFieldsDocMapTestDifferentFieldTypes"() {
        given:
        // 创建不同类型的字段
        TextFieldDescribe textField = new TextFieldDescribe()
        textField.setApiName("textField")
        textField.setLabel("Text Field")
        textField.setMaxLength(100)

        NumberFieldDescribe numberField = new NumberFieldDescribe()
        numberField.setApiName("numberField")
        numberField.setLabel("Number Field")
        numberField.setDecimalPlaces(2)

        DateFieldDescribe dateField = new DateFieldDescribe()
        dateField.setApiName("dateField")
        dateField.setLabel("Date Field")

        List<IFieldDescribe> allList = Lists.newArrayList(textField, numberField, dateField)

        // 数据库中没有现有字段
        List<IFieldDescribe> fieldsInDB = Lists.newArrayList()

        when:
        Map<String, Object> result = DescribeLogicServiceImpl.getFieldsDocMap(allList, fieldsInDB)

        then:
        result != null
        result.size() == 1
        result.containsKey("added_fields")
        result.get("added_fields") instanceof List

        // 验证所有字段都被正确转换
        def allFields = result.get("added_fields") as List
        allFields.size() == 3

        // 验证text字段
        def textDoc = allFields.find { it.api_name == "textField" } as Map
        textDoc.type == "text"
        textDoc.label == "Text Field"
        textDoc.max_length == 100

        // 验证number字段
        def numberDoc = allFields.find { it.api_name == "numberField" } as Map
        numberDoc.type == "number"
        numberDoc.label == "Number Field"
        numberDoc.decimal_places == 2


    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldsDocMap方法在处理多个属性同时变更的场景
     */
    def "getFieldsDocMapTestMultipleChangedProperties"() {
        given:
        // 新建测试字段 - 当前字段列表
        TextFieldDescribe field = new TextFieldDescribe()
        field.setApiName("field")
        field.setLabel("Updated Label")
        field.setMaxLength(200)
        field.setHelpText("Updated Help Text")
        field.setRequired(true)
        field.setActive(true)
        field.setIndex(true)
        field.setDefaultValue("new default")

        List<IFieldDescribe> allList = Lists.newArrayList(field)

        // 数据库中的旧字段
        TextFieldDescribe oldField = new TextFieldDescribe()
        oldField.setApiName("field")
        oldField.setLabel("Original Label")
        oldField.setMaxLength(100)
        oldField.setHelpText("Original Help Text")
        oldField.setRequired(false)
        oldField.setActive(false)
        oldField.setIndex(false)
        oldField.setDefaultValue("old default")

        List<IFieldDescribe> fieldsInDB = Lists.newArrayList(oldField)

        when:
        Map<String, Object> result = DescribeLogicServiceImpl.getFieldsDocMap(allList, fieldsInDB)

        then:
        result != null
        result.size() == 3

        // 验证有变更的字段
        def changedFields = result.get("changed_fields") as List
        changedFields.size() == 1

        // 验证变更的字段内容
        def changedField = changedFields[0] as Map
        changedField.api_name == "field"

        // 验证变更属性列表
        def changedProperties = changedField.changed_properties as Map
        changedProperties.size() >= 7  // 至少应该有7个变更的属性

        // 验证每个变更的属性
        changedProperties.label == "Updated Label"
        changedProperties.label_old == "Original Label"

        changedProperties.max_length == 200
        changedProperties.max_length_old == 100

        changedProperties.help_text == "Updated Help Text"
        changedProperties.help_text_old == "Original Help Text"

        changedProperties.is_required == true
        changedProperties.is_required_old == false

        changedProperties.is_active == true
        changedProperties.is_active_old == false

        changedProperties.is_index == true
        changedProperties.is_index_old == false

        changedProperties.default_value == "new default"
        changedProperties.default_value_old == "old default"
    }
}
