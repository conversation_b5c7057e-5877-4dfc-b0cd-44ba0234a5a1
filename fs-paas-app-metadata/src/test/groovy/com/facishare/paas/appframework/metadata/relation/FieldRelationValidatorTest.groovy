package com.facishare.paas.appframework.metadata.relation

import com.facishare.crm.openapi.Utils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.metadata.FormulaEncodeConfig
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import org.powermock.reflect.Whitebox

import static com.facishare.paas.appframework.metadata.relation.FieldRelationGraphBuilder.GraphLayer.of

/**
 * create by zhaoju on 2020/08/13
 */
class FieldRelationValidatorTest extends GraphHelper {

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    // C_M2DF_R2LD  C_M2DF_S2SN
    def "统->从计->lookup默"() {
        when:
        IObjectDescribe objectD = ObjectD()
        IObjectDescribe salesOrderProductObj = SalesOrderProductObj()
        IObjectDescribe productObj = ProductObj()

        IObjectDescribe objectM = ObjectM()
        // M统计=sum（D计算1）
        IFieldDescribe count = Count("sum__c", "objectM", "formula__c", "objectD")
        objectM.getFieldDescribe("sum__c") >> count
        objectM.getFieldDescribes() >> [count]

        List<FieldRelationGraphBuilder.GraphLayer> graphLayers = [of([objectD]),
                                                                  of([objectM, salesOrderProductObj]),
                                                                  of([productObj])]
        FieldRelationGraph fieldRelationGraph = buildFieldRelationGraph(graphLayers)

        def fields = [FieldRelationValidator.ValidateField.of(count, false, false)]
        then:
        println fieldRelationGraph.graph
        FieldRelationValidator.builder()
                .describe(objectM)
                .fields(fields)
                .graph(fieldRelationGraph)
                .build()
                .doValidate()
        noExceptionThrown()
    }

    // C_L2RF_R2LD
    def "统->相关计->lookup默"() {
        when:
        IObjectDescribe objectA = ObjectA()
        IObjectDescribe salesOrderProductObj = SalesOrderProductObj()
        IObjectDescribe productObj = ProductObj()

        IObjectDescribe objectM = ObjectM()
        // M统计=sum（D计算1）
        IFieldDescribe count = Count("sum__c", "objectM", "formula__c", "objectA", "object_m_id")
        objectM.getFieldDescribe("sum__c") >> count
        objectM.getFieldDescribes() >> [count]

        List<FieldRelationGraphBuilder.GraphLayer> graphLayers = [of([objectA]),
                                                                  of([objectM, salesOrderProductObj]),
                                                                  of([productObj])]
        FieldRelationGraph fieldRelationGraph = buildFieldRelationGraph(graphLayers)

        def fields = [FieldRelationValidator.ValidateField.of(count, false, false)]
        println fieldRelationGraph.graph
        FieldRelationValidator.builder()
                .describe(objectM)
                .fields(fields)
                .graph(fieldRelationGraph)
                .build()
                .doValidate()
        then:
        thrown(ValidateException)
    }

    def "统->相关-默->本统->从默"() {
        when:
        IObjectDescribe salesOrderProductObj = SalesOrderProductObj()
        IObjectDescribe productObj = ProductObj()

        IObjectDescribe salesOrderObj = SalesOrderObj()
        IFieldDescribe object_m_id = IObjectReferenceField("object_m_id", Utils.SALES_ORDER_API_NAME, "objectM")
        List<IFieldDescribe> fieldList = salesOrderObj.getFieldDescribes()
        fieldList.add(object_m_id)
        salesOrderObj.getFieldDescribe("object_m_id") >> object_m_id
        salesOrderObj.getFieldDescribes() >> fieldList

        fieldList.collect { println it.getApiName() }

        IObjectDescribe objectM = ObjectM()
        // M统计=sum（D计算1）
        IFieldDescribe count = Count("sum__c", "objectM", "order_amount", Utils.SALES_ORDER_API_NAME, "object_m_id")
        objectM.getFieldDescribe("sum__c") >> count
        objectM.getFieldDescribes() >> [count]

        List<FieldRelationGraphBuilder.GraphLayer> graphLayers = [of([salesOrderObj]),
                                                                  of([objectM, salesOrderProductObj]),
                                                                  of([productObj])]
        FieldRelationGraph fieldRelationGraph = buildFieldRelationGraph(graphLayers)

        def fields = [FieldRelationValidator.ValidateField.of(count, false, false)]
        println fieldRelationGraph.graph
        FieldRelationValidator.builder()
                .describe(objectM)
                .fields(fields)
                .graph(fieldRelationGraph)
                .build()
                .doValidate()
        then:
        noExceptionThrown()
    }

    def "test getEncoded"() {
        when:
        IObjectDescribe objectM = ObjectM()
        IObjectDescribe objectD = ObjectD()
        IObjectDescribe salesOrderProductObj = SalesOrderProductObj()
        IObjectDescribe productObj = ProductObj()
        List<FieldRelationGraphBuilder.GraphLayer> graphLayers = [of([objectD]),
                                                                  of([objectM, salesOrderProductObj]),
                                                                  of([productObj])]
        FieldRelationGraph fieldRelationGraph = buildFieldRelationGraph(graphLayers)


        def fieldRelationValidator = FieldRelationValidator.builder()
                .describe(objectM)
                .fields([])
                .graph(fieldRelationGraph)
                .build()

        then:
        def node = fieldRelationGraph.getNode(objectM.getApiName(), "sum__c")
        println node
        def relationDescribe = fieldRelationValidator.getEncoded(node.get(), null, null, false)
        println relationDescribe

        relationDescribe.encode == "C"
        relationDescribe.relationDesc == "对象M.sum__c"
    }

    def buildFieldRelationGraph(List<FieldRelationGraphBuilder.GraphLayer> graphLayers) {
        FieldRelationGraphBuilder.builder()
//                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .fillFieldType(true)
                .ignoreInvalidVariable(true)
                .build()
                .getGraph()
    }

    def ObjectA() {
        IObjectDescribe objectA = Mock(IObjectDescribe)
        objectA.getApiName() >> "objectA"
        objectA.getDisplayName() >> "对象A"

        IFieldDescribe number = Number("number__c", "objectA")
        // A lookup M
        IFieldDescribe object_m_id = IObjectReferenceField("object_m_id", "objectA", "objectM")
        // A lookup 订单产品
        IFieldDescribe sales_order_product_id = IObjectReferenceField("sales_order_product_id", "objectA", Utils.SALES_ORDER_PRODUCT_API_NAME)
        // A.计算1=订单产品.价格 *D数量
        IFieldDescribe formula = Formula("formula__c", "objectD", '$sales_order_product_id__r.product_price$ * $number__c$')

        objectA.getFieldDescribe("number__c") >> number
        objectA.getFieldDescribe("object_m_id") >> object_m_id
        objectA.getFieldDescribe("sales_order_product_id") >> sales_order_product_id
        objectA.getFieldDescribe("formula__c") >> formula

        objectA.getFieldDescribes() >> [number, object_m_id, sales_order_product_id, formula]

        objectA
    }

    def ObjectD() {
        IObjectDescribe objectD = Mock(IObjectDescribe)
        objectD.getApiName() >> "objectD"
        objectD.getDisplayName() >> "对象D"
        // D md M
        IFieldDescribe masterDetail = MasterDetail("masterDetail", "objectD", "objectM")
        // D lookup 订单产品
        IFieldDescribe d_lookup_m = IObjectReferenceField("d_lookup_m", "objectD", Utils.SALES_ORDER_PRODUCT_API_NAME)

        IFieldDescribe number = Number("number__c", "objectD")

        // D.计算1=订单产品.价格 *D数量
        IFieldDescribe formula = Formula("formula__c", "objectD", '$d_lookup_m__r.product_price$ * $number__c$')

        objectD.getFieldDescribes() >> [masterDetail, d_lookup_m, number, formula]
        objectD.getFieldDescribe("masterDetail") >> masterDetail
        objectD.getFieldDescribe("d_lookup_m") >> d_lookup_m
        objectD.getFieldDescribe("number__c") >> number
        objectD.getFieldDescribe("formula__c") >> formula
        objectD
    }

    def ObjectM() {
        IObjectDescribe objectM = Mock(IObjectDescribe)
        objectM.getApiName() >> "objectM"
        objectM.getDisplayName() >> "对象M"
        objectM
    }

    static {
        def path = FieldRelationValidatorTest.class.getClassLoader()
                .getResource("logback.xml")
                .toURI()
                .path
        println path
        System.setProperty("logback.configurationFile", path)
        FormulaEncodeConfig.blackLimitEncode = Lists.newArrayList(FormulaEncodeConfig.CONFIG_SPLITTER.split(black_limit_encode))
        FormulaEncodeConfig.empathyEncode = Lists.newArrayList(FormulaEncodeConfig.CONFIG_SPLITTER.split(empathy_encode))
    }

    static String black_limit_encode = 'C_L2RF_D2MC,C_L2RF_D2MF,C_L2RF_D2MD,C_L2RF_D2MN,C_L2RF_R2LC,C_L2RF_R2LF,' +
            'C_L2RF_R2LD,C_L2RF_R2LN,C_M2DC_M2DC,C_L2RC_M2DC,C_M2DC_L2RC,C_L2RC_L2RC,C_M2DC_L2RD_S2SC,C_M2DC_M2DD_S2SC,C_L2RC_L2RD_S2SC,C_L2RC_M2DD_S2SC,C_M2DC_L2RF_S2SC,C_M2DC_M2DF_S2SC,C_L2RC_L2RF_S2SC,C_L2RC_M2DF_S2SC,C_L2RD_R2LD_D2MC,C_L2RD_R2LD_D2MF,C_L2RD_R2LD_D2MD,C_L2RD_R2LD_R2LC,C_L2RD_R2LD_R2LF,C_L2RD_R2LD_R2LD,C_L2RD_R2LD_S2SC,C_L2RD_D2MD_D2MC,C_L2RD_D2MD_D2MF,C_L2RD_D2MD_D2MD,C_L2RD_D2MD_R2LC,C_L2RD_D2MD_R2LF,C_L2RD_D2MD_R2LD,C_L2RD_D2MD_S2SC,C_M2DD_R2LD_D2MC,C_M2DD_R2LD_D2MF,C_M2DD_R2LD_D2MD,C_M2DD_R2LD_R2LC,C_M2DD_R2LD_R2LF,C_M2DD_R2LD_R2LD,C_M2DD_R2LD_S2SC,C_M2DD_D2MD_D2MC,C_M2DD_D2MD_D2MF,C_M2DD_D2MD_D2MD,C_M2DD_D2MD_R2LC,C_M2DD_D2MD_R2LF,C_M2DD_D2MD_R2LD,C_M2DD_D2MD_S2SC,C_L2RD_R2LF_D2MC,C_L2RD_R2LF_D2MF,C_L2RD_R2LF_D2MD,C_L2RD_R2LF_R2LC,C_L2RD_R2LF_R2LF,C_L2RD_R2LF_R2LD,C_L2RD_R2LF_S2SC,C_L2RD_D2MF_D2MC,C_L2RD_D2MF_D2MF,C_L2RD_D2MF_D2MD,C_L2RD_D2MF_R2LC,C_L2RD_D2MF_R2LF,C_L2RD_D2MF_R2LD,C_L2RD_D2MF_S2SC,C_M2DD_R2LF_D2MC,C_M2DD_R2LF_D2MF,C_M2DD_R2LF_D2MD,C_M2DD_R2LF_R2LC,C_M2DD_R2LF_R2LF,C_M2DD_R2LF_R2LD,C_M2DD_R2LF_S2SC,C_M2DD_D2MF_D2MC,C_M2DD_D2MF_D2MF,C_M2DD_D2MF_D2MD,C_M2DD_D2MF_R2LC,C_M2DD_D2MF_R2LF,C_M2DD_D2MF_R2LD,C_M2DD_D2MF_S2SC,C_M2DD_S2SC_M2DC,C_M2DD_S2SC_M2DF,C_M2DD_S2SC_L2RC,C_M2DD_S2SC_L2RF,C_L2RD_S2SC_M2DC,C_L2RD_S2SC_M2DF,C_L2RD_S2SC_L2RC,C_L2RD_S2SC_L2RF,D_D2MC_M2DF_D2MC,D_D2MC_M2DF_D2MF,D_D2MC_M2DF_D2MD,D_D2MC_M2DF_R2LC,D_D2MC_M2DF_R2LF,D_D2MC_M2DF_R2LD,D_D2MC_M2DF_S2SC,D_D2MC_M2DD_D2MC,D_D2MC_M2DD_D2MF,D_D2MC_M2DD_D2MD,D_D2MC_M2DD_R2LC,D_D2MC_M2DD_R2LF,D_D2MC_M2DD_R2LD,D_D2MC_M2DD_S2SC,D_R2LC_M2DF_D2MC,D_R2LC_M2DF_D2MF,D_R2LC_M2DF_D2MD,D_R2LC_M2DF_R2LC,D_R2LC_M2DF_R2LF,D_R2LC_M2DF_R2LD,D_R2LC_M2DF_S2SC,D_R2LC_M2DD_D2MC,D_R2LC_M2DD_D2MF,D_R2LC_M2DD_D2MD,D_R2LC_M2DD_R2LC,D_R2LC_M2DD_R2LF,D_R2LC_M2DD_R2LD,D_R2LC_M2DD_S2SC,D_D2MC_L2RF_D2MC,D_D2MC_L2RF_D2MF,D_D2MC_L2RF_D2MD,D_D2MC_L2RF_R2LC,D_D2MC_L2RF_R2LF,D_D2MC_L2RF_R2LD,D_D2MC_L2RF_S2SC,D_D2MC_L2RD_D2MC,D_D2MC_M2DF_D2MC,D_D2MC_M2DF_D2MF,D_D2MC_M2DF_D2MD,D_D2MC_M2DF_R2LC,D_D2MC_M2DF_R2LF,D_D2MC_M2DF_R2LD,D_D2MC_M2DF_S2SC,D_D2MC_M2DD_D2MC,D_D2MC_M2DD_D2MF,D_D2MC_M2DD_D2MD,D_D2MC_M2DD_R2LC,D_D2MC_M2DD_R2LF,D_D2MC_M2DD_R2LD,D_D2MC_M2DD_S2SC,D_R2LC_M2DF_D2MC,D_R2LC_M2DF_D2MF,D_R2LC_M2DF_D2MD,D_R2LC_M2DF_R2LC,D_R2LC_M2DF_R2LF,D_R2LC_M2DF_R2LD,D_R2LC_M2DF_S2SC,D_R2LC_M2DD_D2MC,D_R2LC_M2DD_D2MF,D_R2LC_M2DD_D2MD,D_R2LC_M2DD_R2LC,D_R2LC_M2DD_R2LF,D_R2LC_M2DD_R2LD,D_R2LC_M2DD_S2SC,D_D2MC_L2RF_D2MC,D_D2MC_L2RF_D2MF,D_D2MC_L2RF_D2MD,D_D2MC_L2RF_R2LC,D_D2MC_L2RF_R2LF,D_D2MC_L2RF_R2LD,D_D2MC_L2RF_S2SC,D_D2MC_L2RD_D2MC,D_D2MC_L2RD_D2MF,D_D2MC_L2RD_D2MD,D_D2MC_L2RD_R2LC,D_D2MC_L2RD_R2LF,D_D2MC_L2RD_R2LD,D_D2MC_L2RD_S2SC,D_R2LC_L2RF_D2MC,D_R2LC_L2RF_D2MF,D_R2LC_L2RF_D2MD,D_R2LC_L2RF_R2LC,D_R2LC_L2RF_R2LF,D_R2LC_L2RF_R2LD,D_R2LC_L2RF_S2SC,D_R2LC_L2RD_D2MC,D_R2LC_L2RD_D2MF,D_R2LC_L2RD_D2MD,D_R2LC_L2RD_R2LC,D_R2LC_L2RD_R2LF,D_R2LC_L2RD_R2LD,D_R2LC_L2RD_S2SC,D_D2MD_S2SC_M2DC,D_D2MD_S2SC_M2DF_D2MC,D_D2MD_S2SC_M2DF_D2MF,D_D2MD_S2SC_M2DF_D2MD,D_D2MD_S2SC_M2DF_R2LC,D_D2MD_S2SC_M2DF_R2LF,D_D2MD_S2SC_M2DF_R2LD,D_D2MD_S2SC_M2DF_S2SC,D_D2MD_S2SC_M2DD_D2MC,D_D2MD_S2SC_M2DD_D2MF,D_D2MD_S2SC_M2DD_D2MD,D_D2MD_S2SC_M2DD_R2LC,D_D2MD_S2SC_M2DD_R2LF,D_D2MD_S2SC_M2DD_R2LD,D_D2MD_S2SC_M2DD_S2SC,D_D2MD_S2SC_L2RC,D_D2MD_S2SC_L2RF_D2MC,D_D2MD_S2SC_L2RF_D2MF,D_D2MD_S2SC_L2RF_D2MD,D_D2MD_S2SC_L2RF_R2LC,D_D2MD_S2SC_L2RF_R2LF,D_D2MD_S2SC_L2RF_R2LD,D_D2MD_S2SC_L2RF_S2SC,D_D2MD_S2SC_L2RD_D2MC,D_D2MD_S2SC_L2RD_D2MF,D_D2MD_S2SC_L2RD_D2MD,D_D2MD_S2SC_L2RD_R2LC,D_D2MD_S2SC_L2RD_R2LF,D_D2MD_S2SC_L2RD_R2LD,D_D2MD_S2SC_L2RD_S2SC,D_R2LD_S2SC_M2DC,D_R2LD_S2SC_M2DF_D2MC,D_R2LD_S2SC_M2DF_D2MF,D_R2LD_S2SC_M2DF_D2MD,D_R2LD_S2SC_M2DF_R2LC,D_R2LD_S2SC_M2DF_R2LF,D_R2LD_S2SC_M2DF_R2LD,D_R2LD_S2SC_M2DF_S2SC,D_R2LD_S2SC_M2DD_D2MC,D_R2LD_S2SC_M2DD_D2MF,D_R2LD_S2SC_M2DD_D2MD,D_R2LD_S2SC_M2DD_R2LC,D_R2LD_S2SC_M2DD_R2LF,D_R2LD_S2SC_M2DD_R2LD,D_R2LD_S2SC_M2DD_S2SC,D_R2LD_S2SC_L2RC,D_R2LD_S2SC_L2RF_D2MC,D_R2LD_S2SC_L2RF_D2MF,D_R2LD_S2SC_L2RF_D2MD,D_R2LD_S2SC_L2RF_R2LC,D_R2LD_S2SC_L2RF_R2LF,D_R2LD_S2SC_L2RF_R2LD,D_R2LD_S2SC_L2RF_S2SC,D_R2LD_S2SC_L2RD_D2MC,D_R2LD_S2SC_L2RD_D2MF,D_R2LD_S2SC_L2RD_D2MD,D_R2LD_S2SC_L2RD_R2LC,D_R2LD_S2SC_L2RD_R2LF,D_R2LD_S2SC_L2RD_R2LD,D_R2LD_S2SC_L2RD_S2SC,D_D2MD_S2SC_M2DC,D_D2MD_S2SC_M2DF_D2MC,D_D2MD_S2SC_M2DF_D2MF,D_D2MD_S2SC_M2DF_D2MD,D_D2MD_S2SC_M2DF_R2LC,D_D2MD_S2SC_M2DF_R2LF,D_D2MD_S2SC_M2DF_R2LD,D_D2MD_S2SC_M2DF_S2SC,D_D2MD_S2SC_M2DD_D2MC,D_D2MD_S2SC_M2DD_D2MF,D_D2MD_S2SC_M2DD_D2MD,D_D2MD_S2SC_M2DD_R2LC,D_D2MD_S2SC_M2DD_R2LF,D_D2MD_S2SC_M2DD_R2LD,D_D2MD_S2SC_M2DD_S2SC,D_D2MD_S2SC_L2RC,D_D2MD_S2SC_L2RF_D2MC,D_D2MD_S2SC_L2RF_D2MF,D_D2MD_S2SC_L2RF_D2MD,D_D2MD_S2SC_L2RF_R2LC,D_D2MD_S2SC_L2RF_R2LF,D_D2MD_S2SC_L2RF_R2LD,D_D2MD_S2SC_L2RF_S2SC,D_D2MD_S2SC_L2RD_D2MC,D_D2MD_S2SC_L2RD_D2MF,D_D2MD_S2SC_L2RD_D2MD,D_D2MD_S2SC_L2RD_R2LC,D_D2MD_S2SC_L2RD_R2LF,D_D2MD_S2SC_L2RD_R2LD,D_D2MD_S2SC_L2RD_S2SC,D_R2LD_S2SC_M2DC,D_R2LD_S2SC_M2DF_D2MC,D_R2LD_S2SC_M2DF_D2MF,D_R2LD_S2SC_M2DF_D2MD,D_R2LD_S2SC_M2DF_R2LC,D_R2LD_S2SC_M2DF_R2LF,D_R2LD_S2SC_M2DF_R2LD,D_R2LD_S2SC_M2DF_S2SC,D_R2LD_S2SC_M2DD_D2MC,D_R2LD_S2SC_M2DD_D2MF,D_R2LD_S2SC_M2DD_D2MD,D_R2LD_S2SC_M2DD_R2LC,D_R2LD_S2SC_M2DD_R2LF,D_R2LD_S2SC_M2DD_R2LD,D_R2LD_S2SC_M2DD_S2SC,D_R2LD_S2SC_L2RC,D_R2LD_S2SC_L2RF_D2MC,D_R2LD_S2SC_L2RF_D2MF,D_R2LD_S2SC_L2RF_D2MD,D_R2LD_S2SC_L2RF_R2LC,D_R2LD_S2SC_L2RF_R2LF,D_R2LD_S2SC_L2RF_R2LD,D_R2LD_S2SC_L2RF_S2SC,D_R2LD_S2SC_L2RD_D2MC,D_R2LD_S2SC_L2RD_D2MF,D_R2LD_S2SC_L2RD_D2MD,D_R2LD_S2SC_L2RD_R2LC,D_R2LD_S2SC_L2RD_R2LF,D_R2LD_S2SC_L2RD_R2LD,D_R2LD_S2SC_L2RD_S2SC,D_D2MD_D2MD_D2MF,D_D2MD_D2MD_R2LF,D_D2MD_D2MD_D2MC,D_D2MD_D2MD_R2LC,D_D2MD_R2LD_D2MF,D_D2MD_R2LD_R2LF,D_D2MD_R2LD_D2MC,D_D2MD_R2LD_R2LC,D_R2LD_D2MD_D2MF,D_R2LD_D2MD_R2LF,D_R2LD_D2MD_D2MC,D_R2LD_D2MD_R2LC,D_R2LD_R2LD_D2MF,D_R2LD_R2LD_R2LF,D_R2LD_R2LD_D2MC,D_R2LD_R2LD_R2LCD_D2MD_D2MF_D2MF,D_D2MD_D2MF_R2LF,D_D2MD_D2MF_D2MC,D_D2MD_D2MF_R2LC,D_D2MD_R2LF_D2MF,D_D2MD_R2LF_R2LF,D_D2MD_R2LF_D2MC,D_D2MD_R2LF_R2LC,D_R2LD_D2MF_D2MF,D_R2LD_D2MF_R2LF,D_R2LD_D2MF_D2MC,D_R2LD_D2MF_R2LC,D_R2LD_R2LF_D2MF,D_R2LD_R2LF_R2LF,D_R2LD_R2LF_D2MC,D_R2LD_R2LF_R2LC,D_D2MF_S2SC_M2DC,D_D2MF_S2SC_M2DF_D2MC,D_D2MF_S2SC_M2DF_D2MF,D_D2MF_S2SC_M2DF_D2MD,D_D2MF_S2SC_M2DF_R2LC,D_D2MF_S2SC_M2DF_R2LF,D_D2MF_S2SC_M2DF_R2LD,D_D2MF_S2SC_M2DF_S2SC,D_D2MF_S2SC_M2DD_D2MC,D_D2MF_S2SC_M2DD_D2MF,D_D2MF_S2SC_M2DD_D2MD,D_D2MF_S2SC_M2DD_R2LC,D_D2MF_S2SC_M2DD_R2LF,D_D2MF_S2SC_M2DD_R2LD,D_D2MF_S2SC_M2DD_S2SC,D_D2MF_S2SC_L2RC,D_D2MF_S2SC_L2RF_D2MC,D_D2MF_S2SC_L2RF_D2MF,D_D2MF_S2SC_L2RF_D2MD,D_D2MF_S2SC_L2RF_R2LC,D_D2MF_S2SC_L2RF_R2LF,D_D2MF_S2SC_L2RF_R2LD,D_D2MF_S2SC_L2RF_S2SC,D_D2MF_S2SC_L2RD_D2MC,D_D2MF_S2SC_L2RD_D2MF,D_D2MF_S2SC_L2RD_D2MD,D_D2MF_S2SC_L2RD_R2LC,D_D2MF_S2SC_L2RD_R2LF,D_D2MF_S2SC_L2RD_R2LD,D_D2MF_S2SC_L2RD_S2SC,D_R2LF_S2SC_M2DC,D_R2LF_S2SC_M2DF_D2MC,D_R2LF_S2SC_M2DF_D2MF,D_R2LF_S2SC_M2DF_D2MD,D_R2LF_S2SC_M2DF_R2LC,D_R2LF_S2SC_M2DF_R2LF,D_R2LF_S2SC_M2DF_R2LD,D_R2LF_S2SC_M2DF_S2SC,D_R2LF_S2SC_M2DD_D2MC,D_R2LF_S2SC_M2DD_D2MF,D_R2LF_S2SC_M2DD_D2MD,D_R2LF_S2SC_M2DD_R2LC,D_R2LF_S2SC_M2DD_R2LF,D_R2LF_S2SC_M2DD_R2LD,D_R2LF_S2SC_M2DD_S2SC,D_R2LF_S2SC_L2RC,D_R2LF_S2SC_L2RF_D2MC,D_R2LF_S2SC_L2RF_D2MF,D_R2LF_S2SC_L2RF_D2MD,D_R2LF_S2SC_L2RF_R2LC,D_R2LF_S2SC_L2RF_R2LF,D_R2LF_S2SC_L2RF_R2LD,D_R2LF_S2SC_L2RF_S2SC,D_R2LF_S2SC_L2RD_D2MC,D_R2LF_S2SC_L2RD_D2MF,D_R2LF_S2SC_L2RD_D2MD,D_R2LF_S2SC_L2RD_R2LC,D_R2LF_S2SC_L2RD_R2LF,D_R2LF_S2SC_L2RD_R2LD,D_R2LF_S2SC_L2RD_S2SC,D_D2MF_D2MD_D2MF,D_D2MF_D2MD_R2LF,D_D2MF_D2MD_D2MC,D_D2MF_D2MD_R2LC,D_D2MF_R2LD_D2MF,D_D2MF_R2LD_R2LF,D_D2MF_R2LD_D2MC,D_D2MF_R2LD_R2LC,D_R2LF_D2MD_D2MF,D_R2LF_D2MD_R2LF,D_R2LF_D2MD_D2MC,D_R2LF_D2MD_R2LC,D_R2LF_R2LD_D2MF,D_R2LF_R2LD_R2LF,D_R2LF_R2LD_D2MC,D_R2LF_R2LD_R2LC,D_D2MF_D2MF_D2MF,D_D2MF_D2MF_R2LF,D_D2MF_D2MF_D2MC,D_D2MF_D2MF_R2LC,D_D2MF_R2LF_D2MF,D_D2MF_R2LF_R2LF,D_D2MF_R2LF_D2MC,D_D2MF_R2LF_R2LC,D_R2LF_D2MF_D2MF,D_R2LF_D2MF_R2LF,D_R2LF_D2MF_D2MC,D_R2LF_D2MF_R2LC,D_R2LF_R2LF_D2MF,D_R2LF_R2LF_R2LF,D_R2LF_R2LF_D2MC,D_R2LF_R2LF_R2LC,F_D2MC_M2DF_D2MC,F_D2MC_M2DF_D2MF,F_D2MC_M2DF_D2MD,F_D2MC_M2DF_R2LC,F_D2MC_M2DF_R2LF,F_D2MC_M2DF_R2LD,F_D2MC_M2DF_S2SC,F_D2MC_M2DD_D2MC,F_D2MC_M2DD_D2MF,F_D2MC_M2DD_D2MD,F_D2MC_M2DD_R2LC,F_D2MC_M2DD_R2LF,F_D2MC_M2DD_R2LD,F_D2MC_M2DD_S2SC,F_R2LC_M2DF_D2MC,F_R2LC_M2DF_D2MF,F_R2LC_M2DF_D2MD,F_R2LC_M2DF_R2LC,F_R2LC_M2DF_R2LF,F_R2LC_M2DF_R2LD,F_R2LC_M2DF_S2SC,F_R2LC_M2DD_D2MC,F_R2LC_M2DD_D2MF,F_R2LC_M2DD_D2MD,F_R2LC_M2DD_R2LC,F_R2LC_M2DD_R2LF,F_R2LC_M2DD_R2LD,F_R2LC_M2DD_S2SC,F_D2MC_L2RF_D2MC,F_D2MC_L2RF_D2MF,F_D2MC_L2RF_D2MD,F_D2MC_L2RF_R2LC,F_D2MC_L2RF_R2LF,F_D2MC_L2RF_R2LD,F_D2MC_L2RF_S2SC,F_D2MC_L2RD_D2MC,F_D2MC_M2DF_D2MC,F_D2MC_M2DF_D2MF,F_D2MC_M2DF_D2MD,F_D2MC_M2DF_R2LC,F_D2MC_M2DF_R2LF,F_D2MC_M2DF_R2LD,F_D2MC_M2DF_S2SC,F_D2MC_M2DD_D2MC,F_D2MC_M2DD_D2MF,F_D2MC_M2DD_D2MD,F_D2MC_M2DD_R2LC,F_D2MC_M2DD_R2LF,F_D2MC_M2DD_R2LD,F_D2MC_M2DD_S2SC,F_R2LC_M2DC,F_R2LC_M2DF_D2MC,F_R2LC_M2DF_D2MF,F_R2LC_M2DF_D2MD,F_R2LC_M2DF_R2LC,F_R2LC_M2DF_R2LF,F_R2LC_M2DF_R2LD,F_R2LC_M2DF_S2SC,F_R2LC_M2DD_D2MC,F_R2LC_M2DD_D2MF,F_R2LC_M2DD_D2MD,F_R2LC_M2DD_R2LC,F_R2LC_M2DD_R2LF,F_R2LC_M2DD_R2LD,F_R2LC_M2DD_S2SC,F_D2MC_L2RC,F_D2MC_L2RF_D2MC,F_D2MC_L2RF_D2MF,F_D2MC_L2RF_D2MD,F_D2MC_L2RF_R2LC,F_D2MC_L2RF_R2LF,F_D2MC_L2RF_R2LD,F_D2MC_L2RF_S2SC,F_D2MC_L2RD_D2MC,F_D2MC_L2RD_D2MF,F_D2MC_L2RD_D2MD,F_D2MC_L2RD_R2LC,F_D2MC_L2RD_R2LF,F_D2MC_L2RD_R2LD,F_D2MC_L2RD_S2SC,F_R2LC_L2RF_D2MC,F_R2LC_L2RF_D2MF,F_R2LC_L2RF_D2MD,F_R2LC_L2RF_R2LC,F_R2LC_L2RF_R2LF,F_R2LC_L2RF_R2LD,F_R2LC_L2RF_S2SC,F_R2LC_L2RD_D2MC,F_R2LC_L2RD_D2MF,F_R2LC_L2RD_D2MD,F_R2LC_L2RD_R2LC,F_R2LC_L2RD_R2LF,F_R2LC_L2RD_R2LD,F_R2LC_L2RD_S2SC,F_D2MD_S2SC_M2DC,F_D2MD_S2SC_M2DF_D2MC,F_D2MD_S2SC_M2DF_D2MF,F_D2MD_S2SC_M2DF_D2MD,F_D2MD_S2SC_M2DF_R2LC,F_D2MD_S2SC_M2DF_R2LF,F_D2MD_S2SC_M2DF_R2LD,F_D2MD_S2SC_M2DF_S2SC,F_D2MD_S2SC_M2DD_D2MC,F_D2MD_S2SC_M2DD_D2MF,F_D2MD_S2SC_M2DD_D2MD,F_D2MD_S2SC_M2DD_R2LC,F_D2MD_S2SC_M2DD_R2LF,F_D2MD_S2SC_M2DD_R2LD,F_D2MD_S2SC_M2DD_S2SC,F_D2MD_S2SC_L2RC,F_D2MD_S2SC_L2RF_D2MC,F_D2MD_S2SC_L2RF_D2MF,F_D2MD_S2SC_L2RF_D2MD,F_D2MD_S2SC_L2RF_R2LC,F_D2MD_S2SC_L2RF_R2LF,F_D2MD_S2SC_L2RF_R2LD,F_D2MD_S2SC_L2RF_S2SC,F_D2MD_S2SC_L2RD_D2MC,F_D2MD_S2SC_L2RD_D2MF,F_D2MD_S2SC_L2RD_D2MD,F_D2MD_S2SC_L2RD_R2LC,F_D2MD_S2SC_L2RD_R2LF,F_D2MD_S2SC_L2RD_R2LD,F_D2MD_S2SC_L2RD_S2SC,F_R2LD_S2SC_M2DC,F_R2LD_S2SC_M2DF_D2MC,F_R2LD_S2SC_M2DF_D2MF,F_R2LD_S2SC_M2DF_D2MD,F_R2LD_S2SC_M2DF_R2LC,F_R2LD_S2SC_M2DF_R2LF,F_R2LD_S2SC_M2DF_R2LD,F_R2LD_S2SC_M2DF_S2SC,F_R2LD_S2SC_M2DD_D2MC,F_R2LD_S2SC_M2DD_D2MF,F_R2LD_S2SC_M2DD_D2MD,F_R2LD_S2SC_M2DD_R2LC,F_R2LD_S2SC_M2DD_R2LF,F_R2LD_S2SC_M2DD_R2LD,F_R2LD_S2SC_M2DD_S2SC,F_R2LD_S2SC_L2RC,F_R2LD_S2SC_L2RF_D2MC,F_R2LD_S2SC_L2RF_D2MF,F_R2LD_S2SC_L2RF_D2MD,F_R2LD_S2SC_L2RF_R2LC,F_R2LD_S2SC_L2RF_R2LF,F_R2LD_S2SC_L2RF_R2LD,F_R2LD_S2SC_L2RF_S2SC,F_R2LD_S2SC_L2RD_D2MC,F_R2LD_S2SC_L2RD_D2MF,F_R2LD_S2SC_L2RD_D2MD,F_R2LD_S2SC_L2RD_R2LC,F_R2LD_S2SC_L2RD_R2LF,F_R2LD_S2SC_L2RD_R2LD,F_R2LD_S2SC_L2RD_S2SC,F_D2MD_S2SC_M2DC,F_D2MD_S2SC_M2DF_D2MC,F_D2MD_S2SC_M2DF_D2MF,F_D2MD_S2SC_M2DF_D2MD,F_D2MD_S2SC_M2DF_R2LC,F_D2MD_S2SC_M2DF_R2LF,F_D2MD_S2SC_M2DF_R2LD,F_D2MD_S2SC_M2DF_S2SC,F_D2MD_S2SC_M2DD_D2MC,F_D2MD_S2SC_M2DD_D2MF,F_D2MD_S2SC_M2DD_D2MD,F_D2MD_S2SC_M2DD_R2LC,F_D2MD_S2SC_M2DD_R2LF,F_D2MD_S2SC_M2DD_R2LD,F_D2MD_S2SC_M2DD_S2SC,F_D2MD_S2SC_L2RC,F_D2MD_S2SC_L2RF_D2MC,F_D2MD_S2SC_L2RF_D2MF,F_D2MD_S2SC_L2RF_D2MD,F_D2MD_S2SC_L2RF_R2LC,F_D2MD_S2SC_L2RF_R2LF,F_D2MD_S2SC_L2RF_R2LD,F_D2MD_S2SC_L2RF_S2SC,F_D2MD_S2SC_L2RD_D2MC,F_D2MD_S2SC_L2RD_D2MF,F_D2MD_S2SC_L2RD_D2MD,F_D2MD_S2SC_L2RD_R2LC,F_D2MD_S2SC_L2RD_R2LF,F_D2MD_S2SC_L2RD_R2LD,F_D2MD_S2SC_L2RD_S2SC,F_R2LD_S2SC_M2DC,F_R2LD_S2SC_M2DF_D2MC,F_R2LD_S2SC_M2DF_D2MF,F_R2LD_S2SC_M2DF_D2MD,F_R2LD_S2SC_M2DF_R2LC,F_R2LD_S2SC_M2DF_R2LF,F_R2LD_S2SC_M2DF_R2LD,F_R2LD_S2SC_M2DF_S2SC,F_R2LD_S2SC_M2DD_D2MC,F_R2LD_S2SC_M2DD_D2MF,F_R2LD_S2SC_M2DD_D2MD,F_R2LD_S2SC_M2DD_R2LC,F_R2LD_S2SC_M2DD_R2LF,F_R2LD_S2SC_M2DD_R2LD,F_R2LD_S2SC_M2DD_S2SC,F_R2LD_S2SC_L2RC,F_R2LD_S2SC_L2RF_D2MC,F_R2LD_S2SC_L2RF_D2MF,F_R2LD_S2SC_L2RF_D2MD,F_R2LD_S2SC_L2RF_R2LC,F_R2LD_S2SC_L2RF_R2LF,F_R2LD_S2SC_L2RF_R2LD,F_R2LD_S2SC_L2RF_S2SC,F_R2LD_S2SC_L2RD_D2MC,F_R2LD_S2SC_L2RD_D2MF,F_R2LD_S2SC_L2RD_D2MD,F_R2LD_S2SC_L2RD_R2LC,F_R2LD_S2SC_L2RD_R2LF,F_R2LD_S2SC_L2RD_R2LD,F_R2LD_S2SC_L2RD_S2SC,F_D2MD_D2MD_D2MF,F_D2MD_D2MD_R2LF,F_D2MD_D2MD_D2MC,F_D2MD_D2MD_R2LC,F_D2MD_R2LD_D2MF,F_D2MD_R2LD_R2LF,F_D2MD_R2LD_D2MC,F_D2MD_R2LD_R2LC,F_R2LD_D2MD_D2MF,F_R2LD_D2MD_R2LF,F_R2LD_D2MD_D2MC,F_R2LD_D2MD_R2LC,F_R2LD_R2LD_D2MF,F_R2LD_R2LD_R2LF,F_R2LD_R2LD_D2MC,F_R2LD_R2LD_R2LC,F_D2MD_D2MF_D2MF,F_D2MD_D2MF_R2LF,F_D2MD_D2MF_D2MC,F_D2MD_D2MF_R2LC,F_D2MD_R2LF_D2MF,F_D2MD_R2LF_R2LF,F_D2MD_R2LF_D2MC,F_D2MD_R2LF_R2LC,F_R2LD_D2MF_D2MF,F_R2LD_D2MF_R2LF,F_R2LD_D2MF_D2MC,F_R2LD_D2MF_R2LC,F_R2LD_R2LF_D2MF,F_R2LD_R2LF_R2LF,F_R2LD_R2LF_D2MC,F_R2LD_R2LF_R2LC,F_D2MF_S2SC_M2DC,F_D2MF_S2SC_M2DF_D2MC,F_D2MF_S2SC_M2DF_D2MF,F_D2MF_S2SC_M2DF_D2MD,F_D2MF_S2SC_M2DF_R2LC,F_D2MF_S2SC_M2DF_R2LF,F_D2MF_S2SC_M2DF_R2LD,F_D2MF_S2SC_M2DF_S2SC,F_D2MF_S2SC_M2DD_D2MC,F_D2MF_S2SC_M2DD_D2MF,F_D2MF_S2SC_M2DD_D2MD,F_D2MF_S2SC_M2DD_R2LC,F_D2MF_S2SC_M2DD_R2LF,F_D2MF_S2SC_M2DD_R2LD,F_D2MF_S2SC_M2DD_S2SC,F_D2MF_S2SC_L2RC,F_D2MF_S2SC_L2RF_D2MC,F_D2MF_S2SC_L2RF_D2MF,F_D2MF_S2SC_L2RF_D2MD,F_D2MF_S2SC_L2RF_R2LC,F_D2MF_S2SC_L2RF_R2LF,F_D2MF_S2SC_L2RF_R2LD,F_D2MF_S2SC_L2RF_S2SC,F_D2MF_S2SC_L2RD_D2MC,F_D2MF_S2SC_L2RD_D2MF,F_D2MF_S2SC_L2RD_D2MD,F_D2MF_S2SC_L2RD_R2LC,F_D2MF_S2SC_L2RD_R2LF,F_D2MF_S2SC_L2RD_R2LD,F_D2MF_S2SC_L2RD_S2SC,F_R2LF_S2SC_M2DC,F_R2LF_S2SC_M2DF_D2MC,F_R2LF_S2SC_M2DF_D2MF,F_R2LF_S2SC_M2DF_D2MD,F_R2LF_S2SC_M2DF_R2LC,F_R2LF_S2SC_M2DF_R2LF,F_R2LF_S2SC_M2DF_R2LD,F_R2LF_S2SC_M2DF_S2SC,F_R2LF_S2SC_M2DD_D2MC,F_R2LF_S2SC_M2DD_D2MF,F_R2LF_S2SC_M2DD_D2MD,F_R2LF_S2SC_M2DD_R2LC,F_R2LF_S2SC_M2DD_R2LF,F_R2LF_S2SC_M2DD_R2LD,F_R2LF_S2SC_M2DD_S2SC,F_R2LF_S2SC_L2RC,F_R2LF_S2SC_L2RF_D2MC,F_R2LF_S2SC_L2RF_D2MF,F_R2LF_S2SC_L2RF_D2MD,F_R2LF_S2SC_L2RF_R2LC,F_R2LF_S2SC_L2RF_R2LF,F_R2LF_S2SC_L2RF_R2LD,F_R2LF_S2SC_L2RF_S2SC,F_R2LF_S2SC_L2RD_D2MC,F_R2LF_S2SC_L2RD_D2MF,F_R2LF_S2SC_L2RD_D2MD,F_R2LF_S2SC_L2RD_R2LC,F_R2LF_S2SC_L2RD_R2LF,F_R2LF_S2SC_L2RD_R2LD,F_R2LF_S2SC_L2RD_S2SC,F_D2MF_D2MD_D2MF,F_D2MF_D2MD_R2LF,F_D2MF_D2MD_D2MC,F_D2MF_D2MD_R2LC,F_D2MF_R2LD_D2MF,F_D2MF_R2LD_R2LF,F_D2MF_R2LD_D2MC,F_D2MF_R2LD_R2LC,F_R2LF_D2MD_D2MF,F_R2LF_D2MD_R2LF,F_R2LF_D2MD_D2MC,F_R2LF_D2MD_R2LC,F_R2LF_R2LD_D2MF,F_R2LF_R2LD_R2LF,F_R2LF_R2LD_D2MC,F_R2LF_R2LD_R2LC,F_D2MF_D2MF_D2MF,F_D2MF_D2MF_R2LF,F_D2MF_D2MF_D2MC,F_D2MF_D2MF_R2LC,F_D2MF_R2LF_D2MF,F_D2MF_R2LF_R2LF,F_D2MF_R2LF_D2MC,F_D2MF_R2LF_R2LC,F_R2LF_D2MF_D2MF,F_R2LF_D2MF_R2LF,F_R2LF_D2MF_D2MC,F_R2LF_D2MF_R2LC,F_R2LF_R2LF_D2MF,F_R2LF_R2LF_R2LF,F_R2LF_R2LF_D2MC,F_R2LF_R2LF_R2LC,'
    static String empathy_encode = 'C_M2DF_S2SD,C_M2DF_S2SF,C_L2RF_S2SD,C_L2RF_S2SF,C_L2RD_R2LD_S2SF,C_L2RD_R2LD_S2SD,C_L2RD_D2MD_S2SF,C_L2RD_D2MD_S2SD,C_M2DD_R2LD_S2SF,C_M2DD_R2LD_S2SD,C_M2DD_D2MD_S2SF,C_M2DD_D2MD_S2SD,C_L2RD_R2LF_S2SF,C_L2RD_R2LF_S2SD,C_L2RD_D2MF_S2SF,C_L2RD_D2MF_S2SD,C_M2DD_R2LF_S2SF,C_M2DD_R2LF_S2SD,C_M2DD_D2MF_S2SF,C_M2DD_D2MF_S2SD,C_M2DD_S2SF,C_M2DD_S2SD,C_R2LD_S2SF,C_R2LD_S2SD,D_D2MC_M2DD_S2SF,D_D2MC_M2DD_S2SD,D_D2MC_M2DF_S2SF,D_D2MC_M2DF_S2SD,D_R2LC_M2DD_S2SF,D_R2LC_M2DD_S2SD,D_R2LC_M2DF_S2SF,D_R2LC_M2DF_S2SD,D_D2MC_L2RD_S2SF,D_D2MC_L2RD_S2SD,D_D2MC_L2RF_S2SF,D_D2MC_L2RF_S2SD,D_R2LC_L2RD_S2SF,D_R2LC_L2RD_S2SD,D_R2LC_L2RF_S2SF,D_R2LC_L2RF_S2SD,D_S2SF,D_S2SD,D_D2MD_S2SC_M2DD_S2SF,D_D2MD_S2SC_M2DD_S2SD,D_D2MD_S2SC_M2DF_S2SF,D_D2MD_S2SC_M2DF_S2SD,D_D2MD_S2SC_L2RD_S2SF,D_D2MD_S2SC_L2RD_S2SD,D_D2MD_S2SC_L2RF_S2SF,D_D2MD_S2SC_L2RF_S2SD,D_R2LD_S2SC_M2DD_S2SF,D_R2LD_S2SC_M2DD_S2SD,D_R2LD_S2SC_M2DF_S2SF,D_R2LD_S2SC_M2DF_S2SD,D_R2LD_S2SC_L2RD_S2SF,D_R2LD_S2SC_L2RD_S2SD,D_R2LD_S2SC_L2RF_S2SF,D_R2LD_S2SC_L2RF_S2SD,D_D2MD_S2SD,D_R2LD_S2SD,D_D2MD_S2SF,D_R2LD_S2SF,D_D2MD_D2MD_S2SF,D_D2MD_D2MD_S2SD,D_D2MD_R2LD_S2SF,D_D2MD_R2LD_S2SD,D_R2LD_D2MD_S2SF,D_R2LD_D2MD_S2SD,D_R2LD_R2LD_S2SF,D_R2LD_R2LD_S2SD,D_D2MD_D2MF_S2SF,D_D2MD_D2MF_S2SD,D_D2MD_R2LF_S2SF,D_D2MD_R2LF_S2SD,D_R2LD_D2MF_S2SF,D_R2LD_D2MF_S2SD,D_R2LD_R2LF_S2SF,D_R2LD_R2LF_S2SD_D2MF_S2SD,D_R2LF_S2SD,D_D2MF_S2SF,D_R2LF_S2SF,D_D2MF_D2MD_S2SF,D_D2MF_D2MD_S2SD,D_D2MF_R2LD_S2SF,D_D2MF_R2LD_S2SD,D_R2LF_D2MD_S2SF,D_R2LF_D2MD_S2SD,D_R2LF_R2LD_S2SF,D_R2LF_R2LD_S2SD,D_D2MF_D2MF_S2SF,D_D2MF_D2MF_S2SD,D_D2MF_R2LF_S2SF,D_D2MF_R2LF_S2SD,D_R2LF_D2MF_S2SF,D_R2LF_D2MF_S2SD,D_R2LF_R2LF_S2SF,D_R2LF_R2LF_S2SD,F_D2MC_M2DD_S2SF,F_D2MC_M2DD_S2SD,F_D2MC_M2DF_S2SF,F_D2MC_M2DF_S2SD,F_R2LC_M2DD_S2SF,F_R2LC_M2DD_S2SD,F_R2LC_M2DF_S2SF,F_R2LC_M2DF_S2SD,F_D2MC_L2RD_S2SF,F_D2MC_L2RD_S2SD,F_D2MC_L2RF_S2SF,F_D2MC_L2RF_S2SD,F_R2LC_L2RD_S2SF,F_R2LC_L2RD_S2SD,F_R2LC_L2RF_S2SF,F_R2LC_L2RF_S2SD,F_S2SF,F_S2SD,F_D2MD_S2SC_M2DD_S2SF,F_D2MD_S2SC_M2DD_S2SD,F_D2MD_S2SC_M2DF_S2SF,F_D2MD_S2SC_M2DF_S2SD,F_D2MD_S2SC_L2RD_S2SF,F_D2MD_S2SC_L2RD_S2SD,F_D2MD_S2SC_L2RF_S2SF,F_D2MD_S2SC_L2RF_S2SD,F_R2LD_S2SC_M2DD_S2SF,F_R2LD_S2SC_M2DD_S2SD,F_R2LD_S2SC_M2DF_S2SF,F_R2LD_S2SC_M2DF_S2SD,F_R2LD_S2SC_L2RD_S2SF,F_R2LD_S2SC_L2RD_S2SD,F_R2LD_S2SC_L2RF_S2SF,F_R2LD_S2SC_L2RF_S2SD,F_D2MD_S2SD,F_R2LD_S2SD,F_D2MD_S2SF,F_R2LD_S2SF,F_D2MD_D2MD_S2SF,F_D2MD_D2MD_S2SD,F_D2MD_R2LD_S2SF,F_D2MD_R2LD_S2SD,F_R2LD_D2MD_S2SF,F_R2LD_D2MD_S2SD,F_R2LD_R2LD_S2SF,F_R2LD_R2LD_S2SD,F_D2MD_D2MF_S2SF,F_D2MD_D2MF_S2SD,F_D2MD_R2LF_S2SF,F_D2MD_R2LF_S2SD,F_R2LD_D2MF_S2SF,F_R2LD_D2MF_S2SD,F_R2LD_R2LF_S2SF,F_R2LD_R2LF_S2SD_D2MF_S2SD,F_R2LF_S2SD,F_D2MF_S2SF,F_R2LF_S2SF,F_D2MF_D2MD_S2SF,F_D2MF_D2MD_S2SD,F_D2MF_R2LD_S2SF,F_D2MF_R2LD_S2SD,F_R2LF_D2MD_S2SF,F_R2LF_D2MD_S2SD,F_R2LF_R2LD_S2SF,F_R2LF_R2LD_S2SD,F_D2MF_D2MF_S2SF,F_D2MF_D2MF_S2SD,F_D2MF_R2LF_S2SF,F_D2MF_R2LF_S2SD,F_R2LF_D2MF_S2SF,F_R2LF_D2MF_S2SD,F_R2LF_R2LF_S2SF,F_R2LF_R2LF_S2SD,'
}
