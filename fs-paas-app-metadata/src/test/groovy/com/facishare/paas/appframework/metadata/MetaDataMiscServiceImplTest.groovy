package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.fsc.api.model.CreateFileShareIds
import com.facishare.fsc.api.service.SharedFileService
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.common.service.PhoneNumberService
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds
import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation
import com.facishare.paas.appframework.common.service.dto.UserInfo
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.Pair
import com.facishare.paas.appframework.coordination.CrmService
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.flow.ApprovalFlowService
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType
import com.facishare.paas.appframework.metadata.dto.ImageInfo
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService
import com.facishare.paas.appframework.metadata.treeview.TreeViewServiceImpl
import com.facishare.paas.appframework.metadata.util.ExcelUtil
import com.facishare.paas.appframework.metadata.util.WebFileUtil
import com.facishare.paas.appframework.privilege.DataPrivilegeService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.appframework.privilege.UserRoleInfoService
import com.facishare.paas.appframework.privilege.dto.Permissions
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderManager
import com.facishare.paas.metadata.api.INameCache
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy
import com.facishare.paas.metadata.impl.NameCache
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe
import com.facishare.paas.metadata.impl.describe.WhatListFieldDescribe
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.support.CountryAreaService
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.paas.metadata.util.SpringUtil
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.fxiaoke.release.GrayRule
import com.github.autoconf.ConfigFactory
import com.github.autoconf.base.ProcessInfo
import com.github.autoconf.helper.ConfigHelper
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Specification

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([ObjectDescribeExt, ConfigFactory, ObjectDataExt, AppFrameworkConfig, I18N, RequestContextManager, ConfigHelper, FsGrayRelease, SpringUtil, CountryAreaManager])
@PowerMockIgnore(["javax.management.", "javax.xml.parsers.", "com.sun.org.apache.xerces.internal.jaxp.*", "ch.qos.logback.*", "org.slf4j.*"])
class MetaDataMiscServiceImplTest extends Specification {

    @Shared
    String channelObjDescribe = '''{"tenant_id":"74255","store_table_name":"fmcg_channel_name","description":"","index_version":1,"is_deleted":false,"define_type":"package","display_name_r":"渠道","release_version":"6.4","package":"CRM","is_active":true,"last_modified_time":1672211944010,"create_time":1656402836418,"is_support_tree_view":true,"last_modified_by":"1000","display_name":"渠道","created_by":"-10000","version":17,"is_open_display_name":false,"icon_index":14,"api_name":"ChannelObj","icon_path":"","is_udef":true,"short_name":"wqk","_id":"62bab3940a56450001cf01a1","fields":{"tenant_id":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","is_unique":false,"description":"tenant_id","label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"lock_rule":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836407,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定规则","is_unique":false,"rules":[],"default_value":"default_lock_rule","label":"锁定规则","type":"lock_rule","is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"62bab3940a56450001cf0184","is_single":false,"label_r":"锁定规则","is_index_field":false,"index_name":"s_1","status":"new"},"channel_state":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836408,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"default_value":true,"label":"渠道状态","type":"true_or_false","is_required":false,"api_name":"channel_state","options":[{"label":"启用","value":true},{"label":"禁用","value":false}],"define_type":"package","_id":"62bab3940a56450001cf0185","is_single":false,"label_r":"渠道状态","is_index_field":false,"index_name":"b_1","help_text":"","status":"new"},"field_aoSLi__c":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[],"define_type":"custom","input_mode":"","is_single":false,"is_extend":true,"index_name":"s_6","is_index":true,"is_active":true,"create_time":1670924779031,"is_encrypted":false,"label":"查找fj-对象A","target_api_name":"object_w4o31__c","target_related_list_name":"target_related_list_2ng2Y__c","field_num":1,"target_related_list_label":"渠道","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_aoSLi__c","_id":"639849eb45205900019b939a","is_index_field":true,"help_text":"","status":"new","description":""},"lock_user":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836409,"is_encrypted":false,"auto_adapt_places":false,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"62bab3940a56450001cf0186","is_single":true,"label_r":"加锁人","is_index_field":false,"index_name":"a_1","status":"new"},"tree_view":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402997501,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"树形视图层级","is_unique":false,"label":"树形视图层级","type":"tree_path","is_need_convert":false,"is_required":false,"api_name":"tree_view","define_type":"package","_id":"62bab4350059070001d7c95f","is_single":false,"label_r":"树形视图层级","is_index_field":false,"index_name":"s_5","help_text":"","status":"released"},"extend_obj_data_id":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836410,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"连接通表的记录ID,扩展字段用","is_unique":true,"label":"扩展字段在mt_data中的记录ID","type":"text","is_required":false,"api_name":"extend_obj_data_id","define_type":"system","_id":"62bab3940a56450001cf0187","is_single":false,"is_index_field":false,"index_name":"t_3","max_length":64,"status":"released"},"is_deleted":{"describe_api_name":"ChannelObj","is_index":false,"create_time":1656402836418,"is_unique":false,"description":"is_deleted","default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","options":[],"define_type":"system","index_name":"is_del","status":"released"},"field_C2atD__c":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1672211943758,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":false,"label":"不能被选择","type":"true_or_false","field_num":2,"is_required":false,"api_name":"field_C2atD__c","options":[{"label":"是","value":true},{"label":"否","value":false}],"define_type":"custom","_id":"63abede873ab8900018bdca0","is_single":false,"is_extend":true,"is_index_field":false,"index_name":"b_2","help_text":"","status":"new","description":""},"life_status_before_invalid":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836412,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"62bab3940a56450001cf0189","is_single":false,"label_r":"作废前生命状态","is_index_field":false,"index_name":"t_4","max_length":256,"status":"new"},"object_describe_api_name":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","is_unique":false,"description":"object_describe_api_name","label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","max_length":200,"status":"released"},"owner_department":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":true,"label_r":"负责人主属部门","index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1656402836415,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"62bab3940a56450001cf018c","is_index_field":false,"help_text":"","status":"new"},"out_owner":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836418,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"index_name":"o_owner","config":{"display":1},"status":"released","description":""},"owner":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836417,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"wheres":[],"api_name":"owner","define_type":"package","_id":"62bab3940a56450001cf018d","is_single":true,"label_r":"负责人","is_index_field":false,"index_name":"owner","help_text":"","status":"new"},"channel_description":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"type":"long_text","default_to_zero":false,"is_required":false,"define_type":"package","is_single":false,"label_r":"渠道描述","index_name":"t_1","max_length":2000,"is_index":true,"is_active":true,"create_time":1656402836418,"is_encrypted":false,"min_length":0,"default_value":"","label":"渠道描述","api_name":"channel_description","_id":"62bab3940a56450001cf018e","is_index_field":false,"help_text":"","status":"new"},"channel_level":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"type":"number","decimal_places":0,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"label_r":"渠道层级","index_name":"d_1","max_length":14,"is_index":true,"is_active":true,"create_time":1656402836419,"is_encrypted":false,"display_style":"input","step_value":1,"length":14,"default_value":"","label":"渠道层级","api_name":"channel_level","_id":"62bab3940a56450001cf018f","is_index_field":false,"round_mode":4,"help_text":"","status":"new"},"lock_status":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836422,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"62bab3940a56450001cf0192","is_single":false,"label_r":"锁定状态","is_index_field":false,"index_name":"s_2","config":{},"help_text":"","status":"new"},"package":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","is_unique":false,"description":"package","label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","max_length":200,"status":"released"},"last_modified_time":{"describe_api_name":"ChannelObj","is_index":true,"create_time":1656402836418,"is_unique":false,"description":"last_modified_time","label":"最后修改时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-ddHH:mm:ss","index_name":"md_time","status":"released"}
                                ,"create_time":{"describe_api_name":"ChannelObj","is_index":true,"create_time":1656402836418,"is_unique":false,"description":"create_time","label":"创建时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-ddHH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836424,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"62bab3940a56450001cf0194","is_single":false,"label_r":"生命状态","is_index_field":false,"index_name":"s_3","config":{},"help_text":"","status":"new"},"superior_channel":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","is_required":false,"wheres":[],"define_type":"package","input_mode":"","is_single":false,"label_r":"上级渠道","index_name":"s_4","is_index":true,"is_active":true,"create_time":1656402836425,"is_encrypted":false,"tree_view_field":"tree_view","label":"上级渠道","target_api_name":"ChannelObj","target_related_list_name":"target_related_list_ChannelObj_ChannelObj__c","target_related_list_label":"渠道","action_on_target_delete":"set_null","related_wheres":[],"api_name":"superior_channel","_id":"62bab46f0059070001d7c961","is_index_field":true,"help_text":"","status":"new"},"last_modified_by":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836418,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"display_name":{"expression_type":"js","return_type":"text","describe_api_name":"ChannelObj","is_index":true,"is_active":true,"expression":"$channel_number$","create_time":1657526688643,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"计算字段","type":"formula","decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"display_name","define_type":"package","_id":"62cbd9a3e9073b000172a23b","is_single":false,"is_index_field":false,"index_name":"t_5","help_text":"","status":"new"},"out_tenant_id":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","is_unique":false,"description":"out_tenant_id","label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","index_name":"o_ei","config":{"display":0},"max_length":200,"status":"released"},"version":{"describe_api_name":"ChannelObj","is_index":false,"create_time":1656402836418,"length":8,"is_unique":false,"description":"version","label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"created_by":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836418,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"relevant_team":{"describe_api_name":"ChannelObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1656402836431,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"62bab3940a56450001cf019b","is_single":false,"label_r":"相关团队","is_index_field":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"record_type":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836430,"is_encrypted":false,"auto_adapt_places":false,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"62bab3940a56450001cf019a","is_single":false,"label_r":"业务类型","is_index_field":false,"index_name":"r_type","config":{},"help_text":"","status":"released"},"channel_number":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","is_single":false,"label_r":"渠道编号","index_name":"t_2","max_length":100,"is_index":true,"is_active":true,"create_time":1656402836432,"is_encrypted":false,"default_value":"","label":"渠道编号","api_name":"channel_number","_id":"62bab3940a56450001cf019c","is_index_field":false,"help_text":"","status":"new"},"data_own_department":{"describe_api_name":"ChannelObj","is_index":true,"is_active":true,"create_time":1656402836433,"is_encrypted":false,"auto_adapt_places":false,"description":"","is_unique":false,"where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"wheres":[],"api_name":"data_own_department","define_type":"package","_id":"62bab3940a56450001cf019d","is_single":true,"label_r":"归属部门","is_index_field":false,"index_name":"data_owner_dept_id","help_text":"","status":"new"},"name":{"describe_api_name":"ChannelObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"name","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","is_single":false,"label_r":"主属性","index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1656402836653,"is_encrypted":false,"default_value":"","label":"渠道","api_name":"name","_id":"62bab3940a56450001cf019e","is_index_field":false,"help_text":"","status":"new"},"_id":{"describe_api_name":"ChannelObj","is_index":false,"is_active":true,"create_time":1656402836418,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"}},"actions":{}}'''


    def setup() {
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig("fs-paas-appframework-config")).thenReturn(null)
    }

    def "fillObjectDataWithRefObject 1"() {
        setup:
//        PowerMockito.mockStatic(ObjectDescribeExt.class);
//        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)

        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(ObjectDataExt.class)
        PowerMockito.when(ObjectDataExt.getRefObjectDataIds([], [])).thenReturn([:])

        def miscService = new MetaDataMiscServiceImpl();
        def sourceObject = new ObjectDescribe(["fields": ["abc": ["api_name": "abc", "type": "group", "group_type": "what"]]])
        def objectDataList = [new ObjectData(["abc": "111"])]
        def user = new User("1", "1")
        when:
        miscService.fillObjectDataWithRefObject(sourceObject, objectDataList, user)
        then:
        true
    }

    def "fillObjectDataObjectManyWithRefObject"() {
        setup:
//        PowerMockito.mockStatic(ObjectDescribeExt.class);
//        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)

        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(ObjectDataExt.class)
        PowerMockito.when(ObjectDataExt.getRefObjectDataIds([], [])).thenReturn([:])

        def miscService = new MetaDataMiscServiceImpl();
        def sourceObject = new ObjectDescribe(["fields": ["abc": ["api_name": "abc", "type": "group", "group_type": "what"]]])
        def objectDataList = [new ObjectData(["abc": "111"])]
        def user = new User("1", "1")
        when:
        miscService.fillObjectDataObjectManyWithRefObject(sourceObject, objectDataList, user)
        then:
        true
    }


    def "fillObjectDataWithRefObject 2"() {
        setup:
//        PowerMockito.mockStatic(ObjectDescribeExt.class);
//        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)

        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(ObjectDataExt.class)
        PowerMockito.when(ObjectDataExt.getRefObjectDataIds([], [])).thenReturn([:])

        def miscService = new MetaDataMiscServiceImpl();
        def sourceObject = new ObjectDescribe(["fields": ["abc": ["api_name": "abc", "type": "group", "group_type": "what"]]])
        def objectDataList = [new ObjectData(["abc": "111"])]
        def user = new User("1", "1")
        def refObjectDataMap = ["abc": [new ObjectData()]]
        when:
        miscService.fillObjectDataWithRefObject(sourceObject, objectDataList, user, refObjectDataMap)
        then:
        true
    }

    def "fillObjectDataWithRefObject 3"() {
        setup:
//        PowerMockito.mockStatic(ObjectDescribeExt.class);
//        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)

        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(ObjectDataExt.class)
        PowerMockito.when(ObjectDataExt.getRefObjectDataIds([], [])).thenReturn([:])

        def miscService = new MetaDataMiscServiceImpl();
        def sourceObject = new ObjectDescribe(["fields": ["abc": ["api_name": "abc", "type": "group", "group_type": "what"]]])
        def objectDataList = [new ObjectData(["abc": "111"])]
        def user = new User("1", "1")
        def refObjectDataMap = ["abc": [new ObjectData()]]
        when:
        miscService.fillObjectDataWithRefObject(sourceObject, objectDataList, user, refObjectDataMap, false)
        then:
        true
    }


    def "fillObjectDataWithRefObject"() {
        setup:
        PowerMockito.mockStatic(ObjectDescribeExt.class);
        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)

        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(ObjectDataExt.class)
        PowerMockito.when(ObjectDataExt.getRefObjectDataIds([], [])).thenReturn([:])

        def miscService = new MetaDataMiscServiceImpl();

        def sourceObject = new ObjectDescribe()
        def objectDataList = [new ObjectData(["abc": "111"])]
        def user = new User("1", "1")
        def refObjectDataMap = ["abc": [new ObjectData()]]
        def handleSFAObject = false


        def refFieldList = [new ObjectReferenceFieldDescribe(["api_name": "abc"])]
        when:
        miscService.fillObjectDataWithRefObject(sourceObject, objectDataList, user, refObjectDataMap, handleSFAObject, refFieldList)
        then:
        true
    }


    def "fillRefObjDisplayName"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.when(AppFrameworkConfig.isSupportDisplayNameFieldEnterprise("1")).thenReturn(true)
        PowerMockito.when(AppFrameworkConfig.isSupportDisplayNameField("Account")).thenReturn(true)

//        PowerMockito.mockStatic(ObjectDescribeExt.class);
//        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)


        def describeLogicService = Stub(DescribeLogicService)
        def metaDataFindService = Stub(MetaDataFindService)
        def miscService = new MetaDataMiscServiceImpl(describeLogicService: describeLogicService, metaDataFindService: metaDataFindService)
        describeLogicService.findDescribeListWithoutFields("1", Sets.newHashSet("Account")) >> [new ObjectDescribe(["api_name": "Account", "is_open_display_name": true])]
        metaDataFindService.findObjectDataByIdsIgnoreAll("", [], "") >> [new ObjectData(["_id": "id123", "display_name": "d11"]), new ObjectData(["_id": "id2", "display_name": "d22"])]

        def objectDataList = [new ObjectData(["abc": ["Account": ["id1", "id2"], "Contact": ["id11"]]])]
        def refFieldList = [new ObjectReferenceFieldDescribe(["api_name": "abc", "target_api_name": "Account"])]
        def idNameMap = ["id1": "name1", "id123": "name123", "id2": "name2", "22": "name22", "33": "name33"]
        def user = new User("1", "1")
        def idListMap = ["Account": ["id123", "id2"]]

        when:
        miscService.fillRefObjDisplayName(user, idListMap, idNameMap, ["abc": refFieldList])
        then:
        true
    }

    def "fillRefObjNameToData"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.when(AppFrameworkConfig.isSupportDisplayNameFieldEnterprise("1")).thenReturn(true)
        PowerMockito.when(AppFrameworkConfig.isSupportDisplayNameField("Account")).thenReturn(true)

//        PowerMockito.mockStatic(ObjectDescribeExt.class);
//        PowerMockito.when(ObjectDescribeExt.isWhatField(null)).thenReturn(true)


        def describeLogicService = Stub(DescribeLogicService)
        def metaDataFindService = Stub(MetaDataFindService)
        def miscService = new MetaDataMiscServiceImpl(describeLogicService: describeLogicService, metaDataFindService: metaDataFindService)
//        describeLogicService.findDescribeListWithoutFields("1", Sets.newHashSet("Account")) >> [new ObjectDescribe(["api_name":"Account", "is_open_display_name":true])]
//        metaDataFindService.findObjectDataByIdsIgnoreAll("",[],"") >> [new ObjectData(["_id":"id123", "display_name":"d11"]), new ObjectData(["_id":"id2", "display_name":"d22"])]

        def objectDataList = [new ObjectData(["abc_id": [["_id": "id1"], ["id": "id2"]], "abc": "123", "abc_many": ["22", "33"]])]
        def refFieldList = [
                new ObjectReferenceFieldDescribe(["api_name": "abc", "type": "object_reference", "target_api_name": "Account"]),
                new WhatListFieldDescribe(["api_name": "abc_what_list", "type": "group", "group_type": "what_list", "fields": ["id_field": "abc_id"]]),
                new ObjectReferenceManyFieldDescribe(["api_name": "abc_many", "type": "object_reference_many", "target_api_name": "Account"])
        ]
        def idListMap = ["id1": "name1", "id123": "name123", "id2": "name2", "22": "name22", "33": "name33"]

        when:
        miscService.fillRefObjNameToData(objectDataList, refFieldList, idListMap)
        then:
        true
    }


//    def "fillNameFromNameCache"() {
//        given:
//        PowerMockito.mockStatic(ConfigFactory.class)
//        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)
//
//        def dataProxy = Stub(ObjectDataProxy)
//        def metaDataFindService = Stub(MetaDataFindService)
//        def miscService = new MetaDataMiscServiceImpl(dataProxy: dataProxy, metaDataFindService:metaDataFindService)
//        dataProxy.findRecordName(null, "", []) >> [new NameCache(id: "11", name: "name11")]
////        describeLogicService.findDescribeListWithoutFields("1", Sets.newHashSet("Account")) >> [new ObjectDescribe(["api_name":"Account", "is_open_display_name":true])]
////        metaDataFindService.findObjectDataByIdsIgnoreAll("",[],"") >> [new ObjectData(["_id":"id123", "display_name":"d11"]), new ObjectData(["_id":"id2", "display_name":"d22"])]
//
//        def refObjectDataMap = ["Account":[new ObjectData(["abc_id":[["_id":"id1"],["id":"id2"]], "abc":"123", "abc_many":["22", "33"]])]]
//        def idNameMap = ["id1":"name1", "id123":"name123","id2":"name2","22":"name22","33":"name33"]
//        def source = ""
//        def user = new User("1", "1")
//        def idListMap = ["Account":["111"]]
//
//        when:
//        miscService.fillNameFromNameCache(source, user, refObjectDataMap, idListMap, idNameMap)
//        then:
//        true
//    }

    def "batchStartApproval"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def approvalFlowService = Stub(ApprovalFlowService)
        def miscService = new MetaDataMiscServiceImpl(approvalFlowService: approvalFlowService)
        approvalFlowService.batchStartApproval(null, null, null, null, null) >> [:]

        def objectDataList = [new ObjectData(["_id": "123", "object_describe_api_name": "Account"])]
        def dataMap = ["Accunt": ["abc": "111"]]
        def user = new User("1", "1")
        when:
        miscService.batchStartApproval(ApprovalFlowTriggerType.CREATE, user, objectDataList, dataMap)
        then:
        true
    }

    def "batchStartApproval2"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def approvalFlowService = Stub(ApprovalFlowService)
        def miscService = new MetaDataMiscServiceImpl(approvalFlowService: approvalFlowService)
        approvalFlowService.batchStartApproval(null, null, null, null, null) >> [:]

        def objectDataList = [new ObjectData(["_id": "123", "object_describe_api_name": "Account"])]
        def dataMap = ["Accunt": ["abc": "111"]]
        def user = new User("1", "1")
        when:
        miscService.batchStartApproval("1", user, objectDataList, dataMap, dataMap)
        then:
        true
    }

    def "batchStartApprovalAsynchronous"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def approvalFlowService = Stub(ApprovalFlowService)
        def miscService = new MetaDataMiscServiceImpl(approvalFlowService: approvalFlowService)
        approvalFlowService.batchStartApprovalAsynchronous(null, null, null, null, null) >> void

        def objectDataList = [new ObjectData(["_id": "123", "object_describe_api_name": "Account"])]
        def dataMap = ["Accunt": ["abc": "111"]]
        def user = new User("1", "1")
        when:
        miscService.batchStartApprovalAsynchronous(ApprovalFlowTriggerType.CREATE, user, objectDataList, dataMap)
        then:
        true
    }

    def "doDataPrivilegeCheck"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def providerManager = Stub(DataPrivilegeProviderManager)
        def userRoleInfoService = Stub(UserRoleInfoService)
        def dataPrivilegeService = Stub(DataPrivilegeService)
        def miscService = new MetaDataMiscServiceImpl(providerManager: providerManager, userRoleInfoService: userRoleInfoService,
                dataPrivilegeService: dataPrivilegeService)
        providerManager.getProvider("Account") >> null
        userRoleInfoService.isAdmin(null) >> false
        def user = new User("1", "1")
        dataPrivilegeService.checkDataPrivilege(user, ["111", "222"], "Account") >> ["111": Permissions.READ_WRITE, "222": Permissions.READ_WRITE]

        def sourceObject = new ObjectDescribe(["api_name": "Account", "fields": [:]])
        def objectDataList = [new ObjectData(["_id": "111"]), new ObjectData(["_id": "222"])]
        when:
        miscService.doDataPrivilegeCheck(user, objectDataList, sourceObject, "Edit")
        then:
        true
    }

    def "checkPrivilege"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def providerManager = Stub(DataPrivilegeProviderManager)
        def userRoleInfoService = Stub(UserRoleInfoService)
        def dataPrivilegeService = Stub(DataPrivilegeService)
        def functionPrivilegeService = Stub(FunctionPrivilegeService)
        def miscService = new MetaDataMiscServiceImpl(providerManager: providerManager, userRoleInfoService: userRoleInfoService,
                dataPrivilegeService: dataPrivilegeService, functionPrivilegeService: functionPrivilegeService)
        providerManager.getProvider("Account") >> null
        userRoleInfoService.isAdmin(null) >> false
        def user = new User("1", "1")
        dataPrivilegeService.checkDataPrivilege(user, ["111", "222"], "Account") >> ["111": Permissions.READ_WRITE, "222": Permissions.READ_WRITE]
        functionPrivilegeService.funPrivilegeCheck(user, "Account", "Edit") >> true

        def sourceObject = new ObjectDescribe(["api_name": "Account", "fields": [:]])
        def objectDataList = [new ObjectData(["_id": "111"]), new ObjectData(["_id": "222"])]
        when:
        miscService.checkPrivilege(user, objectDataList, sourceObject, "Edit")
        then:
        true
    }

    def "checkOutUserDataPrivilege"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(RequestContext.builder().build())

        def dataPrivilegeService = Stub(DataPrivilegeService)
        def userRoleInfoService = Stub(UserRoleInfoService)
        def miscService = new MetaDataMiscServiceImpl(dataPrivilegeService: dataPrivilegeService, userRoleInfoService: userRoleInfoService)
        def user = new User("1", "1")
        dataPrivilegeService.getOutDataPrivilege(_ as User, _ as String, _ as String) >> OutDataPrivilege.PUBLIC_READONLY
        userRoleInfoService.isAdmin(null) >> false

        def sourceObject = new ObjectDescribe(["api_name": "Account", "fields": [:]])
        def objectDataList = [new ObjectData(["_id": "111"]), new ObjectData(["_id": "222"])]
        when:
        miscService.checkOutUserDataPrivilege(user, objectDataList, sourceObject)
        then:
        true
    }

    def "checkSameDownstreamWrite"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(RequestContext.builder().build())

        def dataPrivilegeService = Stub(DataPrivilegeService)
        def userRoleInfoService = Stub(UserRoleInfoService)
        def miscService = new MetaDataMiscServiceImpl(dataPrivilegeService: dataPrivilegeService, userRoleInfoService: userRoleInfoService)
        def user = new User("1", "1")
        dataPrivilegeService.getOutDataPrivilege(user, "", "Account") >> OutDataPrivilege.PUBLIC_READONLY
        userRoleInfoService.isAdmin(null) >> false

        def sourceObject = new ObjectDescribe(["api_name": "Account", "fields": [:]])
        def objectDataList = [new ObjectData(["_id": "111"]), new ObjectData(["_id": "222"])]
        when:
        miscService.checkSameDownstreamWrite(user, objectDataList, sourceObject)
        then:
        true
    }

    def "checkPublicReadonly"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(RequestContext.builder().build())

        def orgService = Stub(OrgService)
        def miscService = new MetaDataMiscServiceImpl(orgService: orgService)
        def user = new User("1", "1")
        def userInfo = new UserInfo()
        userInfo.tenantId = "1"
        userInfo.id = "********"
        orgService.batchGetResponsibleEmployeeByUserId(_ as String, _ as String, _ as Set<String>) >> [userInfo]

        def objectDataList = [new ObjectData(["_id": "111", "out_owner": ["********"]]), new ObjectData(["_id": "222"])]
        when:
        miscService.checkPublicReadonly(user, objectDataList)
        then:
        true
    }

    def "checkSameDownstreamReadOnly"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(RequestContext.builder().build())

        def orgService = Stub(OrgService)
        def stubUserRoleInfoService = Stub(UserRoleInfoService)
        def miscService = new MetaDataMiscServiceImpl(orgService: orgService, userRoleInfoService: stubUserRoleInfoService)

        def user = new User("1", "1")
        def userInfo = new UserInfo()
        userInfo.tenantId = "1"
        userInfo.id = "********"
        orgService.batchGetResponsibleEmployeeByUserId(_ as String, _ as String, _ as Set<String>) >> [userInfo]
        stubUserRoleInfoService.isAdmin(_) >> true
        def sourceObject = new ObjectDescribe(["api_name": "Account", "fields": [:]])
        def objectDataList = [
                new ObjectData(["_id": "111", "out_owner": ["********"]]),
                new ObjectData(["_id": "222", "out_tenant_id": "3333333"])]
        when:
        miscService.checkSameDownstreamReadOnly(user, objectDataList, sourceObject, OutDataPrivilege.PUBLIC_READONLY)
        then:
        true
    }

    def "getPermissionMapResultByDetailDataList"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig.class)
//        PowerMockito.when(AppFrameworkConfig.getWLJTempRecordMap()).thenReturn(["1": ["default": Sets.newHashSet()]])


        def metaDataFindService = Stub(MetaDataFindService)
        def dataPrivilegeService = Stub(DataPrivilegeService)

        def miscService = new MetaDataMiscServiceImpl(metaDataFindService: metaDataFindService, dataPrivilegeService: dataPrivilegeService)
        def user = new User("1", "1")
        dataPrivilegeService.checkDataPrivilege(user, ["M111", "M222"], "master") >>
                ["M111": Permissions.READ_WRITE, "M222": Permissions.NO_PERMISSION]
        metaDataFindService.findObjectDataByIdsIgnoreFormula(user.getTenantId(), ["111", "222"], "AccountDetail") >>
                [new ObjectData(["_id": "111", "masterId": "M111"]),
                 new ObjectData(["_id": "222", "masterId": "M222"])]


        def detailObject = new ObjectDescribe(["api_name": "AccountDetail", "fields": ["masterId": ["api_name": "masterId", "type": "master_detail"]]])
        def idList = ["111", "222"]
        def detailIdToMasterIdMap = ["111": "M111", "222": "M222"]
        when:
        miscService.getPermissionMapResultByDetailDataList(user, idList, detailObject, "master", detailIdToMasterIdMap)
        then:
        true
    }

    def "canUpdateAccountName"() {
        given:
        def crmService = Stub(CrmService)
        crmService.getConfigValue("1", 11) >> "1"
        def miscService = new MetaDataMiscServiceImpl(crmService: crmService)
        when:
        miscService.canUpdateAccountName("1")
        then:
        true
    }

    def "validateLookupData"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig)
        PowerMockito.when(AppFrameworkConfig.isSkipValidateLookupGrayTenant("")).thenReturn(false)
        PowerMockito.when(AppFrameworkConfig.validateLookupSearchDataInDb()).thenReturn(true)

        PowerMockito.mockStatic(I18N)
        PowerMockito.when(I18N.text(I18NKey.UNSATISFIED_CONDITION, "1")).thenReturn("zzzz")

        def refField = new ObjectReferenceFieldDescribe([
                "api_name"         : "masterId",
                "describe_api_name": "AccountObj",
                "target_api_name"  : "AccountObj",
                "wheres"           : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ])
        def wheres = new Wheres()
        wheres.connector = "OR"
        wheres.filters = [FilterExt.of(Operator.EQ, "masterId", "M111").getFilter()]

        PowerMockito.mockStatic(ObjectDescribeExt)
        PowerMockito.when(ObjectDescribeExt.getWheresBy(refField.getWheres())).thenReturn([wheres])

        def metaDataFindService = Stub(MetaDataFindService)
        def describeLogicService = Stub(DescribeLogicService)
        def miscService = new MetaDataMiscServiceImpl(metaDataFindService: metaDataFindService, describeLogicService: describeLogicService)

        def user = new User("1", "1")
        metaDataFindService.findBySearchQueryIgnoreAll(user, "Account", null).getData() >>
                [new ObjectData(["_id": "111", "masterId": "M111"])]
        describeLogicService.findObjectsWithoutCopyIfGray(_ as String, _ as Collection<String>) >> ["AccountObj": new ObjectDescribe(["reference_describe_api_name": "AccountObj", "api_name": "AccountObj"])]

        def referenceField = ObjectReferenceWrapper.of(refField)
        def objectData = new ObjectData(["_id": "111", "masterId": "M111"])

        when:
        miscService.validateLookupData(user, objectData, referenceField)
        then:
        true
    }


    def "validateLookupDataIgnorePolygonal"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig)
        PowerMockito.when(AppFrameworkConfig.isSkipValidateLookupGrayTenant("")).thenReturn(false)
        PowerMockito.when(AppFrameworkConfig.validateLookupSearchDataInDb()).thenReturn(true)

        PowerMockito.mockStatic(I18N)
        PowerMockito.when(I18N.text(I18NKey.UNSATISFIED_CONDITION, "1")).thenReturn("zzzz")

        def refField = new ObjectReferenceFieldDescribe([
                "api_name"         : "masterId",
                "describe_api_name": "AccountObj",
                "target_api_name"  : "AccountObj",
                "wheres"           : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ])
        def wheres = new Wheres()
        wheres.connector = "OR"
        wheres.filters = [FilterExt.of(Operator.EQ, "masterId", "M111").getFilter()]

        PowerMockito.mockStatic(ObjectDescribeExt)
        PowerMockito.when(ObjectDescribeExt.getWheresBy(refField.getWheres())).thenReturn([wheres])

        def metaDataFindService = Stub(MetaDataFindService)
        def describeLogicService = Stub(DescribeLogicService)
        def miscService = new MetaDataMiscServiceImpl(metaDataFindService: metaDataFindService, describeLogicService: describeLogicService)
        def user = new User("1", "1")
        metaDataFindService.findBySearchQueryIgnoreAll(user, "Account", null).getData() >>
                [new ObjectData(["_id": "111", "masterId": "M111"])]


        def objectData = new ObjectData(["_id": "111", "masterId": "M111"])

        describeLogicService.findObjectsWithoutCopyIfGray(_ as String, _ as Collection<String>) >> ["AccountObj": new ObjectDescribe(["reference_describe_api_name": "AccountObj", "api_name": "AccountObj"])]

        when:
        miscService.validateLookupDataIgnorePolygonal(user, objectData, refField)
        then:
        true
    }


    def "validateLookupData 2"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig)
        PowerMockito.when(AppFrameworkConfig.isSkipValidateLookupGrayTenant("")).thenReturn(false)
        PowerMockito.when(AppFrameworkConfig.validateLookupSearchDataInDb()).thenReturn(true)

        PowerMockito.mockStatic(I18N)
        PowerMockito.when(I18N.text(I18NKey.UNSATISFIED_CONDITION, "1")).thenReturn("zzzz")

        def user = new User("1", "1")

        def metaDataFindService = Stub(MetaDataFindService)
        def describeLogicService = Stub(DescribeLogicService)
        def miscService = new MetaDataMiscServiceImpl(metaDataFindService: metaDataFindService, describeLogicService: describeLogicService)
        metaDataFindService.findBySearchQueryIgnoreAll(user, "Account", null).getData() >>
                [new ObjectData(["_id": "111", "masterId": "M111"])]

        def objectData = new ObjectData(["_id": "111", "masterId": "M111"])
        def describe = new ObjectDescribe(["fields": ["masterId": [
                "api_name"         : "masterId",
                "type"             : "object_reference",
                "describe_api_name": "AccountObj",
                "target_api_name"  : "AccountObj",
                "wheres"           : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ]]])
        describeLogicService.findObjectsWithoutCopyIfGray(_ as String, _ as Collection<String>) >> ["AccountObj": new ObjectDescribe(["reference_describe_api_name": "AccountObj", "api_name": "AccountObj"])]

        when:
        miscService.validateLookupData(user, [objectData], describe)
        then:
        true
    }


    def "generateTemplate"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(AppFrameworkConfig)
        PowerMockito.when(AppFrameworkConfig.isSkipValidateLookupGrayTenant("")).thenReturn(false)
        PowerMockito.when(AppFrameworkConfig.validateLookupSearchDataInDb()).thenReturn(true)

        PowerMockito.mockStatic(I18N)
        PowerMockito.when(I18N.text(I18NKey.UNSATISFIED_CONDITION, "1")).thenReturn("zzzz")


        def gdsHandler = Stub(GDSHandler)
        def excelUtil = Stub(ExcelUtil)
        def webFileUtil = Stub(WebFileUtil.class)

        def user = new User("1", "1")
        gdsHandler.getEAByEI("1") >> "fs"

        def rowList = [
                Pair.of("record_type", new RecordTypeFieldDescribe(["api_name": "record_type"]))
        ]


        def sampleList = [["default__c|biz1|biz2"]]
        excelUtil.genarateTemplate("fs", user.getUserId(),
                "客户",
                rowList,
                sampleList) >> [["default__c", "biz1", "biz2"]]
        webFileUtil.uploadFile("fs", "1", new ByteArrayOutputStream()) >> "tnpath"

        def miscService = new MetaDataMiscServiceImpl(gdsHandler: gdsHandler, excelUtil: excelUtil, webFileUtil: webFileUtil)


        when:
        miscService.generateTemplate(user, "客户", rowList, sampleList)
        then:
        true
    }

    def "getUpdatedFieldValueMapFromUpdateObjectDataList"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def objectData = new ObjectData(["_id": "111", "masterId": "M111"])
        def objectDataInDB = Stub(ObjectData)
        objectDataInDB.setId("111")
        def describe = new ObjectDescribe(["fields": ["masterId": [
                "api_name": "masterId",
                "type"    : "object_reference",
                "wheres"  : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ]]])
        def metaDataFindService = Stub(MetaDataFindService)
        def user = new User("1", "1")
        def miscService = new MetaDataMiscServiceImpl(metaDataFindService: metaDataFindService)
        when:
        metaDataFindService.findObjectDataByIds(_ as String, _ as List<String>, _ as String) >> objectDataInDB
        def res = miscService.getUpdatedFieldValueMapFromUpdateObjectDataList(user, [objectData], describe)
        then:
        noExceptionThrown()
    }

    def "findRecordName"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)                                                                                             \

        PowerMockito.mockStatic(ConfigHelper.class)
        def processInfo = PowerMockito.mock(ProcessInfo)
        PowerMockito.when(ConfigHelper.getProcessInfo()).thenReturn(processInfo)

        PowerMockito.mockStatic(FsGrayRelease)
        def grayReleaseBiz = PowerMockito.mock(FsGrayReleaseBiz)
        PowerMockito.when(FsGrayRelease.getInstance(Mockito.anyString())).thenReturn(grayReleaseBiz)

        def user = new User("1", "1")
        IActionContext context = ActionContextExt.of(user as User).getContext();
        def dataProxy = Stub(ObjectDataProxy)
        def miscService = new MetaDataMiscServiceImpl(dataProxy: dataProxy)

        INameCache nameCache = new NameCache()

        when:
        dataProxy.findRecordName(_ as IActionContext, _ as String, _ as List<String>) >> [nameCache]
        miscService.findRecordName(context, "accountObj", ids)
        then:
        noExceptionThrown();
        where:
        ids      || res
        []       || _
        ["1243"] || _
    }

    def "findNameByIds"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        PowerMockito.mockStatic(ConfigHelper.class)
        def processInfo = PowerMockito.mock(ProcessInfo)
        PowerMockito.when(ConfigHelper.getProcessInfo()).thenReturn(processInfo)

        PowerMockito.mockStatic(FsGrayRelease)
        def grayReleaseBiz = PowerMockito.mock(FsGrayReleaseBiz)
        PowerMockito.when(FsGrayRelease.getInstance(Mockito.anyString())).thenReturn(grayReleaseBiz)

        def user = new User("1", "1")
        IActionContext context = ActionContextExt.of(user as User).getContext();
        def dataProxy = Stub(ObjectDataProxy)
        def miscService = new MetaDataMiscServiceImpl(dataProxy: dataProxy)
        INameCache nameCache = new NameCache()

        when:
        dataProxy.findRecordName(_ as IActionContext, _ as String, _ as List<String>) >> [nameCache]
        miscService.findNameByIds(user, "AccountObj", ids)
        then:
        noExceptionThrown()
        where:
        ids      || res
        []       || _
        ["1243"] || _

    }


    def "fillUserInfo"() {
        given:
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig("fs-paas-appframework-config")).thenReturn(null)
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text("paas.udobj.system")).thenReturn("你好")

        def user = new User.UserBuilder().tenantId("74255").userId("1000").build()
        UserInfo userInfo = new UserInfo();
        userInfo.setId("123")
        userInfo.setName("你好")
        def orgService = Stub(OrgService)
        def miscService = new MetaDataMiscServiceImpl(orgService: orgService);
        def describe = new ObjectDescribe(["fields": ["masterId": [
                "api_name": "masterId",
                "type"    : "out_employee",
                "wheres"  : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ]]])
        ObjectData objectData = new ObjectData(["id": "1234"]);
        when:
        orgService.getUserNameByIds(_ as String, _ as String, _ as List<String>) >> [userInfo]
        miscService.fillUserInfo(describe, [objectData], user);
        then:
        noExceptionThrown()
    }


    def "fillDepartmentInfo"() {
        given:
        def orgService = Stub(OrgService)
        def miscService = new MetaDataMiscServiceImpl(orgService: orgService)
        def describe = new ObjectDescribe(["fields": ["masterId": [
                "api_name": "masterId",
                "type"    : "department",
                "wheres"  : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ]]])
        def user = new User.UserBuilder().tenantId("74255").userId("1000").build()
        ObjectData objectData = new ObjectData(["id": "1234"]);
        when:
        miscService.fillDepartmentInfo(describe, [objectData], user, QueryDeptInfoByDeptIds.DeptStatusEnum.ALL)
        then:
        noExceptionThrown()
    }

    def "getCountryCascadeJsonString"() {
        given:
        def countryAreaService = Stub(CountryAreaService)
        MetaDataMiscService metaDataMiscService = new MetaDataMiscServiceImpl(countryAreaService: countryAreaService)
        when:
        countryAreaService.getCountryCascadeJsonString() >> "测试"
        metaDataMiscService.getCountryCascadeJsonString();
        then:
        true
    }

    def "fillPhoneNumberInformation"() {
        given:
        def phoneNumberService = Stub(PhoneNumberService)
        MetaDataMiscService metaDataMiscService = new MetaDataMiscServiceImpl(phoneNumberService: phoneNumberService)
        QueryPhoneNumberInformation.Result queryPhoneNumberInformation = QueryPhoneNumberInformation.Result.builder()
                .city("北京")
                .mobile("1234567")
                .province("北京")
                .build()
        ObjectData objectData = new ObjectData(["id": "1234"]);
        def describe = new ObjectDescribe(["fields": ["masterId": [
                "api_name": "masterId",
                "type"    : "department",
                "wheres"  : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ]]])
        when:
        phoneNumberService.batchQueryPhoneNumberInfo(_) >> [queryPhoneNumberInformation]
        metaDataMiscService.fillPhoneNumberInformation(describe, objectData)
        then:
        noExceptionThrown()

    }

    def "fillPhoneNumberInformation(IObjectDescribe describe, List<IObjectData> objectDataList)"() {
        given:
        def phoneNumberService = Stub(PhoneNumberService)
        MetaDataMiscService metaDataMiscService = new MetaDataMiscServiceImpl(phoneNumberService: phoneNumberService)
        QueryPhoneNumberInformation.Result queryPhoneNumberInformation = QueryPhoneNumberInformation.Result.builder()
                .city("北京")
                .mobile("1234567")
                .province("北京")
                .build()
        ObjectData objectData = new ObjectData(["id": "1234", "masterId": "1223455"]);
        def describe = new ObjectDescribe(["fields": ["masterId": [
                "api_name": "masterId",
                "type"    : "phone_number",
                "wheres"  : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
        ]]])
        when:
        phoneNumberService.batchQueryPhoneNumberInfo(_) >> [queryPhoneNumberInformation]
        metaDataMiscService.fillPhoneNumberInformation(describe, [objectData])
        then:
        noExceptionThrown()
    }

    def "parsePaymentObjOrderNames"() {
        given:

        PowerMockito.mockStatic(ConfigHelper.class)
        def processInfo = PowerMockito.mock(ProcessInfo)
        PowerMockito.when(ConfigHelper.getProcessInfo()).thenReturn(processInfo)

        PowerMockito.mockStatic(FsGrayRelease)
        def grayReleaseBiz = PowerMockito.mock(FsGrayReleaseBiz)
        PowerMockito.when(FsGrayRelease.getInstance(Mockito.anyString())).thenReturn(grayReleaseBiz)

        def dataProxy = Stub(ObjectDataProxy)
        MetaDataMiscService miscService = new MetaDataMiscServiceImpl(dataProxy: dataProxy)
        ObjectData objectData = new ObjectData(["id": "1234", "masterId": "1223455", "order_id": "875643432"]);
        def describe = new ObjectDescribe(
                ["fields"     : ["masterId": [
                        "api_name": "masterId",
                        "type"    : "phone_number",
                        "wheres"  : [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]
                ]], "api_name": "PaymentObj"])
        def user = new User.UserBuilder().tenantId("74255").userId("1000").build()
        INameCache nameCache = new NameCache();
        nameCache.setId("123455")
        nameCache.setDescribeApiName("PaymentObj")
        nameCache.setName("api_name")
        when:
        dataProxy.findRecordName(_ as IActionContext, _ as String, _ as List<String>) >> [nameCache]
        miscService.parsePaymentObjOrderNames(describe, [objectData], user, true);
        then:
        noExceptionThrown()
    }

    def "fillCountryAreaLabel"() {
        given:

        def user = new User.UserBuilder().tenantId("74255").userId("1000").build()
        PowerMockito.mockStatic(ConfigFactory.class)
        PowerMockito.when(ConfigFactory.getConfig(null, {})).thenReturn(null)

        def applicationContext = PowerMockito.mock(ApplicationContext.class)

        PowerMockito.mockStatic(SpringUtil.class)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext);
        def countryAreaService1 = PowerMockito.mock(CountryAreaService.class)
        PowerMockito.when(applicationContext.getBean(CountryAreaService.class)).thenReturn(countryAreaService1)

        PowerMockito.mockStatic(CountryAreaManager.class)
        PowerMockito.when(CountryAreaManager.getLabelByCode(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("测测是")

        PowerMockito.mockStatic(FsGrayRelease)
        def grayReleaseBiz = PowerMockito.mock(FsGrayReleaseBiz)
        PowerMockito.when(FsGrayRelease.getInstance(Mockito.anyString())).thenReturn(grayReleaseBiz)
//        PowerMockito.when(grayReleaseBiz.isAllow(Mockito.anyString(), Mockito.anyString())).thenReturn(true)
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(RequestContext.builder().user(user).build())

        ObjectData objectData = new ObjectData(["id": "1234", "masterId": "1223455", "order_id": "875643432"]);
        def countryAreaService = Stub(CountryAreaService)
        def metaDataGlobalService = Stub(MetaDataGlobalService)
        def maskFieldLogicService = Stub(MaskFieldLogicService)
        MetaDataMiscService miscService = new MetaDataMiscServiceImpl(countryAreaService: countryAreaService, metaDataGlobalService: metaDataGlobalService, maskFieldLogicService: maskFieldLogicService)

        when:
        countryAreaService.batchQueryLabelsByCodes(_ as String, _ as String, _ as Set<String>) >> ["masterId", "1223455"]
        miscService.fillCountryAreaLabel(describe, [objectData])
        then:
        noExceptionThrown()
        where:
        describe                                                                                                                                                                                                            || res
        new ObjectDescribe(["fields": ["masterId": ["api_name": "masterId", "type": "town", "wheres": [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]]], "api_name": "PaymentObj"]) || _
        new ObjectDescribe(["fields": ["masterId": ["api_name": "masterId", "type": "city", "wheres": [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]]], "api_name": "PaymentObj"]) || _
    }

    def "fillImageInformation"() {
        given:
        def user = new User("74255", "1000")

        def sharedFileService = Stub(SharedFileService)
        def gdsHandler = Stub(GDSHandler)
        MetaDataMiscService miscService = new MetaDataMiscServiceImpl(sharedFileService: sharedFileService, gdsHandler: gdsHandler)

        def describe = new ObjectDescribe(["fields": ["masterId": ["api_name": "masterId", "type": IFieldType.IMAGE, "wheres": [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]]], "api_name": "PaymentObj"])
        ObjectData objectData = new ObjectData(["id": "1234", "masterId": [getImageInfo()], "order_id": "875643432"]);

        CreateFileShareIds.Result createFileShareIds = new CreateFileShareIds.Result(["fileIdMap": ["www.baidu.com.png": "ceshi"]])

        when:
        gdsHandler.getEAByEI() >> "74255"
        sharedFileService.createFileShareIds(_) >> createFileShareIds
        miscService.fillImageInformation(describe, objectData, detailObjectData, detailDescribeMap as Map<String, IObjectDescribe>, user)

        then:
        noExceptionThrown()
        where:
        detailObjectData                                                                                      | detailDescribeMap || res
        ["masterId": [new ObjectData(["id": "1234", "masterId": [getImageInfo()], "order_id": "875643432"])]] | [:]               || _
    }

    def "getImageInfo"() {
        ImageInfo imageInfo = new ImageInfo()
        imageInfo.setExt("12123")
        imageInfo.setPath("www.baidu.com")
        def json = JSON.toJSON(imageInfo)
        return json
    }

    def "fillMaskFieldValue"() {
        given:
        def userRoleInfoService = Stub(UserRoleInfoService)
        def orgService = Stub(OrgService)
        def maskFieldLogicService = Stub(MaskFieldLogicService)
        MetaDataMiscService miscService = new MetaDataMiscServiceImpl(userRoleInfoService: userRoleInfoService, orgService: orgService, maskFieldLogicService: maskFieldLogicService)


        def user = new User.UserBuilder().tenantId("74255").userId("1000").build()
        ObjectData objectData = new ObjectData(["id": "1234", "masterId": [getImageInfo()], "order_id": "875643432"]);
        def describe = new ObjectDescribe(["fields": ["masterId": ["is_show_mask": true, "remove_mask_roles": ["role": ["122"]], "api_name": "masterId", "type": IFieldType.IMAGE, "wheres": [["filters": [["operator": "EQ", "field_values": ["M111"], "field_name": "masterId"]]]]]], "api_name": "PaymentObj"])
        QueryDeptInfoByUserIds.MainDeptInfo mainDeptInfo = new QueryDeptInfoByUserIds.MainDeptInfo();
        mainDeptInfo.setUserId("1000")
        mainDeptInfo.setUserName("测试")
        mainDeptInfo.setLeaderId("1122")
        mainDeptInfo.setDeptId("123444")
        when:
        userRoleInfoService.isAdmin(_) >> true
        orgService.getMainDeptInfo(_, _, _) >> ["ces": mainDeptInfo]
        miscService.fillMaskFieldValue(user, [objectData], describe)
        then:
        noExceptionThrown()

    }

    def "test fillTreeViewObjectFullPath"() {
        given:
        def describeLogicService = Mock(DescribeLogicServiceImpl)
        def treeViewService = Mock(TreeViewServiceImpl)
        MetaDataMiscService miscService = new MetaDataMiscServiceImpl(describeLogicService: describeLogicService, 'treeViewService': treeViewService)
        def idListMap = ["ChannelObj": ['62c24fe267ee370001e80608']]
        def idNameMap = ['62c24fe267ee370001e80608': '大卖场','ChannelObj_62c24fe267ee370001e80608': '大卖场']
        def grayRule = new GrayRule('white:74255')
        AppFrameworkConfig.treeViewSupportObject = ['ChannelObj': grayRule]
        IObjectDescribe objectDescribe = new ObjectDescribe(JSON.parseObject(channelObjDescribe))
        def objectData = Spy(ObjectData)
        objectData.set(IObjectData.ID, '62c24fe267ee370001e80608')
        objectData.set(IObjectData.DESCRIBE_API_NAME, 'ChannelObj')
        objectData.set(IObjectData.NAME, '大卖场')
        objectData.set('superior_channel', '62c24f6767ee370001e802ba')
        objectData.set('tree_view', '62bab6eeb4d84300013daa37.62c24f6767ee370001e802ba')

        def objectData1 = Spy(ObjectData)
        objectData1.set(IObjectData.ID, '62bab6eeb4d84300013daa37')
        objectData1.set(IObjectData.DESCRIBE_API_NAME, 'ChannelObj')
        objectData1.set(IObjectData.NAME, '相关渠道')
        def objectData2 = Spy(ObjectData)
        objectData2.set(IObjectData.ID, '62c24f6767ee370001e802ba')
        objectData2.set(IObjectData.DESCRIBE_API_NAME, 'ChannelObj')
        objectData2.set(IObjectData.NAME, '现代渠道')
        objectData2.set('superior_channel', '62bab6eeb4d84300013daa37')
        objectData2.set('tree_view', '62bab6eeb4d84300013daa37')

        when:
        describeLogicService.findObjectsWithoutCopyIfGray('74255', ['ChannelObj']) >> ['ChannelObj': objectDescribe]
        treeViewService.findTreeViewObjDataListByIds(_, _, _, _) >> [objectData] >> [objectData1, objectData2]
        miscService.fillTreeViewObjectFullPath(User.systemUser('74255'), idListMap, idNameMap)
        then:
        idNameMap.get('ChannelObj_62c24fe267ee370001e80608') == '相关渠道/现代渠道/大卖场'
    }

}
