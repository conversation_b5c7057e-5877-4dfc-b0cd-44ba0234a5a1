package com.facishare.paas.appframework.metadata.duplicated;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class RedisCacheTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }
    @Autowired
    RedisDao redisDao;
    //
//    static String duplicatedSearchJson = "{\"createTime\":*************,\"createdBy\":\"1000\",\"dataScope\":\"[]\",\"describeApiName\":\"object_3xvwy__c\",\"effective\":true,\"enable\":true,\"id\":\"67fe43ac6e7c2e2c4b9ff333\",\"invalidNotDuplicateSearch\":false,\"lastModifiedBy\":\"1000\",\"lastModifiedTime\":*************,\"mark\":\"\",\"name\":\"查重规则名称\",\"operatorId\":\"1000\",\"ruleApiName\":\"rule_hror4__c\",\"supportImport\":false,\"tenantId\":\"74255\",\"type\":\"NEW\",\"useableRules\":{\"relatedDescribes\":[],\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"emptyPolicy\":\"IGNORE_EMPTY\",\"fieldName\":\"field_hK624__c\",\"fieldValue\":\"DISTANCE\",\"range\":\"300\"}],\"connector\":\"OR\"}],\"showFields\":[\"field_hK624__c\"],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0,\"wheres\":\"[]\"}\n";
//
//    // 用户提供的实际对象描述JSON数据
//    static String objectDescribeJson = "{\"bigObject\":false,\"containerDocument\":{\"tenant_id\":\"74255\",\"package\":\"CRM\",\"is_active\":true,\"last_modified_time\":1744601929661,\"create_time\":1744601905709,\"description\":\"\",\"last_modified_by\":\"1000\",\"display_name\":\"查重支持范围\",\"created_by\":\"1000\",\"version\":2,\"is_open_display_name\":false,\"index_version\":1,\"icon_index\":0,\"is_deleted\":false,\"api_name\":\"object_3xvwy__c\",\"icon_path\":\"\",\"is_udef\":true,\"define_type\":\"custom\",\"short_name\":\"paC\",\"_id\":\"67fc8331193a5b00077dea9e\",\"fields\":{\"tenant_id\":{\"describe_api_name\":\"object_3xvwy__c\",\"is_index\":false,\"is_active\":true,\"create_time\":1744601905709,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"租户ID\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"index_name\":\"ei\",\"max_length\":200,\"status\":\"released\"},\"field_hK624__c\":{\"describe_api_name\":\"object_3xvwy__c\",\"auto_location\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"location\",\"is_required\":false,\"enable_clone\":true,\"define_type\":\"custom\",\"is_single\":false,\"index_name\":\"t_2\",\"is_index\":true,\"is_active\":true,\"create_time\":1744601928940,\"is_encrypted\":false,\"label\":\"定位\",\"is_geo_index\":false,\"field_num\":10,\"inherit_type\":3,\"api_name\":\"field_hK624__c\",\"range_limit\":false,\"_id\":\"67fc8349193a5b00077deb54\",\"radius_range\":100,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"new\"}},\"release_version\":\"6.4\",\"actions\":{}},\"fieldDescribeMap\":{\"tenant_id\":{\"active\":true,\"apiName\":\"tenant_id\",\"containerDocument\":{\"$ref\":\"$.containerDocument.fields.tenant_id\"},\"createTime\":1744601905709,\"defaultIsExpression\":false,\"defaultToZero\":false,\"defineType\":\"system\",\"describeApiName\":\"object_3xvwy__c\",\"description\":\"tenant_id\",\"enableClone\":true,\"enableMultiLang\":false,\"encrypted\":false,\"index\":false,\"indexField\":false,\"indexName\":\"ei\",\"isNeedConvert\":false,\"label\":\"租户ID\",\"maxLength\":200,\"pattern\":\"\",\"required\":true,\"status\":\"released\",\"type\":\"text\",\"unique\":false},\"field_hK624__c\":{\"active\":true,\"apiName\":\"field_hK624__c\",\"autoLocation\":false,\"containerDocument\":{\"$ref\":\"$.containerDocument.fields.field_hK624__c\"},\"createTime\":1744601928940,\"defaultIsExpression\":false,\"defaultToZero\":false,\"defineType\":\"custom\",\"describeApiName\":\"object_3xvwy__c\",\"enableClone\":true,\"enableMultiLang\":false,\"encrypted\":false,\"fieldNum\":10,\"helpText\":\"\",\"id\":\"67fc8349193a5b00077deb54\",\"index\":true,\"indexField\":false,\"indexName\":\"t_2\",\"inheritType\":3,\"isGeoIndex\":false,\"label\":\"定位\",\"radiusRange\":false,\"rangeLimit\":false,\"required\":false,\"status\":\"new\",\"type\":\"location\",\"unique\":false}},\"openDisplayName\":false,\"publicObject\":false,\"releaseVersion\":\"6.4\",\"shortName\":\"paC\",\"socialObject\":false,\"udef\":true,\"version\":2}";
    private static String saveKeyStr
            = "[\n"
            + "   {\n"
            + "        \"dataDup\": {\n"
            + "            \"DUPLICATED_123_Account_name\": [\"001\", \"002\"],\n"
            + "            \"DUPLICATED_123_Account_phone\": [\"001\", \"003\"]\n"
            + "        },\n"
            + "        \"geoDup\": {\n"
            + "            \"DUPLICATED_GEO_123_Account_address\": {\n"
            + "                \"001\": [120.5, 30.5],\n"
            + "                \"002\": [120.6, 30.6]\n"
            + "            }\n"
            + "       }\n"
            + "    }\n"
            + "]";

    private static final String saveStr = "{\"dataDup\":{},\"geoDup\":{\"67ff6ab325ae8e76debf8bea_DUPLICATED:GEO:74255:object_3xvwy__c:0:field_hK624__c\":{\"67fc83a4193a5b00077ded17\":[116.331953,39.977195]}}}";
    private static final String simpleJson = "{\"dataDup\":{},\"geoDup\":{}}";

    private static final String keyContainerStr = "{\"duplicatedSearch\":{\"createTime\":*************,\"createdBy\":\"1000\",\"dataScope\":\"[]\",\"describeApiName\":\"object_3xvwy__c\",\"effective\":true,\"enable\":true,\"id\":\"67fe43ac6e7c2e2c4b9ff333\",\"invalidNotDuplicateSearch\":false,\"lastModifiedBy\":\"1000\",\"lastModifiedTime\":*************,\"mark\":\"\",\"name\":\"查重规则名称\",\"operatorId\":\"1000\",\"ruleApiName\":\"rule_hror4__c\",\"supportImport\":false,\"tenantId\":\"74255\",\"type\":\"NEW\",\"useableRules\":{\"relatedDescribes\":[],\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"emptyPolicy\":\"IGNORE_EMPTY\",\"fieldName\":\"field_hK624__c\",\"fieldValue\":\"DISTANCE\",\"range\":\"300\"}],\"connector\":\"OR\"}],\"showFields\":[\"field_hK624__c\"],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0,\"wheres\":\"[]\"},\"preciseLockKeys\":[\"DUPLICATED_SEARCH_LOCKDUPLICATED_SEARCH_74255_object_3xvwy__c_REDISSON_DUPLICATED_SEARCH_DATA_74255_object_3xvwy__c_rule_hror4__c_0_field_hK624__c_116.331953:39.977195\"],\"redisTempKey\":\"DUPLICATED_SEARCH_DATA_74255_object_3xvwy__c\",\"sourceDataId\":\"67fc83a4193a5b00077ded17\"}\n";
//    @Test
//    public void testSave2() {
////        String json = objectMapper.writeValueAsString(jsonMap);
//        Map saveKey = JSONObject.parseObject(simpleJson,Map.class);
//        Object o = redisDao.evalScript(SAVE_LUA_SCRIPT_V2,
//                Lists.newArrayList("67ff6ab325ae8e76debf8bea_DUPLICATED:GEO:74255:object_3xvwy__c:0:field_hK624__c"),
//                String.valueOf(600),
//                saveStr);
//        System.out.println(o);
//    }
//
//    @Test
//    public void test() {
////        String json = objectMapper.writeValueAsString(jsonMap);
//      redisDao.set("ceshi","1111");
//
//    }
//
//    @Test
//    public void testDuplicated(){
//        DuplicatedSearchKeyContainer keyContainer = JSONObject.parseObject( keyContainerStr, DuplicatedSearchKeyContainer.class);
//        List<String> searchKeys = Lists.newArrayList("{\"dataDup\":[],\"geoDup\":[\"67ff6ab325ae8e76debf8bea_DUPLICATED:GEO:74255:object_3xvwy__c:0:field_hK624__c:116.331953:39.977195:300\"]}");
//        Object object = redisDao.evalScript(SEARCH_LUA_SCRIPT_V2, searchKeys, "67ff6ab325ae8e76debf8bea_DUPLICATED_SEARCH_DATA_74255_object_3xvwy__c");
//        System.out.println(object);
//    }


}
