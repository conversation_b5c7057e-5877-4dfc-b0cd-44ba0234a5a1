package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.ILayoutRuleService
import com.facishare.paas.metadata.exception.ErrorCode
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.impl.LayoutRuleInfo
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.util.SpringUtil
import com.fxiaoke.release.FsGrayRelease
import com.fxiaoke.release.FsGrayReleaseBiz
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([AppFrameworkConfig, ConfigFactory, ActionContextExt, RequestContextManager, FsGrayRelease, User, UdobjGrayConfig, SpringUtil])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
class LayoutRuleLogicServiceImplTest extends Specification {
    LayoutRuleLogicService layoutRuleLogicService
    ILayoutRuleService layoutRuleService
    DescribeLogicService describeLogicService
    LayoutLogicService layoutLogicService
    LicenseService licenseService
    MetadataServiceException metadataServiceException
    User user

    def setup() {
        PowerMockito.mockStatic(ConfigFactory)
        PowerMockito.mockStatic(FsGrayRelease)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.mockStatic(UdobjGrayConfig)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.mockStatic(AppFrameworkConfig)
        PowerMockito.spy(ActionContextExt)
        PowerMockito.mock(User)
        def grayReleaseBiz = PowerMockito.mock(FsGrayReleaseBiz)
        def requestContext = PowerMockito.mock(RequestContext)
        def applicationContext = PowerMockito.mock(ApplicationContext)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(FsGrayRelease.getInstance(Mockito.anyString())).thenReturn(grayReleaseBiz)
        PowerMockito.when(UdobjGrayConfig.isAllow(Mockito.anyString(), Mockito.anyString())).thenReturn(true)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        PowerMockito.when(ConfigFactory.getConfig(Mockito.any())).thenReturn(null)
        describeLogicService = Mock(DescribeLogicService)
        layoutLogicService = Mock(LayoutLogicService)
        licenseService = Mock(LicenseService)
        layoutRuleService = PowerMockito.mock(ILayoutRuleService)
        metadataServiceException = new MetadataServiceException(ErrorCode.SYSTEM_ERROR, "测试异常情况")
        layoutRuleLogicService = new LayoutRuleLogicServiceImpl(layoutRuleService: layoutRuleService,
                describeLogicService: describeLogicService,
                layoutLogicService: layoutLogicService,
                licenseService: licenseService)
        user = User.systemUser('74255')
    }

    def "test createLayoutRule"() {
        given:
        String json = '''{
    "api_name": "layout_rule_xfl23__c",
    "label": "测试",
    "description": "",
    "layout_api_name": "layout_69z5h__c",
    "describe_api_name": "object_2dH3t__c",
    "status": 0,
    "main_field": "field_sSZ05__c",
    "main_field_branches": [
        {
            "main_field_filter": {
                "value_type": 0,
                "operator": "EQ",
                "field_name": "field_sSZ05__c",
                "field_values": [
                    "option1"
                ]
            },
            "branches": [
                {
                    "conditions": [
                        {
                            "value_type": 0,
                            "operator": "EQ",
                            "field_name": "field_e41b5__c",
                            "field_values": [
                                "11"
                            ]
                        },
                        {
                            "value_type": 0,
                            "operator": "EQ",
                            "field_name": "field_mPdkI__c",
                            "field_values": [
                                true
                            ]
                        }
                    ],
                    "result": {
                        "show_field": [
                            {
                                "field_api_name": "field_4Pcjg__c"
                            }
                        ],
                        "required_field": [
                            {
                                "field_api_name": "field_4Pcjg__c"
                            }
                        ]
                    }
                }
            ]
        }
    ]
}'''
        LayoutRuleInfo layoutRuleInfo = JSON.parseObject(json, LayoutRuleInfo.class)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe mainField = new TextFieldDescribe()
        mainField.setApiName("field_sSZ05__c")
        IFieldDescribe branch1 = new TextFieldDescribe()
        branch1.setApiName("field_e41b5__c")
        IFieldDescribe branch2 = new TextFieldDescribe()
        branch2.setApiName("field_mPdkI__c")
        IFieldDescribe branch3 = new TextFieldDescribe()
        branch3.setApiName("field_4Pcjg__c")
        objectDescribe.setFieldDescribes(Lists.newArrayList(mainField, branch1, branch2, branch3))
        def tenantId = "78057"
        when:
        layoutRuleService.findByDescribe(_, _) >> []
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        layoutRuleLogicService.createLayoutRule(User.systemUser(tenantId), layoutRuleInfo)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test createLayoutRule exception"() {
        given:
        String json =
                '''{
            "api_name": "layout_rule_xfl23__c",
            "label": "测试",
            "description": "",
            "layout_api_name": "layout_69z5h__c",
            "describe_api_name": "object_2dH3t__c",
            "status": 0,
            "main_field": "field_sSZ05__c",
            "main_field_branches": [
                {
                    "main_field_filter": {
                        "value_type": 0,
                        "operator": "EQ",
                        "field_name": "field_sSZ05__c",
                        "field_values": [
                            "option1"
                        ]
                    },
                    "branches": [
                        {
                            "conditions": [
                                {
                                    "value_type": 0,
                                    "operator": "EQ",
                                    "field_name": "field_e41b5__c",
                                    "field_values": [
                                        "11"
                                    ]
                                },
                                {
                                    "value_type": 0,
                                    "operator": "EQ",
                                    "field_name": "field_mPdkI__c",
                                    "field_values": [
                                        true
                                    ]
                                }
                            ],
                            "result": {
                                "show_field": [
                                    {
                                        "field_api_name": "field_4Pcjg__c"
                                    }
                                ],
                                "required_field": [
                                    {
                                        "field_api_name": "field_4Pcjg__c"
                                    }
                                ]
                            }
                        }
                    ]
                }
            ]
        }'''
        LayoutRuleInfo layoutRuleInfo = JSON.parseObject(json, LayoutRuleInfo.class)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe mainField = new TextFieldDescribe()
        mainField.setApiName("field_sSZ05__c")
        IFieldDescribe branch1 = new TextFieldDescribe()
        branch1.setApiName("field_e41b5__c")
        IFieldDescribe branch2 = new TextFieldDescribe()
        branch2.setApiName("field_mPdkI__c")
        IFieldDescribe branch3 = new TextFieldDescribe()
        branch3.setApiName("field_4Pcjg__c")
        objectDescribe.setFieldDescribes(Lists.newArrayList(mainField, branch1, branch2, branch3))
        def tenantId = "78057"
        when:
        layoutRuleService.findByDescribe(_, _) >> []
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).create(Mockito.anyString(), Mockito.any())
        layoutRuleLogicService.createLayoutRule(User.systemUser(tenantId), layoutRuleInfo)
        then:
        thrown(MetaDataBusinessException.class)
    }


    def "test batchCreateRule"() {
        given:
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo()
        def tenantId = "78057"
        when:
        layoutRuleLogicService.batchCreateRule(User.systemUser(tenantId), Lists.newArrayList(layoutRuleInfo))
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test batchCreateRule exception"() {
        given:
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo()
        def tenantId = "78057"
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).batchCreate(Mockito.anyString(), Mockito.anyList())
        layoutRuleLogicService.batchCreateRule(User.systemUser(tenantId), Lists.newArrayList(layoutRuleInfo))
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test updateLayoutRule"() {
        given:
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo()
        IObjectDescribe objectDescribe = new ObjectDescribe()
        def tenantId = "78057"
        when:
        layoutRuleService.findByDescribe(_, _) >> []
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        layoutRuleLogicService.updateLayoutRule(User.systemUser(tenantId), layoutRuleInfo)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test updateLayoutRule exception"() {
        given:
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo()
        IObjectDescribe objectDescribe = new ObjectDescribe()
        def tenantId = "78057"
        when:
        layoutRuleService.findByDescribe(_, _) >> []
        describeLogicService.findObjectWithoutCopyIfGray(_, _) >> objectDescribe
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).update(Mockito.anyString(), Mockito.any())
        layoutRuleLogicService.updateLayoutRule(User.systemUser(tenantId), layoutRuleInfo)
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test enableLayoutRule"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        def describeApiName = ""
        when:
        layoutRuleService.findByDescribe(_, _) >> []
        layoutLogicService.findLayoutByApiNames(_, _, _) >> [:]
        licenseService.batchGetModuleLicenses(_, _) >> [:]
        PowerMockito.when(AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote(Mockito.any())).thenReturn(isPass)
        layoutRuleLogicService.enableLayoutRule(User.systemUser(tenantId), apiName, describeApiName)
        then:
        notThrown(MetadataServiceException.class)
        where:
        isPass << [true, false]
    }

    def "test enableLayoutRule exception"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        def describeApiName = ""
        when:
        layoutRuleService.findByDescribe(_, _) >> []
        layoutLogicService.findLayoutByApiNames(_, _, _) >> [:]
        licenseService.batchGetModuleLicenses(_, _) >> [:]
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).updateStatus(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean(), Mockito.any())
        layoutRuleLogicService.enableLayoutRule(User.systemUser(tenantId), apiName, describeApiName)
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test disableLayoutRule"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        layoutRuleLogicService.disableLayoutRule(User.systemUser(tenantId), apiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test disableLayoutRule exception"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).updateStatus(Mockito.anyString(), Mockito.any(), Mockito.anyBoolean(), Mockito.any())
        layoutRuleLogicService.disableLayoutRule(User.systemUser(tenantId), apiName)
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test deleteLayoutRule"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        layoutRuleLogicService.deleteLayoutRule(User.systemUser(tenantId), apiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test deleteLayoutRule exception"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).delete(Mockito.anyString(), Mockito.anyString())
        layoutRuleLogicService.deleteLayoutRule(User.systemUser(tenantId), apiName)
        then:
        thrown(MetaDataBusinessException.class)
    }

    def "test findValidLayoutRuleByLayout"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).findByLayout(Mockito.anyString(), Mockito.anyString())
        layoutRuleLogicService.findValidLayoutRuleByLayout(User.systemUser(tenantId), apiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test findLayoutRuleByDescribe"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).findByDescribe(Mockito.anyString(), Mockito.anyString())
        layoutRuleLogicService.findLayoutRuleByDescribe(User.systemUser(tenantId), apiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test findLayoutRuleByLayout"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        def layoutApiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).findByLayout(Mockito.anyString(), Mockito.anyString())
        layoutRuleLogicService.findValidLayoutRuleByLayout(User.systemUser(tenantId), apiName, layoutApiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test findLayoutRuleByApiName"() {
        given:
        def tenantId = "78057"
        def layoutApiName = ""
        when:
        layoutRuleLogicService.findLayoutRuleByApiName(User.systemUser(tenantId), layoutApiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test findLayoutRuleByApiName exception"() {
        given:
        def tenantId = "78057"
        def layoutApiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).findByApiNameAndTenantId(Mockito.anyString(), Mockito.anyString())
        layoutRuleLogicService.findLayoutRuleByApiName(User.systemUser(tenantId), layoutApiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test deleteLayoutRuleByLayout"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        def layoutApiName = ""
        when:
        layoutRuleLogicService.deleteLayoutRuleByLayout(User.systemUser(tenantId), apiName, layoutApiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    def "test deleteLayoutRuleByLayout exception"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        def layoutApiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).deleteByLayoutApiName(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())
        layoutRuleLogicService.deleteLayoutRuleByLayout(User.systemUser(tenantId), apiName, layoutApiName)
        then:
        thrown(MetaDataBusinessException.class)
    }


    def "test deleteLayoutRuleByDescribeApiName"() {
        given:
        def tenantId = "78057"
        def apiName = ""
        when:
        PowerMockito.doThrow(metadataServiceException).when(layoutRuleService).deletedByDescribe(Mockito.anyString(), Mockito.any())
        layoutRuleLogicService.deleteLayoutRuleByDescribeApiName(User.systemUser(tenantId), apiName)
        then:
        notThrown(MetadataServiceException.class)
    }

    @Unroll
    def "布局规则时间校验: #scenario [数据库时间:#dbTime, 参数时间:#paramTime]"() {
        given: "准备测试数据"
        def validateModel = buildValidateModel("testObject", "testLayout", "testRule", paramTime)

        and: "Mock布局服务返回"
        mockLayoutService("testLayout", "testObject", 100L)

        and: "Mock布局规则服务返回"
        mockLayoutRuleService("testObject", "testLayout", "testRule", dbTime)
        when: "执行验证"
        def layoutRuleList = [createLayoutRuleInfo("testRule", "testObject", dbTime)]
        PowerMockito.doReturn(layoutRuleList).when(layoutRuleService).findByLayout(Mockito.anyString(), Mockito.anyString())
        def result = layoutRuleLogicService.layoutRuleValidate(user, [validateModel])

        then: "验证结果"
        result == expectedResult

        where: "测试场景"
        scenario         | dbTime         | paramTime      | expectedResult
        "两个时间都为空" | null           | null           | true
        "数据库时间为空" | null           | 100L           | false
        "参数时间为空"   | 100L           | null           | false
        "数据库时间更新" | 200L           | 100L           | false
        "参数时间更新"   | 100L           | 200L           | false
        "时间相等"       | 100L           | 100L           | true
        "数据库时间为0"  | 0L             | 100L           | false
        "参数时间为0"    | 100L           | 0L             | false
        "时间为最大值"   | Long.MAX_VALUE | Long.MAX_VALUE | true
        "时间为最小值"   | Long.MIN_VALUE | Long.MIN_VALUE | true
    }

    private DescribeLayoutValidateModel buildValidateModel(String objectApiName, String layoutApiName,
                                                           String ruleApiName, Long lastModifiedTime) {
        return DescribeLayoutValidateModel.builder()
                .objectApiName(objectApiName)
                .layoutRuleValidateInfos([
                        DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                                .layoutApiName(layoutApiName)
                                .layoutLastModifiedTime(100L)
                                .layoutRuleInfos([createLayoutRuleInfo(ruleApiName, objectApiName, lastModifiedTime)])
                                .build()
                ])
                .build()
    }

    private void mockLayoutService(String layoutApiName, String objectApiName, Long lastModifiedTime) {
        def layout = new Layout()
        layout.setLastModifiedTime(lastModifiedTime)
        layoutLogicService.findLayoutByApiNames(user.tenantId, [layoutApiName], objectApiName) >>
                [(layoutApiName): layout]
    }

    private void mockLayoutRuleService(String objectApiName, String layoutApiName,
                                       String ruleApiName, Long lastModifiedTime) {
        layoutRuleLogicService.findValidLayoutRuleByLayout(user, objectApiName, layoutApiName) >>
                [createLayoutRuleInfo(ruleApiName, objectApiName, lastModifiedTime)]
    }

    private LayoutRuleInfo createLayoutRuleInfo(String apiName, String objectDescribeApiName, Long lastModifiedTime) {
        def layoutRuleInfo = new LayoutRuleInfo()
        layoutRuleInfo.setApiName(apiName)
        layoutRuleInfo.setLastModifiedTime(lastModifiedTime)
        layoutRuleInfo.setObjectDescribeApiName(objectDescribeApiName)
        layoutRuleInfo.setType("field")
        layoutRuleInfo.setStatus(0)
        return layoutRuleInfo
    }
}
