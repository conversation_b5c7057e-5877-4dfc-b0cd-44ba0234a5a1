package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.ComponentActions
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RequestUtil
import com.facishare.paas.appframework.metadata.button.SpecialButtonManager
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider
import com.facishare.paas.appframework.metadata.layout.ButtonFilter
import com.facishare.paas.appframework.metadata.layout.ButtonOrder
import com.facishare.paas.appframework.metadata.qixin.QiXinAppLogicService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.ui.layout.Button
import com.github.autoconf.base.ProcessInfo
import com.github.autoconf.helper.ConfigHelper
import org.junit.runner.RunWith
import org.mockito.Answers
import org.mockito.Mockito
import org.mockito.stubbing.Answer
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification
import spock.lang.Unroll

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([ComponentActions, RequestUtil, ButtonOrder, ButtonFilter, ConfigHelper])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.model.User",
        "com.facishare.paas.appframework.common.util.ComponentActions",
        "com.facishare.paas.appframework.common.util.AppFrameworkConfig"])
class ButtonLogicServiceSpec extends Specification {
    def specialButtonManager = Mock(SpecialButtonManager)
    QiXinAppLogicService qiXinAppLogicService = Mock(QiXinAppLogicService)
    def buttonLogicService = new ButtonLogicServiceImpl(
      specialButtonManager: specialButtonManager,
      qiXinAppLogicService: qiXinAppLogicService)
    def objectData = Mock(IObjectData)

    void setup() {
        PowerMockito.mockStatic(ConfigHelper.class)
        def processInfo = PowerMockito.mock(ProcessInfo)
        PowerMockito.when(ConfigHelper.getProcessInfo()).thenReturn(processInfo)
        PowerMockito.mockStatic(ButtonOrder)
        PowerMockito.mockStatic(ButtonFilter)
        PowerMockito.mockStatic(RequestUtil)
        PowerMockito.mockStatic(AppFrameworkConfig)
    }

    @Unroll
    def "test getButtonByComponentActions objDesc is null"() {
        given:
        def user = Mock(User)
        def actions = Mock(ComponentActions)
        actions.getActionButtons() >> [Mock(Button, name: "button1"), Mock(Button, name: "button2")]
        def objDesc = null
        def objData = Mock(IObjectData)
        def detail = false
        when:
        def response = buttonLogicService.getButtonByComponentActions(user, actions, objDesc, objData, detail)
        then:
        with(response) {
            it.size() == 2
        }
    }

    def "test getButtonByComponentActions objDesc is not null and is detailPage"() {
        given:
        def user = Mock(User)
        def actions = Mock(ComponentActions)
        actions.getActionButtons() >> [Mock(Button, name: "button1"), Mock(Button, name: "button2")]
        def objDesc = Mock(IObjectDescribe)
        def objData = Mock(IObjectData)
        def detail = true
        and: "return is mobileWXWorkRequest"
        PowerMockito.when(RequestUtil.isMobileWXWorkRequest()).thenReturn(false)
        def provider = Mock(SpecialButtonProvider)
        provider.getSpecialButtons() >> [Mock(Button, name: "button1"), Mock(Button, name: "button2")]
        specialButtonManager.getProvider(_) >> provider
        and:
        def builder = Mock(ButtonFilter.ButtonFilterBuilder)
        builder.build() >> Mock(ButtonFilter)
        PowerMockito.when(ButtonFilter.builder()).thenReturn(builder)
        when:
        def response = buttonLogicService.getButtonByComponentActions(user, actions, objDesc, objData, detail)
        then:
        with(response) {
            it.size() == 2
        }
    }

    def getUdefObjDesc(def apiName) {
        def desc = new ObjectDescribe()
        desc.setApiName(apiName)
        return desc
    }

    def "test getButtonByComponentActionsWithoutFilter"() {
        given:
        def user = Mock(User)
        def objectData = Mock(IObjectData)

        when:
        def response = buttonLogicService.getButtonByComponentActionsWithoutFilter(user, actions, objectDescribe, objectData)

        then:
        response == result

        where:
        actions                      | objectDescribe | isDetailPage || result
        ComponentActions.DETAIL_PAGE | null           | true         || ComponentActions.DETAIL_PAGE.getActionButtons()
        ComponentActions.EDIT_PAGE   | null           | false        || ComponentActions.EDIT_PAGE.getActionButtons()

    }

    def "test filterButton"() {
        given:
        def user = Mock(User)
        def relatedListButtons = Mock(Map)

        when:
        def response = buttonLogicService.filterButton(user, objDesc, relatedListButtons)

        then:
        response.size() == size

        where:
        objDesc                           || size
        getUdefObjDesc('object_kFc8w__c') || 0
    }
}
