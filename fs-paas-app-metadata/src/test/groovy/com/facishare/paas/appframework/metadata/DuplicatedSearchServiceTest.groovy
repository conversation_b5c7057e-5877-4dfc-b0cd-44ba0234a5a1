package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.service.ManageGroupService
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.log.ActionType
import com.facishare.paas.appframework.log.EventType
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.cache.RedisDao
import com.facishare.paas.appframework.metadata.dto.BatchQueryDuplicateSearch
import com.facishare.paas.appframework.metadata.dto.DuplicateSearch
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.data.IDuplicatedSearchRefresh
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IDuplicatedRuleService
import com.facishare.paas.metadata.api.service.IDuplicatedSearchService
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.LocationFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.ConfigFactory
import com.google.common.collect.Sets
import org.apache.curator.shaded.com.google.common.collect.Maps
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification

import java.util.stream.Collectors

@PrepareForTest(value = [ConfigFactory.class, JSON.class])
class DuplicatedSearchServiceTest extends Specification {

    DuplicatedSearchService duplicatedSearchService

    IDuplicatedSearchService duplicatedSearchServiceInMetadata

    DescribeLogicService describeLogicService

    IDuplicatedRuleService duplicatedRuleService

    ManageGroupService manageGroupService

    LogService logService

    DuplicateSearchProxy duplicateSearchProxy;

    DuplicatedSearchDataService duplicatedSearchDataService;

    ConfigService configService

    RedisDao redisDao

    @Shared
    Map<String, IDuplicatedSearch> duplicatedSearchMap = Maps.newHashMap()
    @Shared
    IDuplicatedSearch accountDuplicateRule
    @Shared
    IDuplicatedSearch customObjDuplicateRule
    @Shared
    User user = new User("74255", "1000");
    @Shared
    String duplicateRuleStr = "[{\"describe_api_name\":\"AccountObj\",\"effective\":true,\"type\":\"NEW\",\"enable\":true,\"useable_rules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"LeadsObj\",\"enable\":true,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"company\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"company\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[{\"describeApiName\":\"LeadsObj\",\"verifiedByRules\":false}]}},\"pending_rules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"company\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"company\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[{\"describeApiName\":\"LeadsObj\",\"verifiedByRules\":false}]}}},{\"describeApiName\":\"AccountObj\",\"effective\":true,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[{\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"company\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"company\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"type\":\"TOOL\",\"useableRules\":{\"relatedDescribes\":[{\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"company\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"company\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0},{\"describeApiName\":\"LeadsObj\",\"effective\":false,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"AccountObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"company\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}},{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"ContactObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"type\":\"NEW\",\"useableRules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"AccountObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"company\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}},{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"ContactObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0},{\"describeApiName\":\"LeadsObj\",\"effective\":false,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[{\"describeApiName\":\"AccountObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"company\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}},{\"describeApiName\":\"ContactObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"type\":\"TOOL\",\"useableRules\":{\"relatedDescribes\":[{\"describeApiName\":\"AccountObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"company\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}},{\"describeApiName\":\"ContactObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0},{\"describeApiName\":\"ContactObj\",\"effective\":false,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[{\"describeApiName\":\"LeadsObj\",\"verifiedByRules\":false}]}},\"type\":\"NEW\",\"useableRules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[{\"describeApiName\":\"LeadsObj\",\"verifiedByRules\":false}]}},\"version\":0},{\"describeApiName\":\"ContactObj\",\"effective\":false,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[{\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"type\":\"TOOL\",\"useableRules\":{\"relatedDescribes\":[{\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"name\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"mobile\",\"relatedFieldApiName\":\"mobile\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"name\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"mobile\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0},{\"describeApiName\":\"default_object__c\",\"effective\":false,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"type\":\"NEW\",\"useableRules\":{\"relatedDescribes\":[],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0},{\"describeApiName\":\"default_object__c\",\"effective\":false,\"enable\":false,\"pendingRules\":{\"relatedDescribes\":[],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"type\":\"TOOL\",\"useableRules\":{\"relatedDescribes\":[],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[]}},\"version\":0}]";
    @Shared
    String accountDuplicateRuleStr = "{\"describe_api_name\":\"AccountObj\",\"effective\":true,\"type\":\"NEW\",\"enable\":true,\"useable_rules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"LeadsObj\",\"enable\":true,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"company\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"company\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[{\"describeApiName\":\"LeadsObj\",\"verifiedByRules\":false}]}},\"pending_rules\":{\"relatedDescribes\":[{\"allowCreateWhenDuplicated\":false,\"describeApiName\":\"LeadsObj\",\"enable\":false,\"mappingFields\":[{\"fieldApiName\":\"name\",\"relatedFieldApiName\":\"company\"},{\"fieldApiName\":\"tel\",\"relatedFieldApiName\":\"tel\"},{\"fieldApiName\":\"email\",\"relatedFieldApiName\":\"email\"}],\"rulesDef\":{\"rules\":[{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"company\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"tel\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"},{\"conditions\":[{\"connector\":\"AND\",\"fieldName\":\"email\",\"fieldValue\":\"PRECISE\"}],\"connector\":\"OR\"}],\"showFields\":[],\"showNum\":5}}],\"rules\":[],\"showFields\":[],\"showNum\":5,\"specialConfig\":{\"transforms\":[{\"describeApiName\":\"LeadsObj\",\"verifiedByRules\":false}]}}}"
    @Shared
    String customObjDuplicateRuleStr = "{\"type\":\"NEW\",\"enable\":true,\"support_import\":false,\"use_multi_rule\":true,\"invalid_not_duplicate_search\":false,\"pending_rules\":{\"show_num\":5,\"show_fields\":[\"field_c07l1__c\",\"field_2jvri__c\",\"name\"],\"related_describes\":[],\"special_config\":{\"transforms\":[]},\"rules\":[{\"connector\":\"OR\",\"conditions\":[{\"field_name\":\"field_2jvri__c\",\"field_value\":\"PRECISE\",\"empty_policy\":\"MATCH_EMPTY\",\"define_type\":false,\"connector\":\"AND\"},{\"field_name\":\"field_c07l1__c\",\"field_value\":\"PRECISE\",\"empty_policy\":\"MATCH_EMPTY\",\"define_type\":false,\"connector\":\"AND\"},{\"field_name\":\"name\",\"field_value\":\"PRECISE\",\"empty_policy\":\"IGNORE_EMPTY\",\"connector\":\"AND\"}]}]},\"useable_rules\":{\"show_num\":5,\"show_fields\":[\"field_c07l1__c\",\"field_2jvri__c\",\"name\"],\"related_describes\":[],\"special_config\":{\"transforms\":[]},\"rules\":[{\"connector\":\"OR\",\"conditions\":[{\"field_name\":\"field_2jvri__c\",\"field_value\":\"PRECISE\",\"empty_policy\":\"MATCH_EMPTY\",\"define_type\":false,\"connector\":\"AND\"},{\"field_name\":\"field_c07l1__c\",\"field_value\":\"PRECISE\",\"empty_policy\":\"MATCH_EMPTY\",\"define_type\":false,\"connector\":\"AND\"},{\"field_name\":\"name\",\"field_value\":\"PRECISE\",\"empty_policy\":\"IGNORE_EMPTY\",\"connector\":\"AND\"}]}]},\"wheres\":\"[]\",\"data_scope\":\"[]\",\"name\":\"默认新建查重规则\",\"rule_api_name\":\"rule_cjXHpE__c\",\"mark\":\"\",\"describe_api_name\":\"object_aBP40__c\",\"version\":0,\"related_objects\":{},\"id\":\"64001c8db7b50d0001356674\",\"tenant_id\":\"78057\",\"create_time\":1677728909380,\"created_by\":\"1000\",\"last_modified_time\":1677729920636,\"last_modified_by\":\"1000\",\"effective\":true,\"operator_id\":\"1000\"}"

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    void setup() {
        List<IDuplicatedSearch> duplicatedSearches = JSON.parseArray(duplicateRuleStr, IDuplicatedSearch.class)
        duplicatedSearchMap = duplicatedSearches.stream()
                .collect(Collectors.toMap({ x -> getKey(x.getDescribeApiName(), x.getType()) }, { x -> ((IDuplicatedSearch) x) }));
        accountDuplicateRule = JSON.parseObject(accountDuplicateRuleStr, IDuplicatedSearch.class)
        customObjDuplicateRule = JSON.parseObject(customObjDuplicateRuleStr, IDuplicatedSearch.class)
        describeLogicService = Mock(DescribeLogicService)
        duplicatedRuleService = Mock(IDuplicatedRuleService)
        manageGroupService = Mock(ManageGroupService)
        duplicatedSearchServiceInMetadata = Mock(IDuplicatedSearchService)
        logService = Mock(LogService)
        redisDao = Mock(RedisDao)
        duplicatedSearchDataService = Mock(DuplicatedSearchDataService)
        duplicateSearchProxy = Mock(DuplicateSearchProxy)
        configService = Mock(ConfigService)

        AppFrameworkConfig.multiDuplicateRuleAndSupportFilterGrayEi = ["ALL"]

        duplicatedSearchService = new DuplicatedSearchServiceImpl(
                "describeLogicService": describeLogicService,
                "duplicatedRuleService": duplicatedRuleService,
                "manageGroupService": manageGroupService,
                "logService": logService,
                redisDao: redisDao,
                duplicatedSearchDataService: duplicatedSearchDataService,
                "duplicateSearchProxy": duplicateSearchProxy,
                "duplicatedSearchService": duplicatedSearchServiceInMetadata,
                "configService": configService,
                "duplicatedSearchMap": duplicatedSearchMap
        )
    }


    def "getKey"(String apiName, IDuplicatedSearch.Type type) {
        return apiName + type;
    }

    def "presetDuplicateRuleTest"() {
        when:
        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.presetDuplicateRule(describeApiName, IDuplicatedSearch.Type.NEW, user)
        println duplicatedSearch
        then:
        duplicatedSearch != null
        where:
        describeApiName || res
        "AccountObj"    || _
        "LeadsObj"      || _
        "ContactObj"    || _
    }

    def "createOrUpdateDuplicatedSearchTest"() {
        given:
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        objectDescribe.setDisplayName("客户")
        when:
        describeLogicService.findObjectWithoutCopyIfGray(_ as String, _ as String) >> objectDescribe
        duplicatedRuleService.createOrUpdate(_, _, _, _, _) >> accountDuplicateRule
        duplicatedSearchServiceInMetadata.createOrUpdate(_, _, _) >> accountDuplicateRule
        logService.log(user, EventType.ADD, ActionType.Add, _ as String, _ as String)
        duplicatedSearchService.createOrUpdateDuplicatedSearch(userArg, accountDuplicateRule, false)
        then:
        noExceptionThrown()
        where:
        userArg                   || res
        new User("74255", "1000") || _
        new User("100", "1000")   || _
    }

    def "findDuplicatedSearchByApiNameTest"() {
        when:
        duplicatedRuleService.findByDescribeApiNames(_, _, _, _) >> ["AccountObj": [accountDuplicateRule]]
        duplicatedSearchServiceInMetadata.findByDescribeApiName(_, _, _) >> [accountDuplicateRule]
        duplicatedRuleService.findByDescribeApiName(_, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.findDuplicatedSearchByApiName(userArg, "AccountObj", false)
        then:
        noExceptionThrown()
        where:
        userArg                   || res
        new User("74255", "1000") || _
        new User("100", "1000")   || _
    }

    def "findDuplicatedSearchByApiNameAndTypeTest"() {
        given:

        when:
        duplicatedSearchServiceInMetadata.findOneByDescribeApiNameAndType(_, _, _) >> accountDuplicateRule
        duplicatedSearchService.findDuplicatedSearchByApiNameAndType(tenantId, describeApiName, type, isPending)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName | type                         | isPending || res
        "74255"  | "AccountObj"    | IDuplicatedSearch.Type.NEW   | false     || _
        "1000"   | "sdfasdfa__c"   | IDuplicatedSearch.Type.CLEAN | true      || _
        "78057"  | "LeadsObj"      | IDuplicatedSearch.Type.TOOL  | false     || _
    }

    def "findDuplicateSearchByApiNameAndTypeTest"() {
        given:

        when:
        duplicatedRuleService.findList(_, _, _, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.findDuplicateSearchByApiNameAndType(tenantId, describeApiName, type, isPending)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName | type                         | isPending || res
        "74255"  | "AccountObj"    | IDuplicatedSearch.Type.NEW   | false     || _
        "1000"   | "sdfasdfa__c"   | IDuplicatedSearch.Type.CLEAN | true      || _
        "78057"  | "LeadsObj"      | IDuplicatedSearch.Type.TOOL  | false     || _
    }

    def "findDuplicatedSearchByApiNamesAndTypeTest"() {
        when:
        duplicatedSearchServiceInMetadata.findByDescribeApiNames(_, _, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.findDuplicatedSearchByApiNamesAndType(tenantId, [describeApiName], type, isPending)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName | type                         | isPending || res
        "74255"  | "AccountObj"    | IDuplicatedSearch.Type.NEW   | false     || _
        "1000"   | "sdfasdfa__c"   | IDuplicatedSearch.Type.CLEAN | true      || _
        "78057"  | "LeadsObj"      | IDuplicatedSearch.Type.TOOL  | false     || _
    }

    def "findDuplicatedSearchListByApiNamesAndTypeTest"() {
        when:
        duplicatedSearchServiceInMetadata.findByDescribeApiNames(_, _, _, _) >> duplicateSearch
        duplicatedSearchService.findDuplicatedSearchByApiNamesAndType(tenantId, [describeApiName], type, isPending)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName | type                         | isPending | duplicateSearch                                                      || res
        "74255"  | "AccountObj"    | IDuplicatedSearch.Type.NEW   | false     | []                                                                   || _
        "1000"   | "sdfasdfa__c"   | IDuplicatedSearch.Type.CLEAN | true      | [JSON.parseObject(accountDuplicateRuleStr, IDuplicatedSearch.class)] || _
        "78057"  | "LeadsObj"      | IDuplicatedSearch.Type.TOOL  | false     | [JSON.parseObject(accountDuplicateRuleStr, IDuplicatedSearch.class)] || _
    }


    def "isEnableDuplicate"() {
        when:
        duplicatedRuleService.findList(_, _, _, _, _) >> duplicateSearch
        duplicatedSearchServiceInMetadata.findOneByDescribeApiNameAndType(_, _, _, _) >> accountDuplicateRule
        duplicatedSearchService.isEnableDuplicate(new User(tenantId, "1000"), describeApiName, type, isPending)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName | type                         | isPending | duplicateSearch                                                      || res
        "74255"  | "AccountObj"    | IDuplicatedSearch.Type.NEW   | false     | []                                                                   || _
        "1000"   | "sdfasdfa__c"   | IDuplicatedSearch.Type.CLEAN | true      | [JSON.parseObject(accountDuplicateRuleStr, IDuplicatedSearch.class)] || _
        "78057"  | "LeadsObj"      | IDuplicatedSearch.Type.TOOL  | false     | [JSON.parseObject(accountDuplicateRuleStr, IDuplicatedSearch.class)] || _
    }

    def "updateStatus"() {
        when:
        duplicatedSearchServiceInMetadata.enableOrDisable(_ as String, _ as String, IDuplicatedSearch.Type.NEW, _ as boolean)
        duplicatedSearchService.updateStatus(new User(tenantId, "1000"), describeApiName, type, enable)
        then:
        noExceptionThrown()
        where:
        tenantId | describeApiName | type                         | enable || res
        "74255"  | "AccountObj"    | IDuplicatedSearch.Type.NEW   | false  || _
        "1000"   | "sdfasdfa__c"   | IDuplicatedSearch.Type.CLEAN | true   || _
        "78057"  | "LeadsObj"      | IDuplicatedSearch.Type.TOOL  | false  || _
    }

    def "findDuplicateDataByTypeTest"() {
        given:
        //objectData
        IObjectData objectData = new ObjectData()
        objectData.setId("*********")
        objectData.setDisplayName("客户")
        objectData.setDescribeApiName("AccountObj")
        objectData.set("field_c07l1__c", "2234214")
        objectData.set("name", "你好")
        objectData.set("field_2jvri__c", "234swe")
        //objectDescribe
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        objectDescribe.setDisplayName("客户")
        IFieldDescribe fieldDescribeLocation = new LocationFieldDescribe()
        fieldDescribeLocation.setDescribeApiName("AccountObj")
        fieldDescribeLocation.setApiName("field_location")
        objectDescribe.setFieldDescribes([fieldDescribeLocation,])
        //DuplicateSearchResult
        DuplicateSearchResult duplicateSearchResult = DuplicateSearchResult.builder()
                .code("200").message("success")
                .duplicateDataList(
                        [DuplicateSearchResult.DuplicateData.builder()
                                 .apiName("AccountObj")
                                 .ruleApiName("rule_sdsadas__c")
                                 .dataIds(repeatDataIds).build()]).build()

        //DuplicateSearch
        DuplicateSearch duplicateSearch = DuplicateSearch.builder().describeApiName("AccountObj").tenantId(tenantId).type(IDuplicatedSearch.Type.NEW).objectData(ObjectDataExt.of(objectData).toMap()).build();
        when:
        duplicatedSearchServiceInMetadata.findOneByDescribeApiNameAndType(_, _, _, _) >> customObjDuplicateRule
        duplicateSearchProxy.getDuplicateSearchResult(_) >> duplicateSearchResult
        redisDao.mget(_) >> ["**************", "*********"]
        duplicateSearchProxy.getDuplicateSearchResult(_, _) >> duplicateSearchResult
        duplicatedSearchService.findDuplicateDataByType(describeApiName, new User(tenantId, "1000"), objectData, type, objectDescribe)
        then:
        noExceptionThrown()
        where:
        describeApiName | tenantId | type                       | repeatDataIds                   || res
        "AccountObj"    | "74255"  | IDuplicatedSearch.Type.NEW | ["**************", "*********"] || _
        "AccountObj"    | "74255"  | IDuplicatedSearch.Type.NEW | []                              || _
    }

    def "findDuplicateDataListByTypeTest"() {
        given:
        //DuplicateSearchResult
        BatchQueryDuplicateSearch.DuplicateSearchResult duplicateSearchResult = BatchQueryDuplicateSearch.DuplicateSearchResult.builder()
                .describeApiName("AccountObj").sourceDataId("*********").dataIds(Sets.newLinkedHashSet(["**************", "*********"])).build()
        BatchQueryDuplicateSearch.Result result = new BatchQueryDuplicateSearch.Result()
        result.setDuplicateDataList([duplicateSearchResult])
        result.setCode("200")
        result.setMessage("success")
        //objectData
        IObjectData objectData = new ObjectData()
        objectData.setId("*********")
        objectData.setDisplayName("客户")
        objectData.setDescribeApiName("AccountObj")
        objectData.set("field_c07l1__c", "2234214")
        objectData.set("name", "你好")
        objectData.set("field_2jvri__c", "234swe")
        //objectDescribe
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        objectDescribe.setDisplayName("客户")
        IFieldDescribe fieldDescribeLocation = new LocationFieldDescribe()
        fieldDescribeLocation.setDescribeApiName("AccountObj")
        fieldDescribeLocation.setApiName("field_location")
        objectDescribe.setFieldDescribes([fieldDescribeLocation])
        when:
        duplicatedSearchServiceInMetadata.findOneByDescribeApiNameAndType(_, _, _, _) >> customObjDuplicateRule
        duplicateSearchProxy.batchQueryDuplicateSearch(_, _) >> result
        redisDao.mget(_) >> ["**************", "*********"]
        duplicatedSearchService.findDuplicateDataListByType(new User("74255", "1100"), [objectData], IDuplicatedSearch.Type.NEW, objectDescribe, false)
        then:
        noExceptionThrown()

    }


    def "processFieldValueForDuplicatedSearchTest"() {
        given:
        //objectDescribe
        IObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setApiName("AccountObj")
        objectDescribe.setDisplayName("客户")
        IFieldDescribe fieldDescribeLocation = new LocationFieldDescribe()
        fieldDescribeLocation.setDescribeApiName("AccountObj")
        fieldDescribeLocation.setApiName("field_location")
        objectDescribe.setFieldDescribes([fieldDescribeLocation])
        //objectData
        IObjectData objectData = new ObjectData()
        objectData.setId("*********")
        objectData.setDisplayName("客户")
        objectData.setDescribeApiName("AccountObj")
        objectData.set("field_c07l1__c", "2234214")
        objectData.set("name", "你好")
        objectData.set("field_2jvri__c", "234swe")
        when:
        duplicatedSearchService.processFieldValueForDuplicatedSearch(customObjDuplicateRule, objectDescribe, [objectData], new User("74255", "1000"))
        then:
        noExceptionThrown()
    }

    def "findAllTest"() {
        when:
        duplicatedSearchServiceInMetadata.findAll(_, _) >> [customObjDuplicateRule]
        def result = duplicatedSearchService.findAll("74255", false)
        then:
        result == res
        noExceptionThrown()
        where:
        isPending | res
        false     | [customObjDuplicateRule]
        true      | [customObjDuplicateRule]
    }

    def "findAllObjectApiNameByType"() {
        when:
        duplicatedRuleService.findByDescribeApiNames(_, _, _, _) >> ["AccountObj": [accountDuplicateRule]]
        def res = duplicatedSearchService.findAllObjectApiNameByType(User.systemUser("74255"), false, IDuplicatedSearch.Type.NEW)
        then:
        noExceptionThrown()
    }

    def "getDuplicateSearchListByType"() {
        when:

        duplicatedRuleService.findList(_, _, _, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.getDuplicateSearchListByType("74255", "AccountObj", IDuplicatedSearch.Type.NEW)
        then:
        noExceptionThrown()
    }

    def "deleteAndRefreshRule"() {
        when:
        duplicatedSearchService.deleteAndRefreshRule("74255", "AccountObj", [new IDuplicatedSearchRefresh("rule_cfssd__c", accountDuplicateRule)])
        then:
        noExceptionThrown()
    }

    def "fillRuleName"() {
        when:
        duplicatedSearchService.fillRuleName(accountDuplicateRule)
        then:
        noExceptionThrown()
    }


    def "findByTenant"() {
        when:
        duplicatedSearchService.findByTenant("74255", false)
        then:
        noExceptionThrown()
    }


    def "useMultiRule"() {
        when:
        duplicatedSearchService.useMultiRule(user, "AccountObj__c")
        then:
        noExceptionThrown()
    }

    def "changeSupportMultiRule"() {
        when:
        duplicatedSearchService.changeSupportMultiRule(user, "AccountObj__c", false)
        then:
        noExceptionThrown()
    }


    def "needDuplicateSearch"() {
        when:
        RequestContext context = RequestContext.builder()
                .requestSource(RequestContext.RequestSource.CEP)
                .peerName(RequestContext.RequestSource.CEP.name())
                .tenantId("74255")
                .user(user)
                .appId("CRM")
                .build()
        duplicatedSearchService.needDuplicateSearch(context, "AccountObj", "rule_sdhisa__c", false)
        then:
        noExceptionThrown()
    }


    def "queryDuplicateSearchManageGroup"() {
        when:
        duplicatedSearchService.queryDuplicateSearchManageGroup(user, "AccountObj__c", "object_management")
        then:
        noExceptionThrown()
    }

    def "deletedDuplicateSearchManageGroup"() {
        when:
        duplicatedSearchService.deletedDuplicateSearchManageGroup(user, "AccountObj__c")
        then:
        noExceptionThrown()
    }


    def "createOrUpdateCleanRule"() {
        given:

        when:
        duplicatedSearchService.createOrUpdateCleanRule(user, accountDuplicateRule, false)
        then:
        noExceptionThrown()
    }


    def "deleteByDescribeApiName"() {
        when:
        duplicatedSearchService.deleteByDescribeApiName(user, "AccountObj")
        then:
        noExceptionThrown()

    }

    def "findDuplicatedSearchByRuleApiName"() {
        when:
        duplicatedRuleService.findByRuleApiName(_, _, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.findDuplicatedSearchByRuleApiName("AccountObj", "rule_sdasda__c", "74255", false)
        then:
        noExceptionThrown()
    }

    def "findDuplicatedSearchByRuleApiNameOrFirstRule"() {
        when:

        duplicatedRuleService.findList(_, _, _, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.findDuplicatedSearchByRuleApiNameOrFirstRule("AccountObj", "rule_sdsadas__c", IDuplicatedSearch.Type.NEW, "74255", false);
        then:
        noExceptionThrown();
    }

    def "deleteByRuleApiName"() {
        when:
        duplicatedSearchService.deleteByRuleApiName("AccountObj", "rule_sdasda__c", "74255")
        then:
        noExceptionThrown();
    }


    def "enableOrDisable"() {
        when:
        duplicatedSearchService.enableOrDisable("74255", "AccountObj", "rule_sdad__c", IDuplicatedSearch.Type.NEW, false)
        then:
        noExceptionThrown();
    }

    def "getDuplicateSearchRuleCount"() {
        when:
        duplicatedSearchService.getDuplicateSearchRuleCount("AccountObj", IDuplicatedSearch.Type.NEW, "74255");
        then:
        noExceptionThrown()
    }

    def "dealPriority"() {
        when:
        duplicatedSearchService.dealPriority("AccountObj", [:], "74255")
        then:
        noExceptionThrown();
    }

    def "findDuplicatedSearchByRuleApiNames"() {
        when:
        duplicatedSearchService.findDuplicatedSearchByRuleApiNames("AccountObj", [], "74255", false)
        then:
        noExceptionThrown()
    }

    def "findAllDuplicateSearchByApiNameAndType"() {
        when:
        duplicatedRuleService.findByDescribeApiNames(_, _, _, _) >> ["sdas": [accountDuplicateRule]]
        duplicatedSearchService.findAllDuplicateSearchByApiNameAndType("74255", Sets.newHashSet(), IDuplicatedSearch.Type.NEW, false,
                DuplicateSearchOrderByType.ORDER_BY_CREATE_TIME);
        then:

        noExceptionThrown()
    }


    def "getDuplicateData"() {
        when:
        configService.findTenantConfig(_, _) >> true;
        duplicatedSearchService.getDuplicateData(user, new ObjectDescribe(), ObjectDataExt.of([:]).getObjectData(), "rule_sdasd__c", false)

        then:
        noExceptionThrown()
    }

    def "duplicateSearchLock"() {
        when:
        duplicatedRuleService.findList(_, _, _, _, _) >> [accountDuplicateRule]
        duplicatedSearchService.duplicateSearchLock(user, new ObjectDescribe(), ObjectDataExt.of([:]).getObjectData())
        then:
        noExceptionThrown()
    }

}
