package com.facishare.paas.appframework.metadata.importobject.dataconvert


import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.CountryAreaManager
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.CityFiledDescribe
import com.facishare.paas.metadata.support.CountryAreaService
import com.facishare.paas.metadata.util.SpringContextUtil
import org.powermock.reflect.Whitebox
import org.springframework.context.ApplicationContext
import spock.lang.Specification

class CountryAreaImportFieldDataConverterTest extends Specification {

    CountryAreaService countryAreaService = Mock(CountryAreaService.class)

    Map areMap = [
            "is_index"               : false,
            "is_need_convert"        : false,
            "is_required"            : false,
            "api_name"               : "city",
            "options"                : [
                    [
                            "resource_bundle_key": "",
                            "child_options"      : [
                                    [
                                            "district": [
                                                    "628",
                                                    "629",
                                                    "632",
                                                    "633",
                                                    "634",
                                                    "635",
                                                    "636",
                                                    "637",
                                                    "638",
                                                    "639",
                                                    "640",
                                                    "641",
                                                    "642",
                                                    "643",
                                                    "644",
                                                    "645"
                                            ]
                                    ]
                            ],
                            "label"              : "北京市",
                            "standard_code"      : "110100000000",
                            "value"              : "283"
                    ]
            ],
            "define_type"            : "package",
            "is_unique"              : false,
            "label"                  : "市",
            "type"                   : "city",
            "cascade_parent_api_name": "province",
            "status"                 : "released"
    ]

    def setup() {
        ApplicationContext CONTEXT = Mock(ApplicationContext.class)
        Whitebox.setInternalState(SpringContextUtil, "CONTEXT", CONTEXT)
        Whitebox.setInternalState(CountryAreaManager, "countryAreaService", countryAreaService)
    }

    def "test"() {
        given:
        def cai = new CountryAreaImportFieldDataConverter()
        def data = new ObjectData()
        data.set("field_vzkHy__c", "中国")
        data.set("field_wDRWh__c", "山西省")
        data.set("field_CU0Rd__c", "北京市")
        data.set("field_0iW1h__c", "河北")
        def fieldDescribe = new CityFiledDescribe()
        fieldDescribe.setCascadeParentApiName("field_wDRWh__c")
        fieldDescribe.setApiName("field_CU0Rd__c")
        def user = new User("74255", "1000")
        when:
        countryAreaService.getCountryAreaMap(*_) >> [:]
        def result = cai.convertFieldData(data, fieldDescribe, user)
        then:
        print(result)
        result.getValue() == null
    }

}
