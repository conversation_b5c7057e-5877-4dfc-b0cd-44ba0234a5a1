package com.facishare.paas.appframework.metadata

import com.facishare.crm.userdefobj.DefObjConstants
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.license.LicenseService
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo
import com.facishare.paas.appframework.log.LogService
import com.facishare.paas.appframework.metadata.component.BatchQueryComponentData
import com.facishare.paas.appframework.metadata.component.CustomComponentProxy
import com.facishare.paas.appframework.metadata.component.QueryComponentList
import com.facishare.paas.appframework.metadata.dto.LayoutRoleInfo
import com.facishare.paas.appframework.metadata.dto.auth.RestResult
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoModel
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoPojo
import com.facishare.paas.appframework.metadata.layout.LayoutContext
import com.facishare.paas.appframework.metadata.layout.LayoutTypes
import com.facishare.paas.appframework.metadata.layout.factory.ListLayoutFactory
import com.facishare.paas.auth.model.RoleViewPojo
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.MasterDetail
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent
import com.facishare.paas.metadata.service.impl.LayoutServiceImpl
import com.facishare.paas.metadata.ui.layout.ILayout
import com.github.autoconf.ConfigFactory
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.powermock.api.mockito.PowerMockito.mockStatic
import static org.powermock.api.mockito.PowerMockito.when


@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([ConfigFactory, TenantLicenseInfo, ListLayoutExt, AppFrameworkConfig, LayoutExt, LayoutContext,
        DefObjConstants, ObjectDescribeExt, I18N])
@PowerMockIgnore(value = ["org.slf4j.*", "com.sun.org.apache.xerces.*", "javax.xml.parsers.*", "javax.management.*", "sun.security.*"])
@SuppressStaticInitializationFor("com.facishare.crm.userdefobj.DefObjConstants")
class LayoutLogicServiceTest extends Specification {

    LayoutLogicServiceImpl layoutLogicService
    def describeLogicService
    def layoutService
    def logService
    def eventLogicService
    def licenseService
    def functionLogicService
    def customComponentProxy
    def recordTypeLogicService
    def recordTypeAuthProxy
    def configService
    ApplicationLayeredGrayService applicationLayeredGrayService
    ListLayoutFactory listLayoutFactory

    def user
    def layoutExt

    void setup() {
//        mockStatic(Logger)
        mockStatic(ConfigFactory)
        mockStatic(TenantLicenseInfo)
        mockStatic(ListLayoutExt)
        mockStatic(AppFrameworkConfig)
        mockStatic(LayoutExt)
        mockStatic(LayoutContext)
        mockStatic(DefObjConstants)
        mockStatic(ObjectDescribeExt)
//        mockStatic(I18N)
//        I18N.text(_) >> ""

        user = Mock(User)
        layoutExt = Mock(LayoutExt)

        licenseService = Mock(LicenseService)
        describeLogicService = Mock(DescribeLogicService)
        layoutService = Mock(LayoutServiceImpl)
        logService = Mock(LogService)
        eventLogicService = Mock(UIEventLogicService)
        functionLogicService = Mock(FunctionLogicService)
        customComponentProxy = Mock(CustomComponentProxy)
        recordTypeLogicService = Mock(RecordTypeLogicService)
        configService = Mock(ConfigService)
        applicationLayeredGrayService = Mock(ApplicationLayeredGrayService)
        listLayoutFactory = Mock(ListLayoutFactory)
        layoutLogicService = new LayoutLogicServiceImpl(licenseService: licenseService,
                describeLogicService: describeLogicService,
                layoutService: layoutService,
                logService: logService,
                eventLogicService: eventLogicService,
                functionLogicService: functionLogicService,
                customComponentProxy: customComponentProxy,
                recordTypeLogicService: recordTypeLogicService,
                configService: configService,
                applicationLayeredGrayService: applicationLayeredGrayService,
                listLayoutFactory: listLayoutFactory
        )
    }

    def getEvents() {
        return [[type                     : 3,
                 describe_api_name        : "object_kFc8w__c",
                 trigger_describe_api_name: "object_kFc8w__c",
                 trigger_field_api_names  : ["field_1Jz05__c"],
                 triggers                 : [1],
                 func_api_name            : "abc123__c",
                 func_name                : "func_abc123",
                 func_describe            : "1",
                 define_type              : "custom"]]
    }

    def getUIEvents() {
        return [UIEventExt.of(["_id": "abc"]).getUiEvent()]
    }

    def getCustomMobileComponents() {
        return [[api_name: "form_component", header: "详细信息", type: "form"],
                [api_name: "object_x__c_field_y__c_related_list", header: "2333", type: "relatedlist"]
        ]
    }

    def getLayout(events, id) {
        def layout = new Layout([components: getCustomMobileComponents()])
        layout.setId(id)
        layout.setName("layout_x__c")
        layout.setDisplayName("布局名称")
        layout.setRefObjectApiName("object_kFc8w__c")
        layout.setLayoutType(ILayout.DETAIL_LAYOUT_TYPE)
        layout.setLayoutType(ILayout.DETAIL_LAYOUT_TYPE)
        layout.setEnableMobileLayout(true)
        layout.setMobileLayout(getMobileLayout(events, id))
        layout.setEvents(events)
        return layout
    }

    def getMobileLayout(events, id) {
        def mobileLayout = [
                components         : getCustomMobileComponents(),
                ref_object_api_name: "object_x__c"
        ]
        return mobileLayout
    }

    def getObjDesc(objectApiName) {
        def desc = new ObjectDescribe([api_name: objectApiName])
        return desc
    }

    def getObjDesc(Map map) {
        def desc = new ObjectDescribe(map)
        return desc
    }

    def getTabsComponents() {
        return [new TabsComponent()]
    }

    def getMDFieldDesc(Map map) {
        return new MasterDetailFieldDescribe(map)
    }

    def "test createLayout"() {
        given:
        def user = Mock(User)
        def tenantLicenseInfoBuilder = Mock(TenantLicenseInfo.TenantLicenseInfoBuilder)
        def tenantLicenseInfo = Mock(TenantLicenseInfo)
        and:
        when(TenantLicenseInfo.builder()).thenReturn(tenantLicenseInfoBuilder)
        tenantLicenseInfoBuilder.licenseService(_) >> tenantLicenseInfoBuilder
        tenantLicenseInfoBuilder.user(_) >> tenantLicenseInfoBuilder
        tenantLicenseInfoBuilder.build() >> tenantLicenseInfo
        tenantLicenseInfo.init(_) >> tenantLicenseInfo

        and: "public int findCountByType"
        1 * describeLogicService.findObject(_, _) >> desc
        1 * layoutService.findByTypes(_, _, _) >> [getLayout([], null)]
        and: "private List<ILayout> appendDefaultLayoutIfAbsent"
        when(AppFrameworkConfig.manyAbstractLayout("", null)).thenReturn(true)
        when(LayoutExt.buildDefaultMobileListLayout(null)).thenReturn(getLayout([], null))
        and: "private void checkLayoutComponents"
        when(LayoutExt.of(layout)).thenReturn(new LayoutExt(layout))
        layoutService.findByTypes(*_) >> []

        1 * eventLogicService.createEvents(_, _) >> getUIEvents()
        1 * describeLogicService.findObject(_, _) >> desc
        1 * layoutService.create(_) >> getLayout(null, null)
        1 * logService.log(_, _, _, _, _)
        1 * functionLogicService.saveRelation(_, _)


        when:
        def response = layoutLogicService.createLayout(user, layout)
        then:
        result == response.getName()
        where:
        layout                       | desc                      | events      || result
        getLayout(getEvents(), null) | getObjDesc("object_x__c") | getEvents() || "layout_x__c"
    }

    def "test updateLayout"() {
        given:
        def tenantLicenseInfoBuilder = Mock(TenantLicenseInfo.TenantLicenseInfoBuilder)
        def tenantLicenseInfo = Mock(TenantLicenseInfo)
        1 * describeLogicService.findObject(_, _) >> getObjDesc("object_x__c")
        1 * layoutService.update(_) >> getLayout(null, "id123")
        and: "private void checkUIEventNumber"
        when(TenantLicenseInfo.builder()).thenReturn(tenantLicenseInfoBuilder)
        tenantLicenseInfoBuilder.licenseService(_) >> tenantLicenseInfoBuilder
        tenantLicenseInfoBuilder.user(_) >> tenantLicenseInfoBuilder
        tenantLicenseInfoBuilder.build() >> tenantLicenseInfo
        tenantLicenseInfo.init(_) >> tenantLicenseInfo
        and: "private void checkLayoutComponents"
        when(LayoutExt.of(layout)).thenReturn(new LayoutExt(layout))
        layoutExt.isNewLayout() >> true
        layoutExt.getFormComponent() >> Optional.of(layout.getComponents())
        layoutExt.getTabsComponents() >> getTabsComponents()
        and: "private void addEventWithLayout"


        when:
        def response = layoutLogicService.updateLayout(user, layout)
        then:
        result == response.getName()
        where:
        layout                          || result
        getLayout(getEvents(), "id123") || "layout_x__c"
    }

    def "test findLayoutById by layoutId tenantId"() {
        given:
        1 * layoutService.findById(layoutId, tenantId) >> null
        when:
        def response = layoutLogicService.findLayoutById(layoutId, tenantId)
        then:
        result == response
        where:
        layoutId | tenantId || result
        null     | "74255"  || null

    }

    def getQueryComponentListResult(code) {
        QueryComponentList.Result result = new QueryComponentList.Result()
        result.setCode(code)
    }

    def "test findCustomComponents by tenantId objectApiName"() {
        given:
        customComponentProxy.queryComponentList(_, _) >> qResult
        when:
        def response = layoutLogicService.findCustomComponents(tenantId, objApiName)
        then:
        result == response.size()
        where:
        tenantId | objApiName | qResult                        || result
        "74255"  | ""         | null                           || 0
        "74255"  | "obj_x__c" | getQueryComponentListResult(0) || 0
        "74255"  | "obj_x__c" | getQueryComponentListResult(1) || 0

    }

    def getBatchQueryComponentDataResult(code) {
        BatchQueryComponentData.Result result = new BatchQueryComponentData.Result()
        result.setCode(code)
        return result
    }

    def "test findCustomComponentsByApiNames"() {
        given:
        customComponentProxy.batchQueryComponentData(_, _) >> qResult
        when:
        def response = layoutLogicService.findCustomComponentsByApiNames(tenantId, customApiNames)
        then:
        result == response.size()
        where:
        tenantId | customApiNames       | qResult                             || result
        "74255"  | []                   | getBatchQueryComponentDataResult(0) || 0
        "74255"  | ["c__c, a__c, b__c"] | getBatchQueryComponentDataResult(0) || 0
        "74255"  | ["c__c, a__c, b__c"] | getBatchQueryComponentDataResult(1) || 0
    }

    def "test findCustomComponents"() {
        given:
        when(LayoutContext.isMobileLayout()).thenReturn(isMobile)
        when(LayoutExt.of(layout)).thenReturn(new LayoutExt(layout))
        when(LayoutExt.of(layout.getMobileLayout())).thenReturn(new LayoutExt(new Layout(getMobileLayout([], null))))
        when:
        def response = layoutLogicService.findCustomComponents(tenantId, layout)
        then:
        result == response.size()
        where:
        tenantId | layout                        | isMobile || result
        "74255"  | getLayout(getEvents(), "123") | true     || 0
        "74255"  | getLayout(getEvents(), "123") | false    || 0
    }

    def "test getBusinessComponents"() {
        given:
        describeLogicService.findObject(_, _) >> getObjDesc(objApiName)
        licenseService.existModule(tenantId, _) >> existModule
        when(DefObjConstants.isComponentInvisible(objApiName, "foo")).thenReturn(false)
        and: "getDetailPageBusinessComponents"
        def objDescExt = Mock(ObjectDescribeExt)
        when(DefObjConstants.isReferenceObjectInvisible(objApiName, "foo")).thenReturn(false)
        objDescExt.getDetailObjectDescribeStructures(_) >> []
        objDescExt.getRelatedObjectDescribeStructures(_) >> []
        and: "getEditPageBusinessComponents"
        def md = Mock(MasterDetail)
        objDescExt.getMasterDetailField() >> Optional.of(md)
        objDescExt.getObjectDescribe() >> getObjDesc([])
        describeLogicService.findDetailDescribesCreateWithMaster(_, _) >> [objDescExt.getObjectDescribe()]
        def mtc = [
                api_name: objApiName + ComponentExt.MASTER_DETAIL_SUFFIX
        ]
        when(ObjectDescribeExt.of(any() as IObjectDescribe)).thenReturn(objDescExt)
        when:
        def response = layoutLogicService.getBusinessComponents(tenantId, objApiName, layoutType)
        then:
        result == response.size()
        where:
        tenantId | objApiName    | layoutType       | existModule                                               || result
        "74255"  | "AccountObj"  | ""               | [account_hierarchy_app: true, contact_relation_app: true] || 5
        "74255"  | "object_x__c" | LayoutTypes.EDIT | [account_hierarchy_app: true, contact_relation_app: true] || 1
        "74255"  | "AccountObj"  | "UN_KNOW"        | [account_hierarchy_app: true, contact_relation_app: true] || 0
    }

    def "test findAssignedListLayout"() {
        given:
        recordTypeLogicService.findRoleViewList(user, objApi, LayoutTypes.LIST_LAYOUT, false) >> roleToView
        def roleInfoResult = Mock(RoleInfoModel.Result)
        def restResult = Mock(RestResult)
        restResult.getRoles() >> roleInfo
        roleInfoResult.getResult() >> restResult
        recordTypeAuthProxy.roleInfo(_, _) >> roleInfoResult
        when:
        def response = layoutLogicService.findAssignedListLayout(objApi, layouts, user)
        then:
        result == response.size()
        where:
        objApi        | layouts           | roleToView                                               | roleInfo                                            || result
        "object_x__c" | [] as Set<String> | [new RoleViewPojo(roleCode: "1", viewId: "layout_x__c")] | []                                                  || 0
        "object_x__c" | [] as Set<String> | [new RoleViewPojo(roleCode: "1", viewId: "layout_x__c")] | [new RoleInfoPojo(roleName: "name", roleCode: "1")] || 1
    }

    def "test saveListLayoutAssign"() {
        given:
        def recordLayouts = [new LayoutRoleInfo.RecordLayoutInfo(layoutApiName: "layout_x__c", recordType: "record_default__c")]
        def layoutRoles = [new LayoutRoleInfo(roleCode: 1, recordLayout: recordLayouts)]
        times * recordTypeLogicService.upsertRoleViewList(_, _)
        expect:
        layoutLogicService.saveListLayoutAssign(objApi, layoutRoles, user)
        where:
        objApi        | times
        "object_x__c" | 1
    }

    def "test getEditLayoutStatus"() {
        given:
        configService.findTenantConfig(_, _) >> ""
        when:
        def response = layoutLogicService.getEditLayoutStatus(tenantId, objApi)
        then:
        result == new String(response).size()
        where:
        tenantId | objApi        || result
        "1"      | "object_x__c" || 1
    }
}
