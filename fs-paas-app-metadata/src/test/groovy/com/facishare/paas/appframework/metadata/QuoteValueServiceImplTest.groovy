package com.facishare.paas.appframework.metadata


import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.FormulaFieldDescribe
import com.facishare.paas.metadata.impl.describe.ImageFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.google.common.collect.Lists
import org.apache.commons.lang.StringUtils
import spock.lang.Specification
import spock.lang.Unroll
import com.facishare.paas.appframework.metadata.QuoteValueServiceImpl.QuoteInfo
import com.facishare.paas.metadata.api.describe.IFieldType
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import com.facishare.paas.appframework.core.model.User

class QuoteValueServiceImplTest extends Specification {
    RequestContext requestContext
    IObjectDescribe objectDescribe
    QuoteValueServiceImpl quoteValueService
    IObjectDescribe quotedDescribe

    def initCondition(String version) {
        requestContext = RequestContext.builder().clientInfo(version).build();
        RequestContextManager.setContext(requestContext)
        objectDescribe = createSimpleDescribe()
    }

    def setup() {
        quoteValueService = new QuoteValueServiceImpl()
        quotedDescribe = Mock(IObjectDescribe)
    }

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    /**
     * 测试fillQuoteImageInfoForLowVersion方法，区分是否低于675版本，来决定引用图片字段是否返回提示
     * @return
     */
    def "test filterQuoteImage"() {
        given:
        initCondition(version)
        QuoteValueServiceImpl service = new QuoteValueServiceImpl()
        List<QuoteValueServiceImpl.QuoteInfo.QuoteFieldInfo> fieldInfoList = Lists.newArrayList(createQuoteFieldInfo("field_image1", "f1"), createQuoteFieldInfo("field_other", "f2"))
        List<IObjectData> dataList = Lists.newArrayList(new ObjectData());
        when:
        service.fillQuoteImageInfoForLowVersion(objectDescribe, fieldInfoList, dataList)
        then:
        size == fieldInfoList.size()
        StringUtils.equals(dataList.get(0).get("f1"), field_f1)
        StringUtils.equals(dataList.get(0).get("f2"), field_f2)
        where:
        version          || size | field_f1   | field_f2
        "Android.673000" || 1    | ""         | null
        "Android.675000" || 2    | null       | null
    }

    def createSimpleDescribe() {
        IObjectDescribe describe = new ObjectDescribe();
        IFieldDescribe imageField = new ImageFieldDescribe();
        imageField.setApiName("field_image1")
        IFieldDescribe formulaField = new FormulaFieldDescribe();
        formulaField.setApiName("field_formula1")
        describe.addFieldDescribeList(Lists.newArrayList(imageField, formulaField));
        describe
    }

    def createQuoteFieldInfo(String quotedFieldName, String fieldApiName) {
        QuoteValueServiceImpl.QuoteInfo.QuoteFieldInfo fieldInfo = QuoteValueServiceImpl.QuoteInfo.QuoteFieldInfo
                .builder()
                .quotedFieldName(quotedFieldName)
                .fieldApiName(fieldApiName)
                .build()
        fieldInfo
    }
    @Unroll
    def "测试 filterQuoteFieldInfoList 方法"() {
        given: "准备测试数据"
        def describe = new ObjectDescribe()
        describe.setApiName("TestObj")
        
        // 添加不同类型的字段
        def normalField = FieldDescribeFactory.newInstance([api_name: "normalField", type: IFieldType.TEXT])
        def employeeField = FieldDescribeFactory.newInstance([api_name: "employeeField", type: IFieldType.EMPLOYEE])
        def referenceField = FieldDescribeFactory.newInstance([api_name: "referenceField", type: IFieldType.OBJECT_REFERENCE])
        def departmentField = FieldDescribeFactory.newInstance([api_name: "departmentField", type: IFieldType.DEPARTMENT])
        def countryField = FieldDescribeFactory.newInstance([api_name: "countryField", type: IFieldType.COUNTRY])
        
        describe.setFieldDescribes([normalField, employeeField, referenceField, departmentField, countryField])
        
        def quoteInfo = QuoteInfo.builder()
            .quotedDescribe(describe)
            .fieldInfoList([
                QuoteInfo.QuoteFieldInfo.builder().quotedFieldName("normalField").build(),
                QuoteInfo.QuoteFieldInfo.builder().quotedFieldName("employeeField").build(),
                QuoteInfo.QuoteFieldInfo.builder().quotedFieldName("referenceField").build(),
                QuoteInfo.QuoteFieldInfo.builder().quotedFieldName("departmentField").build(),
                QuoteInfo.QuoteFieldInfo.builder().quotedFieldName("countryField").build(),
                QuoteInfo.QuoteFieldInfo.builder().quotedFieldName("nonExistField").build()
            ])
            .build()
            
        when: "调用过滤方法"
        def result = quoteValueService.filterQuoteFieldInfoList(quoteInfo, batchFillFlag)
        
        then: "验证结果"
        result.size() == expectedSize
        result.collect { it.quotedFieldName }.toSet() == expectedFields.toSet()
        
        where: "测试数据"
        batchFillFlag | expectedSize | expectedFields
        false         | 2            | ["normalField", "countryField"]  // 当灰度关闭时，普通字段和地区字段都会保留
        true          | 1            | ["normalField"]                  // 当灰度开启时，地区字段被过滤掉，只保留普通字段
    }
    
    @Unroll
    def "测试 isValidQuoteField 方法 - #scenario"() {
        given: "准备测试数据"
        def describe = new ObjectDescribe()
        describe.setApiName("TestObj")
        def fieldDescribe = fieldName ? FieldDescribeFactory.newInstance([api_name: fieldName, type: fieldType]) : null
        describe.setFieldDescribes(fieldDescribe ? [fieldDescribe] : [])
        
        def fieldInfo = QuoteInfo.QuoteFieldInfo.builder()
            .quotedFieldName(fieldName)
            .build()
            
        when: "调用验证方法"
        def result = quoteValueService.isValidQuoteField(describe, fieldInfo, batchFillFlag)
        
        then: "验证结果"
        result == expected
        
        where: "测试数据"
        scenario                    | fieldName      | fieldType                    | batchFillFlag | expected
        // 基础字段类型测试
        "普通文本字段-灰度关闭"       | "textField"    | IFieldType.TEXT             | false         | true
        "普通文本字段-灰度开启"       | "textField"    | IFieldType.TEXT             | true          | true
        "数字字段-灰度关闭"          | "numberField"  | IFieldType.NUMBER           | false         | true
        "数字字段-灰度开启"          | "numberField"  | IFieldType.NUMBER           | true          | true
        "日期字段-灰度关闭"          | "dateField"    | IFieldType.DATE             | false         | true
        "日期字段-灰度开启"          | "dateField"    | IFieldType.DATE             | true          | true
        
        // 特殊字段类型测试
        "员工字段-灰度关闭"          | "empField"     | IFieldType.EMPLOYEE         | false         | false
        "员工字段-灰度开启"          | "empField"     | IFieldType.EMPLOYEE         | true          | false
        "引用字段-灰度关闭"          | "refField"     | IFieldType.OBJECT_REFERENCE | false         | false
        "引用字段-灰度开启"          | "refField"     | IFieldType.OBJECT_REFERENCE | true          | false
        "部门字段-灰度关闭"          | "deptField"    | IFieldType.DEPARTMENT       | false         | false
        "部门字段-灰度开启"          | "deptField"    | IFieldType.DEPARTMENT       | true          | false
        
        // 地区相关字段测试
        "国家字段-灰度关闭"          | "countryField" | IFieldType.COUNTRY          | false         | true
        "国家字段-灰度开启"          | "countryField" | IFieldType.COUNTRY          | true          | false
        "省份字段-灰度关闭"          | "provinceField"| IFieldType.PROVINCE         | false         | true
        "省份字段-灰度开启"          | "provinceField"| IFieldType.PROVINCE         | true          | false
        "城市字段-灰度关闭"          | "cityField"    | IFieldType.CITY             | false         | true
        "城市字段-灰度开启"          | "cityField"    | IFieldType.CITY             | true          | false
        "区县字段-灰度关闭"          | "districtField"| IFieldType.DISTRICT         | false         | true
        "区县字段-灰度开启"          | "districtField"| IFieldType.DISTRICT         | true          | false
        
        // 边界情况测试
        "空字段名"                  | ""             | IFieldType.TEXT             | false         | false
        "null字段名"               | null           | IFieldType.TEXT             | false         | false
        "长字段名"                 | "a" * 255      | IFieldType.TEXT             | false         | true
        "特殊字符字段名"            | "field#%"      | IFieldType.TEXT             | false         | true
    }

    @Unroll
    def "测试 formatAreaNames 方法 - #scenario"() {
        given: "准备测试数据"
        def areaCodes = codeList
        def areaInfoMap = infoMap
        
        when: "调用格式化方法"
        def result = quoteValueService.formatAreaNames(areaCodes, areaInfoMap)
        
        then: "验证结果"
        result == expected
        
        where: "测试数据"
        scenario                    | codeList              | infoMap                                                                | expected
        "空列表"                    | []                    | [:]                                                                     | ""
        "单个存在的区域码"          | ["CN001"]             | ["CN001": mockCountryInfo("中国", "CN001")]                             | "中国"
        "单个不存在的区域码"        | ["CN999"]             | [:]                                                                     | "CN999"
        "多个区域码-全部存在"       | ["CN001", "JP001"]    | ["CN001": mockCountryInfo("中国", "CN001"), 
                                                             "JP001": mockCountryInfo("日本", "JP001")]                              | "中国,日本"
        "多个区域码-部分存在"       | ["CN001", "KR999"]    | ["CN001": mockCountryInfo("中国", "CN001")]                             | "中国,KR999"
        "多个区域码-全部不存在"     | ["XX001", "YY001"]    | [:]                                                                     | "XX001,YY001"
        "特殊字符区域码"            | ["CN-001"]            | ["CN-001": mockCountryInfo("中国-特区", "CN-001")]                      | "中国-特区"
        "空区域码"                  | [""]                  | ["": mockCountryInfo("未知", "")]                                       | "未知"
        "null区域码"                | [null]                | [(null): mockCountryInfo("未知", null)]                                 | "未知"
    }
    
    // 辅助方法 - 创建CountryInfo mock对象
    private MetaDataGlobalService.CountryInfo mockCountryInfo(String label, String value) {
        MetaDataGlobalService.CountryInfo.of(value, label)
    }
    
    @Unroll
    def "测试 fillAreaInfo 方法 - #scenario"() {
        given: "准备测试数据"
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        
        // 准备ObjectData列表
        def objectDataList = [
            new ObjectData(['_id': 'data1', 'areaRef1': 'CN001']),
            new ObjectData(['_id': 'data2', 'areaRef1': 'JP001']),
            new ObjectData(['_id': 'data3'])
        ]
        
        // 准备QuoteInfo对象
        def quoteFieldInfo = QuoteInfo.QuoteFieldInfo.builder()
            .quotedFieldName("countryField")
            .lookupFieldApiName("areaRef1")
            .fieldApiName("area1")
            .quotedFieldType(isCountryField ? IFieldType.COUNTRY : IFieldType.TEXT)
            .build()
            
        def quoteDescribe = new ObjectDescribe()
        quoteDescribe.setApiName("QuotedObj")
        
        // 设置字段描述
        def fieldDescribe = FieldDescribeFactory.newInstance([
            "api_name": "countryField",
            "type": isCountryField ? IFieldType.COUNTRY : IFieldType.TEXT
        ])
        quoteDescribe.setFieldDescribes([fieldDescribe])
        
        // 模拟引用数据
        def lookupData = new ObjectData(['_id': 'CN001', 'countryField': 'CN001'])
        
        def quoteInfo = QuoteInfo.builder()
            .quotedDescribeApiName("QuotedObj")
            .fieldInfoList([quoteFieldInfo])
            .quotedDescribe(quoteDescribe)
            .dataList([lookupData])
            .build()
        
        def quoteInfoList = [quoteInfo]
        
        // 模拟MetaDataGlobalService
        quoteValueService.metaDataGlobalService = Mock(MetaDataGlobalService)
        quoteValueService.metaDataGlobalService.batchQueryAreaLabelsByCodes(_, _) >> { u, codes ->
            [
                MetaDataGlobalService.CountryInfo.of("CN001", "中国"),
                MetaDataGlobalService.CountryInfo.of("JP001", "日本")
            ]
        }
        
        when: "调用fillAreaInfo方法"
        quoteValueService.fillAreaInfo(user, objectDataList, quoteInfoList, isForCalculate, fieldTypes)
        
        then: "验证结果"
        if (shouldFill) {
            objectDataList[0].get("area1") == "中国"
            objectDataList[1].get("area1") == "日本"
            objectDataList[2].get("area1") == null
        } else {
            objectDataList[0].get("area1") == null
            objectDataList[1].get("area1") == null
            objectDataList[2].get("area1") == null
        }
        
        where: "测试数据"
        scenario                   | isCountryField | isForCalculate | fieldTypes                      | shouldFill
        "计算模式-国家字段"         | true           | true           | [IFieldType.COUNTRY]           | true
        "非计算模式-国家字段"       | true           | false          | [IFieldType.COUNTRY]           | true
        "计算模式-非匹配字段类型"   | true           | true           | [IFieldType.CITY]              | false
        "非计算模式-非匹配字段类型" | true           | false          | [IFieldType.CITY]              | false
        "计算模式-多字段类型"       | true           | true           | [IFieldType.COUNTRY, IFieldType.CITY] | true
        "非计算模式-多字段类型"     | true           | false          | [IFieldType.COUNTRY, IFieldType.CITY] | true
    }
}
