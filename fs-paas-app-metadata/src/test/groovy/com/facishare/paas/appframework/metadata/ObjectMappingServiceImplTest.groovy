package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.common.service.dto.DeptInfo
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds
import com.facishare.paas.appframework.common.util.CollectionUtils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IObjectMappingParams
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectMappingRuleService
import com.facishare.paas.metadata.exception.ErrorCode
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.ObjectMappingParams
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class ObjectMappingServiceImplTest extends Specification {
    ObjectMappingService objectMappingService
    def describeLogicService = Mock(DescribeLogicService)
    def customButtonService = Mock(CustomButtonServiceImpl)
    def ruleList = [Mock(IObjectMappingRuleInfo)]
    OrgService orgService = Mock(OrgService)
    IObjectMappingRuleService mappingRuleService = Mock(IObjectMappingRuleService)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    def setup() {
        objectMappingService = new ObjectMappingServiceImpl('mappingRuleService': mappingRuleService, 'describeLogicService': describeLogicService,
                'customButtonService': customButtonService, 'orgService': orgService)
    }
    def "test createRule method"() {
        given:
        def user = User.systemUser('74255')

        when:
        def result = objectMappingService.createRule(user, ruleList)

        then:
        result == ruleList
        1 * mappingRuleService.createObjectMappingRule(ruleList) >> ruleList
    }

    def "test createRule method exception"() {
        given:
        def user = User.systemUser('74255')

        when:
        mappingRuleService.createObjectMappingRule(ruleList) >> { throw new MetadataServiceException(ErrorCode.PARA_BLANK, 'error') }
        def result = objectMappingService.createRule(user, ruleList)

        then:
        thrown(ValidateException)
    }

    def "test updateRule method"() {
        given:
        def user = User.systemUser('74255')

        when:
        def result = objectMappingService.updateRule(user, ruleList)

        then:
        result == ruleList
        1 * mappingRuleService.setObjectMappingRule(ruleList) >> ruleList
    }

    def "test updateRule method exception"() {
        given:
        def user = User.systemUser('74255')

        when:
        mappingRuleService.setObjectMappingRule(ruleList) >> { throw new MetadataServiceException(ErrorCode.PARA_BLANK, 'error') }
        def result = objectMappingService.updateRule(user, ruleList)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeInvalidDetailObjectData方法的正常场景，包括空规则列表、无从对象规则、目标对象存在和不存在的情况
     */
    @Unroll
    def "removeInvalidDetailObjectData Test #testCase"() {
        given: "设置测试环境"
        def tenantId = 'testTenantId'
        User user = User.systemUser(tenantId)

        // 创建参数
        IObjectMappingParams param = new ObjectMappingParams()
        Map<String, List<IObjectData>> detailsData = Maps.newHashMap()

        // 为从对象数据添加测试数据
        if (setupDetailData) {
            sourceApiNames.each { sourceApiName ->
                def dataList = Lists.newArrayList()
                dataList.add(Mock(IObjectData))
                detailsData.put(sourceApiName, dataList)
            }
            param.setDetailObjectData(detailsData)
        }

        // 创建规则列表
        List<IObjectMappingRuleInfo> rules = []
        if (setupRules) {
            rulesConfig.each { config ->
                def rule = Mock(IObjectMappingRuleInfo)
                rule.getMdType() >> config.mdType
                rule.getSourceApiName() >> config.sourceApiName
                rule.getTargetApiName() >> config.targetApiName
                rules.add(rule)
            }
        }

        // 模拟describeLogicService
        if (setupDescribeLogicService) {
            Map<String, IObjectDescribe> targetDescribes = Maps.newHashMap()
            if (!CollectionUtils.empty(existingTargetObjects)) {
                existingTargetObjects.each { targetApiName ->
                    targetDescribes.put(targetApiName, Mock(IObjectDescribe))
                }
            }
            describeLogicService.findObjectsWithoutCopy(user.getTenantId(), _) >> targetDescribes
        }

        when: "调用方法"
        objectMappingService.removeInvalidDetailObjectData(user, rules, param)

        then: "验证结果"
        if (expectToRemove) {
            sourceApiNames.each { sourceApiName ->
                if (expectRemovedSourceApiNames.contains(sourceApiName)) {
                    assert !param.getDetailObjectData().containsKey(sourceApiName)
                } else {
                    assert param.getDetailObjectData().containsKey(sourceApiName)
                }
            }
        } else {
            sourceApiNames.each { sourceApiName ->
                if (setupDetailData) {
                    assert param.getDetailObjectData().containsKey(sourceApiName)
                }
            }
        }

        where: "测试场景"
        testCase                    << ["空规则列表", "无从对象规则", "从对象规则目标对象存在", "部分从对象规则目标对象不存在", "全部从对象规则目标对象不存在", "从对象数据为空", "mdType为空时不影响"]
        setupRules                  << [false, true, true, true, true, true, true]
        setupDetailData             << [true, true, true, true, true, false, true]
        setupDescribeLogicService   << [false, false, true, true, true, true, true]
        rulesConfig                 << [
            [],  // 空规则列表
            [
                [mdType: 0, sourceApiName: "source1", targetApiName: "target1"],
                [mdType: 0, sourceApiName: "source2", targetApiName: "target2"]
            ],  // 无从对象规则
            [
                [mdType: 1, sourceApiName: "source1", targetApiName: "target1"],
                [mdType: 1, sourceApiName: "source2", targetApiName: "target2"]
            ],  // 从对象规则目标对象存在
            [
                [mdType: 1, sourceApiName: "source1", targetApiName: "target1"],
                [mdType: 1, sourceApiName: "source2", targetApiName: "target2"]
            ],  // 部分从对象规则目标对象不存在
            [
                [mdType: 1, sourceApiName: "source1", targetApiName: "target1"],
                [mdType: 1, sourceApiName: "source2", targetApiName: "target2"]
            ],  // 全部从对象规则目标对象不存在
            [
                [mdType: 1, sourceApiName: "source1", targetApiName: "target1"],
                [mdType: 1, sourceApiName: "source2", targetApiName: "target2"]
            ],  // 从对象数据为空
            [
                [mdType: null, sourceApiName: "source1", targetApiName: "target1"],
                [mdType: null, sourceApiName: "source2", targetApiName: "target2"]
            ]   // mdType为空时不影响
        ]
        sourceApiNames              << [["source1", "source2"], ["source1", "source2"], ["source1", "source2"], ["source1", "source2"], ["source1", "source2"], ["source1", "source2"], ["source1", "source2"]]
        existingTargetObjects       << [[], [], ["target1", "target2"], ["target1"], [], ["target1", "target2"], ["target1"]]
        expectToRemove              << [false, false, false, true, true, false, false]
        expectRemovedSourceApiNames << [[], [], [], ["source2"], ["source1", "source2"], [], []]
        logCallCount                << [0, 0, 0, 1, 2, 0, 0]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeInvalidDetailObjectData方法处理空值情况，包括规则中sourceApiName为空的情况
     */
    def "removeInvalidDetailObjectData Test Handle Null Values"() {
        given: "设置测试环境包含null值"
        def tenantId = 'testTenantId'
        User user = User.systemUser(tenantId)

        // 创建参数
        IObjectMappingParams param = new ObjectMappingParams()
        Map<String, List<IObjectData>> detailsData = Maps.newHashMap()

        def dataList1 = Lists.newArrayList()
        dataList1.add(Mock(IObjectData))
        detailsData.put("source1", dataList1)

        def dataList2 = Lists.newArrayList()
        dataList2.add(Mock(IObjectData))
        detailsData.put("source2", dataList2)

        param.setDetailObjectData(detailsData)

        // 创建规则列表 - 包含空sourceApiName的规则
        List<IObjectMappingRuleInfo> rules = []
        def rule1 = Mock(IObjectMappingRuleInfo)
        rule1.getMdType() >> 1
        rule1.getSourceApiName() >> "source1"
        rule1.getTargetApiName() >> "target1"
        rules.add(rule1)

        def rule2 = Mock(IObjectMappingRuleInfo)
        rule2.getMdType() >> 1
        rule2.getSourceApiName() >> null  // 空sourceApiName
        rule2.getTargetApiName() >> "target2"
        rules.add(rule2)

        // 模拟describeLogicService
        Map<String, IObjectDescribe> targetDescribes = Maps.newHashMap()
        targetDescribes.put("target1", Mock(IObjectDescribe))
        describeLogicService.findObjectsWithoutCopy(user.getTenantId(), _) >> targetDescribes

        when: "调用方法"
        objectMappingService.removeInvalidDetailObjectData(user, rules, param)

        then: "验证结果 - source1保留，source2保留(因为没有对应规则)"
        param.getDetailObjectData().containsKey("source1")
        param.getDetailObjectData().containsKey("source2")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeInvalidDetailObjectData方法处理多个从对象规则指向同一目标对象的情况
     */
    def "removeInvalidDetailObjectData Test Multiple Source To Same Target"() {
        given: "设置测试环境多个源指向同一目标"
        def tenantId = 'testTenantId'
        User user = User.systemUser(tenantId)

        // 创建参数
        IObjectMappingParams param = new ObjectMappingParams()
        Map<String, List<IObjectData>> detailsData = Maps.newHashMap()

        // 3个源对象
        ["source1", "source2", "source3"].each { sourceApiName ->
            def dataList = Lists.newArrayList()
            dataList.add(Mock(IObjectData))
            detailsData.put(sourceApiName, dataList)
        }
        param.setDetailObjectData(detailsData)

        // 创建规则列表 - source1, source2同时指向target1，source3指向target2
        List<IObjectMappingRuleInfo> rules = []

        def rule1 = Mock(IObjectMappingRuleInfo)
        rule1.getMdType() >> 1
        rule1.getSourceApiName() >> "source1"
        rule1.getTargetApiName() >> "target1"
        rules.add(rule1)

        def rule2 = Mock(IObjectMappingRuleInfo)
        rule2.getMdType() >> 1
        rule2.getSourceApiName() >> "source2"
        rule2.getTargetApiName() >> "target1"  // 同样指向target1
        rules.add(rule2)

        def rule3 = Mock(IObjectMappingRuleInfo)
        rule3.getMdType() >> 1
        rule3.getSourceApiName() >> "source3"
        rule3.getTargetApiName() >> "target2"
        rules.add(rule3)

        // 模拟describeLogicService - 只有target1存在
        Map<String, IObjectDescribe> targetDescribes = Maps.newHashMap()
        targetDescribes.put("target1", Mock(IObjectDescribe))
        describeLogicService.findObjectsWithoutCopy(user.getTenantId(), _) >> targetDescribes

        when: "调用方法"
        objectMappingService.removeInvalidDetailObjectData(user, rules, param)

        then: "验证结果 - source1和source2保留(对应target1存在)，source3移除(对应target2不存在)"
        param.getDetailObjectData().containsKey("source1")
        param.getDetailObjectData().containsKey("source2")
        !param.getDetailObjectData().containsKey("source3")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试removeInvalidDetailObjectData方法当param.getDetailObjectData()为null的情况
     */
    def "removeInvalidDetailObjectData Test DetailObjectData Null"() {
        given: "设置测试环境detailsData为null"
        def tenantId = 'testTenantId'
        User user = User.systemUser(tenantId)

        // 创建参数 - detailObjectData为null
        IObjectMappingParams param = new ObjectMappingParams()
        param.setDetailObjectData(null)

        // 创建规则列表
        List<IObjectMappingRuleInfo> rules = []
        def rule = Mock(IObjectMappingRuleInfo)
        rule.getMdType() >> 1
        rule.getSourceApiName() >> "source1"
        rule.getTargetApiName() >> "target1"
        rules.add(rule)

        // 模拟describeLogicService
        Map<String, IObjectDescribe> targetDescribes = Maps.newHashMap()
        describeLogicService.findObjectsWithoutCopy(user.getTenantId(), _) >> targetDescribes

        when: "调用方法"
        objectMappingService.removeInvalidDetailObjectData(user, rules, param)

        then: "方法执行不抛异常，无移除操作"
        noExceptionThrown()
    }
    
    def "test fillDeptAndOrg"() {
        given: "准备测试数据"
        def tenantId = "74255"
        def user = User.systemUser(tenantId)
        def ownerId = ownerId_
        def deptId = "2001"

        // Mock masterDescribe
        def masterDescribe = new ObjectDescribe()
        masterDescribe.setApiName("AccountObj")
        if (isOpenOrg) {
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
                "api_name": "data_own_organization",
                "type": "employee"
            ])  
            masterDescribe.setFieldDescribes([fieldDescribe])
        }

        // Mock rules
        def rule = new ObjectMappingRuleInfo()
        rule.setMasterRuleApiName(null)
        def fieldMapping = new ObjectMappingRuleDetailInfo()
        fieldMapping.setTargetFieldName("owner")
        rule.setFieldMapping([fieldMapping])

        // Mock result
        def result = new ObjectMappingParams()
        def objectData = new ObjectData()
        objectData.setDescribeApiName("Account")
        objectData.set("owner", ownerId)
        if (hasExistingDeptId) {
            objectData.set("data_own_department", [existingDeptId])
        }
        if (hasExistingOrg) {
            objectData.set("data_own_organization", existingOrg)
            if (existingOrgName) {
                objectData.set("data_own_organization_name", existingOrgName)
            }
        }
        result.setObjectData(objectData)

        // Mock detail data if needed
        if (hasDetailData) {
            def detailData = new ObjectData()
            detailData.setDescribeApiName("Contact")
            detailData.set("owner", ownerId)
            result.setDetailObjectData(["Contact": [detailData]])
        }

        and: "Mock service dependencies"
        // Mock getMainDeptInfo response
        def mainDeptInfo = new QueryDeptInfoByUserIds.MainDeptInfo()
        mainDeptInfo.setDeptId(deptId)
        orgService.getMainDeptInfo(tenantId, ownerId, [ownerId]) >> [(ownerId): mainDeptInfo]

        // Mock findMainOrgAndDeptByUserId response when needed
        def orgInfo
        if (isOpenOrg) {
            def deptInfo = new DeptInfo()
            deptInfo.setDeptId(orgId)
            deptInfo.setDeptName(orgName)
            def orgInfoItem = OrganizationInfo.OrgInfo.builder()
                    .userId(ownerId)
                    .mainOrg(deptInfo)
                    .build()
            orgInfo = OrganizationInfo.of([orgInfoItem])
        } else {
            orgInfo = OrganizationInfo.empty()
        }

        orgService.findMainOrgAndDeptByUserId(tenantId, user.getUserId(), [ownerId]) >> orgInfo

        when: "执行fillDeptAndOrg方法"
        Whitebox.invokeMethod(objectMappingService, "fillDeptAndOrg", result, [rule], user, masterDescribe)

        then: "验证结果"
        def resultData = result.getObjectData()
        resultData.get("data_own_department") == expectedDeptId
        if (isOpenOrg) {
            resultData.get("data_own_organization") == expectedOrg
            resultData.get("data_own_organization_name") == expectedOrgName
        }
        if (hasDetailData) {
            def detailResult = result.getDetailObjectData()["Contact"][0]
            detailResult.get("data_own_department") == expectedDeptId
            if (isOpenOrg) {
                detailResult.get("data_own_organization") == expectedOrg
                detailResult.get("data_own_organization_name") == expectedOrgName
            }
        }

        where: "测试不同场景"
        isOpenOrg | hasExistingDeptId | existingDeptId | hasExistingOrg | existingOrg       | existingOrgName | hasDetailData | ownerId_                      | orgId    | orgName     | expectedDeptId | expectedOrg            | expectedOrgName
        false     | false             | null           | false          | null              | null            | false         | "1001"                        | "3001"   | "测试组织"    | ["2001"]      | null                  | null
        false     | true              | "2002"         | false          | null              | null            | false         | "1001"                        | "3001"   | "测试组织"    | ["2002"]      | null                  | null
        true      | false             | null           | false          | null              | null            | false         | "1001"                        | "3001"   | "测试组织"    | ["2001"]      | ["3001"]              | "测试组织"
        true      | false             | null           | true           | ["3002"]          | "已有组织"       | false         | "1001"                        | "3001"   | "测试组织"    | ["2001"]      | ["3002"]              | "已有组织"
        true      | false             | null           | false          | null              | null            | true          | "1001"                        | "3001"   | "测试组织"    | ["2001"]      | ["3001"]              | "测试组织"
        true      | false             | null           | false          | null              | null            | false         | User.SUPPER_ADMIN_USER_ID     | "3001"   | "测试组织"    | ["2001"]      | [User.COMPANY_ID]     | "全集团"
        true      | false             | null           | false          | null              | null            | false         | ""                            | "3001"   | "测试组织"    | ["2001"]      | [User.COMPANY_ID]     | "全集团"
    }
}
