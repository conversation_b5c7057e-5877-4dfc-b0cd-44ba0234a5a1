package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.config.ObjectConfigService;
import com.facishare.paas.appframework.metadata.dto.CustomSceneResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.metadata.layout.component.ISummaryComponentInfo;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.query.SearchQueryRender;
import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraph;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphService;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.wishFul.RecordingService;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.*;
import com.facishare.paas.metadata.api.service.IRecycleBinService;
import com.facishare.paas.metadata.checker.FieldOptionChecker;
import com.facishare.paas.metadata.dao.pg.entity.metadata.InvalidData;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.arg.FindInvalidDataListArg;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.service.impl.RelationServiceImpl;
import com.facishare.paas.metadata.service.impl.RelevantTeamServiceImpl;
import com.facishare.recording.api.model.HistoryRecordingInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 对元数据服务的封装
 * <p>
 * Created by yusb on 2017/12/14.
 */
@Slf4j
@Service("metaDataFindService")
public class MetaDataFindServiceImpl implements MetaDataFindService {

    @Autowired
    private ObjectDataProxy dataProxy;
    @Autowired
    private CustomSceneService customSceneService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private IRecycleBinService recycleBinService;
    @Autowired
    private CrmService crmService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private FieldOptionChecker fieldOptionChecker;
    @Autowired
    private DataPrivilegeService dataPrivilegeService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private RelationServiceImpl relationServiceImpl;
    @Autowired
    private RelevantTeamServiceImpl relevantTeamService;
    @Autowired
    private MetaDataMiscService metaDataMiscService;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private MetaDataComputeService metaDataComputeService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private ObjectConfigService objectConfigService;
    @Autowired
    private SearchTemplateLogicService searchTemplateLogicService;
    @Autowired
    private FollowLogicService followLogicService;
    @Autowired
    private FieldRelationGraphService fieldRelationGraphService;
    @Autowired
    private FileStoreService fileStoreService;
    @Autowired
    private RecordingService recordingService;

    @Override
    @SuppressWarnings("谨慎使用,会跳过数据权限")
    @Deprecated
    public IObjectData findObjectData(String tenantId, String id, IObjectDescribe describe) {
        IObjectData objectData = findObjectDataIncludeNull(tenantId, id, describe);
        if (objectData == null) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.DATA_NOT_USED));
        }
        return objectData;
    }

    @Override
    public IObjectData findObjectData(User user, String id, IObjectDescribe describe) {
        IActionContext context = buildContext(user);
        return findObjectData(context, id, describe);
    }

    @Override
    public IObjectData findObjectData(IActionContext context, String id, IObjectDescribe describe) {
        try {
            IObjectData objectData = dataProxy.findById(id, context.getEnterpriseId(), context, describe.getApiName());
            if (objectData == null) {
                throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
            }

            handleMultiLangField(Lists.newArrayList(objectData), describe, context);
            batchCalculate(Lists.newArrayList(objectData), describe, context);
            batchConvertRichTextFieldFromMetadata(Lists.newArrayList(objectData), describe);
            batchConvertNPathUrl(context, Lists.newArrayList(objectData), describe);
            return objectData;
        } catch (MetadataServiceException e) {
            //防止日志打印太多信息
            if (context.getObjectDescribe() != null) {
                context.setObjectDescribe(null);
            }
            log.warn("findObjectData error,context:{},id:{},apiName:{}", context, id, describe.getApiName(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectData findObjectData(User user, String id, String apiName) {
        IActionContext context = buildContext(user);
        return findObjectData(context, id, apiName);
    }

    @Override
    public IObjectData findObjectData(IActionContext context, String id, String apiName) {
        try {
            IObjectData objectData = dataProxy.findById(id, context.getEnterpriseId(), context, apiName);
            if (objectData == null) {
                throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
            }
            handleMultiLangField(Lists.newArrayList(objectData), apiName, context);
            batchCalculate(Lists.newArrayList(objectData), apiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), apiName);
            batchConvertRichTextFieldFromMetadata(Lists.newArrayList(objectData), objectDescribe);
            batchConvertNPathUrl(context, Lists.newArrayList(objectData), objectDescribe);
            return objectData;
        } catch (MetadataServiceException e) {
            //防止日志打印太多信息
            if (context.getObjectDescribe() != null) {
                context.setObjectDescribe(null);
            }
            log.warn("findObjectData error,context:{},id:{},apiName:{}", context, id, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectData findObjectDataIgnoreFormula(User user, String id, String objectApiName) {

        IActionContext context = buildContext(user);
        context.setDoCalculate(false);
        ActionContextExt.of(context).disableDeepQuote();

        return findObjectData(context, id, objectApiName);
    }

    @Override
    public IObjectData findObjectDataIgnoreRelevantTeam(User user, String id, String objectApiName) {
        IActionContext context = buildContext(user, true);
        ActionContextExt.of(context).disableDeepQuote();

        return findObjectData(context, id, objectApiName);
    }

    @Override
    public IObjectData findObjectDataIgnoreAll(User user, String id, String objectApiName) {
        IActionContext context = buildContext(user, true);
        context.setDoCalculate(false);
        ActionContextExt.of(context).disableDeepQuote();

        return findObjectData(context, id, objectApiName);
    }

    @Override
    public IObjectData findObjectDataIncludeDeleted(User user, String id, String objectApiName) {
        IActionContext context = buildContext(user);
        return getObjectDataIncludeDeleted(context, id, objectApiName);
    }

    @Override
    public IObjectData getObjectDataIncludeDeleted(IActionContext context, String id, String objectApiName) {
        try {
            IObjectData objectData = dataProxy.findById(id, context.getEnterpriseId(), context, objectApiName, true);
            if (Objects.isNull(objectData)) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.DATA_NOT_USED));
            }
            handleMultiLangField(Lists.newArrayList(objectData), objectApiName, context);
            batchCalculate(Lists.newArrayList(objectData), objectApiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectApiName);
            batchConvertRichTextFieldFromMetadata(Lists.newArrayList(objectData), objectDescribe);
            batchConvertNPathUrl(context, Lists.newArrayList(objectData), objectDescribe);

            return objectData;
        } catch (MetadataServiceException e) {
            log.warn("findObjectDataIncludeDeleted error,context:{},id:{},objectApiName:{}", context, id, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectData findObjectDataIgnoreStatus(User user, String id, String objectApiName) {
        IActionContext context = buildContext(user, RequestUtil.skipRelevantTeam());
        return getObjectDataIgnoreStatus(context, id, objectApiName);
    }

    @Override
    public IObjectData findObjectDataIgnoreStatusAndFormula(User user, String id, String objectApiName) {
        IActionContext context = buildContext(user);
        context.setDoCalculate(false);
        ActionContextExt.of(context).disableDeepQuote();
        return getObjectDataIgnoreStatus(context, id, objectApiName);
    }

    private IObjectData getObjectDataIgnoreStatus(IActionContext context, String id, String objectApiName) {
        try {
            List<IObjectData> objectDataList = dataProxy.findByIdsIncludeDeleted(Lists.newArrayList(id), context.getEnterpriseId(), objectApiName, context);
            if (CollectionUtils.empty(objectDataList)) {
                throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
            }
            handleMultiLangField(objectDataList, objectApiName, context);
            batchCalculate(objectDataList, objectApiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectApiName);
            batchConvertRichTextFieldFromMetadata(objectDataList, objectDescribe);
            batchConvertNPathUrl(context, objectDataList, objectDescribe);

            return objectDataList.get(0);
        } catch (MetadataServiceException e) {
            log.warn("getObjectDataIgnoreStatus error,context:{},id:{},objectApiName:{}", context, id, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectData findObjectDataIncludeDeletedIgnoreFormula(User user, String id, String objectApiName) {
        IActionContext context = buildContext(user);
        context.setDoCalculate(false);
        ActionContextExt.of(context).disableDeepQuote();
        return getObjectDataIncludeDeleted(context, id, objectApiName);
    }

    @Override
    public IObjectData findMasterObjectData(User user, IObjectData detailData, IObjectDescribe detailDescribe) {
        MasterDetailFieldDescribe masterDetail = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe().orElse(null);
        if (masterDetail == null) {
            return null;
        }
        String masterId = detailData.get(masterDetail.getApiName(), String.class);
        if (Strings.isNullOrEmpty(masterId)) {
            return null;
        }
        IActionContext context = buildContext(user);
        try {
            return dataProxy.findById(masterId, user.getTenantId(), context, masterDetail.getTargetApiName());
        } catch (MetadataServiceException e) {
            log.warn("findMasterObjectData failed,user:{},detailApiName:{},masterApiName:{},masterId:{}", user,
                    detailDescribe.getApiName(), masterDetail.getTargetApiName(), masterId);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectData findObjectDataIncludeNull(String tenantId, String id, IObjectDescribe describe) {
        try {
            IActionContext context = new ActionContext();
            context.setPrivilegeCheck(false);
            context.setEnterpriseId(tenantId);
            context.setUserId(User.SUPPER_ADMIN_USER_ID);
            context.setObjectDescribe(describe);

            IObjectData objectData = dataProxy.findById(id, tenantId, context, describe.getApiName());
            if (objectData == null) {
                return null;
            }

            batchCalculate(Lists.newArrayList(objectData), describe, context);
            batchConvertRichTextFieldFromMetadata(Lists.newArrayList(objectData), describe);
            batchConvertNPathUrl(context, Lists.newArrayList(objectData), describe);
            return objectData;
        } catch (MetadataServiceException e) {
            log.warn("findObjectData error,tenantId:{},id:{},apiName:{}", tenantId, id, describe.getApiName(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, IObjectData> findMasterObjectData(User user, List<IObjectData> detailDataList, IObjectDescribe detailDescribe) {
        MasterDetailFieldDescribe masterDetail = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe().orElse(null);
        if (masterDetail == null) {
            return Maps.newHashMap();
        }

        List<String> masterIds = detailDataList.stream().map(x -> x.get(masterDetail.getApiName(), String.class))
                .filter(x -> !Strings.isNullOrEmpty(x)).distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(masterIds)) {
            return Maps.newHashMap();
        }

        List<IObjectData> masterDataList = findObjectDataByIdsIgnoreFormula(user.getTenantId(), masterIds, masterDetail.getTargetApiName());
        Map<String, IObjectData> masterDataMap = masterDataList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        Map<String, IObjectData> result = Maps.newHashMap();
        detailDataList.forEach(x -> {
            String masterId = x.get(masterDetail.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(masterId) && masterDataMap.containsKey(masterId)) {
                result.put(x.getId(), masterDataMap.get(masterId));
            }
        });

        return result;
    }

    @Override
    public List<IObjectData> findObjectDataByIds(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, false);
        return findObjectDataByIds(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIgnoreFormula(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, true);
        ActionContextExt.of(context).disableDeepQuote();
        return findObjectDataByIds(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIgnoreRelevantTeam(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, false);
        ActionContextExt.of(context).skipRelevantTeam();
        return findObjectDataByIds(context, ids, objectApiName);
    }

    private ActionContext createActionContext(String tenantId, boolean ignoreFormula) {
        ActionContext context = new ActionContext();
        context.setPrivilegeCheck(false);
        context.setEnterpriseId(tenantId);
        context.setUserId(User.SUPPER_ADMIN_USER_ID);
        context.setActionCode(IActionContext.ACTION_CODE_COMPLETE_QUERY);
        if (ignoreFormula) {
            context.setDoCalculate(false);
        }
        return context;
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIgnoreAll(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, true);
        ActionContextExt.of(context).disableDeepQuote().skipRelevantTeam();
        return findObjectDataByIds(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIgnoreAllExceptFormula(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, false);
        ActionContextExt.of(context).disableDeepQuote().skipRelevantTeam();
        return findObjectDataByIds(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIgnoreAllAndExtraInfo(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, true);
        ActionContextExt.of(context).disableDeepQuote().skipRelevantTeam().setSkipFillExtraInfo();
        return findObjectDataByIds(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIgnoreAllAndExtraInfoExceptFormula(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, false);
        ActionContextExt.of(context).disableDeepQuote().skipRelevantTeam().setSkipFillExtraInfo();
        return findObjectDataByIds(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIds(IActionContext context, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        try {
            List<IObjectData> objectDataList = dataProxy.findByIds(ids, context.getEnterpriseId(), objectApiName, context);

            handleMultiLangField(objectDataList, objectApiName, context);
            batchCalculate(objectDataList, objectApiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectApiName);
            batchConvertRichTextFieldFromMetadata(objectDataList, objectDescribe);
            batchConvertNPathUrl(context, objectDataList, objectDescribe);
//            batchConvertDateFieldValue(context.getEnterpriseId(), objectDataList, objectApiName);

            return objectDataList;
        } catch (MetadataServiceException e) {
            log.warn("findObjectDataByIds error,context:{},ids:{},objectApiName:{}", context, ids, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> findObjectDataByIdsExcludeInvalid(IActionContext context, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.searchInDB();
        queryExt.setNeedReturnCountNum(false);
        queryExt.setLimit(ids.size());
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectApiName);
        queryExt.addFilter(Operator.IN, IObjectData.ID, ids);
        queryExt.addIsDeletedFalseFilter();

        try {
            ActionContextExt.of(context).setSearchRichTextExtra(true);
            QueryResult<IObjectData> queryResult = dataProxy.findBySearchQuery(context.getEnterpriseId(), objectApiName,
                    (SearchTemplateQuery) queryExt.getQuery(), context);
            List<IObjectData> objectDataList = CollectionUtils.nullToEmpty(queryResult.getData());

            handleMultiLangField(objectDataList, objectApiName, context);
            batchCalculate(objectDataList, objectApiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectApiName);
            batchConvertRichTextFieldFromMetadata(objectDataList, objectDescribe);
            batchConvertNPathUrl(context, objectDataList, objectDescribe);

            return objectDataList;
        } catch (MetadataServiceException e) {
            log.warn("findObjectDataByIdsExcludeInvalid error,context:{},ids:{},objectApiName:{}", context, ids, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> findObjectDataByIdsExcludeInvalidIgnoreAll(String tenantId, List<String> ids, String objectApiName) {
        ActionContext context = createActionContext(tenantId, true);
        ActionContextExt.of(context).disableDeepQuote().skipRelevantTeam();
        return findObjectDataByIdsExcludeInvalid(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIncludeDeleted(User user, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }

        IActionContext context = buildContext(user);

        return getObjectDataByIdsIncludeDeleted(context, ids, objectApiName);
    }

    private List<IObjectData> getObjectDataByIdsIncludeDeleted(IActionContext context, List<String> ids, String objectApiName) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setNeedReturnCountNum(false);
        queryExt.setLimit(ids.size());
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectApiName);
        queryExt.addFilter(Operator.IN, IObjectData.ID, ids);

        try {
            QueryResult<IObjectData> queryResult = dataProxy.findBySearchQuery(context.getEnterpriseId(), objectApiName,
                    (SearchTemplateQuery) queryExt.getQuery(), context);
            List<IObjectData> objectDataList = CollectionUtils.nullToEmpty(queryResult.getData());

            handleMultiLangField(objectDataList, objectApiName, context);
            batchCalculate(objectDataList, objectApiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectApiName);
            batchConvertRichTextFieldFromMetadata(objectDataList, objectDescribe);
            batchConvertNPathUrl(context, objectDataList, objectDescribe);

            return objectDataList;
        } catch (MetadataServiceException e) {
            log.warn("findObjectDataByIdsIncludeDeleted error,context:{},ids:{},objectApiName:{}", context, ids, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreFormula(User user, List<String> ids, String objectApiName) {
        return findObjectDataByIdsIncludeDeletedIgnoreFormula(user, ids, objectApiName, false);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreFormula(User user, List<String> ids, String objectApiName, boolean needAllMultiLangValue) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }

        //老对象返回的数据已经包括作废数据
        if (ObjectDescribeExt.isSFANotStandardObject(objectApiName)) {
            return findObjectDataByIdsIgnoreFormula(user.getTenantId(), ids, objectApiName);
        }

        IActionContext context = buildContext(user);
        context.setDoCalculate(false);
        ActionContextExt.of(context)
                .disableDeepQuote()
                .setKeepAllMultiLangValue(needAllMultiLangValue);

        return getObjectDataByIdsIncludeDeleted(context, ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreAll(User user, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        // 还没有标准化的对象，需要走这个接口查询
        if (ObjectDescribeExt.isSFANotStandardObject(objectApiName)) {
            return findObjectDataByIdsIgnoreFormula(user.getTenantId(), ids, objectApiName);
        }
        ActionContextExt contextExt = ActionContextExt.of(user);
        contextExt.setDoCalculate(false);
        contextExt.setPrivilegeCheck(false);
        contextExt.disableDeepQuote().skipRelevantTeam();
        return getObjectDataByIdsIncludeDeleted(contextExt.getContext(), ids, objectApiName);
    }

    @Override
    public List<IObjectData> findObjectDataByIdsWithQueryContext(QueryContext queryContext, List<String> ids, String objectApiName) {
        if (Strings.isNullOrEmpty(objectApiName) || CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(ids.size());
        query.setNeedReturnCountNum(false);
        SearchTemplateQueryExt.of(query).searchInDB();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, IObjectData.ID, ids);
        QueryResult<IObjectData> queryResult = findByQueryWithContext(queryContext, objectApiName, query);
        return CollectionUtils.nullToEmpty(queryResult.getData());
    }

    @Override
    public List<IObjectData> findDetailObjectDataList(IObjectDescribe detailDescribe, IObjectData masterObjectData, User user) {
        IActionContext context = buildContext(user);
        return findDetailObjectDataList(context, detailDescribe, masterObjectData);
    }

    @Override
    public List<IObjectData> findDetailObjectDataList(User user, IObjectDescribe detailDescribe, List<IObjectData> masterDataList) {
        return findDetailObjectDataList(buildContext(user), detailDescribe, masterDataList, UnaryOperator.identity());
    }

    @Override
    public List<IObjectData> findDetailObjectDataList(IActionContext context, IObjectDescribe detailDescribe, List<IObjectData> masterDataList, UnaryOperator<SearchTemplateQueryExt> function) {
        if (CollectionUtils.empty(masterDataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> dataList = Lists.newArrayList();
        List<String> masterDataIds = masterDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<List<String>> masterDataIdGroups = Lists.partition(masterDataIds, 100);
        masterDataIdGroups.forEach(masterDataIdGroup -> {
            dataList.addAll(getDetailDataByPages(context, detailDescribe, Sets.newHashSet(masterDataIdGroup), function));
        });

        return dataList;
    }

    @Override
    public List<IObjectData> findDetailObjectDataList(IActionContext context, IObjectDescribe detailDescribe, IObjectData masterObjectData) {
        return getDetailDataByPages(context, detailDescribe, Sets.newHashSet(masterObjectData.getId()), UnaryOperator.identity());
    }

    private List<IObjectData> getDetailDataByPages(IActionContext context, IObjectDescribe detailDescribe, Set<String> masterDataIds, UnaryOperator<SearchTemplateQueryExt> function) {
        List<IObjectData> result = Lists.newArrayList();
        String idOffset = null;
        int batchDataNum;
        int pageSize = 1000;
        int queryTimes = 0;
        int maxQueryTimes = AppFrameworkConfig.getMaxDetailDataQueryTimes();
        // 构建 searchQuery
        SearchTemplateQueryExt queryExt = buildSearchQuery(masterDataIds, detailDescribe, pageSize);
        function.apply(queryExt);
        do {
            // 重置 id 筛选条件
            queryExt.resetSearchTemplateQuery(idOffset);
            // 查询数据
            QueryResult<IObjectData> queryResult = findBySearchQuery(context, detailDescribe.getApiName(), (SearchTemplateQuery) queryExt.getQuery());
            List<IObjectData> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                batchDataNum = queryData.size();
                idOffset = queryData.get(batchDataNum - 1).getId();
                result.addAll(queryData);
            } else {
                batchDataNum = 0;
            }
            queryTimes++;
        } while (batchDataNum >= pageSize && idOffset != null && queryTimes < maxQueryTimes);

        // batchCalculate(result, detailDescribe, context);
        // batchConvertRichTextFieldFromMetadata(result, detailDescribe);
        // batchConvertNPathUrl(context, result, detailDescribe);
        return result;
    }

    private SearchTemplateQueryExt buildSearchQuery(Set<String> masterDataIds, IObjectDescribe detailDescribe, int pageSize) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        //不需要数据总数
        queryExt.setNeedReturnCountNum(false);
        queryExt.setOffset(0);
        queryExt.setLimit(pageSize);
        String mdFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailField().map(IFieldDescribe::getApiName)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        queryExt.addFilter(Operator.IN, mdFieldName, Lists.newArrayList(masterDataIds));
        queryExt.searchInDB();

        queryExt.resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));

        return queryExt;
    }

    @Override
    public List<IObjectData> findDetailObjectDataListIgnoreFormula(IObjectDescribe detailDescribe, IObjectData masterObjectData, User user) {
        IActionContext context = buildContext(user);
        context.setDoCalculate(false);
        ActionContextExt.of(context).disableDeepQuote();

        return findDetailObjectDataList(context, detailDescribe, masterObjectData);
    }

    @Override
    public List<IObjectData> findDetailObjectDataListIgnoreFormula(IObjectData masterObjectData, User user) {
        List<IObjectData> resultAllDetailData = Lists.newArrayList();
        List<IObjectDescribe> detailDescribes =
                describeLogicService.findDetailDescribesWithoutCopyIfGray(user.getTenantId(), masterObjectData.getDescribeApiName());
        if (CollectionUtils.empty(detailDescribes)) {
            return resultAllDetailData;
        }
        detailDescribes.forEach(x -> resultAllDetailData.addAll(findDetailObjectDataListIgnoreFormula(x, masterObjectData, user)));

        return resultAllDetailData;
    }

    @Override
    public List<IObjectData> findDetailIncludeInvalidObjectDataListIgnoreFormula(IObjectData masterObjectData, User user) {
        List<IObjectData> resultAllDetailData = Lists.newArrayList();
        List<IObjectDescribe> allDetailDescribes =
                describeLogicService.findDetailDescribesWithoutCopyIfGray(user.getTenantId(), masterObjectData.getDescribeApiName());
        if (CollectionUtils.empty(allDetailDescribes)) {
            return resultAllDetailData;
        }
        for (IObjectDescribe iObjectDescribe : allDetailDescribes) {
            List<IObjectData> detailObjectDataList = findDetailIncludeInvalidObjectDataListIgnoreFormula(iObjectDescribe, masterObjectData, user);
            resultAllDetailData.addAll(detailObjectDataList);
        }

        return resultAllDetailData;
    }

    @Override
    public List<IObjectData> findDetailIncludeInvalidObjectDataListIgnoreFormula(IObjectDescribe detailDescribe, IObjectData masterObjectData, User user) {
        try {
            IActionContext context = buildContext(user);
            context.put("include_invalid", true);
            context.setDoCalculate(false);
            ActionContextExt.of(context).disableDeepQuote();

            List<IObjectData> detailObjectDataList = dataProxy.findDetailObjectDataList(detailDescribe,
                    masterObjectData, context);

            return detailObjectDataList == null ? Lists.newArrayList() : detailObjectDataList;
        } catch (MetadataServiceException e) {
            log.warn("findDetailObjectDataList error,user:{},detailApiName:{},masterDataId:{}",
                    user, detailDescribe.getApiName(), masterObjectData.getId(), e);
            throw new MetaDataBusinessException(e);
        }

    }

    @Override
    public Map<String, List<IObjectData>> findDetailObjectDataList(List<IObjectDescribe> detailDescribeList, IObjectData masterObjectData, User user) {
        if (CollectionUtils.empty(detailDescribeList)) {
            return Maps.newHashMap();
        }
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        for (IObjectDescribe detailDescribe : detailDescribeList) {
            List<IObjectData> detailObjectDataList = findDetailObjectDataList(detailDescribe, masterObjectData, user);
            result.put(detailDescribe.getApiName(), detailObjectDataList);
        }
        return result;
    }

    @Override
    public List<IObjectData> findDataWithWhere(User user, String apiName, List<IFilter> filters, List<Wheres>
            whereList, int offset, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(offset);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setWheres(whereList);

        return findBySearchQuery(user, apiName, searchTemplateQuery).getData();
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                                      SearchTemplateQuery searchTemplateQuery) {
        return findBySearchQuery(user, describe, apiName, searchTemplateQuery, false);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                                      SearchTemplateQuery searchTemplateQuery, boolean isSimple) {
        return findBySearchQuery(user, describe, apiName, searchTemplateQuery, isSimple, false);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                                      SearchTemplateQuery searchTemplateQuery, boolean isSimple,
                                                      boolean esSearchSkipRecentUpdateCheck) {
        return findBySearchQuery(user, describe, apiName, searchTemplateQuery, isSimple, esSearchSkipRecentUpdateCheck, false);
    }


    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName, SearchTemplateQuery searchTemplateQuery, boolean isSimple, boolean esSearchSkipRecentUpdateCheck, boolean skipRelevantTeam, boolean searchRichTextExtra) {
        QueryContext queryContext = QueryContext.builder()
                .user(user)
                .isSimple(isSimple)
                .esSearchSkipRecentUpdateCheck(esSearchSkipRecentUpdateCheck)
                .skipRelevantTeam(skipRelevantTeam)
                .searchRichTextExtra(searchRichTextExtra)
                .build();
        return findByQueryWithContext(queryContext, apiName, searchTemplateQuery);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName, SearchTemplateQuery searchTemplateQuery, boolean isSimple, boolean esSearchSkipRecentUpdateCheck, boolean skipRelevantTeam) {
        return findBySearchQuery(user, describe, apiName, searchTemplateQuery, isSimple, esSearchSkipRecentUpdateCheck, skipRelevantTeam, false);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(QueryContext queryContext,
                                                      String describeApiName,
                                                      SearchTemplateQuery searchTemplateQuery) {
//        IActionContext context = buildContext(queryContext.getUser(), queryContext.isSkipRelevantTeam());
//        ActionContextExt actionContextExt = ActionContextExt.of(context);
//        if (queryContext.isSimple()) {
//            //不处理计算字段
//            actionContextExt.setDoCalculate(false);
//            //元数据不处理引用字段
//            actionContextExt.disableDeepQuote();
//        }
//        if (queryContext.isEsSearchSkipRecentUpdateCheck()) {
//            actionContextExt.esSearchSkipRecentUpdateCheck();
//        }
//        if (queryContext.isPaginationOptimization()) {
//            actionContextExt.paginationOptimization();
//        }
//        return findBySearchQuery(context, describeApiName, searchTemplateQuery);
        return findByQueryWithContext(queryContext, describeApiName, searchTemplateQuery);
    }

    @Override
    public QueryResult<IObjectData> findByQueryWithContext(QueryContext queryContext, String describeApiName,
                                                           SearchTemplateQuery searchTemplateQuery) {
        StopWatch stopWatch = StopWatch.create("findByQueryWithContext");
        try {
            //actionContext
            ActionContextExt actionContextExt = ActionContextExt.of(queryContext.getUser())
                    .setSkipRelevantTeam(queryContext.isSkipRelevantTeam())
                    .setCalculateFormula(queryContext.isCalculateFormula())
                    .setCalculateQuote(queryContext.isCalculateQuote())
                    .setConvertQuoteForView(queryContext.isConvertQuoteForView())
                    .setCalculateCount(queryContext.isCalculateCount())
                    .setSearchRichTextExtra(queryContext.isSearchRichTextExtra())
                    .setTimeoutNSecond(queryContext.getTimeoutNSecond())
                    .setStatOnEmptyResult(queryContext.getStatOnEmptyResult());
            // 从上下文中获取,本次查询是否要保留支持多语言的字段中各语言的数据
            Optional.ofNullable(queryContext.getKeepAllMultiLangValue())
                    .ifPresent(actionContextExt::setKeepAllMultiLangValue);
            //不要元数据补充引用字段，自己计算
            if ((queryContext.isCalculateQuote() && queryContext.isConvertQuoteForView())
                    || Boolean.FALSE.equals(searchTemplateQuery.getNeedReturnQuote())) {
                actionContextExt.disableDeepQuote();
            }
            if (queryContext.isSimple()) {
                //不处理计算字段
                actionContextExt.setDoCalculate(false);
                //元数据不处理引用字段
                actionContextExt.disableDeepQuote();
            }

            //跳过校验的优先级更高
            if (queryContext.isEsSearchSkipRecentUpdateCheck()) {
                actionContextExt.esSearchSkipRecentUpdateCheck();
            } else if (queryContext.isEsRecentUpdateCheck()) {
                actionContextExt.esRedisRecentUpdateCheck();
            }

            if (queryContext.isPaginationOptimization()) {
                actionContextExt.paginationOptimization();
            }
            //SearchTemplateQuery
            if (!Utils.AGGREGATE_VALUE_OBJ_API_NAME.equals(describeApiName)) {
                SearchTemplateQueryExt.of(searchTemplateQuery).addDeletedFilterIfNoDeletedFilter(queryContext.isIncludeInvalid());
            }
            SearchTemplateQueryExt.of(searchTemplateQuery).trimFieldValue();
            String tenantId = queryContext.getUser().getTenantId();
            IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
            stopWatch.lap("findObject");
            SearchTemplateQueryExt.of(searchTemplateQuery).validateWheresAndFilters(queryContext.getUser(), describe, queryContext.isValidateFilterField());
            stopWatch.lap("validateWheresAndFilters");

            IActionContext context = actionContextExt.getContext();
            QueryResult<IObjectData> result;
            if (queryContext.onlySelectPreferredField()) {
                result = findBySearchTemplateQueryWithFields(context, describeApiName, searchTemplateQuery,
                        queryContext.getProjectionFields());
                stopWatch.lap("findBySearchTemplateQueryWithFields");
            } else {
                result = dataProxy.findBySearchQuery(tenantId, describeApiName, searchTemplateQuery, context);
                stopWatch.lap("dataProxy.findBySearchQuery");
                batchCalculate(result.getData(), describe, context);
                stopWatch.lap("batchCalculate");
                handleMultiLangField(result.getData(), describe, searchTemplateQuery.getWhatDescribeApiName(), context);
                stopWatch.lap("handleMultiLangField");
            }
            // 若 findExplicitTotalNum 参数为 true，则将 dataList 置空
            if (Boolean.TRUE.equals(searchTemplateQuery.getFindExplicitTotalNum())) {
                result.setData(Lists.newArrayList());
            } else {
                IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
                batchConvertRichTextFieldFromMetadata(result.getData(), objectDescribe);
                batchConvertNPathUrl(context, result.getData(), objectDescribe);
                stopWatch.lap("convertRichText");
                //补充各种字段的__r
                if (queryContext.isFillExtendInfo()) {
                    metaDataMiscService.fillExtendFieldInfo(describe, result.getData(), queryContext.getUser(),
                            queryContext.isNotFillQuote(), queryContext.isNotFillMask());
                    stopWatch.lap("fillExtendFieldInfo");
                }
                if (queryContext.isNeedOptionLabel()) {
                    metaDataMiscService.fillSelectLabelInfo(describe, result.getData());
                }
            }
            return result;
        } catch (MetadataServiceException e) {
            log.warn("findByQueryWithContext error,context:{},apiName:{},searchTemplateQuery:{}", queryContext,
                    describeApiName, searchTemplateQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryIfFillExtendFieldInfo(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery,
                                                                           boolean isIncludeLookup, boolean isIncludeQuoteValue, boolean isFillExtendField,
                                                                           boolean isFillMaskField) {
        QueryResult<IObjectData> queryResult = findBySearchQuery(user, describe, describe.getApiName(), searchTemplateQuery);
        List<IObjectData> dataList = queryResult.getData();
        fillExtendFieldInfo(user, describe, isIncludeLookup, isIncludeQuoteValue, isFillExtendField, isFillMaskField, dataList);
        queryResult.setData(dataList);
        return queryResult;
    }

    private void fillExtendFieldInfo(User user, IObjectDescribe describe, boolean isIncludeLookup, boolean isIncludeQuoteValue, boolean isFillExtendField, boolean isFillMaskField, List<IObjectData> dataList) {
        //填充lookup字段的__r的值
        if (isIncludeLookup || isFillExtendField) {
            metaDataMiscService.fillObjectDataWithRefObject(describe, dataList, user, null, true);
            metaDataMiscService.parsePaymentObjOrderNames(describe, dataList, user, false);

        }
        //填充处理引用字段的值
        if (isFillExtendField || isIncludeQuoteValue) {
            Boolean oldValue = RequestUtil.setRecordCalculateLog(false);
            quoteValueService.fillQuoteFieldValue(user, dataList, describe, false);
            RequestUtil.setRecordCalculateLog(oldValue);
        }
        if (isFillExtendField) {
            metaDataMiscService.fillCountryAreaLabel(describe, dataList, user);
            metaDataMiscService.fillUserInfo(describe, dataList, user);
            metaDataMiscService.fillDepartmentInfo(describe, dataList, user);
            metaDataMiscService.fillDataVisibilityRange(user, describe, dataList);
        }
        if (isFillMaskField) {
            metaDataMiscService.fillMaskFieldValue(user, dataList, describe, false);
        }
    }


    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, String apiName, SearchTemplateQuery searchTemplateQuery) {
        return findBySearchQuery(user, null, apiName, searchTemplateQuery);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(IActionContext context, String apiName, SearchTemplateQuery searchTemplateQuery) {
        if (!Utils.AGGREGATE_VALUE_OBJ_API_NAME.equals(apiName)) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addDeletedFilterIfNoDeletedFilter();
        }
        try {
            SearchTemplateQueryExt.of(searchTemplateQuery).trimFieldValue();
            IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), apiName);
            SearchTemplateQueryExt.of(searchTemplateQuery).validateWheresAndFilters(
                    User.builder().tenantId(context.getEnterpriseId()).userId(context.getUserId()).build(), describe);
            QueryResult<IObjectData> result = dataProxy.findBySearchQuery(context.getEnterpriseId(),
                    apiName, searchTemplateQuery, context);

            if (Objects.isNull(result)) {
                log.error("findBySearchQuery error:result is null,context:{},apiName:{},searchTemplateQuery:{}",
                        context, apiName, searchTemplateQuery.toJsonString());
                throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
            }

            // 若 findExplicitTotalNum 参数为 true，则将 dataList 制空
            if (Boolean.TRUE.equals(searchTemplateQuery.getFindExplicitTotalNum())) {
                result.setData(Lists.newArrayList());
            }

            handleMultiLangField(result.getData(), describe, searchTemplateQuery.getWhatDescribeApiName(), context);
            batchCalculate(result.getData(), apiName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), apiName);
            batchConvertRichTextFieldFromMetadata(result.getData(), objectDescribe);
            batchConvertNPathUrl(context, result.getData(), objectDescribe);

            return result;
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQuery error,context:{},apiName:{},searchTemplateQuery:{}", context,
                    apiName, searchTemplateQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void handleIgnore(IActionContext context, SearchTemplateQuery searchTemplateQuery) {
        //不处理计算字段
        context.setDoCalculate(false);
        //元数据不处理引用字段、不获取相关团队
        ActionContextExt.of(context).disableDeepQuote().skipRelevantTeam();
        //跳过数据权限
        searchTemplateQuery.setPermissionType(0);
        //不返回总记录数
        searchTemplateQuery.setNeedReturnCountNum(false);
        //不返回引用字段
        searchTemplateQuery.setNeedReturnQuote(false);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryIgnoreAll(User user, String apiName, SearchTemplateQuery searchTemplateQuery) {
        IActionContext context = buildContext(user);
        handleIgnore(context, searchTemplateQuery);
        return findBySearchQuery(context, apiName, searchTemplateQuery);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryWithFieldsIgnoreAll(User user, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        IActionContext context = buildContext(user);
        handleIgnore(context, searchTemplateQuery);
        return findBySearchTemplateQueryWithFields(context, apiName, searchTemplateQuery, fieldList);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryWithDeleted(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery) {
        return findBySearchQueryWithDeleted(user, describe, searchTemplateQuery, false);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryWithDeleted(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery, boolean skipRelevantTeam) {
        return findBySearchQueryWithDeleted(user, describe, searchTemplateQuery, skipRelevantTeam, false);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryWithDeleted(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery, boolean skipRelevantTeam, boolean searchRichTextExtra) {
        IActionContext context = buildContext(user, skipRelevantTeam);
        ActionContextExt.of(context).setSearchRichTextExtra(searchRichTextExtra);
        return findBySearchQueryWithDeleted(context, describe, searchTemplateQuery);
    }

    @Override
    public QueryResult<IObjectData> findBySearchQueryWithDeleted(IActionContext context, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery) {
        try {
            SearchTemplateQueryExt.of(searchTemplateQuery).trimFieldValue();
            SearchTemplateQueryExt.of(searchTemplateQuery).validateWheresAndFilters(
                    User.builder().tenantId(context.getEnterpriseId()).userId(context.getUserId()).build(), describe);
            if (Boolean.FALSE.equals(searchTemplateQuery.getNeedReturnQuote())) {
                ActionContextExt.of(context).disableDeepQuote();
            }
            QueryResult<IObjectData> result = dataProxy.findBySearchQuery(context.getEnterpriseId(),
                    describe.getApiName(), searchTemplateQuery, context);
            if (Objects.isNull(result)) {
                log.error("findBySearchQuery error:result is null,context:{},apiName:{},searchTemplateQuery:{}",
                        context, describe.getApiName(), searchTemplateQuery.toJsonString());
                throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
            }

            handleMultiLangField(result.getData(), describe, searchTemplateQuery.getWhatDescribeApiName(), context);
            batchCalculate(result.getData(), describe, context);
            batchConvertRichTextFieldFromMetadata(result.getData(), describe);
            batchConvertNPathUrl(context, result.getData(), describe);
            return result;
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQuery error,context:{},apiName:{},searchTemplateQuery:{}", context,
                    describe.getApiName(), searchTemplateQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public QueryResult<IObjectData> findBySearchQuery(User user, String objectApiName, ISearchQuery searchQuery) {
        IActionContext context = buildContext(user);
        try {
            return dataProxy.findBySearchQuery(searchQuery, context.getEnterpriseId(), objectApiName, context);
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQuery error,tenantId:{},objectApiName:{},searchQuery:{}",
                    user.getTenantId(), objectApiName, searchQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public QueryResult<IObjectData> findBySearchTemplateQueryWithFields(IActionContext context, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        StopWatch stopWatch = StopWatch.create("findBySearchTemplateQueryWithFields");
        SearchTemplateQueryExt.of(searchTemplateQuery).addDeletedFilterIfNoDeletedFilter();
        try {
            SearchTemplateQueryExt.of(searchTemplateQuery).trimFieldValue();
            QueryResult<IObjectData> result;
            IObjectDescribe describe = null;
            CalculateField calculateField = null;
            List<String> queryFieldList = Lists.newArrayList(CollectionUtils.nullToEmpty(fieldList));
            if (CollectionUtils.notEmpty(queryFieldList)) {
                describe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), apiName);
                stopWatch.lap("findObject");

                List<IFieldDescribe> fieldDescribeList = ObjectDescribeExt.of(describe).getFieldByApiNames(queryFieldList);
                List<String> selectOtherFields = fieldDescribeList.stream()
                        .filter(x -> FieldDescribeExt.of(x).isSelectOne() || FieldDescribeExt.of(x).isSelectMany())
                        .map(x -> FieldDescribeExt.getSelectOther(x.getApiName()))
                        .filter(x -> !queryFieldList.contains(x))
                        .distinct()
                        .collect(Collectors.toList());
                queryFieldList.addAll(selectOtherFields);
                stopWatch.lap("fillSelectOtherFields");
                boolean cooperativeRichTextGray = AppFrameworkConfig.cooperativeRichTextGray(context.getEnterpriseId(), apiName);
                List<String> richTextExtFields = fieldDescribeList.stream()
                        .filter(x -> RichTextExt.isProcessableRichText(x) || StringUtils.equals(x.getType(), IFieldType.BIG_TEXT)
                                || (cooperativeRichTextGray && RichTextExt.isProcessableCooperativeRichText(x)))
                        .map(x -> RichTextExt.getRichTextNameInMD(x.getApiName()))
                        .filter(x -> !queryFieldList.contains(x))
                        .distinct()
                        .collect(Collectors.toList());
                queryFieldList.addAll(richTextExtFields);
                stopWatch.lap("fillRichTextExtFields");

                //要查询owner_department，必须同时查询owner和last_modified_time，否则sql会报错。
                if (queryFieldList.contains(ObjectDataExt.OWNER_DEPARTMENT)) {
                    if (!queryFieldList.contains(IObjectData.OWNER)) {
                        queryFieldList.add(IObjectData.OWNER);
                    }
                    if (!queryFieldList.contains(IObjectData.LAST_MODIFIED_TIME)) {
                        queryFieldList.add(IObjectData.LAST_MODIFIED_TIME);
                    }
                    stopWatch.lap("fillOwnerDepartmentFields");
                }

                // 开启了字段多语的字段，需要查询 __lang 字段
                for (IFieldDescribe fieldDescribe : fieldDescribeList) {
                    if (BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
                        queryFieldList.add(FieldDescribeExt.getMultiLangExtraFieldName(fieldDescribe.getApiName()));
                    }
                }

                if (context.doCalculate()) {
                    calculateField = getCalculateFields(User.systemUser(context.getEnterpriseId()), describe, fieldList);
                    stopWatch.lap("findCalculateFields");
                }
            }
            if (CalculateField.needCalculate(calculateField)) {
                //有字段需要计算，先查询所有字段
                result = objectDataService.findBySearchQuery(context.getEnterpriseId(), apiName, searchTemplateQuery, context);
                stopWatch.lap("objectDataService.findBySearchQuery");
                List<IObjectData> dataList = result.getData();

                doCalculate(context, describe, dataList, stopWatch, calculateField);
                //只返回用户需要的字段
                List<IObjectData> finalDataList = getDataProjectionDataList(dataList, queryFieldList);
                stopWatch.lap("filterFields");

                //补充引用字段的__v和__o
                List<Quote> quoteList = calculateField.getQuoteList();
                if (CollectionUtils.notEmpty(quoteList)) {
                    Map<String, IObjectData> origDataMap = dataList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x, (x, y) -> x));
                    CollectionUtils.nullToEmpty(quoteList).forEach(quote -> finalDataList.forEach(data -> {
                        IObjectData origData = origDataMap.get(data.getId());
                        String quoteOrigName = FieldDescribeExt.getQuotedValueNameByFieldName(quote.getApiName());
                        if (origData.containsField(quoteOrigName)) {
                            data.set(quoteOrigName, origData.get(quoteOrigName));
                        }
                        String quoteSelectOtherName = FieldDescribeExt.getSelectOther(quote.getApiName());
                        if (origData.containsField(quoteSelectOtherName)) {
                            data.set(quoteSelectOtherName, origData.get(quoteSelectOtherName));
                        }
                    }));
                    stopWatch.lap("fillQuoteFields");
                }

                result.setData(finalDataList);
            } else {
                result = objectDataService.findBySearchQuery(context.getEnterpriseId(), apiName, searchTemplateQuery, queryFieldList, context);
                stopWatch.lap("objectDataService.findBySearchQuery");
            }
            if (Objects.isNull(result)) {
                log.error("findBySearchQueryWithFields error:result is null,context:{},apiName:{},searchTemplateQuery:{},fieldList:{}",
                        context, apiName, searchTemplateQuery.toJsonString(), queryFieldList);
                throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
            }
            handleMultiLangField(result.getData(), apiName, context);
            stopWatch.lap("handleMultiLangField");
            return result;
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQueryWithFields error,context:{},apiName:{},searchTemplateQuery:{},fieldList:{}", context,
                    apiName, searchTemplateQuery.toJsonString(), fieldList, e);
            throw new MetaDataBusinessException(e);
        } finally {
            stopWatch.logSlow(300);
        }
    }

    @Override
    public QueryResult<IObjectData> findObjectDataWithFieldsAndPage(User user, String masterApiName, Set<String> masterDataIds,
                                                                    IObjectDescribe detailDescribe, int pageNum, int pageSize,
                                                                    List<String> fieldList) {
        IActionContext context = buildContext(user, true);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        handleIgnore(context, searchTemplateQuery);
        String mdFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldNameOrThrowException(masterApiName);
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.IN, mdFieldName, Lists.newArrayList(masterDataIds));
        searchTemplateQuery.setNeedReturnCountNum(pageNum == 1);
        searchTemplateQuery.setLimit(pageSize);
        searchTemplateQuery.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, pageSize));
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        try {
            return objectDataService.findBySearchQuery(user.getTenantId(),
                    detailDescribe.getApiName(), searchTemplateQuery, fieldList, context);
        } catch (MetadataServiceException e) {
            log.warn("findObjectDataWithFieldsAndPage error,context:{},masterApiName:{},masterDataIds:{},detailDescribe:{},searchTemplateQuery:{},fieldList:{}",
                    context, masterApiName, masterDataIds, detailDescribe.getApiName(), searchTemplateQuery.toJsonString(), fieldList.toString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> getDataProjectionDataList(List<IObjectData> dataList, List<String> includeFields) {

        if (CollectionUtils.empty(includeFields) || CollectionUtils.empty(dataList)) {
            return dataList;
        }
        List<IObjectData> resultList = Lists.newArrayList();
        dataList.forEach(x -> {
            IObjectData objectData = new ObjectData();
            objectData.setId(x.getId());
            objectData.setDescribeApiName(x.getDescribeApiName());
            objectData.setDescribeId(x.getDescribeId());
            objectData.setTenantId(x.getTenantId());

            includeFields.forEach(y -> objectData.set(y, x.get(y)));

            resultList.add(objectData);
        });
        return resultList;
    }

    @Override
    public ISearchTemplate findSearchTemplate(User user, String templateId, String objectApiName) {
        return findSearchTemplateByIdAndType(user, templateId, objectApiName, "");
    }

    @Override
    public ISearchTemplate findSearchTemplateByIdAndType(User user, String templateId, String describeApiName, String sceneType) {
        ISearchTemplate searchTemplate;
        if (StringUtils.isNotBlank(templateId)) {
            CustomSceneResult byId = customSceneService.findByIdJoinTenant(templateId, describeApiName, sceneType, user);
            searchTemplate = new SearchTemplate(byId.getSearchTemplate());
        } else {
            //如果template_id是"",那么就直接用"查看全部"这个默认模板
            searchTemplate = customSceneService.findDefaultByDescribeApiName(describeApiName, user);
        }
        return searchTemplate;
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId, String searchQueryInfo) {
        return getSearchTemplateQuery(user, objectDescribe, templateId, searchQueryInfo, false);
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId, String searchQueryInfo, boolean isRelatedPage) {
        boolean isAdmin = user.isSupperAdmin() || userRoleInfoService.isAdmin(user);
        return getSearchTemplateQuery(user, objectDescribe, templateId, searchQueryInfo, isRelatedPage, isAdmin);
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId,
                                                      String searchQueryInfo, boolean isRelatedPage, boolean isAdmin) {
        ISearchTemplate searchTemplate = findSearchTemplateByIdAndType(user, templateId, objectDescribe.getApiName(), null);
        return getSearchTemplateQueryWithIgnoreSceneFilter(user, objectDescribe, searchTemplate, searchQueryInfo,
                isAdmin, isRelatedPage, false, false);
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe, String templateId, String searchQueryInfo, boolean isIgnoreSceneFilter) {
        return getSearchTemplateQueryWithIgnoreSceneFilter(user, objectDescribe, templateId, "", searchQueryInfo, false, isIgnoreSceneFilter, false);
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe, String templateId, String searchTemplateType, String searchQueryInfo, boolean isRelatedPage, boolean isIgnoreSceneFilter, boolean isIgnoreSceneRecordType) {
        ISearchTemplate searchTemplate = findSearchTemplateByIdAndType(user, templateId, objectDescribe.getApiName(), searchTemplateType);
        return getSearchTemplateQueryWithIgnoreSceneFilter(user, objectDescribe, searchTemplate, searchQueryInfo,
                isRelatedPage, isIgnoreSceneFilter, isIgnoreSceneRecordType);
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe,
                                                                           ISearchTemplate searchTemplate, String searchQueryInfo,
                                                                           boolean isRelatedPage, boolean isIgnoreSceneFilter,
                                                                           boolean isIgnoreSceneRecordType) {
        return getSearchTemplateQueryWithIgnoreSceneFilter(user, objectDescribe, searchTemplate, searchQueryInfo,
                isAdmin(user), isRelatedPage, isIgnoreSceneFilter, isIgnoreSceneRecordType);
    }

    @Override
    public SearchTemplateQuery buildDetailSearchTemplateQuery(User user, ObjectDescribeExt detailDescribe, IObjectData masterData) {
        SearchTemplateQuery query = getSearchTemplateQuery(user, detailDescribe, null, "{\"limit\":2000,\"offset\":0}", true);
        // 设置主数据
        String refFieldName = detailDescribe.getMasterDetailFieldName(masterData.getDescribeApiName())
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, refFieldName, masterData.getId());
        // 优先按照order_by字段排序
        SearchTemplateQueryExt.of(query).orderByFirst();
        return query;
    }

    @Override
    public void handleDataRightsParameter(IObjectDescribe describe,
                                          ISearchTemplateQuery searchTemplateQuery, User user) {
        IDataRightsParameter dataRightsParameter = buildDataRightParameter(describe);
        if (user.isOutUser()) {
            // 调用深研接口查询外部数据权限配置
            OutDataPrivilege outDataPrivilege = dataPrivilegeService.getOutDataPrivilege(user, RequestUtil.getAppId(),
                    describe.getApiName());
            dataRightsParameter.setLinkAppDataAuthRange(outDataPrivilege.getValue());
            searchTemplateQuery.setPermissionType(2);
        } else {
            if (functionPrivilegeService.funDataViewPrivilegeCheck(user, describe)) {
                return;
            }
            searchTemplateQuery.setPermissionType(1);
        }
        searchTemplateQuery.setDataRightsParameter(dataRightsParameter);
    }

    private IDataRightsParameter buildDataRightParameter(IObjectDescribe describe) {
        IDataRightsParameter parameter = new DataRightsParameter();
        parameter.setSceneType("all");
        parameter.setRoleType("1");
        parameter.setCascadeDept(true);
        parameter.setCascadeSubordinates(true);
        ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().ifPresent(it -> {
            // 从对象走主对象的数据权限
            parameter.setIsDetailObject(true);
            parameter.setMasterIdFieldApiName(it.getApiName());
            parameter.setMasterObjectApiName(it.getTargetApiName());
        });
        return parameter;
    }

    @Override
    public Query findSearchQuery(User user, IObjectDescribe objectDescribe, String searchQueryInfo, SearchQueryContext context) {
        ISearchTemplate searchTemplate = context.getSearchTemplate();
        if (Objects.isNull(searchTemplate)) {
            searchTemplate = findSearchTemplateByIdAndType(user, context.getTemplateId(), objectDescribe.getApiName(), context.getSearchTemplateType());
        }
        return SearchQueryRender.builder()
                .isRelatedPage(context.isRelatedPage())
                .isIgnoreSceneFilter(context.isIgnoreSceneFilter())
                .isIgnoreSceneRecordType(context.isIgnoreSceneRecordType())
                .dataPrivilegeService(dataPrivilegeService)
                .userRoleInfoService(userRoleInfoService)
                .functionPrivilegeService(functionPrivilegeService)
                .crmService(crmService)
                .searchTemplate(searchTemplate)
                .describe(objectDescribe)
                .searchQueryInfo(searchQueryInfo)
                .followLogicService(followLogicService)
                .metaDataFindService(this)
                .user(user)
                .build()
                .render();
    }

    private SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe,
                                                                            ISearchTemplate searchTemplate,
                                                                            String searchQueryInfo, boolean isAdmin,
                                                                            boolean isRelatedPage, boolean isIgnoreSceneFilter,
                                                                            boolean isIgnoreSceneRecordType) {
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        isAdmin = user.isSupperAdmin() || isAdmin;
        mergeFilter(user, objectDescribe, queryExt, searchTemplate, isAdmin, isIgnoreSceneFilter, isIgnoreSceneRecordType);
        //自定义对象默认排序
        queryExt.handelOrders(searchTemplate, objectDescribe, isRelatedPage);
        processFilterSupportDisplayName(user.getTenantId(), objectDescribe, query);
        renderERVarFromFilterValues(user, query.getFilters());
        return query;
    }

    @Override
    public QueryResult<IObjectData> findDetailObjectDataWithPage(User user, String masterApiName, String masterDataId,
                                                                 IObjectDescribe detailDescribe, int pageNum,
                                                                 int pageSize, List<IFilter> filters) {
        return findDetailObjectDataBatchWithPage(user, masterApiName, Lists.newArrayList(masterDataId), detailDescribe, pageNum, pageSize, filters);
    }

    @Override
    public QueryResult<IObjectData> findDetailObjectDataBatchWithPage(User user, String masterApiName, List<String> masterDataIds,
                                                                      IObjectDescribe detailDescribe, int pageNum,
                                                                      int pageSize, List<IFilter> filters) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        queryExt.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, pageSize));
        queryExt.setLimit(pageSize);
        String mdFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldNameOrThrowException(masterApiName);
        queryExt.addFilter(Operator.IN, mdFieldName, masterDataIds);
        queryExt.searchInDB();
        if (CollectionUtils.notEmpty(filters)) {
            queryExt.addFilters(filters);
        }

        return findBySearchQuery(user, detailDescribe, detailDescribe.getApiName(), (SearchTemplateQuery) queryExt.getQuery(), true);
    }

    @Override
    public QueryResult<IObjectData> findDetailObjectDataWithPageIncludeInvalid(User user, String masterApiName, String masterDataId,
                                                                               IObjectDescribe detailDescribe, int pageNum,
                                                                               int pageSize, List<IFilter> filters) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        queryExt.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, pageSize));
        queryExt.setLimit(pageSize);
        String mdFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldNameOrThrowException(masterApiName);
        queryExt.addFilter(Operator.EQ, mdFieldName, masterDataId);
        if (CollectionUtils.notEmpty(filters)) {
            queryExt.addFilters(filters);
        }

        return findBySearchQueryWithDeleted(user, detailDescribe, (SearchTemplateQuery) queryExt.getQuery());
    }

    @Override
    public QueryResult<IObjectData> findDetailObjectDataBatchWithPageIncludeInvalid(User user, String masterApiName, List<String> masterDataIds,
                                                                                    IObjectDescribe detailDescribe, int pageNum,
                                                                                    int pageSize, List<IFilter> filters) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        queryExt.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, pageSize));
        queryExt.setLimit(pageSize);
        String mdFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldNameOrThrowException(masterApiName);
        queryExt.addFilter(Operator.IN, mdFieldName, masterDataIds);
        if (CollectionUtils.notEmpty(filters)) {
            queryExt.addFilters(filters);
        }

        return findBySearchQueryWithDeleted(user, detailDescribe, (SearchTemplateQuery) queryExt.getQuery());
    }

    @Override
    public QueryResult<IObjectData> findRelatedObjectDataWithPage(User user, String masterDataId,
                                                                  IObjectDescribe relatedDescribe, String lookupFieldName,
                                                                  int pageNum, int pageSize, List<IFilter> filters) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        queryExt.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, pageSize));
        queryExt.setLimit(pageSize);
        queryExt.addFilter(Operator.EQ, lookupFieldName, masterDataId);
        if (CollectionUtils.notEmpty(filters)) {
            queryExt.addFilters(filters);
        }

        return findBySearchQuery(user, relatedDescribe.getApiName(), (SearchTemplateQuery) queryExt.getQuery());
    }

    @Override
    public QueryResult<IObjectData> findRelatedObjectDataWithFields(User user, String masterDataId, IObjectDescribe relatedDescribe, List<String> lookupFieldNames, int pageNum, int pageSize, List<IFilter> filters) {
        return findRelatedObjectDataWithFields(user, masterDataId, relatedDescribe, lookupFieldNames, pageNum, pageSize, filters, false);
    }

    @Override
    public QueryResult<IObjectData> findRelatedObjectDataWithFields(User user, String masterDataId,
                                                                    IObjectDescribe relatedDescribe, List<String> lookupFieldNames,
                                                                    int pageNum, int pageSize, List<IFilter> filters, boolean isSimple) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        queryExt.setOffset(SearchTemplateQueryExt.calculateOffset(pageNum, pageSize));
        queryExt.setLimit(pageSize);

        List<Wheres> Wheres = Lists.newArrayList();
        lookupFieldNames.forEach(x -> {
            Wheres where = new Wheres();
            IFilter filter = new Filter();
            filter.setFieldName(x);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(masterDataId));
            where.setFilters(Lists.newArrayList(filter));
            Wheres.add(where);
        });

        if (CollectionUtils.notEmpty(Wheres)) {
            queryExt.setWheres(Wheres);
        }

        return findBySearchQuery(user, relatedDescribe, relatedDescribe.getApiName(), (SearchTemplateQuery) queryExt.getQuery(), isSimple);
    }

    @Override
    public IRelatedListQuery.QueryResult<IObjectData> findRelatedObjectData(IRelatedListQuery relatedListQuery, User
            user) {
        IActionContext actionContext = buildContext(user);
        return findRelatedObjectData(relatedListQuery, user, actionContext);
    }

    @Override
    public IRelatedListQuery.QueryResult<IObjectData> findRelatedObjectData(IRelatedListQuery relatedListQuery, User
            user, IActionContext actionContext) {
        try {
            IRelatedListQuery.QueryResult<IObjectData> relatedList = dataProxy.findRelatedList(relatedListQuery,
                    actionContext);
            return relatedList;
        } catch (MetadataServiceException e) {
            log.warn("Error in find related object data,user:{},query:{}", user, relatedListQuery.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public QueryResult<IObjectData> findInvalidData(FindInvalidDataListArg arg) {
        try {
            QueryResult<InvalidData> queryResult = recycleBinService.findInvalidData(arg);

            QueryResult<IObjectData> result = new QueryResult<>();
            result.setTotalNumber(queryResult.getTotalNumber());
            List<IObjectData> invalidDataList = ObjectDataExt.fromInvalidDataList(queryResult.getData());
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(arg.getTenantId(), arg.getDescribeApiName());
            metaDataMiscService.fillUserInfo(objectDescribe, invalidDataList, User.systemUser(arg.getTenantId()),
                    false, Lists.newArrayList(IObjectData.LAST_MODIFIED_BY));
            result.setData(invalidDataList);

            return result;
        } catch (MetadataServiceException e) {
            log.warn("findInvalidData error,arg:{}", arg, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public QueryResult<IObjectData> findDetailDataBySearchQuery(String tenantId,
                                                                String objectDescribeAPIName, SearchTemplateQuery searchTemplateQuery,
                                                                IActionContext context) {
        try {

            SearchTemplateQueryExt.patch(searchTemplateQuery);
            SearchTemplateQueryExt.of(searchTemplateQuery).trimFieldValue();
            QueryResult<IObjectData> result = dataProxy.findDetailDataBySearchQuery(tenantId, objectDescribeAPIName, searchTemplateQuery, context);

            handleMultiLangField(result.getData(), objectDescribeAPIName, context);
            batchCalculate(result.getData(), objectDescribeAPIName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, objectDescribeAPIName);
            batchConvertRichTextFieldFromMetadata(result.getData(), objectDescribe);
            batchConvertNPathUrl(context, result.getData(), objectDescribe);

            return result;
        } catch (MetadataServiceException e) {
            log.error("Error in find detail data ,tenantId:{},objectDescribeAPIName:{},query:{}", tenantId, objectDescribeAPIName, searchTemplateQuery, e);
            throw new MetaDataBusinessException(e);
        }

    }

    @Deprecated
    @Override
    public Map<String, List<IObjectData>> findRefObjectDataIfHasQuoteField(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe) {
        return null;
    }

    @Override
    public Map<String, List<IObjectData>> findRefObjectData(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe) {
        List<IFieldDescribe> refFieldList = ObjectDescribeExt.of(objectDescribe).getAllRefFieldDescribes();
        return findRefObjectData(user, dataList, objectDescribe, refFieldList);
    }

    @Override
    public Map<String, List<IObjectData>> findRefObjectData(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe, List<IFieldDescribe> refFieldList) {
        Map<String, List<String>> refObjectDataIdMap = ObjectDataExt.getRefObjectDataIds(refFieldList, dataList);

        if (CollectionUtils.empty(refObjectDataIdMap)) {
            return Maps.newHashMap();
        }

        Map<String, List<IObjectData>> refObjectDataMap = Maps.newConcurrentMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        refObjectDataIdMap.forEach((x, y) -> {
            parallelTask.submit(() -> {
                List<IObjectData> refObjectDataList = findObjectDataByIdsIncludeDeletedIgnoreFormula(user, y, x);
                refObjectDataMap.put(x, refObjectDataList);
            });
        });
        try {
            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("findRefObjectDatas error,tenantId:{},apiName:{}", objectDescribe.getTenantId(), objectDescribe.getApiName(), e);
        }

        return refObjectDataMap;
    }

    @Override
    public Map<String, String> findObjectIdByName(User user, String objectApiName, List<String> nameList) {
        if (CollectionUtils.empty(nameList)) {
            return Maps.newHashMap();
        }
        ISearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.setLimit(nameList.size());
        queryExt.setOffset(0);
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectApiName);
        queryExt.addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId());
        queryExt.addFilter(Operator.IN, IObjectData.NAME, nameList);
        IActionContext context = buildContext(user);
        SearchTemplateQuery searchTemplateQuery = queryExt.toSearchTemplateQuery();
        handleIgnore(context, searchTemplateQuery);
        QueryResult<IObjectData> queryResult = findBySearchTemplateQueryWithFields(
                context, objectApiName, searchTemplateQuery,
                Lists.newArrayList(IObjectData.ID, IObjectData.NAME));

        Map<String, String> map = Maps.newHashMap();
        if (CollectionUtils.empty(queryResult.getData())) {
            return map;
        }

        queryResult.getData().forEach(a -> map.put(a.getName(), a.getId()));
        return map;
    }

    @Override
    public void mergeWithDbData(String tenantId, String objectApiName, List<IObjectData> dataList) {
        List<String> idList = dataList.stream().filter(x -> ObjectDataExt.of(x).hasId()).map(x -> x.getId()).collect(Collectors.toList());
        List<IObjectData> dbDataList = findObjectDataByIdsIgnoreFormula(tenantId, idList, objectApiName);
        Map<String, IObjectData> dbDataMap = dbDataList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        dataList.stream().filter(x -> ObjectDataExt.of(x).hasId()).forEach(data -> {
            if (dbDataMap.containsKey(data.getId())) {
                ObjectDataExt.of(data).merge(dbDataMap.get(data.getId()));
            }
        });
    }

    @Override
    public List<IObjectData> findEmployeeInfoByUserIds(String tenantId, List<String> userIds) {
        List<IObjectData> dataList = findEmployeeInfoByUserIdsIgnoreFormula(tenantId, userIds);

        IActionContext context = buildContext(new User(tenantId, User.SUPPER_ADMIN_USER_ID));
        batchCalculate(dataList, ObjectDescribeExt.PERSONNEL_OBJ_API_NAME, context);
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, ObjectDescribeExt.PERSONNEL_OBJ_API_NAME);
        batchConvertRichTextFieldFromMetadata(dataList, objectDescribe);
        batchConvertNPathUrl(context, dataList, objectDescribe);

        return dataList;
    }

    @Override
    public List<IObjectData> findEmployeeInfoByUserIdsForCalculate(String tenantId, List<String> userIds) {
        List<IObjectData> dataList = findEmployeeInfoByUserIdsIgnoreFormula(tenantId, userIds);
        IActionContext context = buildContext(new User(tenantId, User.SUPPER_ADMIN_USER_ID));
        batchCalculate(dataList, ObjectDescribeExt.PERSONNEL_OBJ_API_NAME, context);
        return dataList;
    }

    @Override
    public List<IObjectData> findEmployeeInfoByUserIdsIgnoreFormula(String tenantId, List<String> userIds) {
        if (CollectionUtils.empty(userIds)) {
            return Lists.newArrayList();
        }

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.searchInDB();
        queryExt.setNeedReturnCountNum(false);
        queryExt.setLimit(userIds.size());
        queryExt.addFilter(Operator.IN, ObjectDataExt.USER_ID, userIds);

        IActionContext context = ActionContextExt.of(User.systemUser(tenantId)).skipRelevantTeam().disableDeepQuote().getContext();
        context.setDoCalculate(false);

        QueryResult<IObjectData> queryResult = findBySearchQuery(context,
                ObjectDescribeExt.PERSONNEL_OBJ_API_NAME, (SearchTemplateQuery) queryExt.getQuery());

        return queryResult.getData();
    }

    @Override
    public List<IObjectData> findDepartmentInfoByDepartIds(String tenantId, List<String> departIds) {
        return findDepartmentInfoByDepartIdsInternal(tenantId, departIds, false);
    }

    @Override
    public List<IObjectData> findDepartmentInfoByDepartIdsIgnoreFormula(String tenantId, List<String> departIds) {
        return findDepartmentInfoByDepartIdsInternal(tenantId, departIds, true);
    }

    /**
     * 部门信息查询内部公共方法
     *
     * @param tenantId      租户ID
     * @param departIds     部门ID列表
     * @param ignoreFormula 是否忽略公式计算
     * @return 部门信息列表
     */
    private List<IObjectData> findDepartmentInfoByDepartIdsInternal(String tenantId, List<String> departIds, boolean ignoreFormula) {
        if (CollectionUtils.empty(departIds)) {
            return Lists.newArrayList();
        }

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.searchInDB();
        queryExt.setNeedReturnCountNum(false);
        queryExt.setLimit(departIds.size());
        queryExt.addFilter(Operator.IN, ObjectDataExt.DEPT_ID, departIds);

        ActionContextExt contextExt = ActionContextExt.of(User.systemUser(tenantId)).skipRelevantTeam().disableDeepQuote();
        if (ignoreFormula) {
            contextExt.setDoCalculate(false);
        }
        IActionContext context = contextExt.getContext();

        QueryResult<IObjectData> queryResult = findBySearchQuery(context,
                ObjectDescribeExt.DEPARTMENT_OBJ_API_NAME, (SearchTemplateQuery) queryExt.getQuery());

        return queryResult.getData();
    }

    @Override
    public List<String> findReferencedDataList(User user, String objectApiName, List<String> idList) {
        return relationServiceImpl.findReferenceData(idList, objectApiName, user.getTenantId());
    }

    @Override
    public List<IObjectData> findByIdsIncludeInvalid(List<String> ids, String tenantId, String objectDescribeAPIName, IActionContext context) {
        try {
            List<IObjectData> result = dataProxy.findByIdsIncludeInvalid(ids, tenantId, objectDescribeAPIName, context);
            handleMultiLangField(result, objectDescribeAPIName, context);
            //处理不落地的计算字段
            batchCalculate(result, objectDescribeAPIName, context);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectDescribeAPIName);
            batchConvertRichTextFieldFromMetadata(result, objectDescribe);
            batchConvertNPathUrl(context, result, objectDescribe);

            return result;
        } catch (MetadataServiceException e) {
            log.error("Error in find findByIdsIncludeInvalid ,ids:{}, tenantId:{},objectDescribeAPIName:{},context:{}", ids, tenantId, objectDescribeAPIName, context, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, Map<String, List<RelevantTeam>>> batchFindTeamMember(String tenantId, Map<String, List<String>> apiNameAndIdsMap) {
        Map<String, Map<String, List<RelevantTeam>>> relevantTeamMaps = relevantTeamService.batchFindTeamMember(tenantId, apiNameAndIdsMap);
        return relevantTeamMaps;
    }

    @Override
    public List<IObjectData> findSimpleDataByIds(String tenantId, String apiName, List<String> ids, List<String> fieldList, IActionContext context) {
        try {
            List<IObjectData> resultList = objectDataService.findSimpleDataByIds(tenantId, apiName, ids, fieldList, context);
            return resultList;
        } catch (MetadataServiceException e) {
            log.warn("findSimpleDataByIds error,context:{},apiName:{},ids:{},fieldList:{}", context,
                    apiName, ids, JSON.toJSONString(fieldList), e);
            throw new MetaDataBusinessException(e);
        }

    }

    @Override
    public String getSeqId(String tenantId, String describeApiName) {
        try {
            return objectDataService.getSeqId(tenantId, describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("getSeqId error,tenantId:{},describeApiName:{}",
                    tenantId, describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> findByAggregateSearchQuery(User user,
                                                        SearchTemplateQuery query,
                                                        String describeApiName,
                                                        String groupField) {
        try {
            IGroupByParameter groupByParameter = new GroupByParameter();
            groupByParameter.setGroupBy(Lists.newArrayList(groupField));
            query.setGroupByParameter(groupByParameter);
            return objectDataService.aggregateFindBySearchQuery(buildContext(user), query, describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("aggregateFindBySearchQuery error, user:{}, describeApiName:{}, searchQuery:{}",
                    user, describeApiName, query.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> aggregateFindBySearchQuery(User user, SearchTemplateQuery query, String describeApiName) {
        IActionContext context = ActionContextExt.of(user).getContext();
        return aggregateFindBySearchQuery(context, query, describeApiName);
    }

    private List<IObjectData> aggregateFindBySearchQuery(IActionContext context, SearchTemplateQuery query, String describeApiName) {
        try {
            SearchTemplateQueryExt.of(query).trimFieldValue();
            return objectDataService.aggregateFindBySearchQuery(context, query, describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("aggregateFindBySearchQuery error, tenantId:{}, describeApiName:{}, searchQuery:{}",
                    context.getEnterpriseId(), describeApiName, query.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectData> aggregateFindBySearchQuery(String tenantId,
                                                        SearchTemplateQuery query,
                                                        String describeApiName,
                                                        String groupField,
                                                        String aggFunction,
                                                        String aggField) {
        AggFunctionArg aggFunctionArg = AggFunctionArg.builder()
                .aggFunction(aggFunction)
                .aggField(aggField)
                .build();
        return aggregateFindBySearchQuery(User.systemUser(tenantId), query, describeApiName, groupField,
                Lists.newArrayList(aggFunctionArg));
    }

    @Override
    public List<IObjectData> aggregateFindBySearchQuery(User user,
                                                        SearchTemplateQuery query,
                                                        String describeApiName,
                                                        String groupField,
                                                        List<AggFunctionArg> aggFunctionArgs) {
        SearchTemplateQueryExt.of(query).addDeletedFilterIfNoDeletedFilter();
        IGroupByParameter groupByParameter = new GroupByParameter();
        if (!Strings.isNullOrEmpty(groupField)) {
            groupByParameter.setGroupBy(Lists.newArrayList(groupField));
        } else {
            groupByParameter.setGroupBy(Lists.newArrayList());
        }
        groupByParameter.setAggFunctions(aggFunctionArgs);
        query.setGroupByParameter(groupByParameter);
        if (log.isDebugEnabled()) {
            log.debug("aggregateFindBySearchQuery,tenantId:{},describeApiName:{},groupField:{},aggFunctionArgs:{},query:{}",
                    user.getTenantId(), describeApiName, groupField, aggFunctionArgs, query.toJsonString());
        }
        return aggregateFindBySearchQuery(user, query, describeApiName);
    }

    @Override
    public List<IObjectData> aggregateFindBySearchQueryWithGroupFields(User user, SearchTemplateQuery query, String describeApiName, List<String> groupFields, String aggFunction, String aggField) {
        AggFunctionArg aggFunctionArg = AggFunctionArg.builder()
                .aggFunction(aggFunction)
                .aggField(aggField)
                .build();
        return aggregateFindBySearchQueryWithGroupFields(user, query, describeApiName, groupFields, Lists.newArrayList(aggFunctionArg));
    }

    @Override
    public List<IObjectData> aggregateFindBySearchQueryWithGroupFields(User user,
                                                                       SearchTemplateQuery query,
                                                                       String describeApiName,
                                                                       List<String> groupField,
                                                                       List<AggFunctionArg> aggFunctionArgs) {
        IGroupByParameter groupByParameter = new GroupByParameter();
        if (CollectionUtils.notEmpty(groupField)) {
            groupByParameter.setGroupBy(Lists.newArrayList(groupField));
        } else {
            groupByParameter.setGroupBy(Lists.newArrayList());
        }
        groupByParameter.setAggFunctions(aggFunctionArgs);
        query.setGroupByParameter(groupByParameter);
        if (log.isDebugEnabled()) {
            log.debug("aggregateFindBySearchQuery,tenantId:{},describeApiName:{},groupField:{},aggFunctionArgs:{},query:{}",
                    user.getTenantId(), describeApiName, groupField, aggFunctionArgs, query.toJsonString());
        }
        // TODO: 2021/12/20 queryTemp
        return aggregateFindBySearchQuery(user, query, describeApiName);
    }

    // TODO: 2021/3/4 提供context参数
    @Override
    public void queryDataAndHandle(User user, SearchTemplateQuery searchQuery, IObjectDescribe objectDescribe,
                                   int dataBatchSize, int maxQueryCount, boolean skipRelevantTeam,
                                   Consumer<QueryResult<IObjectData>> consumer) {
        queryDataAndHandle(user, searchQuery, objectDescribe.getApiName(), dataBatchSize, maxQueryCount, skipRelevantTeam, consumer);
    }

    @Override
    public void queryDataAndHandle(User user, SearchTemplateQuery searchQuery, String describeApiName,
                                   int dataBatchSize, int maxQueryCount, boolean skipRelevantTeam, Consumer<QueryResult<IObjectData>> consumer) {
        QueryContext queryContext = QueryContext.builder()
                .user(user)
                .isSimple(false)
                .esSearchSkipRecentUpdateCheck(true)
                .paginationOptimization(true)
                .skipRelevantTeam(skipRelevantTeam)
                .build();
        queryDataAndHandle(queryContext, searchQuery, describeApiName, dataBatchSize, maxQueryCount, consumer);
    }

    @Override
    public void queryDataAndHandle(QueryContext queryContext, SearchTemplateQuery searchQuery, IObjectDescribe objectDescribe,
                                   int dataBatchSize, int maxQueryCount, Consumer<QueryResult<IObjectData>> consumer) {
        queryDataAndHandle(queryContext, searchQuery, objectDescribe.getApiName(), dataBatchSize, maxQueryCount, consumer);
    }

    @Override
    public void queryDataAndHandle(QueryContext queryContext, SearchTemplateQuery searchQuery, String describeApiName,
                                   int dataBatchSize, int maxQueryCount, Consumer<QueryResult<IObjectData>> consumer) {
        String idOffset = null;
        int batchDataNum;
        int queryCount = 0;
        searchQuery.setNeedReturnCountNum(Boolean.FALSE);
        searchQuery.setFindExplicitTotalNum(null);

        do {
            if (maxQueryCount > 0 && queryCount >= maxQueryCount) {
                log.warn("query count exceed max value,queryCount:{},maxCount:{}", queryCount, maxQueryCount);
                break;
            }
            // 将上次查询的最后一条数据的id作为本次查询条件
            resetSearchTemplateQuery(searchQuery, dataBatchSize, idOffset);
            QueryResult<IObjectData> queryResult = findBySearchQuery(queryContext, describeApiName, searchQuery);
            List<IObjectData> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                batchDataNum = queryData.size();
                idOffset = queryData.get(batchDataNum - 1).getId();
            } else {
                batchDataNum = 0;
            }
            // 处理查询到的数据
            consumer.accept(queryResult);
            queryCount++;
        } while (batchDataNum == dataBatchSize && idOffset != null);
    }

    @Override
    public Map<String, String> querySummaryData(User user, String describeApiName, SearchTemplateQuery query, List<ISummaryComponentInfo> summaryInfos) {
        if (CollectionUtils.empty(summaryInfos)) {
            return Maps.newHashMap();
        }

        List<AggFunctionArg> aggFunctionArgs = summaryInfos.stream().map(x ->
                        AggFunctionArg.builder().aggFunction(x.getType()).aggField(x.getFieldName()).build())
                .collect(Collectors.toList());
        SearchTemplateQueryExt.of(query).addDeletedFilterIfNoDeletedFilter();
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList());
        groupByParameter.setAggFunctions(aggFunctionArgs);
        query.setGroupByParameter(groupByParameter);

        //SummaryField接口强制走es
        IActionContext context = ActionContextExt.of(user).setEsSearchSkipRecentUpdateCheck(true).getContext();
        List<IObjectData> aggResult = aggregateFindBySearchQuery(context, query, describeApiName);

        Map<String, String> summaryData = Maps.newHashMap();
        if (CollectionUtils.notEmpty(aggResult)) {
            IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
            summaryInfos.forEach(x -> {
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(x.getFieldName());
                if (Objects.isNull(fieldDescribe)) {
                    return;
                }
                String key = AggregateRule.buildAggFieldName(x.getType(), x.getFieldName());
                Object value = aggResult.get(0).get(key);
                if (!ObjectDataExt.isValueEmpty(value)) {
                    int decimalPlaces = FieldDescribeExt.of(fieldDescribe).getDecimalPlaces();
                    value = new BigDecimal(value.toString()).setScale(decimalPlaces, RoundingMode.HALF_DOWN).toPlainString();
                    if (Strings.isNullOrEmpty(x.getApiName())) {
                        summaryData.put(key, value.toString());
                    } else {
                        summaryData.put(x.getApiName(), value.toString());
                    }
                }
            });
        }

        return summaryData;
    }

    @Override
    public boolean existData(String tenantId, String objectDescribeApiName) {
        try {
            return objectDataService.existData(tenantId, objectDescribeApiName);
        } catch (MetadataServiceException e) {
            log.warn("existData error,context:{},tenantId:{},objectDescribeApiName:{}", tenantId,
                    objectDescribeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void checkOptionUsedByData(User user, String objectDescribeApiName, String fieldApiName, Set<String> values) {
        boolean isExist;
        try {
            //isExist is true throws Exception
            isExist = objectDataService.checkDataValueExist(user.getTenantId(), objectDescribeApiName, fieldApiName, Lists.newArrayList(values));
        } catch (MetadataServiceException e) {
            log.warn("checkDataValueExist error,context:{},tenantId:{},objectDescribeApiName:{},field:{}, values:{}",
                    user.getTenantId(), objectDescribeApiName, fieldApiName, values, e);
            throw new MetaDataBusinessException(e);
        }
        if (isExist) {
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.OPTION_NOT_REMOVE));
        }
    }

    @Override
    public boolean checkDataRelevantTeamOnlyExistOwner(User user, String objectDescribeApiName) {
        try {
            return objectDataService.checkDataRelevantTeam(user.getTenantId(), objectDescribeApiName);
        } catch (MetadataServiceException e) {
            log.warn("checkDataRelevantTeam error, tenantId:{},objectDescribeApiName:{}",
                    user.getTenantId(), objectDescribeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }


    @Override
    public void checkOptionUsedByReference(User user, String objectDescribeApiName, String fieldApiName, Set<String> values) {
        final IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);
        final IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);

        if (fieldDescribe instanceof SelectOneFieldDescribe) {
            List<ISelectOption> options = ((SelectOneFieldDescribe) fieldDescribe).getSelectOptions();
            options.stream().filter(option -> values.contains(option.getValue()))
                    .forEach(option -> {
                        try {
                            fieldOptionChecker.checkReferenceEntity(user.getTenantId(), objectDescribeApiName,
                                    fieldApiName, option.getValue(), option.getLabel());
                        } catch (MetadataServiceException e) {
                            log.warn("checkDataValueReference error,context:{},tenantId:{},objectDescribeApiName:{},field:{}, values:{}",
                                    user.getTenantId(), objectDescribeApiName, fieldApiName, values, e);
                            throw new MetaDataBusinessException(e);
                        }
                    });
        }
    }

    @Override
    public int countObjectDataWithoutFilter(String tenantId, String objectApiName, String searchSource) {
        ISearchTemplateQuery query = new SearchTemplateQuery();
        if (!Strings.isNullOrEmpty(searchSource)) {
            query.setSearchSource(searchSource);
        }
        return countObjectDataFromDB(tenantId, objectApiName, query);
    }

    @Override
    public Integer countObjectDataFromDB(String tenantId, String objApiName, ISearchTemplateQuery query) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.onlyQueryTotalNumIgnorePermission();
        queryExt.addDeletedFilterIfNoDeletedFilter();
        return findBySearchQuery(User.systemUser(tenantId), objApiName, queryExt.toSearchTemplateQuery()).getTotalNumber();
    }

    @Override
    public void processFilterSupportDisplayName(String tenantId, IObjectDescribe objectDescribe, ISearchTemplateQuery searchQuery) {
        if (!AppFrameworkConfig.isSupportDisplayNameFieldEnterprise(tenantId)) {
            return;
        }
        processFilterSupportDisplayName(tenantId, objectDescribe, searchQuery.getFilters());
    }

    @Override
    public void processFilterSupportDisplayName(String tenantId, IObjectDescribe objectDescribe, List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return;
        }
        Map<String, ObjectDescribeExt> objectDescribeMap = Maps.newHashMap();
        filters.forEach(filter -> processRefObjFilterSupportDisplayName(tenantId, objectDescribe, objectDescribeMap, filter));
        ObjectDescribeExt.of(objectDescribe).getWhatListFields().forEach(whatList -> {
            if (Objects.isNull(whatList) || AppFrameworkConfig.isDisplayNameSupportWhatListFieldBlackListObject(objectDescribe.getApiName())) {
                return;
            }
            String apiNameFieldApiName = whatList.getApiNameFieldApiName();
            String idFieldApiName = whatList.getIdFieldApiName();
            processGroupFieldFilterSupportDisplayName(tenantId, filters, apiNameFieldApiName, idFieldApiName, GroupField.GROUP_TYPE_WHAT_LIST);
        });
        ObjectDescribeExt.of(objectDescribe).getWhatFields().forEach(what -> {
            if (Objects.isNull(what)) {
                return;
            }
            String apiNameFieldApiName = what.getApiNameFieldApiName();
            String idFieldApiName = what.getIdFieldApiName();
            processGroupFieldFilterSupportDisplayName(tenantId, filters, apiNameFieldApiName, idFieldApiName, GroupField.GROUP_TYPE_WHAT);
        });
    }

    private void processGroupFieldFilterSupportDisplayName(String tenantId, List<IFilter> filters, String apiNameFieldApiName, String idFieldApiName, String fieldType) {
        List<IFilter> filterList = filters.stream()
                .filter(filter -> StringUtils.equals(getFieldNameFromFilter(filter), apiNameFieldApiName) || StringUtils.equals(getFieldNameFromFilter(filter), idFieldApiName))
                .collect(Collectors.toList());
        if (filterList.size() == 2) {
            boolean match = filterList.stream().anyMatch(filter -> {
                if (StringUtils.equals(getFieldNameFromFilter(filter), apiNameFieldApiName)) {
                    return CollectionUtils.nullToEmpty(filter.getFieldValues()).stream().anyMatch(fieldValue -> ObjectDescribeExt.of(describeLogicService.findObjectWithoutCopyIfGray(tenantId, fieldValue)).isSupportDisplayName());
                }
                return false;
            });
            if (match) {
                filters.forEach(filter -> {
                    if (StringUtils.equals(idFieldApiName, getFieldNameFromFilter(filter))) {
                        if (GroupField.GROUP_TYPE_WHAT_LIST.equals(fieldType)
                                || GroupField.GROUP_TYPE_WHAT.equals(fieldType)
                                && StringUtils.defaultString(filter.getFieldName()).endsWith(".name")) {
                            filter.setFieldName(idFieldApiName + "." + FieldDescribeExt.DISPLAY_NAME);
                        }
                    }
                });
            }
        }
    }

    private String getFieldNameFromFilter(IFilter filter) {
        String fieldName = filter.getFieldName();
        return StringUtils.isNotEmpty(fieldName) && fieldName.contains(".name") ? fieldName.split("\\.")[0] : fieldName;
    }

    private void processRefObjFilterSupportDisplayName(String tenantId, IObjectDescribe objectDescribe, Map<String, ObjectDescribeExt> objectDescribeMap, IFilter filter) {
        String fieldName = filter.getFieldName();
        if (StringUtils.isNotEmpty(fieldName) && fieldName.split("\\.").length == 2) {
            String[] fieldNameArray = fieldName.split("\\.");
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldNameArray[0]);
            if (Objects.isNull(fieldDescribe) || StringUtils.isEmpty(FieldDescribeExt.of(fieldDescribe).getRefObjTargetApiNameDirectly())) {
                return;
            }
            String targetApiName = FieldDescribeExt.of(fieldDescribe).getRefObjTargetApiNameDirectly();
            if (StringUtils.equals(FieldDescribeExt.DISPLAY_NAME, fieldNameArray[1])) {
                filter.setIsMasterField(Boolean.TRUE);
            }
            if (AppFrameworkConfig.isSupportDisplayNameField(targetApiName) && StringUtils.equals("name", fieldNameArray[1])) {
                // 预置对象未支持显示字段
                ObjectDescribeExt objectDescribeExt = objectDescribeMap.get(targetApiName);
                if (Objects.isNull(objectDescribeExt)) {
                    objectDescribeExt = ObjectDescribeExt.of(describeLogicService.findObjectWithoutCopyIfGray(tenantId, targetApiName));
                    objectDescribeMap.put(targetApiName, objectDescribeExt);
                }
                if (objectDescribeExt.isSupportDisplayName()) {
                    filter.setFieldName(fieldNameArray[0] + "." + FieldDescribeExt.DISPLAY_NAME);
                    filter.setIsMasterField(Boolean.TRUE);
                }
            }
        }
    }


    private void resetSearchTemplateQuery(SearchTemplateQuery searchQuery, int dataBatchSize, String idOffset) {
        searchQuery.setOffset(0);
        searchQuery.setLimit(dataBatchSize);
        //按照数据id倒序排
        SearchTemplateQueryExt.of(searchQuery).resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));
        if (idOffset != null) {
            Optional<IFilter> filter = searchQuery.getFilters().stream()
                    .filter(x -> IObjectData.ID.equals(x.getFieldName())
                            && Operator.LT.equals(x.getOperator())
                            && IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(x.getFieldDefineType()))
                    .findFirst();
            if (filter.isPresent()) {
                filter.get().setFieldValues(Lists.newArrayList(idOffset));
            } else {
                SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.LT, IObjectData.ID, idOffset, IFieldDescribe.DEFINE_TYPE_SYSTEM);
            }
        }
    }

    private IActionContext buildContext(User user) {
        return this.buildContext(user, false, false);
    }

    private IActionContext buildContext(User user, boolean skipRelevantTeam) {
        return this.buildContext(user, false, skipRelevantTeam);
    }

    private IActionContext buildContext(User user, boolean allowUpdateInvalid, boolean skipRelevantTeam) {
        return ActionContextExt.of(user)
                .allowUpdateInvalid(allowUpdateInvalid)
                .setSkipRelevantTeam(skipRelevantTeam)
                .getContext();
    }

    private void batchCalculate(List<IObjectData> objectDataList, IObjectDescribe objectDescribe, IActionContext context) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_PROJECTION_FIELDS, context.getEnterpriseId())) {
            _batchCalculate(context, objectDataList, objectDescribe);
            return;
        }
        if (!context.doCalculate()) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("batchCalculate");
        //实时计算统计字段
        if (ActionContextExt.of(context).isCalculateCount()) {
            List<Count> countList = ObjectDescribeExt.of(objectDescribe).getCountFields();
            calculateCountFields(context, objectDataList, objectDescribe, countList);
            stopWatch.lap("calculateCount");
        }
        //设置了计算标记的请求实时计算引用字段
        if (ActionContextExt.of(context).isCalculateQuote()) {
            List<Quote> quoteList = ObjectDescribeExt.of(objectDescribe).getQuoteFieldDescribes();
            batchCalculateQuoteFields(context, objectDataList, objectDescribe, quoteList);
            stopWatch.lap("calculateQuote");
        }
        //只需要处理需要实时计算的计算字段
        List<IFieldDescribe> formulaList = ObjectDescribeExt.of(objectDescribe).getCalculateFieldsExcludeDefaultValue();
        batchCalculateFormulaFields(context, objectDataList, objectDescribe, formulaList);
        stopWatch.lap("calculateFormula");
        stopWatch.logSlow(100);
    }

    private void batchCalculate(List<IObjectData> objectDataList, String objectApiName, IActionContext context) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        if (!context.doCalculate()) {
            return;
        }
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), objectApiName);
        batchCalculate(objectDataList, objectDescribe, context);
    }

    private void _batchCalculate(IActionContext context, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        if (!context.doCalculate() || CollectionUtils.empty(objectDataList)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("_batchCalculate-" + objectDescribe.getApiName());
        try {
            CalculateField calculateFields = getCalculateFields(User.systemUser(context.getEnterpriseId()), objectDescribe, null);
            stopWatch.lap("getCalculateFields");
            doCalculate(context, objectDescribe, objectDataList, stopWatch, calculateFields);
        } finally {
            stopWatch.logSlow(100);
        }
    }

    private void doCalculate(IActionContext context, IObjectDescribe objectDescribe, List<IObjectData> objectDataList, StopWatch stopWatch, CalculateField calculateFields) {
        if (calculateFields.isEmpty()) {
            return;
        }
        //实时计算统计字段
        if (ActionContextExt.of(context).isCalculateCount()) {
            List<Count> countList = calculateFields.getCountList();
            calculateCountFields(context, objectDataList, objectDescribe, countList);
            stopWatch.lap("calculateCount");
        }
        //设置了计算标记的请求实时计算引用字段
        if (ActionContextExt.of(context).isCalculateQuote()) {
            List<Quote> quoteList = calculateFields.getQuoteList();
            batchCalculateQuoteFields(context, objectDataList, objectDescribe, quoteList);
            stopWatch.lap("calculateQuote");
        }
        //只需要处理需要实时计算的计算字段
        List<IFieldDescribe> formulaList = calculateFields.getFormulaList();
        batchCalculateFormulaFields(context, objectDataList, objectDescribe, formulaList);
        stopWatch.lap("calculateFormula");
    }

    private CalculateField getCalculateFields(User user, IObjectDescribe objectDescribe, List<String> projectionFields) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        // 没有指定查询的字段，则返回对象下所有引用，统计和计算字段
        if (CollectionUtils.empty(projectionFields)) {
            return new CalculateField(describeExt.getQuoteFieldDescribes(), describeExt.getCountFields(), describeExt.getCalculateFieldsExcludeDefaultValue());
        }

        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_PROJECTION_FIELDS, user.getTenantId())) {
            List<IFieldDescribe> fieldDescribeList = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(projectionFields);
            List<IFieldDescribe> formulaList = fieldDescribeList.stream()
                    .filter(x -> FieldDescribeExt.of(x).isFormula() || FieldDescribeExt.of(x).hasCalculateValue())
                    .collect(Collectors.toList());
            List<Quote> quoteList = fieldDescribeList.stream()
                    .filter(x -> FieldDescribeExt.of(x).isQuoteField())
                    .map(x -> (Quote) x)
                    .collect(Collectors.toList());
            List<Count> countList = fieldDescribeList.stream()
                    .filter(x -> FieldDescribeExt.of(x).isCountField())
                    .map(x -> (Count) x)
                    .collect(Collectors.toList());
            return new CalculateField(quoteList, countList, formulaList);
        }

        FieldRelationGraph graph = fieldRelationGraphService.buildDependencyGraph(null, Lists.newArrayList(objectDescribe), true, true, true);
        Set<String> calculateFieldSet = Sets.newHashSet();
        projectionFields.forEach(fieldName -> graph.getNode(objectDescribe.getApiName(), fieldName).ifPresent(node -> {
            calculateFieldSet.add(node.getFieldApiName());
            //计算当前字段，需要先计算的字段
            Set<FieldNode> reachableNodes = graph.reachableNodes(node);
            reachableNodes.forEach(rNode -> calculateFieldSet.add(rNode.getFieldApiName()));
        }));
        List<Count> countList = Lists.newArrayList();
        List<Quote> quoteList = Lists.newArrayList();
        List<IFieldDescribe> formulaList = Lists.newArrayList();
        for (IFieldDescribe fieldDescribe : describeExt.getFieldByApiNames(calculateFieldSet)) {
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (BooleanUtils.isNotTrue(fieldDescribeExt.isActive())) {
                continue;
            }
            if (fieldDescribeExt.isCountField()) {
                countList.add(fieldDescribeExt.getFieldDescribe());
            } else if (fieldDescribeExt.isQuoteField()) {
                quoteList.add(fieldDescribeExt.getFieldDescribe());
            } else if (fieldDescribeExt.isFormula() || fieldDescribeExt.hasCalculateValue()) {
                formulaList.add(fieldDescribeExt.getFieldDescribe());
            }
        }
        return new CalculateField(quoteList, countList, formulaList);
    }

    private void calculateCountFields(IActionContext context, List<IObjectData> objectDataList, IObjectDescribe objectDescribe,
                                      List<Count> countList) {
        if (CollectionUtils.empty(objectDataList) || CollectionUtils.empty(countList)) {
            return;
        }
        if (!context.doCalculate() || !ActionContextExt.of(context).isCalculateCount()) {
            return;
        }
        Boolean oldRecordCalculateLog = RequestUtil.setRecordCalculateLog(false);
        User user = User.systemUser(objectDescribe.getTenantId());
        Map<String, List<Count>> countGroups = countList.stream().collect(Collectors.groupingBy(x -> x.getSubObjectDescribeApiName()));
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), countGroups.keySet());
        // 结合灰度配置和上下文传入的参数，任一为true则从数据库计算
        boolean calculateFromDB = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_COUNT_FIELD_FROM_DB, user.getTenantId())
                || ActionContextExt.of(context).isCalculateCountFromDB();
        countGroups.keySet().stream().filter(x -> describeMap.containsKey(x)).forEach(x -> {
            List<Count> countGroup = countGroups.get(x);
            IObjectDescribe detailDescribe = describeMap.get(x);
            calculateCountFieldsForDescribe(context, objectDataList, objectDescribe, calculateFromDB, detailDescribe, countGroup);
        });
        RequestUtil.setRecordCalculateLog(oldRecordCalculateLog);
    }

    private void calculateCountFieldsForDescribe(IActionContext context, List<IObjectData> objectDataList, IObjectDescribe objectDescribe,
                                                 boolean calculateFromDB, IObjectDescribe detailDescribe, List<Count> countGroup) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.STAT_ON_EMPTY_RESULT_GRAY, objectDescribe.getTenantId())) {
            objectDataList.forEach(data -> {
                if (calculateFromDB) {
                    metaDataComputeService.calculateCountFieldFromDB(data, objectDescribe, detailDescribe, countGroup, context);
                } else {
                    metaDataComputeService.calculateCountField(data, objectDescribe, detailDescribe, countGroup, context);
                }
            });
            return;
        }
        objectDataList.forEach(data -> {
            if (calculateFromDB) {
                metaDataComputeService.calculateCountFieldFromDB(data, objectDescribe, detailDescribe, countGroup);
            } else {
                metaDataComputeService.calculateCountField(data, objectDescribe, detailDescribe, countGroup);
            }
        });
    }

    //引用了动态全局变量或人员类型字段或时间函数（today()、now()）的属性的计算字段需要实时计算
    private void batchCalculateFormulaFields(IActionContext context, List<IObjectData> objectDataList, IObjectDescribe objectDescribe,
                                             List<IFieldDescribe> fieldList) {
        //设置了计算标记的请求实时计算落地的计算字段
        if (!ActionContextExt.of(context).isCalculateFormula()) {
            fieldList = fieldList.stream()
                    .filter(x -> {
                        FieldDescribeExt fieldExt = FieldDescribeExt.of(x);
                        boolean supportFilter = !fieldExt.isFormula() || FormulaExt.of((Formula) x).supportFilter();
                        return !supportFilter || fieldExt.hasCalculateValue();
                    })
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.empty(fieldList) || CollectionUtils.empty(objectDataList)) {
            return;
        }
        Boolean oldRecordCalculateLog = RequestUtil.setRecordCalculateLog(false);
        try {
            expressionCalculateLogicService.bulkCalculate(objectDescribe, objectDataList, fieldList);
        } catch (AppBusinessException e) {
            log.warn("batchCalculateFormulaWithFields failed:{},tenantId:{},objectApiName:{},dataList:{},fieldList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(objectDataList), fieldList);
        } catch (Exception e) {
            log.error("batchCalculateFormulaWithFields error:{},tenantId:{},objectApiName:{},dataList:{},fieldList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(objectDataList), fieldList, e);
        }
        RequestUtil.setRecordCalculateLog(oldRecordCalculateLog);
    }

    private void batchCalculateQuoteFields(IActionContext context, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, List<Quote> fieldList) {
        if (CollectionUtils.empty(objectDataList) || !ActionContextExt.of(context).isCalculateQuote()) {
            return;
        }
        //后台接口只计算落地的引用字段，不落地的由元数据补充
        if (!ActionContextExt.of(context).isConvertQuoteForView()) {
            fieldList = fieldList.stream().filter(x -> x.isIndex()).collect(Collectors.toList());
        }
        if (CollectionUtils.empty(fieldList)) {
            return;
        }
        Boolean oldRecordCalculateLog = RequestUtil.setRecordCalculateLog(false);
        try {
            User user = User.systemUser(objectDescribe.getTenantId());
            quoteValueService.fillQuoteFieldValue(user, objectDataList, objectDescribe, null,
                    true, fieldList, null, !ActionContextExt.of(context).isConvertQuoteForView());
        } catch (AppBusinessException e) {
            log.warn("batchCalculateQuoteFields failed:{},tenantId:{},objectApiName:{},dataList:{},fieldList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(objectDataList), fieldList);
        } catch (Exception e) {
            log.error("batchCalculateQuoteFields error:{},tenantId:{},objectApiName:{},dataList:{},fieldList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(objectDataList), fieldList, e);
        }
        RequestUtil.setRecordCalculateLog(oldRecordCalculateLog);
    }

    private void batchConvertRichTextFieldFromMetadata(List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        try {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            Set<IFieldDescribe> richTextFields = Sets.newHashSet(describeExt.getRichTextFields());
            Set<IFieldDescribe> cooperativeRichTextFields = Sets.newHashSet(describeExt.getCooperativeRichTextFields());
            Set<IFieldDescribe> bigTextFields = Sets.newHashSet(describeExt.getBigTextFields());
            boolean needConvertCooperativeRichText = AppFrameworkConfig.cooperativeRichTextGray(objectDescribe.getTenantId(), objectDescribe.getApiName());
            CollectionUtils.nullToEmpty(objectDataList).forEach(a -> {
                ObjectDataExt.of(a).convertRichTextFromMetadataWithField(richTextFields);
                if (needConvertCooperativeRichText) {
                    ObjectDataExt.of(a).convertCooperativeRichTextFromMetadataWithField(cooperativeRichTextFields);
                }
                ObjectDataExt.of(a).convertBigTextFromMetadataWithField(bigTextFields);
            });
        } catch (AppBusinessException e) {
            log.warn("batchConvertRichTextFieldFromMetadata failed:{},tenantId:{},objectApiName:{},dataList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(objectDataList));
        } catch (Exception e) {
            log.error("batchConvertRichTextFieldFromMetadata error:{},tenantId:{},objectApiName:{},dataList:{}", e.getMessage(),
                    objectDescribe.getTenantId(), objectDescribe.getApiName(), ObjectDataExt.getDataIdName(objectDataList), e);
        }
    }

    private void batchConvertNPathUrl(IActionContext ctx, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        fileStoreService.generateNPathSignedUrl(ctx, AppFrameworkConfig.signedUrlFieldTypes(ctx.getEnterpriseId()), objectDescribe, objectDataList);
    }

    private void mergeFilter(User user, ObjectDescribeExt objectDescribe, SearchTemplateQueryExt queryExt,
                             ISearchTemplate searchTemplate, boolean isAdmin, boolean isIgnoreSceneFilter,
                             boolean isIgnoreSceneRecordType) {
        if (searchTemplate == null) {
            return;
        }
        //会保障数据都过数据权限
        boolean isCascadeSub = isSubCascadeConfig(user);
        if (user.isOutUser()) {
            //为了保持外部联系人处理逻辑不变，合并在外部联系人处理
            OutDataPrivilege outDataPrivilege =
                    dataPrivilegeService.getOutDataPrivilege(user, RequestContextManager.getContext().getAppId(), objectDescribe.getApiName());
            queryExt.handleOutUserDataPrivilege(user, outDataPrivilege, objectDescribe, searchTemplate, isCascadeSub, isIgnoreSceneFilter);
        } else {
            boolean viewPrivilegeCheck = false;
            if (SearchTemplateExt.of(searchTemplate).isAll() && !isAdmin) {
                viewPrivilegeCheck = functionPrivilegeService.funDataViewPrivilegeCheck(user, objectDescribe);
            }
            queryExt.handleDataPrivilege(user, searchTemplate, objectDescribe, isAdmin,
                    isCascadeSub, isIgnoreSceneFilter, viewPrivilegeCheck);
        }
        queryExt.handelRecordType(searchTemplate, objectDescribe, isIgnoreSceneRecordType);
        followLogicService.handleFollowScene(user, objectDescribe.getApiName(), searchTemplate, queryExt);
        handleRecentVisitScene(user, objectDescribe.getApiName(), searchTemplate, queryExt);
        // 只有 cep 请求才过滤，禁用、删除的字段、选项
        if (RequestUtil.isCepRequest()) {
            queryExt.handelFilter(objectDescribe);
        }
    }

    public void handleRecentVisitScene(User user, String apiName, ISearchTemplate searchTemplate, SearchTemplateQueryExt queryExt) {
        if (!SearchTemplateExt.DefaultScene.RECENT_VISIT.getApiName().equals(searchTemplate.getApiName())) {
            return;
        }
        if (!UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.RECENT_VISIT, user.getTenantId(), apiName)) {
            return;
        }
        List<HistoryRecordingInfo> recentViewData = recordingService.queryRecentViewDataByCurrentUser(user, apiName, 100);
        if (CollectionUtils.empty(recentViewData)) {
            queryExt.addFilter(Operator.EQ, IObjectData.ID, Lists.newArrayList("1 = 2"), FilterExt.FilterValueTypes.N_HAS_ANY_OF);
            return;
        }
        List<String> dataIds = recentViewData.stream().map(HistoryRecordingInfo::getObjDataId).collect(Collectors.toList());
        queryExt.addFilter(Operator.IN, IObjectData.ID, dataIds);
    }

    private boolean isAdmin(User user) {
        return user.isSupperAdmin() || userRoleInfoService.isAdmin(user);
    }

    private boolean isSubCascadeConfig(User user) {
        return crmService.getIsSubCascadeConfig(user.getTenantId());
    }

    private void handleMultiLangField(List<IObjectData> dataList, String describeApiName, IActionContext context) {
        if (!AppFrameworkConfig.objectMultiLangGray(context.getEnterpriseId(), describeApiName)) {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (ActionContextExt.of(context).isKeepAllMultiLangValue()) {
            return;
        }

        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(context.getEnterpriseId(), describeApiName);
        handleMultiLangField(dataList, describe);
    }

    private void handleMultiLangField(List<IObjectData> dataList, IObjectDescribe describe, IActionContext context) {
        handleMultiLangField(dataList, describe, "", context);
    }

    private void handleMultiLangField(List<IObjectData> dataList, IObjectDescribe describe, String whatDescribeApiName, IActionContext context) {
        if (!AppFrameworkConfig.objectMultiLangGray(context.getEnterpriseId(), describe.getApiName())) {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (StringUtils.isNotEmpty(whatDescribeApiName)) {
            describe = mergeWhatDescribe(describe, whatDescribeApiName, context.getEnterpriseId());
        }
        handleMultiLangField(dataList, describe);
    }


    private IObjectDescribe mergeWhatDescribe(IObjectDescribe describe, String whatDescribeApiName, String tenantId) {
        if (StringUtils.isEmpty(whatDescribeApiName)) {
            return describe;
        }
        IObjectDescribe whatDescribe = describeLogicService.findObject(tenantId, whatDescribeApiName);
        IObjectDescribe copyDescribe = ObjectDescribeExt.of(describe).copyOnWrite();
        ObjectDescribeExt.of(copyDescribe).mergeWhatObjectFields(whatDescribe);
        return copyDescribe;
    }

    private void handleMultiLangField(List<IObjectData> dataList, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (!describeExt.enableMultiLangField()) {
            return;
        }
        dataList.stream().map(ObjectDataExt::of).forEach(objectDataExt -> objectDataExt.handleMultiLangField(describeExt));
    }

    @Override
    public void renderERVarFromFilterValues(User user, List<IFilter> filters) {
        // 如果用户的互联企业为空直接返回
        String outTenantId = user.getOutTenantId();
        if (StringUtils.isBlank(outTenantId) || CollectionUtils.empty(filters)) {
            return;
        }

        Map<String, String> variables = Maps.newHashMap();
        variables.put(FilterExt.CURRENT_OUT_TENANT_MAPPER_ACCOUNT, "");
        variables.put(FilterExt.CURRENT_OUT_TENANT_MAPPER_PARTNER, "");


        List<IFilter> argFilters = new ArrayList<>();
        argFilters.add(FilterExt.of(Operator.EQ, IObjectData.ID, outTenantId).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(argFilters);

        Query query = Query.builder()
                .searchQuery(searchQuery)
                .limit(1)
                .build();

        QueryContext qc = QueryContext.builder()
                .user(user)
                .isSimple(true)
                .skipRelevantTeam(false)
                .build();

        QueryResult<IObjectData> result = findByQueryWithContext(
                qc,
                Utils.ENTERPRISE_RELATION_OBJ_API_NAME,
                ((SearchTemplateQuery) query.toSearchTemplateQuery()));

        Optional.ofNullable(result.getData())
                .filter(CollectionUtils::notEmpty)
                .map(dataList -> dataList.get(0))
                .ifPresent(erObjData -> {
                    variables.put(FilterExt.CURRENT_OUT_TENANT_MAPPER_ACCOUNT, erObjData.get("mapper_account_id", String.class));
                    variables.put(FilterExt.CURRENT_OUT_TENANT_MAPPER_PARTNER, erObjData.get("mapper_partner_id", String.class));
                });

        filters.forEach(filter -> {
            FilterExt.of(filter).renderVariable(variables);
        });
    }

    @Data
    private static class CalculateField {
        private final List<Quote> quoteList;
        private final List<Count> countList;
        private final List<IFieldDescribe> formulaList;

        public boolean isEmpty() {
            return CollectionUtils.empty(quoteList) && CollectionUtils.empty(countList) && CollectionUtils.empty(formulaList);
        }

        public static boolean needCalculate(CalculateField calculateField) {
            if (Objects.isNull(calculateField)) {
                return false;
            }
            return !calculateField.isEmpty();
        }
    }

}
