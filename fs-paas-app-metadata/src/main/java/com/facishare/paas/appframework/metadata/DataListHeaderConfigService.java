package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.metadata.api.describe.IFieldListConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhouwr on 2017/10/20
 */
public interface DataListHeaderConfigService {
    List<Map<String, Object>> findFieldListConfig(User user, String objectApiName, String extendAttribute);

    List<Tuple<String, Number>> findFieldWidthConfig(User user, String objectApiName, String extendAttribute);

    List<Map<String, Object>> findFieldListConfigWithWhatField(User user, String objectApiName, String whatApiName);

    List<Map<String, Object>> findFieldListConfigWithWhatField(User user, String objectApiName, String whatApiName,
                                                               String sessionKey);

    IFieldListConfig createFieldListConfig(User user, String objectDescribeApiName, List<Map<String, Object>> fieldList, String whatApiName, String extendAttribute, CommonFilterField filterFields, Boolean convertTopListFilter);

    IFieldListConfig createOrUpdateFieldWidthConfig(User user, String objectDescribeApiName, List<Map<String, Object>> fieldWidth,
                                                    String extendAttribute, CommonFilterField filterFields);

    List<String> findFilterFields(User user, String objectApiName, String extendAttribute);

    Boolean convertTopListFilter(User user, String objectApiName, String extendAttribute);

    List<CommonFilterField.FilterField> findFilterFields(User user, IObjectDescribe describe, String extendAttribute, String whatObjectApiName, ILayout layout);

    Map<String, List<Map>> findFieldWidthConfigs(User user, List<String> objectApiNames);

    void deleteByUserObjApiName(User user, String objApiName);

    /**
     * 清空个人级field_width列的配置
     *
     * @param user
     * @param objAPiName
     * @return
     */
    void wipeSettingsByObjApiNameAndUser(Set<String> settingNames, User user, String objAPiName, String extendAttribute);

    void wipePersonalSettings(User user, String objectDescribeApiName, String whatApiName, String extendAttribute);
}
