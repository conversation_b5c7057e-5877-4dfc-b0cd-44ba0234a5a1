package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.WebPageProxyService;
import com.facishare.paas.appframework.common.service.dto.FindObjectPageComponentList;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ref.RefMessage;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Ref;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Refs;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.component.BatchQueryComponentData;
import com.facishare.paas.appframework.metadata.component.CustomComponentProxy;
import com.facishare.paas.appframework.metadata.component.QueryComponentList;
import com.facishare.paas.appframework.metadata.config.LayoutPreNameConfig;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoPojo;
import com.facishare.paas.appframework.metadata.dto.sfa.CheckIsShowRelatedObj;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlignService;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.EditLayout.EditLayoutStatus;
import com.facishare.paas.appframework.metadata.layout.component.*;
import com.facishare.paas.appframework.metadata.layout.factory.*;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.metadata.layout.strcuture.StructureLayout;
import com.facishare.paas.appframework.metadata.objects.DescribeChangeEvent;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.reference.LayoutRef;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverterManager;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.GetRolesByUserId;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.api.describe.WhatList;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.*;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.webpage.customer.api.service.WebPageService;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.i18n.client.exception.I18nException;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.SALE_RECORD;
import static com.facishare.paas.appframework.core.i18n.I18NKey.*;
import static com.facishare.paas.appframework.metadata.FormComponentExt.BASE_FIELD_SECTION_API_NAME;
import static com.facishare.paas.metadata.ui.layout.ILayout.DETAIL_LAYOUT_TYPE;
import static com.facishare.paas.metadata.ui.layout.ILayout.LIST_LAYOUT_TYPE;
import static java.util.stream.Collectors.*;

/**
 * Created by zhouwr on 2017/10/26
 */
@Slf4j
@Service("layoutLogicService")
public class LayoutLogicServiceImpl implements LayoutLogicService {

    @Autowired
    private ILayoutService layoutService;

    @Autowired
    private UserRoleInfoService userRoleInfoService;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    private LogService logService;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private RecordTypeLogicService recordTypeLogicService;

    @Autowired
    private ButtonLogicService buttonLogicService;

    @Autowired
    private CRMRestService crmRestService;

    @Autowired
    private ObjectDataConverterManager objectDataConverterManager;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private LayoutRuleLogicServiceImpl layoutRuleLogicService;

    @Autowired
    private CustomButtonService customButtonService;

    @Autowired
    private SceneLogicService sceneLogicService;

    @Autowired
    private AutoNumberLogicService autoNumberLogicService;

    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Autowired
    private JobScheduleService jobScheduleService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private UIEventLogicService eventLogicService;

    @Autowired
    private CustomComponentProxy customComponentProxy;

    @Autowired
    private WebPageService webPageService;

    @Autowired
    private WebPageProxyService webPageProxyService;

    @Autowired
    private GdprService gdprService;

    @Autowired
    private FunctionLogicService functionLogicService;

    @Autowired
    private I18nSettingService i18nSettingService;

    @Autowired
    private GlobalFieldAlignService fieldAlignService;

    @Autowired
    private DescribeChangeEvent describeChangeEvent;

    @Autowired
    private ListLayoutFactory listLayoutFactory;

    @Autowired
    private ViewComponentFactory viewComponentFactory;

    @Autowired
    private ListComponentFactory listComponentFactory;

    @Autowired
    private LayoutResourceService layoutResourceService;

    @Autowired
    private ReferenceLogicService referenceLogicService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private DomainPluginLogicService domainPluginLogicService;
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;

    @Autowired
    private ManageGroupService manageGroupService;

    @Autowired
    private RefFieldService refFieldService;

    @Autowired
    private LayoutFactoryManager layoutFactoryManager;
    @Autowired
    private IComponentFactoryManager componentFactoryManager;

    @Autowired
    private ApplicationLayeredGrayService applicationLayeredGrayService;

    private Cache<String, Byte> editLayoutStatusCache = CacheBuilder.newBuilder()
            .expireAfterWrite(120, TimeUnit.SECONDS)
            .maximumSize(10000)
            .build();


    @Override
    @Transactional
    public ILayout createLayout(User user, ILayout layout) {
        return createLayout(user, layout, true);
    }

    @Override
    @Transactional
    public ILayout createLayout(LayoutContext context, ILayout layout) {
        return createLayout(context, layout, true);
    }

    @Override
    @Transactional
    public ILayout createLayout(User user, ILayout layout, boolean checkLayout) {
        return createLayout(buildLayoutContext(user), layout, checkLayout);
    }

    @Override
    @Transactional
    public ILayout createLayout(LayoutContext context, ILayout layout, boolean checkLayout) {
        return createLayout(context, layout, checkLayout, true);
    }

    private ILayout createLayout(LayoutContext context, ILayout layout, boolean checkLayout, boolean addManageGroup) {
        User user = context.getUser();
        if (checkLayout) {
            // 校验UI事件数量
            checkUIEventNumber(user, layout);
            checkLayoutCountLimit(context, layout.getRefObjectApiName(), layout.getLayoutType(), LayoutExt.of(layout).getNamespace());
            checkButtonHiddenCountLimit(layout);
            checkLayoutComponents(layout, user);
        }
        String appId = getAppId(context, layout.getRefObjectApiName(), layout.getLayoutType());
        setCreateLayoutDefaultValue(user, layout, appId);
        syncDetailObjButtonInfo2MobileLayout(layout);

        try {
            // 先保存UI事件
            // 新建布局时，将 UIEvent Id 置空，避免在复制的布局在保存时，提示主键冲突
            List<IUIEvent> eventList = getUIEvents(layout, user.getTenantId()).stream()
                    .peek(uiEvent -> uiEvent.setId(null)).collect(toList());
            if (CollectionUtils.notEmpty(eventList)) {
                eventList = eventLogicService.createEvents(eventList, user);
                addEventIds(layout, eventList);
            }

            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());
            ILayout result = createLayoutAddManageGroup(user, layout, addManageGroup);
            addEventWithLayout(result, eventList);
            // 建立UI事件引用关系
            if (CollectionUtils.notEmpty(eventList)) {
                functionLogicService.saveRelation(user, buildReferenceDataList(eventList, objectDescribe));
            }
            //保存流程布局主从布局引用关系
            saveMasterLayoutReference(user, layout);
            logService.log(user, EventType.ADD, ActionType.CreateLayout, objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()));
            return result;
        } catch (MetadataServiceException e) {
            log.warn("createLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private ILayout createLayoutAddManageGroup(User user, ILayout layout, boolean addManageGroup) throws MetadataServiceException {
        ILayout result = layoutService.create(layout);
        if (addManageGroup && !LayoutExt.of(layout).isFlowLayout() && Strings.isNullOrEmpty(layout.getBindingAppId())) {
            manageGroupService.addToManageGroup(user, result.getRefObjectApiName(), result.getName(), ManageGroupType.LAYOUT);
        }
        return result;
    }

    private void deletedLayoutManageGroup(User user, String refObjectApiName, Collection<String> apiNames) {
        manageGroupService.deleteManageGroup(user, refObjectApiName, apiNames, ManageGroupType.LAYOUT);
    }

    @Override
    public void deletedLayoutManageGroupByParentApiName(User user, String parentApiName) {
        ManageGroup manageGroup = queryLayoutManageGroup(user, parentApiName, ObjectListConfig.OBJECT_MANAGEMENT);
        Set<String> apiNames = manageGroup.getApiNames();
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        deletedLayoutManageGroup(user, parentApiName, apiNames);
    }

    private void saveMasterLayoutReference(User user, ILayout layout) {
        if (!LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        List<IComponent> masterDetailComponents = LayoutExt.of(layout).getMasterDetailComponents();
        if (CollectionUtils.empty(masterDetailComponents)) {
            return;
        }
        String sourceType = SourceTypes.MASTER_FLOW_LAYOUT;
        String sourceValue = layout.getRefObjectApiName() + "." + layout.getName();
        List<ReferenceData> referenceDataList = masterDetailComponents.stream()
                .filter(x -> ComponentExt.of(x).isLayoutSourceFromFlow())
                .map(x -> ReferenceData.builder()
                        .sourceType(sourceType)
                        .sourceValue(sourceValue)
                        .sourceLabel(layout.getDisplayName())
                        .targetType(TargetTypes.DESCRIBE_LAYOUT)
                        .targetValue(ComponentExt.of(x).getRefObjectApiName() + "." + ComponentExt.of(x).getFlowLayoutApiName())
                        .build())
                .collect(toList());
        if (CollectionUtils.empty(referenceDataList)) {
            referenceLogicService.deleteReference(user.getTenantId(), sourceType, sourceValue);
        } else {
            referenceLogicService.deleteAndCreateReference(user.getTenantId(), referenceDataList);
        }
    }

    private void saveMasterLayoutReference(User user, ILayout layout, ILayout oldLayout) {
        if (!LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        Map<String, String> detailFlowLayoutMap = LayoutExt.of(layout).getMasterDetailComponents().stream()
                .collect(Collectors.toMap(x -> x.getName(), x -> ComponentExt.of(x).getFlowLayoutApiName(), (x, y) -> x));
        Map<String, String> oldDetailFlowLayoutMap = LayoutExt.of(oldLayout).getMasterDetailComponents().stream()
                .collect(Collectors.toMap(x -> x.getName(), x -> ComponentExt.of(x).getFlowLayoutApiName(), (x, y) -> x));
        if (detailFlowLayoutMap.equals(oldDetailFlowLayoutMap)) {
            return;
        }

        if (CollectionUtils.empty(detailFlowLayoutMap)) {
            deleteMasterLayoutReference(user, layout);
            return;
        }

        saveMasterLayoutReference(user, layout);
    }

    private void deleteMasterLayoutReference(User user, ILayout layout) {
        if (!LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        String sourceValue = layout.getRefObjectApiName() + "." + layout.getName();
        referenceLogicService.deleteReference(user.getTenantId(), SourceTypes.MASTER_FLOW_LAYOUT, sourceValue);
    }

    private List<ReferenceData> buildReferenceDataList(List<IUIEvent> eventList, IObjectDescribe objectDescribe) {
        List<ReferenceData> referenceDataList = Lists.newArrayList();
        eventList.forEach(event -> {
            ReferenceData referenceData = ReferenceData.builder()
                    .sourceType(SourceTypes.UI_EVENT)
                    .sourceLabel(objectDescribe.getDisplayName() + "-" + UIEventType.of(event.getType()).getLabel())// xxx对象-字段事件
                    .sourceValue(event.getId())
                    .targetType(TargetTypes.FUNCTION)
                    .targetValue(event.getFuncApiName())
                    .build();
            referenceDataList.add(referenceData);
        });
        return referenceDataList;
    }

    private List<ReferenceData> buildDeleteReferenceDataList(List<IUIEvent> eventList) {
        List<ReferenceData> referenceDataList = Lists.newArrayList();
        eventList.forEach(event -> {
            ReferenceData referenceData = ReferenceData.builder()
                    .sourceType(SourceTypes.UI_EVENT)
                    .sourceValue(event.getId())
                    .targetValue(event.getFuncApiName())
                    .build();
            referenceDataList.add(referenceData);
        });
        return referenceDataList;
    }

    private void checkButtonHiddenCountLimit(ILayout layout) {
//        if (!LayoutExt.of(layout).isDetailLayout()) {
//            return;
//        }
//        if (!LayoutExt.of(layout).isNewLayout()) {
//            return;
//        }
//        List<IButton> buttonOrder = LayoutExt.of(layout).getButtonOrder();
//        if (CollectionUtils.empty(buttonOrder)) {
//            throw new ValidateException(I18N.text(I18NKey.LEAST_ONE_BUTTON));
//        }
    }

    @Override
    @Transactional
    public ILayout updateLayout(User user, ILayout layout) {
        return updateLayout(user, layout, true, null);
    }

    @Override
    @Transactional
    public ILayout updateLayout(User user, ILayout layout, IObjectDescribe describeDraft) {
        return updateLayout(user, layout, true, describeDraft);
    }

    @Override
    @Transactional
    public ILayout updateLayout(User user, ILayout layout, boolean checkLayout) {
        return updateLayout(user, layout, checkLayout, null);
    }

    @Override
    @Transactional
    public ILayout updateLayout(User user, ILayout layout, boolean checkLayout, IObjectDescribe describeDraft) {
        if (checkLayout) {
            // 校验UI事件数量
            checkUIEventNumber(user, layout);
            checkButtonHiddenCountLimit(layout);
            checkLayoutComponents(layout, user);
        }
        setUpdateLayoutDefaultValue(user, layout);
        syncDetailObjButtonInfo2MobileLayout(layout);

        try {
            // 先更新UI事件(增加、删除、修改)
            List<IUIEvent> events = Lists.newArrayList();
            ILayout layoutDB = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layout.getName(),
                    layout.getRefObjectApiName(), user.getTenantId(), buildActionContext(user));
            if (isDealUIEvent(layout, layoutDB, user.getTenantId())) {
                // 该参数由布局传入，只有布局保存时才能处理UI事件，防止UI事件被误删除
                events = dealUIEventWhenUpdateLayout(user, layout, layoutDB);
            } else {
                // 为了防止layout的eventIds传空误删事件，补充eventId
                if (Objects.nonNull(layoutDB)) {
                    List<String> eventIds = layoutDB.getUiEventIds();
                    layout.setUiEventIds(eventIds);
                }
            }
            IObjectDescribe objectDesc = Optional.ofNullable(describeDraft)
                    .orElseGet(() -> describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName()));
            ILayout result;
            if (Strings.isNullOrEmpty(layout.getId())) {
                setCreateLayoutDefaultValue(user, layout, layout.getBindingAppId());
                result = createLayoutAddManageGroup(user, layout, false);
                //保存流程布局主从布局依赖关系
                saveMasterLayoutReference(user, layout);
            } else {
                result = layoutService.update(layout);
                saveMasterLayoutReference(user, layout, layoutDB);
            }
            addEventWithLayout(result, events);

            executeReferenceByLayout(RefMessage.ActionType.DELETE_AND_CREATE, objectDesc, Lists.newArrayList(result));

            logService.logWithUpdateLayout(user, EventType.MODIFY,
                    ActionType.UpdateLayout,
                    layoutDB,
                    result,
                    objectDesc.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDesc.getDisplayName()),
                    InternationalItem.builder()
                            .internationalKey(I18NKey.LAYOUT_OBJECT)
                            .defaultInternationalValue(I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDesc.getDisplayName()))
                            .internationalParameters(Lists.newArrayList(layout.getDisplayName(), objectDesc.getDisplayName()))
                            .build()
            );
            return result;
        } catch (MetadataServiceException e) {
            log.warn("updateLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private boolean isDealUIEvent(ILayout layout, ILayout layoutDB, String tenantId) {
        Object dealUI = layout.get(LayoutExt.IS_DEAL_UI);
        boolean isDealUI = Objects.nonNull(dealUI) && (boolean) dealUI;
        if (isDealUI) {
            return true;
        }
        boolean isDetailLayoutAndEditLayoutEnable = false;
        if (Objects.nonNull(layoutDB) && CollectionUtils.notEmpty(layoutDB.getUiEventIds()) && CollectionUtils.empty(layout.getUiEventIds())) {
            LayoutContext layoutContext = LayoutContext.of(User.systemUser(tenantId), layoutDB.getBindingAppId());
            isDetailLayoutAndEditLayoutEnable = LayoutExt.of(layout).isDetailLayout() && isEditLayoutEnable(layoutContext, layout.getRefObjectApiName(), false);
        }
        return isDetailLayoutAndEditLayoutEnable;
    }

    @Override
    public void removeFieldInLayout(String tenantId, String describeApiName, String fieldApiName) {
        removeFieldInLayout(buildLayoutContext(User.systemUser(tenantId)), describeApiName, fieldApiName);
    }

    @Override
    public void removeFieldInLayout(LayoutContext context, String describeApiName, String fieldApiName) {
        List<ILayout> layoutList = findLayoutByObjectApiName(context, describeApiName);
        List<ILayout> layoutList2Update = Lists.newArrayList();
        layoutList.forEach(layout -> {
            AtomicBoolean containsField = new AtomicBoolean(false);
            LayoutExt.of(layout).getFormTables().forEach(formTable -> {
                if (formTable.containsField(fieldApiName)) {
                    containsField.compareAndSet(false, true);
                    formTable.removeFields(Sets.newHashSet(fieldApiName));
                }
            });
            LayoutExt.of(layout).getSummaryKeyComponentInfos().forEach(summaryKeyComponentInfo -> {
                if (summaryKeyComponentInfo.containsField(fieldApiName)) {
                    containsField.compareAndSet(false, true);
                    summaryKeyComponentInfo.removeFields(Sets.newHashSet(fieldApiName));
                }
            });
            if (containsField.get()) {
                layoutList2Update.add(layout);
            }
        });
        layoutList2Update.forEach(layout -> {
            try {
                layoutService.update(layout);
            } catch (MetadataServiceException e) {
                log.warn("update layout failed,tenantId:{},describeApiName:{},layoutApiName:{}", context.getTenantId(),
                        describeApiName, layout.getName(), e);
            }
        });
    }

    //同步web端布局的从对象组件的批量和单条按钮到移动端布局的从对象组件上。
    //如果web端布局隐藏了该组件或者web端的该组件没有配置批量或单条按钮，则使用默认按钮，否则使用web端配置的按钮。
    private void syncDetailObjButtonInfo2MobileLayout(ILayout layout) {
        if (!LayoutExt.of(layout).isEditLayout()) {
            return;
        }
        if (!LayoutExt.of(layout).isEnableMobileLayout()) {
            return;
        }
        LayoutExt.of(layout.getMobileLayout()).getMasterDetailComponents().forEach(component -> {
            List<ListComponentInfo> buttonInfos = ComponentExt.of(component).getButtonInfo();
            Optional<IComponent> componentInWebLayoutOpt = LayoutExt.of(layout).getComponentByApiName(component.getName());
            Optional<ListComponentInfo> batchButtonInfoInWebLayout;
            Optional<ListComponentInfo> singleButtonInfoInWebLayout;
            if (componentInWebLayoutOpt.isPresent()) {
                IComponent componentInWebLayout = componentInWebLayoutOpt.get();
                List<ListComponentInfo> buttonInfosInWebLayout = ComponentExt.of(componentInWebLayout).getButtonInfo();
                batchButtonInfoInWebLayout = buttonInfosInWebLayout.stream().filter(y -> y.renderInListBatch()).findFirst();
                singleButtonInfoInWebLayout = buttonInfosInWebLayout.stream().filter(y -> y.renderInListSingle()).findFirst();
            } else {
                batchButtonInfoInWebLayout = Optional.empty();
                singleButtonInfoInWebLayout = Optional.empty();
            }
            String detailObjApiName = (String) component.get(IMultiTableComponent.REF_OBJECT_API_NAME);
            //同步批量按钮
            syncButtonInfo(ListComponentInfo.ButtonRenderType.LIST_BATCH, detailObjApiName, buttonInfos,
                    batchButtonInfoInWebLayout, layout.getTenantId());
            //同步单条按钮
            syncButtonInfo(ListComponentInfo.ButtonRenderType.LIST_SINGLE, detailObjApiName, buttonInfos,
                    singleButtonInfoInWebLayout, layout.getTenantId());
            ComponentExt.of(component).setButtonInfo(buttonInfos);
        });
    }

    private void syncButtonInfo(ListComponentInfo.ButtonRenderType renderType, String detailObjApiName, List<ListComponentInfo> buttonInfos,
                                Optional<ListComponentInfo> buttonInfoInWebLayout, String tenantId) {
        ListComponentInfo newButtonInfo;
        if (buttonInfoInWebLayout.isPresent()) {
            newButtonInfo = buttonInfoInWebLayout.get();
        } else {
            newButtonInfo = (ListComponentInfo) ListComponentInfo.buildComponentInfo(renderType.getType(), null);
            if (renderType == ListComponentInfo.ButtonRenderType.LIST_BATCH) {
                newButtonInfo.setOrder(EditLayout.buildDefaultListBatchDetailObjButtonNames(detailObjApiName));
            } else if (renderType == ListComponentInfo.ButtonRenderType.LIST_SINGLE) {
                newButtonInfo.setOrder(EditLayout.buildDefaultListSingleDetailObjButtonNames(tenantId, detailObjApiName));
            }
        }
        buttonInfos.removeIf(x -> renderType.getType().equals(x.getRenderType()));
        buttonInfos.add(newButtonInfo);
    }

    private void checkLayoutComponents(ILayout layout, User user) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        //如果是移动端摘要字段布局 即list，校验字段可用性后结束
        if (Objects.equals(ILayout.LIST_LAYOUT_TYPE, layoutExt.getLayoutType())) {

            /* 布局里的列表不再校验字段可用
            【【仲裁——基础业务(郑磊)vs深研制造(李秋林)】进销存无法开启采购模块】https://www.tapd.cn/20019471/bugtrace/bugs/view?bug_id=1120019471001255920
            IObjectDescribe oDescribeExt = ObjectDescribeExt.of(describeLogicService.findObject(user.getTenantId(), layoutExt.getRefObjectApiName()));
            TableComponentExt tableComponentExt = TableComponentExt.of(layoutExt.getTableComponent().get());
            checkAvailableFields(tableComponentExt, oDescribeExt);
             */
            return;
        }
        if (!layoutExt.isNewLayout()) {
            return;
        }
        // form_component组件不能缺失
        if (!layoutExt.getFormComponent().isPresent()) {
            log.warn("layout invalid:no form_component,tenantId:{},objectApiName:{},layout:{}",
                    layout.getTenantId(), layout.getRefObjectApiName(), layout.toJsonString());
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        //校验页签组件的tabs和components
        layoutExt.getTabsComponents().forEach(x -> {
            List<List<String>> components = x.getComponents();
            List<TabSection> tabs = x.getTabs();
            if (CollectionUtils.empty(components) && CollectionUtils.empty(tabs)) {
                return;
            }
            if (components.size() != tabs.size()) {
                log.warn("layout invalid:components.size() != tabs.size(),tenantId:{},objectApiName:{},layout:{}",
                        layout.getTenantId(), layout.getRefObjectApiName(), layout.toJsonString());
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            if (components.stream().anyMatch(c -> Objects.isNull(c))) {
                log.warn("layout invalid:components contains null,tenantId:{},objectApiName:{},layout:{}",
                        layout.getTenantId(), layout.getRefObjectApiName(), layout.toJsonString());
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            if (tabs.stream().anyMatch(t -> Objects.isNull(t) || Strings.isNullOrEmpty(t.getApiName()))) {
                log.warn("layout invalid:tabs contains null or empty api_name,tenantId:{},objectApiName:{},layout:{}",
                        layout.getTenantId(), layout.getRefObjectApiName(), layout.toJsonString());
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
        });
        // 新建编辑页布局,校验相关对象组件
        checkEditLayoutComponent(user, layoutExt);
        //校验流程布局的合法性
        checkFlowLayoutComponents(layoutExt);
        // 判断 formComponent 中是否有重复的字段
//        checkDuplicatedFields(layoutExt);
        layoutExt.clearDuplicatedFormField();
    }

    private void checkFlowLayoutComponents(LayoutExt layoutExt) {
        if (!layoutExt.isFlowLayout()) {
            return;
        }
        layoutExt.getMasterDetailComponents().forEach(x -> {
            if (ComponentExt.of(x).isLayoutSourceFromFlow() && Strings.isNullOrEmpty(ComponentExt.of(x).getFlowLayoutApiName())) {
                throw new ValidateException(I18NExt.getOrDefault(FLOW_LAYOUT_API_NAME_CAN_NOT_BE_EMPTY,
                        "从对象【{0}】的流程布局不能为空", x.getHeader()));// ignoreI18n
            }
        });
    }

    private void checkEditLayoutComponent(User user, LayoutExt layoutExt) {
        if (!layoutExt.isEditLayout()) {
            return;
        }
        // 校验web布局
        checkEditLayoutFormComponent(user, layoutExt);
        // 校验移动端布局
        if (layoutExt.isEnableMobileLayout()) {
            String objectApiName = layoutExt.getRefObjectApiName();
            LayoutExt mobileLayout = LayoutExt.of(Maps.newHashMap(layoutExt.getMobileLayout()));
            mobileLayout.setRefObjectApiName(objectApiName);
            mobileLayout.setDisplayName(layoutExt.getDisplayName());
            checkEditLayoutFormComponent(user, mobileLayout);
        }
    }

    private void checkEditLayoutFormComponent(User user, LayoutExt layoutExt) {
        List<RelatedListFormComponentExt> relatedListFormComponents = layoutExt.getRelatedListFormComponents();
        if (CollectionUtils.empty(relatedListFormComponents)) {
            return;
        }
        // 校验组件个数
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.MASTER_RELATED.getBizCode()));
        tenantLicenseInfo.validateRelatedListFormComponentCount(relatedListFormComponents.size(), layoutExt.getRefObjectApiName());
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layoutExt.getRefObjectApiName());
        // 当前对象不能是从对象
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            log.warn("layout invalid: components unSupport, ei:{},objectApiName:{},layoutName:{}", user.getTenantId(), objectDescribe.getTenantId(), layoutExt.getName());
            throw new ValidateException(I18NExt.text(I18NKey.MASTER_DETAIL_NOT_SUPPORT_RELATED_LIST_FORM_COMPONENT));
        }
        Set<String> refObjectApiNames = relatedListFormComponents.stream()
                .map(RelatedListFormComponentExt::getRefObjectApiName)
                .collect(Collectors.toSet());
        Map<String, IObjectDescribe> refDescribeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), refObjectApiNames);
        // 校验组件配置的对象
        Set<String> uniqueSet = Sets.newHashSet();
        List<String> toDeleted = Lists.newArrayList();
        relatedListFormComponents.forEach(component -> {
            String refObjectApiName = component.getRefObjectApiName();
            IObjectDescribe describe = refDescribeMap.get(refObjectApiName);
            if (Objects.isNull(describe)) {
                log.warn("checkEditLayoutFormComponent,refObjectApiName:{} not exit! ei:{}, describeApiName:{}, layoutName:{}, componentName:{}",
                        refObjectApiName, user.getTenantId(), objectDescribe.getApiName(), layoutExt.getName(), component.getName());
                toDeleted.add(component.getName());
                return;
            }
            String relatedFieldName = component.getFieldApiName();
            if (!describeLogicService.isRelatedListFormSupportObject(user, objectDescribe.getApiName(), describe, relatedFieldName)) {
                log.warn("layout invalid: components unSupport, ei:{},objectApiName:{},layoutName:{},componentName:{},refApiName:{},layoutApiName:{}",
                        user.getTenantId(), layoutExt.getRefObjectApiName(), layoutExt.getName(), component.getName(), refObjectApiName, relatedFieldName);
                throw new ValidateException(I18NExt.text(I18NKey.RELATED_LIST_FORM_COMPONENT_SETTINGS_ERROR, layoutExt.getDisplayName(), component.getComponentName()));
            }
            // 组件不能重复
            String str = String.format("%s_%s", refObjectApiName, relatedFieldName);
            if (!uniqueSet.add(str)) {
                throw new ValidateException(I18NExt.text(I18NKey.RELATED_LIST_FORM_COMPONENT_SETTINGS_ERROR, layoutExt.getDisplayName(), component.getComponentName()));
            }
        });
        layoutExt.removeComponents(toDeleted);
    }

    private void checkDuplicatedFields(LayoutExt layoutExt) {
        List<String> duplicatedFields = Lists.newArrayList();
        Set<String> fieldSet = Sets.newHashSet();
        layoutExt.getFieldList().forEach(it -> {
            if (fieldSet.contains(it)) {
                duplicatedFields.add(it);
            } else {
                fieldSet.add(it);
            }
        });
        if (CollectionUtils.notEmpty(duplicatedFields)) {
            log.warn("layout formComponent field duplicated, tenantId:{}, objectApiName:{}, layoutName:{}, duplicatedFields:{}",
                    layoutExt.getTenantId(), layoutExt.getRefObjectApiName(), layoutExt.getName(), duplicatedFields);
            throw new ValidateException(I18N.text(I18NKey.DUPLICATE_LAYOUT_FIELD, layoutExt.getDisplayName(),
                    String.join(", ", duplicatedFields)));
        }
    }

    @Override
    @Transactional
    public ILayout deleteLayout(User user, String layoutId) {
        ILayout layout = findLayoutById(layoutId, user.getTenantId());
        deleteLayout(user, layout);
        return layout;
    }

    private void validateFormFieldReferenceByDelete(User user, ILayout layout) {
        if (layout == null || !LayoutExt.of(layout).isEditLayout()) {
            return;
        }
        String layoutApiName = layout.getName();
        String objectApiName = layout.getRefObjectApiName();
        if (StringUtils.isEmpty(layoutApiName) || StringUtils.isEmpty(objectApiName)) {
            return;
        }
        List<ILayout> detailLayouts = findByType(user.getTenantId(), objectApiName, LayoutTypes.DETAIL);
        List<String> detailLayoutDisplayNames = Lists.newArrayList();
        detailLayouts.forEach(detailLayout -> {
            Optional<FormComponentExt> formComponent = LayoutExt.of(detailLayout).getFormComponent();
            if (!formComponent.isPresent()) {
                return;
            }
            FormComponentExt formComponentExt = formComponent.get();
            if (StringUtils.equals(formComponentExt.getReferenceFieldConfig(), layoutApiName)) {
                detailLayoutDisplayNames.add(detailLayout.getDisplayName());
            }
        });
        if (CollectionUtils.empty(detailLayoutDisplayNames)) {
            return;
        }
        String delimiter = StringUtils.equalsAny(I18N.getContext().getLanguage(), I18N.ZH_CN, I18N.ZH_TW) ? "，" : ",";
        String layoutsDisplayName = String.join(delimiter, detailLayoutDisplayNames);
        throw new ValidateException(I18NExt.text(LAYOUT_REFERENCED_CANNOT_BE_DISABLED, layoutsDisplayName));
    }

    private void validateFlowLayoutReferenceByDelete(User user, ILayout layout) {
        if (layout == null || !LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        List<ReferenceData> referenceDataList = referenceLogicService.findReferenceByTarget(user.getTenantId(),
                TargetTypes.DESCRIBE_LAYOUT, layout.getRefObjectApiName() + "." + layout.getName());
        if (CollectionUtils.empty(referenceDataList)) {
            return;
        }
        List<String> usedInfoList = referenceDataList.stream()
                .map(referenceData -> "[" + I18NExt.text(referenceData.getSourceType()) + "]" + referenceData.getSourceLabel())
                .collect(Collectors.toList());
        String usedInfo = StringUtils.join(usedInfoList, "|");
        throw new ValidateException(I18NExt.text(LAYOUT_REFERENCED_CANNOT_BE_DELETED, usedInfo));
    }

    @Override
    @Transactional
    public void deleteLayout(User user, ILayout layout) {
        if (layout == null || layout.isDeleted()) {
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.ERRORCODE_LAYOUT_NOT_EXIST));
        }
        if (BooleanUtils.isTrue(layout.isDefault())) {
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.DEFAULT_LAYOUT_CANNOT_BE_DELETED));
        }
        //删除新建编辑页布局时，校验是否有详情页布局引用了该布局的字段，如果有引用，则不允许删除
        validateFormFieldReferenceByDelete(user, layout);
        //删除流程布局时，校验是否有流程节点或别的布局引用了该布局，如果有，则不允许删除
        validateFlowLayoutReferenceByDelete(user, layout);
        setUpdateLayoutDefaultValue(user, layout);
        try {
            //删除布局规则
            layoutRuleLogicService.deleteLayoutRuleByLayout(user, layout.getRefObjectApiName(), layout.getName());

            //删除UI事件
            List<String> ids = layout.getUiEventIds();
            dealLayoutRepeatUiEventId(ids, user, layout);
            if (CollectionUtils.notEmpty(ids)) {
                List<IUIEvent> uiEventDelete = eventLogicService.findEventListByIds(ids, user.getTenantId());
                eventLogicService.batchDelete(ids, user);
                functionLogicService.batchDeleteRelation(user, buildDeleteReferenceDataList(uiEventDelete));
            }

            //删除布局
            layoutService.delete(layout);

            //删除角色布局关联关系
            deleteRoleViewByLayout(user, layout);
            // 移除分管小组
            deletedLayoutManageGroup(user, layout.getRefObjectApiName(), Lists.newArrayList(layout.getName()));

            //删除流程布局引用关系
            deleteMasterLayoutReference(user, layout);
            IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), layout.getRefObjectApiName());
            executeReferenceByLayout(RefMessage.ActionType.DELETE, describe, Lists.newArrayList(layout));
        } catch (MetadataServiceException e) {
            log.warn("deleteLayout error,user:{},describeApiName:{},layoutName:{}", user, layout.getRefObjectApiName(), layout.getName(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private List<String> dealLayoutRepeatUiEventId(List<String> eventIds, User user, ILayout layout) {
        if (CollectionUtils.empty(eventIds)) {
            return Lists.newArrayList();
        }
        //查询ui事件重复的布局
        List<Layout> repeatUIEventLayouts = layoutService.findLayoutByUIEventIds(user.getTenantId(), layout.getRefObjectApiName(), eventIds);
        //删除掉本布局以后，剩余的就是可能与本布局 有重复ui事件的布局
        repeatUIEventLayouts.removeIf(x -> layout.getName().equals(x.getName()));
        List<String> repeatUIEventIds = new ArrayList<>();
        for (Layout repeatLayout : repeatUIEventLayouts) {
            //如果该布局中的ui事件有与eventIds中的id重复的，则代表该ui事件不能被删除，需特殊处理
            List<String> uiEventIds = repeatLayout.getUiEventIds();
            //取交集
            Collection<String> intersection = org.apache.commons.collections4.CollectionUtils.intersection(eventIds, uiEventIds);
            repeatUIEventIds.addAll(intersection);
        }
        eventIds.removeAll(repeatUIEventIds);
        return repeatUIEventIds;
    }


    private void deleteRoleViewByLayout(User user, ILayout layout) {
        recordTypeLogicService.deleteRoleView(user, layout.getRefObjectApiName(), layout.getName(), true);
    }

    @Override
    @Transactional
    public LayoutResult createLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe) {
        return createLayoutAndUpdateDescribe(user, layout, objectDescribe, null);
    }

    @Override
    @Transactional
    public LayoutResult createLayoutAndUpdateDescribe(LayoutContext context, ILayout layout, IObjectDescribe objectDescribe) {
        return createLayoutAndUpdateDescribe(context, layout, objectDescribe, null);
    }

    @Override
    @Transactional
    public LayoutResult createLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe, String persistentDataCalc) {
        return createLayoutAndUpdateDescribe(buildLayoutContext(user), layout, objectDescribe, persistentDataCalc);
    }

    @Override
    @Transactional
    public LayoutResult createLayoutAndUpdateDescribe(LayoutContext context, ILayout layout, IObjectDescribe objectDescribe, String persistentDataCalc) {
        User user = context.getUser();
        String appId = getAppId(context, objectDescribe.getApiName(), layout.getLayoutType());
        //校验字段数量
        describeLogicService.checkCustomFieldCountLimit(user, objectDescribe, null);
        setCreateLayoutDefaultValue(user, layout, appId);
        ObjectDescribeExt.of(objectDescribe).setUpdateDescribeDefaultValue(user);

        //校验layout中单选字段存在级联关系时是否隐藏的父级
        try {
            ObjectDescribeExt.of(objectDescribe).checkSelectOneFiledWithLayout(layout);
        } catch (MetaDataBusinessException e) {
            log.warn("updateLayoutAndUpdateDescribe : {}", e.getMessage());
            throw new MetaDataBusinessException(e.getMessage());
        }

        try {
            IObjectDescribe describeInDb = describeLogicService.findObject(user.getTenantId(), objectDescribe.getApiName());
            // 检查MD字段数量限制，更新时不用校验
            if (!ObjectDescribeExt.of(describeInDb).isSlaveObject() && ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
                describeLogicService.checkDescribeMasterDetailLimit(user.getTenantId(), objectDescribe);
                //如果存在审批流定义，则不允许创建主从字段
                if (ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMaster()) {
                    describeLogicService.checkIfHasApprovalFlowDefinition(user, objectDescribe.getApiName());
                }
                describeLogicService.checkMasterDetailDescribeByChangeOrder(user, objectDescribe, false);
            }

            //字段校验
            List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByObjectDescribe(objectDescribe, describeInDb);
            List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteFieldsByObjectDescribe(objectDescribe, describeInDb);
            fieldRelationCalculateService.checkObjectReferenceFieldsByObjectDescribe(user, objectDescribe, describeInDb);

            ObjectDescribeExt.of(objectDescribe).handleNumberStepValue();
            // 校验自增编码字段是否合法
            autoNumberLogicService.autoNumberValidateByObjectDescribe(objectDescribe, describeInDb, user);
            //定位字段校验
            ObjectDescribeExt.of(objectDescribe).validateGeoLocationField(null);
            //富文本字段校验
            ObjectDescribeExt.of(objectDescribe).validateRichTextFieldInDescribe(null);
            //字段唯一性校验
            ObjectDescribeExt.of(describeInDb).checkFieldUnique(objectDescribe.getFieldDescribes());
            //label特殊字符去除
            ObjectDescribeExt.of(objectDescribe).formatLabel(objectDescribe.getFieldDescribes());
            //校验target_related_list_name重复
            ObjectDescribeExt.of(objectDescribe).validateObjectReferenceField(null);
            ObjectDescribeExt.of(objectDescribe).validateGroupField(null, user.getTenantId());
            ILayout savedLayout = createLayout(context, layout);
            // 负责人字段修改为非必输需要同步更新从对象的负责人字段为非必输
            describeLogicService.syncMasterAndDetailOwnerField(user, objectDescribe, describeInDb);
            // 校验公共对象
            describeLogicService.checkPublicObjectBeforeUpdateDescribe(user, objectDescribe, describeInDb);
            // 获取不需要添加到相关列表的查找关联字段
            List<ObjectReferenceWrapper> referenceFieldDescribes = ObjectDescribeExt.of(objectDescribe).getNotAddToLayoutReferenceFieldDescribes(describeInDb);
            // 从对象开起变更单
            if (!ObjectDescribeExt.of(describeInDb).isSlaveObject() && ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMaster()) {
                describeLogicService.openDetailObjChangeOrder(user, objectDescribe);
            }

            // 保存描述
            IObjectDescribe updatedDescribe = updateAndSeedChangeMsg(user, describeInDb, objectDescribe);
            logService.log(user, EventType.ADD, ActionType.CreateLayout, objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()));

            //同步翻译
            syncI18nPlatform(user, objectDescribe, layout);

            // 新增或变更了自增编码，需要切换重新计算规则
            autoNumberLogicService.diffAutoNumberField(objectDescribe, describeInDb);

            fieldRelationCalculateService.checkDecimalDigitChangeByDescribe(objectDescribe, describeInDb);
            //提交计算任务,重新计算历史数据
            List<String> fieldNameList = Lists.newArrayList();
            List<String> calculateFieldApiNames = calculateFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            List<String> quoteFieldApiNames = quoteFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            fieldNameList.addAll(calculateFieldApiNames);
            fieldNameList.addAll(quoteFieldApiNames);

            if (TenantUtil.isCalcCriteria(user.getTenantId())) {
                List<IFieldDescribe> calcFields = Lists.newArrayList(calculateFields);
                calcFields.addAll(quoteFields);
                jobScheduleService.submitCalculateJob(user, objectDescribe.getApiName(), calcFields, persistentDataCalc, false);
            } else {
                jobScheduleService.submitCalculateJob(user, fieldNameList, objectDescribe.getApiName());
            }

            fieldRelationCalculateService.checkSelectOneChangeOfDescribe(objectDescribe, describeInDb);
            describeLogicService.batchUpsertRelatedListAddToLayoutConfig(user, referenceFieldDescribes);
            executeReferenceByLayout(RefMessage.ActionType.DELETE_AND_CREATE, objectDescribe, Lists.newArrayList(layout));
            return LayoutResult.builder().layout(savedLayout).objectDescribe(updatedDescribe).build();
        } catch (MetadataServiceException e) {
            log.warn("createLayoutAndUpdateDescribe error,user:{},layout:{},objectDescribe:{}",
                    user, layout.toJsonString(), objectDescribe.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private IObjectDescribe updateAndSeedChangeMsg(User user, IObjectDescribe describeInDb, IObjectDescribe objectDescribe) {
        if (ObjectDescribeExt.of(describeInDb).isDownstreamTenantWithPublicObject()) {
            log.warn("isDownstreamTenantWithPublicObject, tenantId:{}, getUpstreamTenantId:{}, describeApiName:{}",
                    user.getTenantId(), describeInDb.getUpstreamTenantId(), describeInDb.getApiName());
            return objectDescribe;
        }
        IObjectDescribe result = describeLogicService.update(objectDescribe);
        describeChangeEvent.sendDescribeMessage(user, describeInDb, objectDescribe);
        return result;
    }


    private void syncI18nPlatform(User user, IObjectDescribe objectDescribe, ILayout layout) {
        try {
            if (licenseService.isSupportMultiLanguage(user.getTenantId())) {
                List<Localization> localizations = Lists.newArrayList();
                List<I18nTrans.TransArg> transArgs = Lists.newArrayList();
                if (LayoutTypes.LIST_LAYOUT.equals(layout.getLayoutType())) {
                    dealListLayoutMultiLanguage(user, objectDescribe, layout, localizations, transArgs);
                } else {
                    dealExpectListLayoutMultiLanguage(user, layout, localizations, transArgs);
                }
                I18nClient.getInstance().save4Translate(user.getTenantIdInt(), localizations, false);
                i18nSettingService.syncTransValueIncludePreKey(user.getTenantId(), transArgs, RequestUtil.getCurrentLang().getValue());
            }
        } catch (I18nException e) {
            log.warn("syncI18nPlatform error,user:{},layout:{},objectDescribe:{}",
                    user, layout.toJsonString(), objectDescribe.toJsonString(), e);
        }
    }

    private void dealListLayoutMultiLanguage(User user, IObjectDescribe objectDescribe, ILayout layout, List<Localization> localizations,
                                             List<I18nTrans.TransArg> transArgs) {
        ListLayoutExt layoutExt = ListLayoutExt.of(layout);
        List<IComponent> components = Lists.newArrayList();
        //如果没有开启顶导航，那么所有的组件存在components中
        //开启定导航，组件存在layoutStructure的components中
        if (!layoutExt.isEnableMultiPage()) {
            components.addAll(layoutExt.toLayoutExt().getComponentsSilently());
        } else {
            List<StructureLayout> layoutList = layoutExt.getLayoutStructureExt().getLayoutList();
            for (StructureLayout structureLayout : layoutList) {
                //获取顶导航的key并判断是否需要同步
                String key = ComponentExt.getTopNavigationKey(layoutExt.getRefObjectApiName(), layoutExt.getName(), structureLayout.getLayoutIndex());
                transArgs.add(I18nTrans.TransArg.builder().name(structureLayout.getLabelPageName()).customKey(key).build());
                //每个顶导航页面下的组件全部取出来
                components.addAll(structureLayout.getComponents());
            }
        }
        Map<String, String> componentPreKeys = findComponentPreKeys(components);
        //处理列表页布局中的组件同步逻辑
        for (IComponent component : components) {
            ComponentExt componentExt = ComponentExt.of(component);
            if (CollectionUtils.notEmpty(ComponentExt.of(component).getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName()))) {
                List<I18nInfo> i18nInfoList = ComponentExt.of(component).getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName());
                List<Localization> localizationList = i18nInfoList.stream()
                        .map(x -> x.toLocalization(layout.getTenantId()))
                        .filter(Objects::isNull).collect(toList());
                localizations.addAll(localizationList);
            }
            if (componentExt.isTabs()) {
                TabsComponent tabsComponent = (TabsComponent) component;
                List<TabSection> tabs = tabsComponent.getTabs();
                tabs.forEach(x -> transArgs.add(I18nTrans.TransArg.builder().name(x.getHeader()).customKey(ComponentExt.getListLayoutTabNameKey(layout.getRefObjectApiName(), layout.getName(), x.getApiName()))
                        .preKeyList(Lists.newArrayList(TabSectionExt.of(x).getNameI18nKey())).build()));
            }

            if (!AppFrameworkConfig.isListLayoutSupportTransComponent(user.getTenantId(), componentExt.getType())) {
                continue;
            }
            String componentKey = ComponentExt.getListComponentKey(layout.getRefObjectApiName(), layout.getName(), component.getName());
            List<String> nameI18nKeyList = CollectionUtils.nullToEmpty(componentExt.getNameI18nKeyList());
            if (CollectionUtils.empty(nameI18nKeyList)) {
                nameI18nKeyList.addAll(componentExt.getBiComponentNameI18nKeyList());
            }
            if (StringUtils.isNotBlank(componentExt.getNameI18nKey())) {
                nameI18nKeyList.add(componentExt.getNameI18nKey());
            } else if (StringUtils.isNotEmpty(componentPreKeys.get(component.getName()))) {
                nameI18nKeyList.add(componentPreKeys.get(component.getName()));
            }
            transArgs.add(I18nTrans.TransArg.builder().name(componentExt.getHeader()).customKey(componentKey).preKeyList(nameI18nKeyList).build());
        }
    }

    private void dealExpectListLayoutMultiLanguage(User user, ILayout layout, List<Localization> localizations,
                                                   List<I18nTrans.TransArg> transArgs) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<IComponent> components = layoutExt.getComponentsSilently();

        if (layoutExt.isEnableMobileLayout()) {
            Set<String> componentSets = components.stream().map(IComponent::getName).collect(toSet());
            LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
            List<IComponent> mobileComponents = mobileLayout.getComponentsSilently();
            components.addAll(mobileLayout.getSuspendedComponent());
            components.addAll(mobileComponents.stream().filter(x -> !componentSets.contains(x.getName()) && AppFrameworkConfig.isMobileSpecialComponents(x.getType())).collect(toList()));
        }
        Map<String, String> componentPreKeys = findComponentPreKeys(components);
        boolean fieldGroupEarthTransFlag = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_GROUP_EARTH_TRANS, user.getTenantId());
        for (IComponent component : components) {
            if (ComponentExt.FORM_COMPONENT.equals(component.getName())) {
                FormComponent formComponent = (FormComponent) component;
                //获取详细信息中的分组
                List<IFieldSection> fieldSections = formComponent.getFieldSections();

                List<Localization> fieldSectionsLocalizations = Lists.newArrayList();
                for (IFieldSection fieldSection : fieldSections) {
                    String sectionApiName = fieldSection.getName();
                    if (fieldGroupEarthTransFlag) {
                        String key = "";
                        if (layoutExt.isDetailLayout()) {
                            key = ComponentExt.getDetailLayoutGroupName(layout.getRefObjectApiName(), layout.getName(), sectionApiName);
                        } else if (layoutExt.isEditLayout()) {
                            key = ComponentExt.getEditLayoutGroupName(layout.getRefObjectApiName(), layout.getName(), sectionApiName);
                        }
                        List<I18nInfo> i18nInfoList = ComponentExt.of(component).getI18nInfoListBySectionApiName(sectionApiName, key);
                        List<Localization> localizationList = i18nInfoList.stream()
                                .map(x -> x.toLocalization(layout.getTenantId()))
                                .filter(Objects::nonNull)
                                .collect(toList());
                        fieldSectionsLocalizations.addAll(localizationList);
                    } else {
                        String key = "";
                        if (layoutExt.isDetailLayout()) {
                            key = ComponentExt.getDetailLayoutGroupName(layout.getRefObjectApiName(), layout.getName(), sectionApiName);
                        } else if (layoutExt.isEditLayout()) {
                            key = ComponentExt.getEditLayoutGroupName(layout.getRefObjectApiName(), layout.getName(), sectionApiName);
                        }
                        //处理分组名称为空的情况（兼容bug）
                        if (!Strings.isNullOrEmpty(fieldSection.getHeader())) {
                            dealPresetGroupName(fieldSection.getHeader(), sectionApiName, key, transArgs);
                        }
                    }

                }
                localizations.addAll(fieldSectionsLocalizations);


            }

            if (ComponentExt.of(component).isTabs()) {
                TabsComponent tabsComponent = (TabsComponent) component;
                List<TabSection> tabs = tabsComponent.getTabs();
                for (ITabSection tabSection : tabs) {
                    TabSectionExt sectionExt = TabSectionExt.of(tabSection);
                    //如果nameI18nKey存在，则代表没有更新layout 的header值
                    if (!Strings.isNullOrEmpty(sectionExt.getNameI18nKey())) {
                        continue;
                    }
                    String tabApiName = sectionExt.getApiName();
                    String key = ComponentExt.getDetailLayoutTabNameKey(layout.getRefObjectApiName(), layout.getName(), tabApiName);
                    //比较描述的header值和i18nkey值，如果不相等则需要同步
                    transArgs.add(I18nTrans.TransArg.builder().name(sectionExt.getHeader()).customKey(key).preKeyList(Lists.newArrayList(sectionExt.getNameI18nKey())).build());
                }
            }
            if (CollectionUtils.notEmpty(ComponentExt.of(component).getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName()))) {
                List<I18nInfo> i18nInfoList = ComponentExt.of(component).getI18nInfoList(layoutExt.getRefObjectApiName(), layoutExt.getName());
                List<Localization> localizationList = i18nInfoList.stream()
                        .map(x -> x.toLocalization(layout.getTenantId()))
                        .filter(Objects::nonNull)
                        .collect(toList());
                localizations.addAll(localizationList);
            }
            String key = "";
            if (LayoutTypes.EDIT.equals(layout.getLayoutType())) {
                key = ComponentExt.getEditLayoutComponentNameKey(layout.getRefObjectApiName(), layout.getName(), component.getName());
            } else {
                key = ComponentExt.getDetailComponentNameKey(layout.getRefObjectApiName(), layout.getName(), component.getName());
            }

            ComponentExt componentExt = ComponentExt.of(component);
            List<String> nameI18nKeyList = CollectionUtils.nullToEmpty(componentExt.getNameI18nKeyList());
            if (CollectionUtils.empty(nameI18nKeyList) && componentExt.isBiComponent()) {
                nameI18nKeyList.addAll(componentExt.getBiComponentNameI18nKeyList());
            }
            if (StringUtils.isNotBlank(componentExt.getNameI18nKey())) {
                nameI18nKeyList.add(componentExt.getNameI18nKey());
            } else if (StringUtils.isNotEmpty(componentPreKeys.get(component.getName()))) {
                nameI18nKeyList.add(componentPreKeys.get(component.getName()));
            }
            if (componentExt.isTextComponent()) {
                transArgs.add(I18nTrans.TransArg.builder().name(componentExt.getContent())
                        .customKey(ComponentExt.getTextComponentContentKey(layout.getRefObjectApiName(), layoutExt.getName(), component.getName(), layoutExt.getLayoutType()))
                        .preKeyList(Lists.newArrayList()).build());
            }
            transArgs.add(I18nTrans.TransArg.builder().name(componentExt.getHeader()).customKey(key).preKeyList(nameI18nKeyList).build());
        }
    }

//    /**
//     * 处理分组、页签、组件名称是否需要同步
//     * 如果需要同步则放到transList中
//     *
//     * @param localizationMap
//     * @param key
//     * @param header
//     * @param transList
//     * @param sectionName
//     * @param isGroup
//     */
//    private void handlerSyncTransValueLayout(Map<String, Localization> localizationMap, String key, String header, List<Save.TransDataRow> transList, String sectionName, Boolean isGroup) {
//        String transValue;
//        Localization localization;
//        if (!CollectionUtils.empty(localizationMap)) {
//            localization = localizationMap.get(key);
//            transValue = Objects.nonNull(localization) ? localization.get(RequestUtil.getCurrentLang().getValue(), null) : null;
//        } else {
//            localization = I18NExt.getLocalization(key);
//            transValue = Objects.nonNull(localization) ? localization.get(RequestUtil.getCurrentLang().getValue(), null) : null;
//        }
//        Save.TransDataRow transDataRow = new Save.TransDataRow();
//        //有翻译词条，但是语言环境下没翻译，还是要去同步翻译。
//        if (Objects.nonNull(localization)) {
//            //比较描述的header值和i18nkey值，如果不相等则需要同步
//            if (Strings.isNullOrEmpty(transValue) || !StringUtils.equals(header, transValue)) {
//                addTransList(transDataRow, key, header, transList);
//            }
//        }
//        //处理基本信息分组
//        if (isGroup) {
//            dealPresetGroupName(transDataRow, header, sectionName, key, transList, RequestUtil.getCurrentLang().getValue());
//        }
//    }

    /**
     * 处理详细信息下的预置分组的特殊逻辑
     * <p>
     * 根据apiName可以判定他是预置的分组
     * 当他的header值有一个与预设值不相同，则代表被更改过，需要同步
     * 作用：如果基本信息分组没有翻译值，但是更改了，也要做同步翻译值操作，解决打开布局时，翻译与语言环境不对应的问题
     *
     * @param key
     * @return
     */
    private void dealPresetGroupName(String header, String sectionName, String key, List<I18nTrans.TransArg> transArgs) {
        if (BASE_FIELD_SECTION_API_NAME.equals(sectionName)) {
            //如果该header值存预置翻译的几种语言中，则代表用户没有改变基本信息的分组名称，并且该分组名称和对应环境的预置翻译不同时，则要同步该翻译
            transArgs.add(I18nTrans.TransArg.builder().name(header).customKey(key).preKeyList(Lists.newArrayList(LAYOUT_BASIC_INFO)).build());
        } else {
            transArgs.add(I18nTrans.TransArg.builder().name(header).customKey(key).preKeyList(Lists.newArrayList()).build());
        }
    }

//    /**
//     * 增加需要同步的数据列表
//     *
//     * @param transDataRow
//     * @param key
//     * @param header
//     * @param transList
//     */
//    private void addTransList(Save.TransDataRow transDataRow, String key, String header, List<Save.TransDataRow> transList) {
//        transDataRow.setTag("custom");
//        transDataRow.setTranslateKey(key);
//        transDataRow.setTranslateValue(header);
//        transDataRow.setDefaultTranslateValue("");
//        transList.add(transDataRow);
//    }

    @Override
    @Transactional
    public LayoutResult updateLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe) {
        return updateLayoutAndUpdateDescribe(user, layout, objectDescribe, null);
    }

    @Override
    @Transactional
    public LayoutResult updateLayoutAndUpdateDescribe(User user, ILayout layout, IObjectDescribe objectDescribe, String persistentDataCalc) {
        if (null == layout.getId() || Strings.isNullOrEmpty(layout.getId())) {
            throw new ValidateException(I18N.text(I18NKey.OF_NOT_EMPTY));
        }

        StopWatch sw = StopWatch.create("updateLayoutAndUpdateDescribe");
        setUpdateLayoutDefaultValue(user, layout);
        ObjectDescribeExt.of(objectDescribe).setUpdateDescribeDefaultValue(user);
        //校验字段数量
        describeLogicService.checkCustomFieldCountLimit(user, objectDescribe, null);
        sw.lap("checkCustomFieldCountLimit");
        try {
            //校验单选字段级联关系
            ObjectDescribeExt.of(objectDescribe).checkSelectOneFiledWithLayout(layout);
        } catch (MetaDataBusinessException e) {
            log.warn("updateLayoutAndUpdateDescribe : {}", e.getMessage());
            throw new MetaDataBusinessException(e.getMessage());
        }

        //检查MD字段数量限制，更新时不用校验
        IObjectDescribe describeInDb = describeLogicService.findObject(user.getTenantId(), objectDescribe.getApiName());
        sw.lap("findObject");

        if (!ObjectDescribeExt.of(describeInDb).isSlaveObject() && ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            describeLogicService.checkDescribeMasterDetailLimit(user.getTenantId(), objectDescribe);
            sw.lap("checkDescribeMasterDetailLimit");
            //如果存在审批流定义，则不允许创建主从字段
            if (ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMaster()) {
                describeLogicService.checkIfHasApprovalFlowDefinition(user, objectDescribe.getApiName());
                sw.lap("checkIfHasApprovalFlowDefinition");
            }
            describeLogicService.checkMasterDetailDescribeByChangeOrder(user, objectDescribe, false);
        }

        try {
            ObjectDescribeExt.of(objectDescribe).handleNumberStepValue();
            // 校验自增编码是否合法
            autoNumberLogicService.autoNumberValidateByObjectDescribe(objectDescribe, describeInDb, user);
            sw.lap("autoNumberValidateByObjectDescribe");
            //字段校验
            List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByObjectDescribe(objectDescribe, describeInDb);
            sw.lap("validateByObjectDescribe");
            List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteFieldsByObjectDescribe(objectDescribe, describeInDb);
            sw.lap("checkQuoteFieldsByObjectDescribe");
            fieldRelationCalculateService.checkObjectReferenceFieldsByObjectDescribe(user, objectDescribe, describeInDb);
            sw.lap("checkObjectReferenceFieldsByObjectDescribe");

            //定位字段校验
            ObjectDescribeExt.of(objectDescribe).validateGeoLocationField(null);
            //富文本字段校验
            ObjectDescribeExt.of(objectDescribe).validateRichTextFieldInDescribe(null);
            LayoutExt.of(layout).validateRichTextFieldInLayout();
            //校验字段重复性   可重复->不可重复（不允许）
            ObjectDescribeExt.of(describeInDb).checkFieldUnique(objectDescribe.getFieldDescribes());
            //label去除特殊字段
            ObjectDescribeExt.of(objectDescribe).formatLabel(objectDescribe.getFieldDescribes());
            //校验target_related_list_name重复
            ObjectDescribeExt.of(objectDescribe).validateObjectReferenceField(null);
            ObjectDescribeExt.of(objectDescribe).validateGroupField(null, user.getTenantId());
            describeLogicService.checkDisplayFields(user, objectDescribe, describeInDb);
            sw.lap("checkDisplayFields");
            // 负责人字段修改为非必输需要同步更新从对象的负责人字段为非必输
            describeLogicService.syncMasterAndDetailOwnerField(user, objectDescribe, describeInDb);
            sw.lap("syncMasterAndDetailOwnerField");
            // 校验公共对象
            describeLogicService.checkPublicObjectBeforeUpdateDescribe(user, objectDescribe, describeInDb);
            sw.lap("checkPublicObjectBeforeUpdateDescribe");
            //WhatList字段可通过函数筛选关联业务模块逻辑处理
            List<WhatList> whatListFields = ObjectDescribeExt.of(objectDescribe).getWhatListFields();
            CollectionUtils.nullToEmpty(whatListFields)
                    .forEach(whatList -> describeLogicService.processWhatListField(whatList, describeInDb));
            List<ObjectReferenceWrapper> referenceFieldDescribes = ObjectDescribeExt.of(objectDescribe).getNotAddToLayoutReferenceFieldDescribes(describeInDb);

            //开启变更单
            if (!ObjectDescribeExt.of(describeInDb).isSlaveObject() && ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMaster()) {
                describeLogicService.openDetailObjChangeOrder(user, objectDescribe);
            }
            sw.lap("processWhatListField");
            IObjectDescribe update = updateAndSeedChangeMsg(user, describeInDb, objectDescribe);
            sw.lap("updateDescribe");

            ILayout updatedLayout = updateLayout(user, layout);
            sw.lap("updateLayout");


            // 新增或变更了自增编码切换重新计算规则
            autoNumberLogicService.diffAutoNumberField(objectDescribe, describeInDb);
            sw.lap("diffAutoNumberField");

            fieldRelationCalculateService.checkDecimalDigitChangeByDescribe(objectDescribe, describeInDb);
            sw.lap("checkDecimalDigitChangeByDescribe");
            //提交计算任务,重新计算历史数据
            List<String> fieldNameList = Lists.newArrayList();
            List<String> calculateFieldApiNames = calculateFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            List<String> quoteFieldApiNames = quoteFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            fieldNameList.addAll(calculateFieldApiNames);
            fieldNameList.addAll(quoteFieldApiNames);

            if (TenantUtil.isCalcCriteria(user.getTenantId())) {
                List<IFieldDescribe> calcFields = Lists.newArrayList(calculateFields);
                calcFields.addAll(quoteFields);
                jobScheduleService.submitCalculateJob(user, objectDescribe.getApiName(), calcFields, persistentDataCalc, false);
            } else {
                jobScheduleService.submitCalculateJob(user, fieldNameList, objectDescribe.getApiName());
            }

            sw.lap("submitCalculateJob");
            fieldRelationCalculateService.checkSelectOneChangeOfDescribe(objectDescribe, describeInDb);
            sw.lap("checkSelectOneChangeOfDescribe");
            // 判断是否购买多语资源包

            sw.lap("existModule_i18n");
            syncI18nPlatform(user, objectDescribe, layout);
            sw.lap("syncI18nPlatform");

            describeLogicService.batchUpsertRelatedListAddToLayoutConfig(user, referenceFieldDescribes);
            executeReferenceByLayout(RefMessage.ActionType.DELETE_AND_CREATE, objectDescribe, Lists.newArrayList(layout));
            return LayoutResult.builder().objectDescribe(update).layout(updatedLayout).build();
        } catch (MetadataServiceException e) {
            log.warn("updateLayoutAndUpdateDescribe error,user:{},layout:{},objectDescribe:{}",
                    user, layout.toJsonString(), objectDescribe.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        } finally {
            sw.logSlow(3000);
        }
    }

    @Override
    public LayoutResult createLayoutAndCreateDescribe(User user, ILayout layout, ILayout listLayout, IObjectDescribe objectDescribe) {
        LayoutContext context = buildLayoutContext(user);
        return createLayoutAndCreateDescribe(context, layout, listLayout, objectDescribe);
    }

    @Override
    public LayoutResult createLayoutAndCreateDescribe(LayoutContext context, ILayout layout, ILayout listLayout, IObjectDescribe objectDescribe) {
        User user = context.getUser();
        setCreateLayoutDefaultValue(user, layout, getAppId(context, objectDescribe.getApiName(), layout.getLayoutType()));
        setCreateLayoutDefaultValue(user, listLayout, getAppId(context, objectDescribe.getApiName(), listLayout.getLayoutType()));
        DescribeResult describeResult = describeLogicService.createDescribe(
                user,
                objectDescribe,
                layout,
                listLayout,
                true);
        executeReferenceByLayout(RefMessage.ActionType.CREATE, objectDescribe, Lists.newArrayList(layout));
        return LayoutResult.builder().objectDescribe(describeResult.getObjectDescribe()).layout(describeResult.getLayout()).build();
    }

    @Override
    public ILayout createListLayout(User user, ILayout layout) {
        LayoutContext context = buildLayoutContext(user);
        return createListLayout(context, layout);
    }

    @Override
    public ILayout createListLayout(LayoutContext context, ILayout layout) {
        User user = context.getUser();
        String appId = getAppId(context, layout.getRefObjectApiName(), layout.getLayoutType());
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());
        checkLayoutCountLimit(context, objectDescribe.getApiName(), layout.getLayoutType());
        setCreateLayoutDefaultValue(user, layout, appId);
        validateListLayout(layout);
        try {
            ILayout result = createLayoutAddManageGroup(user, layout, true);
            executeReferenceByLayout(RefMessage.ActionType.CREATE, objectDescribe, Lists.newArrayList(result));
            logService.log(user, EventType.ADD, ActionType.CreateLayout, objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()));
            return result;
        } catch (MetadataServiceException e) {
            log.warn("createListLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ILayout createAbstractLayout(User user, ILayout layout) {
        LayoutContext context = buildLayoutContext(user);
        return createAbstractLayout(context, layout);
    }

    @Override
    public ILayout createAbstractLayout(LayoutContext context, ILayout layout) {
        User user = context.getUser();
        String appId = getAppId(context, layout.getRefObjectApiName(), layout.getLayoutType());
        if (!AppFrameworkConfig.manyAbstractLayout(layout.getRefObjectApiName(), user.getTenantId())
                || !LayoutExt.of(layout).isMobileListLayout()) {
            throw new ValidateException(I18NKey.LAYOUT_TYPE_NOT_SUPPORT_EXCEPTION);
        }
        try {
            List<Layout> layouts = layoutService.findByTypes(user.getTenantId(), Lists.newArrayList(LayoutTypes.LIST),
                    layout.getRefObjectApiName(), null, buildActionContext(user));
            setLayoutsI18n(layouts);
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());

            if (CollectionUtils.empty(layouts)) {
                ILayout defaultAbLayout = LayoutExt.buildDefaultMobileListLayout(objectDescribe);
                createLayoutAddManageGroup(user, defaultAbLayout, false);
                executeReferenceByLayout(RefMessage.ActionType.CREATE, objectDescribe, Lists.newArrayList(defaultAbLayout));
            } else {
                if (layouts.stream().noneMatch(x -> BooleanUtils.isTrue(x.isDefault()))) {
                    Optional<Layout> optional = layouts.stream()
                            .filter(iLayout -> StringUtils.equals(iLayout.getName(), "layout_mobile_" + objectDescribe.getApiName()))
                            .findFirst();
                    if (optional.isPresent()) {
                        layouts = layouts.stream().sorted(Comparator.comparing(ILayout::getCreateTime)).collect(toList());
                    }
                    Layout defaultLayout = optional.orElse(layouts.get(0));
                    defaultLayout.setIsDefault(true);
                    setUpdateLayoutDefaultValue(user, defaultLayout);
                    layoutService.update(defaultLayout);
                }
            }
            checkLayoutCountLimit(context, layout.getRefObjectApiName(), layout.getLayoutType());
            setCreateLayoutDefaultValue(user, layout, appId);
            ILayout result = createLayoutAddManageGroup(user, layout, true);
            executeReferenceByLayout(RefMessage.ActionType.CREATE, objectDescribe, Lists.newArrayList(layout));
            logService.log(user, EventType.ADD, ActionType.CreateLayout, layout.getRefObjectApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()));
            return result;
        } catch (MetadataServiceException e) {
            log.warn("createListLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ILayout createFLowTaskListLayout(User user, ILayout layout) {
        LayoutContext context = buildLayoutContext(user);
        return createFLowTaskListLayout(context, layout);
    }

    @Override
    public ILayout createFLowTaskListLayout(LayoutContext context, ILayout layout) {
        User user = context.getUser();
        if (!LayoutExt.of(layout).isFlowTaskLayout()) {
            throw new ValidateException(I18NKey.LAYOUT_TYPE_NOT_SUPPORT_EXCEPTION);
        }
        String appId = getAppId(context, layout.getRefObjectApiName(), layout.getLayoutType());
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());
        checkLayoutCountLimit(context, objectDescribe.getApiName(), layout.getLayoutType());
        setCreateLayoutDefaultValue(user, layout, appId);
        try {
            LayoutExt.of(layout).setBindingAppId(null);
            ILayout result = layoutService.create(layout);
            logService.log(user, EventType.ADD, ActionType.CreateLayout, objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()));
            return result;
        } catch (MetadataServiceException e) {
            log.warn("createListLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ILayout updateListLayout(User user, ILayout layout) {
        setUpdateLayoutDefaultValue(user, layout);
        validateListLayout(layout);
        try {
            ILayout layoutDB = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layout.getName(),
                    layout.getRefObjectApiName(), user.getTenantId(), buildActionContext(user));
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());
            //顶导航多语变更往多语平台同步

            syncI18nPlatform(user, objectDescribe, layout);

            ILayout result;
            if (layoutDB == null) {
                setCreateLayoutDefaultValue(user, layout, layout.getBindingAppId());
                result = createLayoutAddManageGroup(user, layout, false);
            } else {
                result = layoutService.update(layout);
            }

            executeReferenceByLayout(RefMessage.ActionType.DELETE_AND_CREATE, objectDescribe, Lists.newArrayList(result));
            logService.logWithUpdateLayout(user, EventType.MODIFY,
                    ActionType.UpdateLayout,
                    layoutDB,
                    result,
                    objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()),
                    InternationalItem.builder()
                            .internationalKey(I18NKey.LAYOUT_OBJECT)
                            .defaultInternationalValue(I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()))
                            .internationalParameters(Lists.newArrayList(layout.getDisplayName(), objectDescribe.getDisplayName()))
                            .build()
            );
            return result;
        } catch (MetadataServiceException e) {
            log.warn("updateListLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ILayout updateAbstractLayout(User user, ILayout layout) {
        if (!AppFrameworkConfig.manyAbstractLayout(layout.getRefObjectApiName(), user.getTenantId())
                || !LayoutExt.of(layout).isMobileListLayout()) {
            throw new ValidateException(I18NKey.LAYOUT_TYPE_NOT_SUPPORT_EXCEPTION);
        }
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());
            ILayout layoutDB = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layout.getName(),
                    layout.getRefObjectApiName(), user.getTenantId(), buildActionContext(user));
            ILayout result;
            if (layoutDB == null) {
                setCreateLayoutDefaultValue(user, layout, layout.getBindingAppId());
                result = createLayoutAddManageGroup(user, layout, false);
            } else {
                setUpdateLayoutDefaultValue(user, layout);
                result = layoutService.update(layout);
            }

            executeReferenceByLayout(RefMessage.ActionType.DELETE_AND_CREATE, objectDescribe, Lists.newArrayList(result));
            logService.logWithUpdateLayout(user, EventType.MODIFY,
                    ActionType.UpdateLayout,
                    layoutDB,
                    result,
                    objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()),
                    InternationalItem.builder()
                            .internationalKey(I18NKey.LAYOUT_OBJECT)
                            .defaultInternationalValue(I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()))
                            .internationalParameters(Lists.newArrayList(layout.getDisplayName(), objectDescribe.getDisplayName()))
                            .build());
            return result;
        } catch (MetadataServiceException e) {
            log.warn("updateAbstractLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    @Transactional
    public ILayout updateFLowTaskListLayout(User user, ILayout layout) {
        if (!LayoutExt.of(layout).isFlowTaskLayout()) {
            throw new ValidateException(I18NKey.LAYOUT_TYPE_NOT_SUPPORT_EXCEPTION);
        }
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), layout.getRefObjectApiName());
            IObjectDescribe whatDescribe = describeLogicService.findObject(user.getTenantId(), layout.getWhatApiName());
            ILayout layoutDB = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layout.getName(),
                    layout.getRefObjectApiName(), user.getTenantId(), buildActionContext(user));


            ILayout result;
            if (layoutDB == null) {
                setCreateLayoutDefaultValue(user, layout, layout.getBindingAppId());
                result = layoutService.create(layout);
            } else {
                if (!Objects.equals(layoutDB.getWhatApiName(), whatDescribe.getApiName())) {
                    log.warn("updateFLowTaskListLayout error,user:{},layoutName:{},refObjectApiName:{},layoutWhatApiName:{},whatDescribeApiName:{}",
                            user, layout.getName(), layout.getRefObjectApiName(), layout.getWhatApiName(), whatDescribe.getApiName());
                    throw new ValidateException(I18N.text(PARAM_ERROR));
                }
                setUpdateLayoutDefaultValue(user, layout);
                result = layoutService.update(layout);
            }
            logService.logWithUpdateLayout(user, EventType.MODIFY,
                    ActionType.UpdateLayout,
                    layoutDB,
                    result,
                    objectDescribe.getApiName(),
                    I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()),
                    InternationalItem.builder()
                            .internationalKey(I18NKey.LAYOUT_OBJECT)
                            .defaultInternationalValue(I18N.text(I18NKey.LAYOUT_OBJECT, layout.getDisplayName(), objectDescribe.getDisplayName()))
                            .internationalParameters(Lists.newArrayList(layout.getDisplayName(), objectDescribe.getDisplayName()))
                            .build()
            );
            return result;
        } catch (MetadataServiceException e) {
            log.warn("updateFLowTaskListLayout error,user:{},layout:{}", user, layout.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void validateListLayout(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!layoutExt.isListLayout()) {
            return;
        }
        validateListLayoutComponentInfo(layout);
        if (layoutExt.isEnableMobileLayout()) {
            LayoutExt mobileLayout = LayoutExt.of(layout.getMobileLayout());
            validateListLayoutComponentInfo(mobileLayout);
            removeListLayoutSingleButtonInfo(mobileLayout);
        }
    }

    private void removeListLayoutSingleButtonInfo(ILayout mobileLayout) {
        // 移除移动端列表页布局关于单条按钮的配置
        ListLayoutExt.of(mobileLayout).getFirstListComponent().ifPresent(listComponentExt -> {
            List<IListComponentInfo> buttonInfo = listComponentExt.getButtonInfo();
            buttonInfo.removeIf(it -> ButtonUsePageType.DataList.getId().equals(it.getRenderType()) && IComponentInfo.PAGE_TYPE_LIST.equals(it.getPageType()));
            listComponentExt.resetButtonInfos(buttonInfo);
        });
    }

    private void validateListLayoutComponentInfo(ILayout layout) {
        ListLayoutExt listLayoutExt = ListLayoutExt.of(layout);
        listLayoutExt.initComponentIfAbsent();
        ListComponentExt listComponentExt = listLayoutExt.getFirstListComponent()
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.LIST_LAYOUT_COMPONENT_INFO_ERROR)));
        List<IScenesComponentInfo> sceneInfo = listComponentExt.getSceneInfo();
        validate(sceneInfo, IScenesComponentInfo::getPageType);

        List<IFiltersComponentInfo> filterInfo = listComponentExt.getFiltersComponentInfo();
        validate(filterInfo, IFiltersComponentInfo::getPageType);

        List<IListComponentInfo> buttonInfo = listComponentExt.getButtonInfo();
        validate(buttonInfo, info -> String.format("%s_%s", info.getPageType(), info.getRenderType()));

        List<IViewComponentInfo> viewInfo = listComponentExt.getViewInfos();
        validate(viewInfo, IViewComponentInfo::getName);
    }

    private <T> void validate(List<? extends T> componentInfos, Function<T, String> function) {
        Set<String> sets = Sets.newHashSet();
        for (T componentInfo : componentInfos) {
            String pageType = function.apply(componentInfo);
            if (sets.contains(pageType)) {
                throw new ValidateException(I18N.text(I18NKey.LIST_LAYOUT_COMPONENT_INFO_ERROR));
            }
            sets.add(pageType);
        }
    }

    /**
     * 校验UI事件的数量
     */
    private void checkUIEventNumber(User user, ILayout layout) {
        // 更新事件数量配额
        List<Map<String, Object>> events = layout.getEvents();
        if (CollectionUtils.empty(events)) {
            return;
        }
//        // 更新事件
//        int quotaUpdate =
//                licenseService.getQuotaByModule(user.getTenantId(), ModulePara.UI_UPDATE_EVENT_LIMIT.getParaKey());
//        // 校验事件
//        int quotaCheck =
//                licenseService.getQuotaByModule(user.getTenantId(), ModulePara.UI_CHECK_EVENT_LIMIT.getParaKey());
        List<UIEventExt> eventList = UIEventExt.ofList(events);
        Map<UIEventExt.EventType, Long> typeCountMap = eventList.stream()
                .filter(x -> StringUtils.isBlank(x.getDefineType())
                        || StringUtils.equals(x.getDefineType(), IFieldDescribe.DEFINE_TYPE_CUSTOM))
                .collect(groupingBy(UIEventExt::getEventEnumType, counting()));
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        tenantLicenseInfo.checkUpdateUIEventCount(Math.toIntExact(typeCountMap.getOrDefault(UIEventExt.EventType.UPDATE, 0L)));
        tenantLicenseInfo.checkCheckUIEventCount(Math.toIntExact(typeCountMap.getOrDefault(UIEventExt.EventType.CHECK, 0L)));
    }

    @Override
    public ILayout findObjectLayoutByApiNameAndType(User user, String layoutApiName, String layoutType, IObjectDescribe describe, IObjectData data) {
        StopWatch stopWatch = StopWatch.create("findObjectLayoutByApiNameAndType");
        String objectApiName = describe.getApiName();
        ILayout layout = changeOrderLogicService.findLayoutWithChangeOrder(user, objectApiName, layoutType, originalDescribeApiName -> {
            ILayout objectLayout = findLayoutByApiNameAndDescribeApiName(user, layoutApiName, originalDescribeApiName);
            processLayoutComponents(user, objectLayout, objectApiName, originalDescribeApiName);
            return objectLayout;
        });
        stopWatch.lap("findLayoutByApiName");
        if (Objects.isNull(layout)) {
            return null;
        }
        getLayoutByTypeWithDataAndPrivilegeAndLayoutLayer(layout, user, layoutType, describe, null, data, null);
        stopWatch.lap("processLayout");
        stopWatch.logSlow(100);
        return layout;
    }

    @Override
    public ILayout findObjectLayout(User user, String recordType, String objectApiName) {
        return findObjectLayout(buildLayoutContext(user), recordType, objectApiName);
    }

    @Override
    public ILayout findObjectLayout(LayoutContext context, String recordType, String objectApiName) {
        return findObjectLayoutByType(context, recordType, objectApiName, LayoutTypes.DETAIL);
    }

    @Override
    public ILayout findObjectLayoutByType(User user, String recordType, String objectApiName, String layoutType) {
        return findObjectLayoutByType(buildLayoutContext(user), recordType, objectApiName, layoutType);
    }

    @Override
    public ILayout findObjectLayoutByType(LayoutContext context, String recordType, String objectApiName, String layoutType) {
        User user = context.getUser();
        String viewType = getViewTypeByLayoutType(context, objectApiName, layoutType);
        return changeOrderLogicService.findLayoutWithChangeOrder(user, objectApiName, viewType, (describeApiName) -> {
            ILayout layout = _findObjectLayoutByType(context, recordType, describeApiName, viewType);
            processLayoutComponents(user, layout, objectApiName, describeApiName);
            return layout;
        });
    }

    private ILayout _findObjectLayoutByType(LayoutContext context, String recordType, String objectApiName, String viewType) {
        StopWatch stopWatch = StopWatch.create("findObjectLayoutByType");
        User user = context.getUser();
        ILayout layout = null;
        String appId = getAppId(context, objectApiName, viewType);
        if (!Strings.isNullOrEmpty(recordType)) {
            Optional<String> layoutAPIName = userRoleInfoService.getMainRoleLayoutAPIName(user, objectApiName, recordType, viewType);
            stopWatch.lap("getMainRoleLayoutAPIName");
            if (layoutAPIName.isPresent()) {
                layout = findLayoutByApiNameAndDescribeApiName(context.getUser(), layoutAPIName.get(), objectApiName);
                stopWatch.lap("findLayoutByApiName");
                if (layout == null) {
                    log.warn("layout not exist, tenantId:{}, userId:{}, objectApiName:{}, recordType:{}, layoutAPIName:{}",
                            user.getTenantId(), user.getUserId(), objectApiName, recordType, layoutAPIName.get());
                }
            } else {
                layout = findOtherRoleBindLayout(user, objectApiName, recordType, viewType);
                log.warn("layoutAPIName not exist, tenantId:{}, userId:{}, objectApiName:{}, recordType:{}",
                        user.getTenantId(), user.getUserId(), objectApiName, recordType);
            }
        }
        if (layout == null) {
            layout = findDefaultLayoutWithAppId(user, viewType, objectApiName, appId);
            stopWatch.lap("findDefaultLayout");
        }
        stopWatch.logSlow(100);
        return layout;
    }

    private ILayout findOtherRoleBindLayout(User user, String objectApiName, String recordType, String viewType) {
        if (!user.isOutUser()) {
            return null;
        }
        //获取当前应用支持的角色
        List<String> listAppOuterRolesByAppId = recordTypeLogicService.findListAppOuterRolesByAppId(user, RequestUtil.getAppId())
                .stream().map(RoleInfoPojo::getRoleCode).collect(Collectors.toList());
        //获取当前用户所有的角色信息
        List<GetRolesByUserId.UserRole> userRoleInfoList = userRoleInfoService.getRoleInfoByUser(user);
        //拿到主角色
        Optional<GetRolesByUserId.UserRole> defaultRole = userRoleInfoList.stream().filter(GetRolesByUserId.UserRole::getDefaultRole).findFirst();
        //如果当前用户有主角色，并且主角色有当前app的使用权限，则直接用默认布局(因为上面已经判断了主角色分配对应的布局)
        if (defaultRole.isPresent() && listAppOuterRolesByAppId.contains(defaultRole.get().getRoleCode())) {
            return null;
        }
        List<String> userRoleCodes = userRoleInfoList.stream().map(GetRolesByUserId.UserRole::getRoleCode).collect(toList());
        //根据业务类型，角色，批量查询对应的视图
        List<RoleViewPojo> roleViewPojos = recordTypeLogicService.batchFindRoleViewList(user, objectApiName,
                Sets.newHashSet(recordType), Sets.newHashSet(userRoleCodes), Sets.newHashSet(viewType), RequestUtil.getAppId());
        List<String> layoutApiNameList = roleViewPojos.stream().map(RoleViewPojo::getViewId).distinct().collect(toList());
        //批量查布局，拿到最新创建的布局
        Layout layout = CollectionUtils.nullToEmpty(findLayoutByApiNames(user.getTenantId(), layoutApiNameList, objectApiName)).values().stream().max(Comparator.comparing(Layout::getCreateTime)).orElse(null);
        if (Objects.isNull(layout)) {
            return null;
        }
        processLayoutComponents(user, layout, objectApiName);
        // 查询UI事件
        completeLayoutWithEvents(layout, user);
        LayoutExt.of(layout).fillFormComponent();
        LayoutExt.of(layout).fillDefaultComponent();
        LayoutExt.of(layout).removeDuplicateComponents();
        LayoutExt.of(layout).removeUnsupportedFields(user);
        return layout;
    }

    private String getViewTypeByLayoutType(LayoutContext context, String objectApiName, String layoutType) {
        if (Strings.isNullOrEmpty(layoutType)) {
            return LayoutTypes.DETAIL;
        }
        if (LayoutTypes.EDIT.equals(layoutType) || LayoutTypes.ADD.equals(layoutType)) {
            if (isEditLayoutEnable(context, objectApiName, true)) {
                return LayoutTypes.EDIT;
            }
        }
        return LayoutTypes.DETAIL;
    }

    @Override
    public ILayout findObjectLayoutByRole(User user, String recordType, String objectApiName, String role) {
        return findObjectLayoutByRole(user, recordType, objectApiName, role, LayoutTypes.DETAIL);
    }

    @Override
    public ILayout findObjectLayoutByRole(User user, String recordType, String objectApiName, String role, String layoutType) {
        return findObjectLayoutByRole(buildLayoutContext(user), recordType, objectApiName, role, layoutType);
    }

    @Override
    public ILayout findObjectLayoutByRole(LayoutContext context, String recordType, String objectApiName, String role, String layoutType) {
        User user = context.getUser();
        if (Strings.isNullOrEmpty(layoutType)) {
            layoutType = LayoutTypes.DETAIL;
        }
        ILayout layout;
        if (Strings.isNullOrEmpty(recordType) || Strings.isNullOrEmpty(role)) {
            layout = findDefaultLayout(context, layoutType, objectApiName);
        } else {
            Optional<String> layoutAPIName = getLayoutByRecordTypeAndRole(context, objectApiName, recordType, role, layoutType);
            if (layoutAPIName.isPresent()) {
                layout = findLayoutByApiName(user, layoutAPIName.get(), objectApiName);
            } else {
                layout = findDefaultLayout(context, layoutType, objectApiName);
            }
        }
        return layout;
    }

    private Optional<String> getLayoutByRecordTypeAndRole(LayoutContext context, String objectAPIName, String recordType,
                                                          String roleCode, String layoutType) {
        String appId = getAppId(context, objectAPIName, layoutType);
        List<RoleViewPojo> roleViewPojoList = recordTypeLogicService.findRoleViewList(context.getUser(), objectAPIName, layoutType,
                false, recordType, roleCode, appId);
        return roleViewPojoList.stream().findFirst().map(RoleViewPojo::getViewId);
    }

    /**
     * 查找业务类型，然后根据业务做处理
     */
    @Override
    public ILayout findObjectLayoutWithType(User user, String recordType, IObjectDescribe describe, String layoutType, IObjectData data) {
        return findObjectLayoutWithType(user, recordType, describe, layoutType, data, true);
    }

    @Override
    public ILayout findObjectLayoutWithType(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType, IObjectData data) {
        return findObjectLayoutWithType(context, recordType, describe, layoutType, data, true);
    }

    @Override
    public ILayout findObjectLayoutWithType(User user, String recordType, IObjectDescribe describe, String layoutType, IObjectData data, boolean isIncludeFormTable) {

        return findObjectLayoutWithType(buildLayoutContext(user), recordType, describe, layoutType, data, isIncludeFormTable);
    }

    @Override
    public ILayout findObjectLayoutWithType(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType, IObjectData data, boolean isIncludeFormTable) {
        return findObjectLayoutWithType(context, recordType, describe, layoutType, data, null, isIncludeFormTable);
    }

    @Override
    public Map<String, ILayout> findObjectLayoutWithType(User user, List<String> recordTypes, IObjectDescribe describe, String layoutType, IObjectData data) {
        return findObjectLayoutWithType(buildLayoutContext(user), recordTypes, describe, layoutType, data);
    }

    @Override
    public Map<String, ILayout> findObjectLayoutWithType(LayoutContext context, List<String> recordTypes, IObjectDescribe describe, String layoutType, IObjectData data) {
        Map<String, ILayout> layoutResult = Maps.newLinkedHashMap();
        if (CollectionUtils.empty(recordTypes)) {
            return layoutResult;
        }
        User user = context.getUser();
        Map<String, String> layoutByRecordTypes = getLayoutByRecordTypes(context, describe.getApiName(), recordTypes);
        Map<String, Layout> layoutMap;
        if (CollectionUtils.notEmpty(layoutByRecordTypes)) {
            Set<String> layoutApiNames = Sets.newHashSet(layoutByRecordTypes.values());
            layoutMap = changeOrderLogicService.findDetailLayoutsWithChangeOrder(user, describe,
                    (describeApiName) -> findLayoutByApiNames(user.getTenantId(), Lists.newArrayList(layoutApiNames), describeApiName));
        } else {
            layoutMap = Maps.newHashMap();
        }

        //尝试从批量查出来的布局中找到默认布局
        ILayout defaultLayout = layoutMap.values().stream().filter(x -> Boolean.TRUE.equals(x.isDefault())).findFirst().orElse(null);
        //分配的布局不存在，则使用默认布局，如果默认布局不在批量查询结果里，则单独查询一次
        if (defaultLayout == null && recordTypes.stream().anyMatch(x -> !layoutMap.containsKey(layoutByRecordTypes.get(x)))) {
            defaultLayout = findDefaultLayout(context, DETAIL_LAYOUT_TYPE, describe.getApiName());
        }

        Set<String> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(user, describe.getApiName());
        Set<String> processedLayouts = Sets.newHashSet();
        for (String recordType : recordTypes) {
            Layout layout = layoutMap.getOrDefault(layoutByRecordTypes.get(recordType), (Layout) defaultLayout);
            //因为过滤详细信息中的字段需要走rpc接口，所以使用Set去重，防止同一个布局多次process。
            if (processedLayouts.add(layout.getName())) {
                processLayout(user, describe, layoutType, data, recordType, layout, unauthorizedFields);
            }
            layoutResult.put(recordType, layout);
        }
        return layoutResult;
    }

    private Map<String, String> getLayoutByRecordTypes(LayoutContext layoutContext, String objectAPIName, List<String> recordTypes) {
        User user = layoutContext.getUser();
        String roleCode = userRoleInfoService.getDefaultRoleCode(user).orElse("");
        if (Strings.isNullOrEmpty(roleCode)) {
            return Maps.newHashMap();
        }
        List<RoleViewPojo> roleViewPojoList = recordTypeLogicService.batchFindRoleViewList(user, objectAPIName,
                Sets.newHashSet(recordTypes), Sets.newHashSet(roleCode), Sets.newHashSet(LayoutTypes.DETAIL), layoutContext.getAppId());
        return roleViewPojoList.stream().collect(Collectors.toMap(RoleViewPojo::getRecordTypeId, RoleViewPojo::getViewId));
    }

    private void processLayout(User user, IObjectDescribe describe, String layoutType, IObjectData data, String recordType,
                               ILayout layout, Set<String> unauthorizedFields) {
        getLayoutByTypeWithDataAndPrivilegeAndLayoutLayer(layout, user, layoutType, describe, recordType, data, unauthorizedFields);

        if (!ILayout.DETAIL_LAYOUT_TYPE.equals(layoutType)) {
            LayoutExt.of(layout).syncFieldsFromFormTableByLayoutOrder(layoutType, false);
            LayoutExt.of(layout).removeOthersExceptFormComponent();
        }

        LayoutExt.of(layout).removeUnsupportedFields(user);
    }

    private ILayout findObjectLayoutWithType(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType
            , IObjectData data, Collection<String> unauthorizedFields, boolean isIncludeFormTable) {
        ILayout layout = findObjectLayoutWithTypeIncludeAllComponent(context, recordType, describe, layoutType, data, unauthorizedFields);

        //除了详情页，其他场景的layout只保留form_component
        if (!ILayout.DETAIL_LAYOUT_TYPE.equals(layoutType)) {
            LayoutExt.of(layout).retainComponentsWithField();
        }

        if (LIST_LAYOUT_TYPE.equals(layoutType) || !isIncludeFormTable) {
            LayoutExt.of(layout).removeOthersExceptFormComponent();
        }

        return layout;
    }

    @Override
    public ILayout findObjectLayoutWithTypeIncludeAllComponent(User user, String recordType, IObjectDescribe describe, String layoutType, IObjectData data) {
        return findObjectLayoutWithTypeIncludeAllComponent(buildLayoutContext(user), recordType, describe, layoutType, data, null);
    }

    @Override
    public ILayout findObjectLayoutWithTypeIncludeAllComponent(LayoutContext context, String recordType, IObjectDescribe describe, String layoutType, IObjectData data) {
        return findObjectLayoutWithTypeIncludeAllComponent(context, recordType, describe, layoutType, data, null);
    }

    public ILayout findObjectLayoutWithTypeIncludeAllComponent(LayoutContext context, String recordType, IObjectDescribe describe
            , String layoutType, IObjectData data, Collection<String> unauthorizedFields) {
        User user = context.getUser();
        StopWatch stopWatch = StopWatch.create("findObjectLayoutWithTypeIncludeAllComponent");
        ILayout layout = findObjectLayoutByType(context, recordType, describe.getApiName(), layoutType);
        stopWatch.lap("findObjectLayoutByType");
        getLayoutByTypeWithDataAndPrivilegeAndLayoutLayer(layout, user, layoutType, describe, recordType, data, unauthorizedFields);
        //处理按顺序将表格表单中的字段排序，如果处理过就不处理了
        if (!ILayout.DETAIL_LAYOUT_TYPE.equals(layoutType) &&
                !(Strings.isNullOrEmpty(recordType) && LIST_LAYOUT_TYPE.equals(layoutType))) {
            if (com.facishare.paas.appframework.metadata.layout.LayoutContext.isMobileLayout()) {
                //只有移动端的主从一起新建的从才会走这块，移动端的主布局会在外面处理
                if (ObjectDescribeExt.of(describe).isCreateWithMaster()) {
                    LayoutExt.of(layout).syncFieldsFromFormTable(null);
                }
            } else {
                LayoutExt.of(layout).syncFieldsFromFormTableByLayoutOrder(layoutType, Objects.isNull(com.facishare.paas.appframework.metadata.layout.LayoutContext.isReplaceFormTable()) ?
                        ObjectDescribeExt.of(describe).isCreateWithMaster() : com.facishare.paas.appframework.metadata.layout.LayoutContext.isReplaceFormTable());
            }
        }

        stopWatch.lap("processLayout");
        //新布局重新生成组件的order
        if (LayoutExt.of(layout).isNewLayout()) {
            LayoutComponents.restoreComponentOrder(LayoutExt.of(layout));
            stopWatch.lap("restoreComponentOrder");
        }
        stopWatch.logSlow(100);

        return layout;
    }

    @Override
    public List<ILayout> getDetailLayouts(String tenantId, IObjectDescribe describe) {
        return getDetailLayouts(buildLayoutContext(User.systemUser(tenantId)), describe);
    }

    @Override
    public List<ILayout> getDetailLayouts(LayoutContext context, IObjectDescribe describe) {
        return findByType(context, describe.getApiName(), ILayout.DETAIL_LAYOUT_TYPE);
    }

    @Override
    public List<ILayout> findFlowTaskListLayoutsByType(User user, String describeApiName, String whatDescribeApiName, String layoutType) {
        return findFlowTaskListLayoutsByType(buildLayoutContext(user), describeApiName, whatDescribeApiName, layoutType);
    }

    @Override
    public List<ILayout> findFlowTaskListLayoutsByType(LayoutContext context, String describeApiName, String whatDescribeApiName, String layoutType) {
        User user = context.getUser();
        if (!LayoutTypes.WHAT_LIST.equals(layoutType) && !LayoutTypes.FLOW_TASK_LIST.equals(layoutType)) {
            return Collections.emptyList();
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        IObjectDescribe whatDescribe = describeLogicService.findObject(user.getTenantId(), whatDescribeApiName);
        ObjectDescribeExt.of(whatDescribe).removeAbstractField();

        String appId = getAppId(context, describeApiName, layoutType);
        List<ILayout> layouts = findByObjectDescribeApiNameAndTenantIdAndWhatApiNameByType(user, describeApiName, whatDescribeApiName, layoutType, appId);
        return appendDefaultFlowTaskListLayoutIfAbsent(user, layouts, Lists.newArrayList(layoutType), describe, whatDescribe);
    }

    @Override
    public ILayout findFlowTaskListLayoutsByLayoutNameAndType(User user, IObjectDescribe describe, IObjectDescribe whatDescribe, String layoutName, String layoutType) {
        if (!LayoutTypes.WHAT_LIST.equals(layoutType) && !LayoutTypes.FLOW_TASK_LIST.equals(layoutType)) {
            throw new ValidateException(I18NExt.text(PARAM_ERROR));
        }
        IActionContext actionContext = buildActionContext(user);
        List<ILayout> layouts;
        try {
            layouts = layoutService.findByObjectDescribeApiNameAndTenantIdAndWhatApiNames(describe.getApiName(), user.getTenantId(),
                    Lists.newArrayList(whatDescribe.getApiName()), layoutName, true, actionContext);
            setLayoutsI18n(layouts);
        } catch (MetadataServiceException e) {
            log.warn("findByObjectDescribeApiNameAndTenantIdAndWhatApiName fail! ei:{}, apiName:{}, whatApiName:{}, layoutName:{}, layoutType:{}",
                    user.getTenantId(), describe.getApiName(), whatDescribe.getApiName(), layoutName, layoutType, e);
            throw new MetaDataBusinessException(e);
        }
        return layouts.stream()
                .filter(it -> Objects.equals(layoutType, it.getLayoutType()))
                .findFirst()
                .orElseGet(() -> {
                    ILayout defaultLayout = layoutFactoryManager.generateDefaultLayout(user, layoutType, describe, whatDescribe);
                    if (Objects.equals(layoutName, defaultLayout.getName())) {
                        return defaultLayout;
                    }
                    return null;
                });
    }

    private List<ILayout> appendDefaultFlowTaskListLayoutIfAbsent(User user, List<ILayout> layoutList, Collection<String> layoutTypes,
                                                                  IObjectDescribe describe, IObjectDescribe whatDescribe) {
        Set<String> layoutTypesInLayoutList = layoutList.stream()
                .filter(it -> BooleanUtils.isTrue(it.isDefault()))
                .map(ILayout::getLayoutType)
                .collect(toSet());
        List<ILayout> resultLayouts = Lists.newArrayList(layoutList);
        for (String layoutType : Lists.newArrayList(LayoutTypes.WHAT_LIST, LayoutTypes.FLOW_TASK_LIST)) {
            if (layoutTypes.contains(layoutType) && !layoutTypesInLayoutList.contains(layoutType)) {
                ILayout defaultLayout = layoutFactoryManager.generateDefaultLayout(user, layoutType, describe, whatDescribe);
                resultLayouts.add(defaultLayout);
            }
        }
        return resultLayouts;
    }

    private List<ILayout> findByObjectDescribeApiNameAndTenantIdAndWhatApiNameByType(User user, String describeApiName,
                                                                                     String whatDescribeApiName, String layoutType, String appId) {
        try {
            IActionContext context = buildActionContext(user);
            List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantIdAndWhatApiNames(describeApiName,
                    user.getTenantId(), Lists.newArrayList(whatDescribeApiName), null, true, appId, context);
            setLayoutsI18n(layouts);
            return layouts.stream()
                    .filter(it -> Objects.equals(layoutType, it.getLayoutType()))
                    .collect(toList());
        } catch (MetadataServiceException e) {
            log.warn("findByObjectDescribeApiNameAndTenantIdAndWhatApiName error,tenantId:{},objectApiName:{},whatAPiName:{}",
                    user.getTenantId(), describeApiName, whatDescribeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private IActionContext buildActionContext(User user) {
        return ActionContextExt.of(user).getContext();
    }

    private LayoutContext buildLayoutContext(User user) {
        return LayoutContext.of(user);
    }

    private List<ILayout> findByObjectDescribeApiNameAndTenantIdAndWhatApiNamesByType(User user, String describeApiName,
                                                                                      List<String> whatDescribeApiNames, String layoutType, String appId) {
        try {
            List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantIdAndWhatApiNames(describeApiName,
                    user.getTenantId(), whatDescribeApiNames, null, false, appId, buildActionContext(user));
            setLayoutsI18n(layouts);
            return layouts.stream()
                    .filter(it -> Objects.equals(layoutType, it.getLayoutType()))
                    .collect(toList());
        } catch (MetadataServiceException e) {
            log.warn("findByObjectDescribeApiNameAndTenantIdAndWhatApiNames error,tenantId:{},objectApiName:{},whatApiNames:{}",
                    user.getTenantId(), describeApiName, whatDescribeApiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ILayout> findByTypes(String tenantId, String describeApiName, List<String> layoutTypes) {
        return findByTypes(buildLayoutContext(User.systemUser(tenantId)), describeApiName, layoutTypes);
    }

    @Override
    public List<ILayout> findByTypes(LayoutContext context, String describeApiName, List<String> layoutTypes) {
        return findByTypesWithGroup(context, describeApiName, layoutTypes, false);
    }

    @Override
    public List<ILayout> findByTypesIncludeFlowLayout(String tenantId, String describeApiName, List<String> layoutTypes) {
        LayoutContext context = buildLayoutContext(User.systemUser(tenantId));
        return findByTypesIncludeFlowLayout(context, describeApiName, layoutTypes);
    }

    @Override
    public List<ILayout> findByTypesIncludeFlowLayout(LayoutContext context, String describeApiName, List<String> layoutTypes) {
        return findByTypesWithGroup(context, describeApiName, layoutTypes, true);
    }

    @Override
    public List<ILayout> findByTypesIncludeFlowLayout(LayoutContext context, String describeApiName, List<String> layoutTypes, Boolean needLayoutLang) {
        return findByTypesWithGroup(context, describeApiName, layoutTypes, true, needLayoutLang);
    }

    private List<ILayout> findByTypesWithGroup(LayoutContext context, String describeApiName, List<String> layoutTypes, boolean includeFlowLayout) {
        return findByTypesWithGroup(context, describeApiName, layoutTypes, includeFlowLayout, false);
    }

    private List<ILayout> findByTypesWithGroup(LayoutContext context, String describeApiName, List<String> layoutTypes, boolean includeFlowLayout, Boolean needLayoutLang) {
        Map<String, List<String>> appId2layoutTypesMap = getAppIdByLayoutTypes(context, describeApiName, layoutTypes);
        List<ILayout> layouts = Lists.newArrayList();
        User user = context.getUser();
        appId2layoutTypesMap.forEach((appId, types) ->
                layouts.addAll(findByTypes(user, describeApiName, types, appId, includeFlowLayout)));
        if (layoutTypes.contains(LayoutTypes.DETAIL)) {
            batchProcessLayoutReference(user, Lists.newArrayList(layouts), describeApiName);
        }

        // 处理多语
        if (Boolean.TRUE.equals(needLayoutLang)) {
            processLayoutLanguage(layouts, user.getTenantId());
        }

        if (layoutTypes.contains(LayoutTypes.LIST_LAYOUT) || layoutTypes.contains(LayoutTypes.LIST)) {
            IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
            return appendDefaultLayoutIfAbsent(layouts, layoutTypes, describe, user.getTenantId());
        }
        return layouts;
    }

    /**
     * 处理布局的多语
     *
     * @param layouts  布局列表
     * @param tenantId 租户ID
     */
    private void processLayoutLanguage(List<ILayout> layouts, String tenantId) {
        if (CollectionUtils.empty(layouts)) {
            return;
        }

        // 在方法内部获取是否支持多语
        boolean supportMultiLanguage = licenseService.isSupportMultiLanguage(tenantId);
        if (!supportMultiLanguage) {
            return;
        }

        for (ILayout layout : layouts) {
            if (Objects.isNull(layout)) {
                continue;
            }
            LayoutExt layoutExt = LayoutExt.of(layout);
            // 只有当布局中有组件时才进行处理
            if (CollectionUtils.notEmpty(layoutExt.getComponentsSilently())) {
                ComponentHeaderSetter.builder()
                        .tenantId(tenantId)
                        .layoutType(layoutExt.getLayoutType())
                        .components(layoutExt.getComponentsSilently())
                        .objectApiName(layoutExt.getRefObjectApiName())
                        .layoutApiName(layoutExt.getName())
                        .existMultiLanguage(supportMultiLanguage)
                        .componentPreKeyMap(findComponentPreKeys(layoutExt.getComponentsSilently()))
                        .build()
                        .reset();
            }
        }
    }

    /**
     * 1、先用指定的appId查询布局
     * 2、includeFlowLayout 为 false
     * 移除查询结果中的流程布局
     * 3、includeFlowLayout 为 true
     * 因为流程布局不区分应用，对于指定了 appId，且 layoutTypes 指定查询详情页布局，需要去查询 crm 下的流程布局
     *
     * @param user
     * @param describeApiName
     * @param layoutTypes
     * @param appId
     * @param includeFlowLayout 需要流程布局
     * @return
     */
    private List<ILayout> findByTypes(User user, String describeApiName, List<String> layoutTypes, String appId, boolean includeFlowLayout) {
        try {
            List<Layout> layouts = layoutService.findByTypes(
                    user.getTenantId(), layoutTypes, describeApiName, appId, buildActionContext(user));
            setLayoutsI18n(layouts);
            if (!includeFlowLayout) {
                return layouts.stream().filter(x -> !LayoutExt.of(x).isFlowLayout()).collect(toList());
            }
            List<ILayout> resultList = new ArrayList<>(layouts);
            if (Strings.isNullOrEmpty(appId) || !layoutTypes.contains(LayoutTypes.DETAIL)) {
                return resultList;
            }
            layouts = layoutService.findByTypes(
                    user.getTenantId(), Lists.newArrayList(LayoutTypes.DETAIL), describeApiName, null, buildActionContext(user));
            setLayoutsI18n(layouts);
            resultList.addAll(layouts.stream().filter(x -> LayoutExt.of(x).isFlowLayout()).collect(toList()));
            return resultList;
        } catch (MetadataServiceException e) {
            log.warn("findByTypesIncludeFlowLayout error,tenantId:{},describeApiName:{},layoutTypes:{}", user.getTenantId(), describeApiName, layoutTypes, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private List<ILayout> findByType(String tenantId, String describeApiName, String layoutType) {
        return findByType(buildLayoutContext(User.systemUser(tenantId)), describeApiName, layoutType);
    }

    private List<ILayout> findByType(LayoutContext context, String describeApiName, String layoutType) {
        List<ILayout> layouts = findByTypes(context, describeApiName, Lists.newArrayList(layoutType));
        if (layouts.size() > 1) {
            Optional<ILayout> layout = layouts.stream().filter(x -> BooleanUtils.isTrue(x.isDefault())).findFirst();
            if (layout.isPresent()) {
                layouts.remove(layout.get());
                layouts.add(0, layout.get());
            }
        }
        return layouts;
    }

    private List<ILayout> appendDefaultLayoutIfAbsent(List<? extends ILayout> layoutList,
                                                      Collection<String> layoutTypes,
                                                      IObjectDescribe describe, String tenantId) {
        List<ILayout> result = new ArrayList<>(layoutList);
        if (CollectionUtils.empty(layoutTypes)) {
            return result;
        }
        if (layoutTypes.contains(ListLayoutExt.LIST_LAYOUT)) {
            if (layoutList.stream().noneMatch(it -> ListLayoutExt.DEFAULT_LIST_LAYOUT.equals(it.getName()))) {
                ILayout defaultListLayout = listLayoutFactory.generateDefaultLayout(User.systemUser(tenantId), describe);
                result.add(defaultListLayout);
            }
        }
        if (layoutTypes.contains(LayoutTypes.LIST) && AppFrameworkConfig.manyAbstractLayout(describe.getApiName(), tenantId)) {
            List<ILayout> abstractLayouts = result.stream().filter(layout -> StringUtils.equals(layout.getLayoutType(), LayoutTypes.LIST))
                    .collect(toList());
            if (CollectionUtils.empty(abstractLayouts)) {
                //没有移动摘要布局补充一个默认移动端摘要布局
                result.add(LayoutExt.buildDefaultMobileListLayout(describe));
            } else {
                if (abstractLayouts.stream().noneMatch(iLayout -> BooleanUtils.isTrue(iLayout.isDefault()))) {
                    abstractLayouts.stream()
                            .min(Comparator.comparingLong(x -> Optional.ofNullable(x).map(ILayout::getCreateTime).orElse(0L)))
                            .ifPresent(x -> x.setIsDefault(true));
                }
            }
        }
        return result;
    }

    @Override
    public List<ILayout> findMobileListLayout(User user, IObjectDescribe objectDescribe, boolean includeButtons) {
        return findMobileListLayout(buildLayoutContext(user), objectDescribe, includeButtons);
    }

    @Override
    public List<ILayout> findMobileListLayout(LayoutContext context, IObjectDescribe objectDescribe, boolean includeButtons) {
        User user = context.getUser();
        List<ILayout> list = findByType(context, objectDescribe.getApiName(), ILayout.LIST_LAYOUT_TYPE);

        //如果没有查询到移动端布局，生成一个默认的
        if (CollectionUtils.empty(list)) {
            list = Lists.newArrayList(LayoutExt.buildDefaultMobileListLayout(objectDescribe));
        }

        fillButton(user, objectDescribe, includeButtons, list);
        return list;
    }

    @Override
    public List<ILayout> findListLayout(User user, IObjectDescribe objectDescribe) {
        return findListLayout(buildLayoutContext(user), objectDescribe);
    }

    @Override
    public List<ILayout> findListLayout(LayoutContext context, IObjectDescribe objectDescribe) {
        return findByType(context, objectDescribe.getApiName(), ListLayoutExt.LIST_LAYOUT);
    }

    private void fillButton(User user, IObjectDescribe objectDescribe, boolean includeButtons, List<ILayout> layouts) {
        if (!includeButtons) {
            return;
        }
        List<IButton> buttons = findButtons(user, objectDescribe);
        layouts.forEach(layout -> layout.setButtons(buttons));
    }

    private List<IButton> findButtons(User user, IObjectDescribe objectDescribe) {
        List<IButton> buttons = buttonLogicService.getButtonByComponentActions(user, ComponentActions.TERMINAL_LIST_PAGE, objectDescribe, null, false);
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_715)) {
            return buttons;
        }
        List<IButton> listNormalButtons = customButtonService.findButtonsByUsePageType(user, objectDescribe, null, ButtonUsePageType.ListNormal);
        if (CollectionUtils.notEmpty(listNormalButtons)) {
            return CollectionUtils.addIfAbsent(buttons, listNormalButtons, (x, y) -> Objects.equals(x.getName(), y.getName()));
        }
        return buttons;
    }

    @Override
    public List<ILayout> findWhatListLayout(User user, IObjectDescribe describe, IObjectDescribe whatDescribe) {
        return findWhatListLayout(buildLayoutContext(user), describe, whatDescribe);
    }

    @Override
    public List<ILayout> findWhatListLayout(LayoutContext context, IObjectDescribe describe, IObjectDescribe whatDescribe) {
        User user = context.getUser();
        String appId = getAppId(context, whatDescribe.getApiName(), LayoutTypes.WHAT_LIST);
        List<ILayout> list = findByObjectDescribeApiNameAndTenantIdAndWhatApiNameByType(user, describe.getApiName(),
                whatDescribe.getApiName(), LayoutTypes.WHAT_LIST, appId);
        return appendDefaultFlowTaskListLayoutIfAbsent(user, list, Lists.newArrayList(LayoutTypes.WHAT_LIST), describe, whatDescribe);
    }

    @Override
    public List<ILayout> findWhatListLayouts(User user, IObjectDescribe describe, List<IObjectDescribe> whatDescribes) {
        return findWhatListLayouts(buildLayoutContext(user), describe, whatDescribes);
    }

    @Override
    public List<ILayout> findWhatListLayouts(LayoutContext context, IObjectDescribe describe, List<IObjectDescribe> whatDescribes) {
        List<String> whatApiNames = whatDescribes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        User user = context.getUser();
        String appId = getAppId(context, null, LayoutTypes.WHAT_LIST);
        List<ILayout> result = findByObjectDescribeApiNameAndTenantIdAndWhatApiNamesByType(user, describe.getApiName(),
                whatApiNames, LayoutTypes.WHAT_LIST, appId);
        Set<String> hasDefaultLayoutDescribeNames = result.stream()
                .filter(layout -> BooleanUtils.isTrue(layout.isDefault()))
                .map(ILayout::getWhatApiName)
                .collect(toSet());
        if (CollectionUtils.empty(hasDefaultLayoutDescribeNames) || hasDefaultLayoutDescribeNames.size() < whatDescribes.size()) {
            List<IObjectDescribe> whatDescribeList = whatDescribes.stream()
                    .filter(whatDescribe -> !hasDefaultLayoutDescribeNames.contains(whatDescribe.getApiName()))
                    .collect(Collectors.toList());
            result.addAll(generateDefaultLayouts(user, describe, whatDescribeList, Lists.newArrayList(LayoutTypes.WHAT_LIST)));
        }
        return result;
    }

    public List<ILayout> generateDefaultLayouts(User user, IObjectDescribe describe, List<IObjectDescribe> whatDescribes, Collection<String> layoutTypes) {
        List<ILayout> result = Lists.newArrayList();
        for (String layoutType : layoutTypes) {
            ILayoutFactory layoutFactory = layoutFactoryManager.getFactory(layoutType);
            for (IObjectDescribe whatDescribe : whatDescribes) {
                ILayoutFactory.Context context = ILayoutFactory.Context.of(user, whatDescribe);
                result.add(layoutFactory.generateDefaultLayout(context, describe));
            }
        }
        return result;
    }

    @Override
    public Map<String, ILayout> findListLayoutByDescribeApiNames(String tenantId, List<String> objectApiNames) {
        try {
            User systemUser = User.systemUser(tenantId);
            Map<String, ILayout> layoutMap = layoutService.findListLayoutByApiNames(tenantId, objectApiNames, null, buildActionContext(systemUser));
            setLayoutsI18n(layoutMap.values());

            if (objectApiNames.contains(ObjectAPINameMapping.SalesOrderProduct.getApiName())
                    && !layoutMap.containsKey(ObjectAPINameMapping.SalesOrderProduct.getApiName())) {
                layoutMap.put(ObjectAPINameMapping.SalesOrderProduct.getApiName(), LayoutExt.buildSalesOrderProductListLayout());
            }

            if (objectApiNames.contains(ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName())
                    && !layoutMap.containsKey(ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName())) {
                layoutMap.put(ObjectAPINameMapping.ReturnedGoodsInvoiceProduct.getApiName(), LayoutExt.buildReturnGoodsInvoiceProductListLayout());
            }

            //没有移动端布局的生成一个默认的
            List<String> noListLayoutObjectNames = objectApiNames.stream().filter(x -> !layoutMap.containsKey(x)).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(noListLayoutObjectNames)) {
                Map<String, IObjectDescribe> noListLayoutObjects = describeLogicService.findObjectsWithoutCopyIfGray(tenantId, noListLayoutObjectNames);
                noListLayoutObjects.forEach((k, v) -> layoutMap.put(k, LayoutExt.buildDefaultMobileListLayout(v)));
            }

            return layoutMap;
        } catch (MetadataServiceException e) {
            log.warn("findListLayoutByDescribeApiNames error,tenantId:{},objectApiNames:{}", tenantId, objectApiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }


    /**
     * 查询已分配的布局否则返回默认布局
     *
     * @param layoutContext 用户
     * @param recordTypes   业务类型APIs
     * @param layouts       布局列表
     * @return 业务类型API: 布局API
     */
    private Map<String, ILayout> findAssignedLayout(LayoutContext layoutContext, String objApi, List<ILayout> layouts, List<String> recordTypes) {
        User user = layoutContext.getUser();

        // 从布局列表中取出默认布局
        ILayout defaultLayout = getDefaultLayout(layouts);

        if (Objects.isNull(defaultLayout)) {
            log.warn("no default layout for tenantId: {}, obj: {}", user.getTenantId(), objApi);
            return null;
        }

        Map<String, ILayout> unassigned = recordTypes.stream().collect(toMap(Function.identity(), recordType -> defaultLayout));

        String defaultRoleCode = userRoleInfoService.getDefaultRoleCode(user).orElse("");
        if (org.apache.commons.lang.StringUtils.isBlank(defaultRoleCode)) {
            log.warn("no default_role_code, by tenantId:{}, userId:{}", user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
            //没有查找到用户的主角色，分配默认布局给该用户
            return unassigned;
        }
        Map<String, ILayout> layoutMap = layouts.stream().collect(Collectors.toMap(ILayout::getName, Function.identity()));
        List<RoleViewPojo> defaultRoleViews = recordTypeLogicService.batchFindRoleViewList(user, objApi, Sets.newHashSet(recordTypes),
                Sets.newHashSet(defaultRoleCode), Sets.newHashSet(LayoutTypes.LIST), layoutContext.getAppId());

        Map<String, ILayout> assigned = Optional.ofNullable(defaultRoleViews).orElse(Lists.newArrayList())
                .stream().collect(toMap(
                        RoleViewPojo::getRecordTypeId, pojo -> layoutMap.getOrDefault(pojo.getViewId(), defaultLayout), (a, b) -> a));

        unassigned.putAll(assigned);

        return unassigned;
    }

    /**
     * 从布局列表中拿出默认布局，如果没有默认布局取第 1 个作为默认布局
     * 布局列表为空 返回 null
     *
     * @param layouts 布局列表
     * @return 默认布局
     */
    private ILayout getDefaultLayout(List<ILayout> layouts) {
        if (CollectionUtils.empty(layouts)) {
            // 布局列表为空则 返回 null
            return null;
        }
        // 布局列表只有一个布局，返回为默认布局
        if (layouts.size() == 1) {
            return layouts.get(0);
        }
        // 找默认布局
        for (ILayout layout : layouts) {
            if (BooleanUtils.isTrue(layout.isDefault())) {
                return layout;
            }
        }
        // 找不到返回第1个
        return layouts.get(0);
    }

    @Override
    public Map<String, Map<String, ILayout>> findMobileListLayoutByDescApis(User user, List<IObjectDescribe> descApis,
                                                                            Map<String, List<IRecordTypeOption>> recordTypeOptionMap,
                                                                            boolean includeButtons) {
        return findMobileListLayoutByDescApis(buildLayoutContext(user), descApis, recordTypeOptionMap, includeButtons);
    }

    @Override
    public Map<String, Map<String, ILayout>> findMobileListLayoutByDescApis(LayoutContext context, List<IObjectDescribe> descApis,
                                                                            Map<String, List<IRecordTypeOption>> recordTypeOptionMap,
                                                                            boolean includeButtons) {
        Map<String, Map<String, ILayout>> res = Maps.newHashMap();
        if (CollectionUtils.empty(descApis)) {
            return res;
        }
        User user = context.getUser();
        for (IObjectDescribe desc : descApis) {

            List<IRecordTypeOption> recordTypeOptions = recordTypeOptionMap.get(desc.getApiName());
            List<String> recordTypes = recordTypeOptions.stream().map(IRecordTypeOption::getApiName).collect(toList());
            List<ILayout> mobileListLayouts = findMobileListLayout(context, desc, includeButtons);

            Map<String, ILayout> recordType2Layout = findAssignedLayout(context, desc.getApiName(), mobileListLayouts, recordTypes);

            res.put(desc.getApiName(), recordType2Layout);
        }
        return res;
    }

    @Override
    public ILayout findLayoutByApiName(User user, String layoutApiName, String objectApiName) {
        ILayout layout = findLayoutByApiNameAndDescribeApiName(user, layoutApiName, objectApiName);
        processLayoutComponents(user, layout, objectApiName);
        return layout;
    }

    private ILayout findLayoutByApiNameAndDescribeApiName(User user, String layoutApiName, String objectApiName) {
        StopWatch stopWatch = StopWatch.create("findLayoutByApiName");
        try {
            ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layoutApiName,
                    objectApiName, user.getTenantId(), buildActionContext(user));
            setLayoutI18n((layout));
            stopWatch.lap("findByNameAndObjectDescribeApiNameAndTenantId");
            if (layout == null) {
                return null;
            }
            // 查询UI事件
            completeLayoutWithEvents(layout, user);
            stopWatch.lap("findEvents");
            LayoutExt.of(layout).fillFormComponent();
            stopWatch.lap("fillFormComponent");
            LayoutExt.of(layout).fillDefaultComponent();
            stopWatch.lap("fillDefaultComponent");
            LayoutExt.of(layout).removeDuplicateComponents();
            stopWatch.lap("removeDuplicateComponents");
            LayoutExt.of(layout).removeUnsupportedFields(user);
            stopWatch.lap("removeUnsupportedFields");

            stopWatch.lap("filterComponent");
            return layout;
        } catch (MetadataServiceException e) {
            log.warn("findLayoutByApiNames error,user:{},layoutApiName:{},objectApiName:{}",
                    user, layoutApiName, objectApiName, e);
            throw new MetaDataBusinessException(e);
        } finally {
            stopWatch.logSlow(100);
        }
    }

    private void processLayoutComponents(User user, ILayout layout, String objectApiName) {
        processLayoutComponents(user, layout, objectApiName, objectApiName);
    }

    /**
     * @param user
     * @param layout
     * @param objectApiName         当前对象的 apiName
     * @param originalObjectApiName 原单对象的 apiName
     */
    private void processLayoutComponents(User user, ILayout layout, String objectApiName, String originalObjectApiName) {
        if (Objects.isNull(layout)) {
            return;
        }
        // 处理详细信息组件的引用，需要用原单对象的布局
        batchProcessLayoutReference(user, Lists.newArrayList(layout), originalObjectApiName);
        // 过滤组件
        filterComponentsAndButtons(user, objectApiName, LayoutExt.of(layout));
        //根据插件过滤组件
        filterComponentsBindingPlugin(user, layout, objectApiName);
    }

    private void filterComponentsBindingPlugin(User user, ILayout layout, String objectApiName) {
        if (Objects.isNull(layout)) {
            return;
        }
        List<IComponent> pluginComponents = LayoutExt.of(layout).getComponentsSilently().stream()
                .filter(x -> StringUtils.isNotEmpty(ComponentExt.of(x).getPluginApiName()))
                .collect(toList());
        List<String> pluginApiNames = pluginComponents.stream()
                .map(x -> ComponentExt.of(x).getPluginApiName())
                .distinct()
                .collect(toList());
        if (CollectionUtils.empty(pluginApiNames)) {
            return;
        }
        Map<String, Boolean> pluginStatusMap = domainPluginLogicService.findPluginStatus(user.getTenantId(),
                objectApiName, pluginApiNames);
        List<String> componentsToRemove = pluginComponents.stream()
                .filter(x -> !pluginStatusMap.getOrDefault(ComponentExt.of(x).getPluginApiName(), false))
                .map(IComponent::getName)
                .collect(toList());
        WebDetailLayout.of(layout).removeComponents(componentsToRemove);
    }

    private void batchProcessLayoutReference(User user, List<ILayout> layouts, String objectApiName) {
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        List<ILayout> detailLayouts = layouts.stream()
                .filter(x -> StringUtils.isNotEmpty(LayoutExt.of(x).getReferenceFieldConfig()))
                .collect(toList());
        if (CollectionUtils.empty(detailLayouts)) {
            return;
        }
        List<String> editLayoutApiNames = detailLayouts.stream()
                .map(x -> LayoutExt.of(x).getReferenceFieldConfig())
                .collect(toList());
        Map<String, Layout> editLayouts = findLayoutByApiNames(user.getTenantId(), editLayoutApiNames, objectApiName);
        IObjectDescribe describeInDb = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectApiName);
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describeInDb);
        detailLayouts.forEach(layout -> {
            String editLayoutApiName = LayoutExt.of(layout).getReferenceFieldConfig();
            Layout editLayout = editLayouts.get(editLayoutApiName);
            if (Objects.nonNull(editLayout)) {
                InitDetailLayoutBuilder.builder()
                        .editLayout(editLayout)
                        .detailLayout(layout)
                        .describe(describeInDb)
                        .optionalFeaturesSwitch(optionalFeaturesSwitch)
                        .describeLogicService(describeLogicService)
                        .build()
                        .mergeEditLayoutFieldConfigToDetailLayout();
            }
        });
    }

    private void filterComponentsAndButtons(User user, String objectApiName, LayoutExt layoutExt) {
        IObjectDescribe describeInDb = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectApiName);
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describeInDb);
        processingLayout(describeInDb, layoutExt, optionalFeaturesSwitch);
        if (!layoutExt.isEnableMobileLayout() || CollectionUtils.empty(layoutExt.getMobileLayout())) {
            return;
        }
        processingLayout(describeInDb, LayoutExt.of(layoutExt.getMobileLayout()), optionalFeaturesSwitch);
    }

    private void processingLayout(IObjectDescribe objectDescribe, LayoutExt layoutExt, OptionalFeaturesSwitchDTO optionalFeaturesSwitch) {
        Boolean isRelatedTeamEnabled = optionalFeaturesSwitch.getIsRelatedTeamEnabled();
        Boolean isFollowUpDynamicEnabled = optionalFeaturesSwitch.getIsFollowUpDynamicEnabled();
        Boolean isModifyRecordEnabled = optionalFeaturesSwitch.getIsModifyRecordEnabled();
        List<String> hiddenComponents = layoutExt.getHiddenComponents();
        if (!isRelatedTeamEnabled && !hiddenComponents.contains(ComponentExt.TEAM_COMPONENT_NAME)) {
            hiddenComponents.add(ComponentExt.TEAM_COMPONENT_NAME);
        }
        if (!isFollowUpDynamicEnabled && !hiddenComponents.contains(ComponentExt.SALE_LOG_COMPONENT_NAME)) {
            hiddenComponents.add(ComponentExt.SALE_LOG_COMPONENT_NAME);
        }
        if (!isModifyRecordEnabled && !hiddenComponents.contains(ComponentExt.OPERATION_LOG_COMPONENT_NAME)) {
            hiddenComponents.add(ComponentExt.OPERATION_LOG_COMPONENT_NAME);
        }
        if (ObjectDescribeExt.of(objectDescribe).isChangeOrderObject() && !hiddenComponents.contains(ComponentExt.BPM_RELATED_NAME)) {
            hiddenComponents.add(ComponentExt.BPM_RELATED_NAME);
        }
        layoutExt.setHiddenComponents(hiddenComponents);

        List<String> hiddenButtons = layoutExt.getHiddenButtons();
        if (!isRelatedTeamEnabled) {
            for (String buttonName : ButtonExt.TEAM_MEMBER_BUTTON_API_NAME) {
                if (!hiddenButtons.contains(buttonName)) {
                    hiddenButtons.add(buttonName);
                }
            }
        }
        if (!isFollowUpDynamicEnabled && !hiddenButtons.contains(SALE_RECORD.getDefaultButtonApiName())) {
            hiddenButtons.add(SALE_RECORD.getDefaultButtonApiName());
        }
        layoutExt.setHiddenButtons(hiddenButtons);
    }

    @Override
    public ILayout findListLayoutByApiName(User user, String layoutApiName, String objectApiName) {
        try {
            ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layoutApiName, objectApiName,
                    user.getTenantId(), buildActionContext(user));
            setLayoutI18n((layout));
            if (Objects.isNull(layout) && ListLayoutExt.DEFAULT_LIST_LAYOUT.equals(layoutApiName)) {
                IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), objectApiName);
                return listLayoutFactory.generateDefaultLayout(user, describe);
            }
            return layout;
        } catch (MetadataServiceException e) {
            log.warn("findListLayoutByApiName error,user:{},layoutApiName:{},objectApiName:{}",
                    user, layoutApiName, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ILayout findAbstractLayoutByApiName(User user, String layoutApiName, IObjectDescribe objectDescribe) {
        try {
            ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layoutApiName,
                    objectDescribe.getApiName(), user.getTenantId(), buildActionContext(user));
            setLayoutI18n((layout));
            //如果没有查询到移动端摘要布局，且是一个默认布局，代码补一个默认的
            if (Objects.isNull(layout) && StringUtils.equals(layoutApiName, "layout_mobile_" + objectDescribe.getApiName())) {
                layout = LayoutExt.buildDefaultMobileListLayout(objectDescribe);
            }
            if (Objects.isNull(layout)) {
                throw new ValidateException(I18NExt.text(PARAM_ERROR));
            }
            if (CollectionUtils.empty(layout.getLayoutStructure())) {
                LayoutExt.of(layout).setDefaultStructureToList();
            }
            LayoutExt.of(layout).setDefaultIncludeFields(objectDescribe);
            TableComponentRender.builder()
                    .functionPrivilegeService(functionPrivilegeService)
                    .user(user)
                    .describeExt(ObjectDescribeExt.of(objectDescribe))
                    .tableComponentExt(TableComponentExt.of(LayoutExt.of(layout).getTableComponent().orElse(null)))
                    .replaceIfNewTabComponent(false)
                    .build()
                    .handleIsShowLabel(layout)
                    .render();
            return layout;
        } catch (MetadataServiceException e) {
            log.warn("findAbstractLayoutByApiName error,user:{},layoutApiName:{},objectApiName:{}",
                    user, layoutApiName, objectDescribe.getApiName(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ILayout findDefaultListLayout(User user, String objectApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectApiName);
        return findDefaultListLayout(user, describe);
    }

    @Override
    public ILayout findDefaultListLayout(User user, IObjectDescribe describe) {
        return findDefaultListLayout(buildLayoutContext(user), describe);
    }

    @Override
    public ILayout findDefaultListLayout(LayoutContext context, IObjectDescribe describe) {
        User user = context.getUser();
        try {
            ILayout defaultListLayout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(ListLayoutExt.DEFAULT_LIST_LAYOUT,
                    describe.getApiName(), user.getTenantId(), buildActionContext(user));
            setLayoutI18n((defaultListLayout));
            if (null == defaultListLayout) {
                return listLayoutFactory.generateDefaultLayout(user, describe);
            }
            return defaultListLayout;
        } catch (MetadataServiceException e) {
            log.warn("findDefaultListLayout fail, ei:{}, objectApiName:{}", user.getTenantId(), describe.getApiName());
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, Layout> findLayoutByApiNames(String tenantId, List<String> layoutApiNames) {
        if (CollectionUtils.empty(layoutApiNames)) {
            return Maps.newHashMap();
        }
        try {
            List<Layout> layouts = layoutService.findByNames(tenantId, layoutApiNames);
            if (CollectionUtils.empty(layouts)) {
                return Maps.newHashMap();
            }
            layouts.forEach(x -> {
                LayoutExt.of(x).fillFormComponent();
                LayoutExt.of(x).removeDuplicateComponents();
            });
            return layouts.stream().collect(Collectors.toMap(x -> x.getName(), x -> x));
        } catch (MetadataServiceException e) {
            log.warn("findLayoutByApiNames error,tenantId:{},layoutApiNames:{}", tenantId, layoutApiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, Layout> findLayoutByApiNames(String tenantId, List<String> layoutApiNames, String describeApiName) {
        if (CollectionUtils.empty(layoutApiNames)) {
            return Maps.newHashMap();
        }
        try {
            User user = User.systemUser(tenantId);
            IActionContext context = buildActionContext(user);
            List<Layout> layouts = layoutService.findByApiNames(tenantId, describeApiName, layoutApiNames, context);
            setLayoutsI18n(layouts);
            if (CollectionUtils.empty(layouts)) {
                return Maps.newHashMap();
            }
            List<ILayout> iLayouts = layouts.stream().map(x -> (ILayout) x).collect(toList());
            batchProcessLayoutReference(user, iLayouts, describeApiName);
            layouts.forEach(x -> {
                LayoutExt.of(x).fillFormComponent();
                LayoutExt.of(x).removeDuplicateComponents();
            });
            return layouts.stream().collect(Collectors.toMap(Layout::getName, x -> x, (x1, x2) -> x1));
        } catch (MetadataServiceException e) {
            log.warn("findLayoutByApiNames error,tenantId:{},layoutApiNames:{},describeApiName:{}", tenantId, layoutApiNames, describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ILayout> findLayoutByObjectApiName(String tenantId, String objectApiName) {
        LayoutContext context = buildLayoutContext(User.systemUser(tenantId));
        return findLayoutByObjectApiName(context, objectApiName);
    }

    @Override
    public List<ILayout> findLayoutByObjectApiName(LayoutContext context, String objectApiName) {
        User user = context.getUser();
        try {
            List<ILayout> layouts = findByObjectDescribeApiNameAndTenantId(context, objectApiName);

            //按照创建时间排序
            if (CollectionUtils.notEmpty(layouts)) {
                layouts.sort(Comparator.comparing(x -> Objects.isNull(x.getCreateTime()) ? Long.valueOf(0) : x.getCreateTime()));
            }
            batchProcessLayoutReference(user, layouts, objectApiName);

            // 兼容top_info下field_section为空数组的数据
            layouts.forEach(this::handleTopInfo);

            return layouts;
        } catch (MetadataServiceException e) {
            log.warn("findLayoutByObjectApiName error,tenantId:{},objectApiName:{}", user.getTenantId(), objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * 若没有开启 appid 的灰度，直接查询crm下的布局
     * 若开启了灰度，需要查询指定应用下的布局和 crm下的布局，并合并两者
     *
     * @param context
     * @param objectApiName
     * @return
     * @throws MetadataServiceException
     */
    private List<ILayout> findByObjectDescribeApiNameAndTenantId(LayoutContext context, String objectApiName) throws MetadataServiceException {
        User user = context.getUser();
        String appId = getAppId(context, objectApiName, null);
        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(
                objectApiName, appId, user.getTenantId(), buildActionContext(user));
        setLayoutsI18n(layouts);
        if (Strings.isNullOrEmpty(appId)) {
            return layouts;
        }
        Map<String, List<String>> appIdByLayoutTypes = getAppIdByLayoutTypes(context, objectApiName, LayoutTypes.ALL_TYPES);

        List<ILayout> layoutsInCrm = layoutService.findByObjectDescribeApiNameAndTenantId(
                objectApiName, null, user.getTenantId(), buildActionContext(user));
        setLayoutsI18n(layoutsInCrm);
        List<ILayout> result = layouts.stream()
                .filter(it -> appIdByLayoutTypes.getOrDefault(appId, Collections.emptyList()).contains(it.getLayoutType()))
                .collect(toList());
        result.addAll(layoutsInCrm.stream()
                .filter(it -> appIdByLayoutTypes.getOrDefault(null, Collections.emptyList()).contains(it.getLayoutType()))
                .collect(toList()));
        return layouts;
    }

    @Override
    public List<ILayout> findLayoutByObjectApiNameAndLayoutType(String tenantId, String objectApiName, String layoutType) {
        LayoutContext context = buildLayoutContext(User.systemUser(tenantId));
        return findLayoutByObjectApiNameAndLayoutType(context, objectApiName, layoutType);
    }

    @Override
    public List<ILayout> findLayoutByObjectApiNameAndLayoutType(LayoutContext context, String objectApiName, String layoutType) {
        return findLayoutByObjectApiNameAndLayoutType(context, objectApiName, layoutType, false);
    }

    @Override
    public List<ILayout> findLayoutByObjectApiNameAndLayoutTypeIncludeFlowLayout(String tenantId, String objectApiName, String layoutType) {
        LayoutContext context = buildLayoutContext(User.systemUser(tenantId));
        return findLayoutByObjectApiNameAndLayoutTypeIncludeFlowLayout(context, objectApiName, layoutType);
    }

    @Override
    public List<ILayout> findLayoutByObjectApiNameAndLayoutTypeIncludeFlowLayout(LayoutContext context, String objectApiName, String layoutType) {
        return findLayoutByObjectApiNameAndLayoutType(context, objectApiName, layoutType, true);
    }

    private List<ILayout> findLayoutByObjectApiNameAndLayoutType(LayoutContext context, String objectApiName, String layoutType, boolean includeFlowLayout) {
        if (Strings.isNullOrEmpty(layoutType)) {
            return findLayoutByObjectApiName(context, objectApiName);
        }
        List<ILayout> layouts = findByTypesWithGroup(context, objectApiName, Lists.newArrayList(layoutType), includeFlowLayout);
        //按照创建时间排序
        List<ILayout> result = CollectionUtils.nullToEmpty(layouts).stream()
                .sorted(Comparator.comparing(this::getCreateTime))
                .collect(Collectors.toList());
        // 兼容top_info下field_section为空数组的数据
        result.forEach(this::handleTopInfo);
        return result;
    }

    private long getCreateTime(ILayout layout) {
        if (BooleanUtils.isTrue(layout.isDefault())) {
            return -1;
        }
        return Objects.isNull(layout.getCreateTime()) ? 0 : layout.getCreateTime();
    }

    private void handleTopInfo(ILayout layout) {
        if (!LayoutExt.of(layout).isDetailLayout()) {
            return;
        }
        Optional<SimpleComponent> topInfoComponent = LayoutExt.of(layout).getTopInfoComponentSilently();
        if (topInfoComponent.isPresent() && CollectionUtils.empty(topInfoComponent.get().getFieldSections())) {
            IFieldSection fieldSection = new FieldSection();
            fieldSection.set(IFieldSection.FORM_FIELDS, Lists.newArrayList());
            List<IFieldSection> fieldSections = topInfoComponent.get().getFieldSections();
            fieldSections.add(fieldSection);
            topInfoComponent.get().setFieldSections(fieldSections);
        }
    }

    @Override
    public ILayout findDefaultLayout(User user, String layoutType, String objectApiName) {
        return findDefaultLayout(buildLayoutContext(user), layoutType, objectApiName);
    }

    @Override
    public ILayout findDefaultLayout(LayoutContext context, String layoutType, String objectApiName) {
        User user = context.getUser();
        return changeOrderLogicService.findLayoutWithChangeOrder(user, objectApiName, layoutType, (describeApiName) -> {
            String appId = getAppId(context, objectApiName, layoutType);
            ILayout layout = findDefaultLayoutWithAppId(user, layoutType, describeApiName, appId);
            processLayoutComponents(user, layout, objectApiName, describeApiName);
            return layout;
        });
    }

    private ILayout findDefaultLayoutWithAppId(User user, String layoutType, String objectApiName, String appId) {
        try {
            ILayout layout = layoutService.findDefault(objectApiName, user.getTenantId(), layoutType, appId, buildActionContext(user));
            setLayoutI18n(layout);
            if (layout == null) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.DEFAULT_LAYOUT_NOT_EXIST));
            }
            processLayoutComponents(user, layout, objectApiName);
            // 查询UI事件
            completeLayoutWithEvents(layout, user);
            LayoutExt.of(layout).fillFormComponent();
            LayoutExt.of(layout).fillDefaultComponent();
            LayoutExt.of(layout).removeDuplicateComponents();
            LayoutExt.of(layout).removeUnsupportedFields(user);

            return layout;
        } catch (MetadataServiceException e) {
            log.warn("findDefaultLayout error,user:{},layoutType:{},objectApiName:{}",
                    user, layoutType, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public int findCountByType(String tenantId, List<String> layoutTypeList, String describeApiName) {
        return findCountByType(buildLayoutContext(User.systemUser(tenantId)), layoutTypeList, describeApiName);
    }

    @Override
    public int findCountByType(LayoutContext context, List<String> layoutTypeList, String describeApiName) {
        List<ILayout> layouts = findByTypes(context, describeApiName, layoutTypeList);
        return layouts.size();
    }

    private int findFlowLayoutCount(LayoutContext context, String describeApiName) {
        List<ILayout> layouts = findByTypesIncludeFlowLayout(context, describeApiName, Lists.newArrayList(LayoutTypes.DETAIL));
        layouts = layouts.stream().filter(x -> LayoutExt.of(x).isFlowLayout()).collect(toList());
        return layouts.size();
    }

    @Override
    public void checkLayoutCountLimit(User user, String describeApiName, String layoutType) {
        checkLayoutCountLimit(buildLayoutContext(user), describeApiName, layoutType);
    }

    @Override
    public void checkLayoutCountLimit(LayoutContext context, String describeApiName, String layoutType) {
        checkLayoutCountLimit(context, describeApiName, layoutType, null);
    }

    private void checkLayoutCountLimit(LayoutContext context, String describeApiName, String layoutType, String namespace) {
        if (!LayoutExt.HAS_COUNT_LIMIT_LAYOUT_TYPES.contains(layoutType)) {
            return;
        }
        User user = context.getUser();
        //流程布局单独校验
        if (LayoutExt.NAMESPACE_FLOW.equals(namespace)) {
            int layoutCount = findFlowLayoutCount(context, describeApiName);
            int maxCount = AppFrameworkConfig.getMaxFlowLayoutCount(user.getTenantId());
            if (layoutCount >= maxCount) {
                throw new LicenseException(I18N.text(I18NKey.SUPPORT_LAYOUT, maxCount));
            }
            return;
        }
        //智能表单的布局不计入分版限制
        int layoutCount = findCountByType(context, LayoutExt.HAS_COUNT_LIMIT_LAYOUT_TYPES, describeApiName);
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        tenantLicenseInfo.checkLayoutCount(layoutCount, describeApiName);
    }

    private void setCreateLayoutDefaultValue(User user, ILayout layout, String appId) {
        layout.setTenantId(user.getTenantId());
        layout.setCreatedBy(user.getUserId());
        layout.setLastModifiedBy(user.getUserId());
        LayoutExt layoutExt = LayoutExt.of(layout);
        appId = layoutExt.isFlowLayout() ? null : appId;
        layoutExt.fillAppId(appId);
    }

    private void setUpdateLayoutDefaultValue(User user, ILayout layout) {
        layout.setTenantId(user.getTenantId());
        layout.setLastModifiedBy(user.getUserId());
    }

    private void getLayoutByTypeWithDataAndPrivilegeAndLayoutLayer(ILayout layout,
                                                                   User user,
                                                                   String layoutType,
                                                                   IObjectDescribe describe,
                                                                   String recordType,
                                                                   IObjectData data,
                                                                   Collection<String> unauthorizedFields) {
        List<IFormField> orderFormFieldList = LayoutExt.of(layout).getOrderLayoutFieldList(true);
        List<FormComponentExt> formComponentList = LayoutExt.of(layout).getFormComponents();
        List<SummaryKeyComponentInfo> summaryKeyComponentInfos = LayoutExt.of(layout).getSummaryKeyComponentInfos();
        List<FormTable> formTableList = LayoutExt.of(layout).getFormTables();
        if (CollectionUtils.notEmpty(formComponentList) || CollectionUtils.notEmpty(formTableList) || CollectionUtils.notEmpty(summaryKeyComponentInfos)) {
            FormComponentRender.builder()
                    .functionPrivilegeService(functionPrivilegeService)
                    .objectDescribeService(describeLogicService)
                    .user(user)
                    .describeExt(ObjectDescribeExt.of(describe))
                    .formComponentExtList(formComponentList)
                    .summaryKeyComponentInfos(summaryKeyComponentInfos)
                    .formTableList(formTableList)
                    .layoutType(layoutType)
                    .dbLayoutType(layout.getLayoutType())
                    .recordType(recordType)
                    .data(data)
                    .unauthorizedFields(unauthorizedFields)
                    .orderFormFieldList(orderFormFieldList)
                    .build()
                    .render();
        }
    }

    @Override
    public boolean checkNeedShowRelatedObjs(User user, String objectDataId, String objectDescribeApiName, List<String> needHideFields) {
        if (!(ObjectAPINameMapping.Account.getApiName().equals(objectDescribeApiName)
                || ObjectAPINameMapping.Leads.getApiName().equals(objectDescribeApiName))) {
            return true;
        }

        CheckIsShowRelatedObj.CheckIsShowRelatedObjResult response = crmRestService.checkIsShowRelatedObj(user,
                ObjectAPINameMapping.toOldAPIName(objectDescribeApiName), objectDataId);
        log.debug("checkIsShowRelatedObj response1 :" + response);
        if (response == null) {
            return true;
        }
        for (CheckIsShowRelatedObj.PoolPermissionEntity field : response.getPermissions()) {
            log.debug("checkIsShowRelatedObj field :" + field);
            String oldFieldName = field.getFieldName();
            Boolean isVisible = field.getIsVisble();
            if (isVisible) {
                continue;
            }

            ObjectDataConverter dataConverter = objectDataConverterManager.getObjectDataConverter(objectDescribeApiName);
            if (dataConverter != null && needHideFields != null) {
                String newApiName = dataConverter.toNewFieldName(oldFieldName);
                if (newApiName != null) {
                    needHideFields.add(newApiName);
                }
            }
            if (Objects.nonNull(needHideFields)) {
                if ("Area".equals(oldFieldName)) {
                    needHideFields.add("country");
                    needHideFields.add("province");
                    needHideFields.add("city");
                    needHideFields.add("district");
                } else if ("Industry".equals(oldFieldName)) {
                    needHideFields.add("industry_level1");
                    needHideFields.add("industry_level2");
                }
            }
        }
        return !response.getIsOnlyShowDetails();
    }

    @Override
    public ILayout getLayoutWithComponents(User user, String recordType, IObjectDescribe describe, IObjectData objectData,
                                           List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                           PageType pageType) {
        return getLayoutWithComponents(user, recordType, describe, objectData, relatedObjectList, detailObjectList, null, pageType);
    }

    @Override
    public ILayout getLayoutWithComponents(LayoutContext context, String recordType, IObjectDescribe describe, IObjectData objectData,
                                           List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                           PageType pageType) {
        return getLayoutWithComponents(context, recordType, describe, objectData, relatedObjectList, detailObjectList, null, pageType);
    }

    @Override
    public ILayout getLayoutWithComponents(User user, String recordType, IObjectDescribe describe,
                                           IObjectData objectData, List<RelatedObjectDescribeStructure> relatedObjectList,
                                           List<RelatedObjectDescribeStructure> detailObjectList,
                                           Collection<String> unauthorizedFields, PageType pageType) {
        boolean fromRecycleBin = objectData != null && ObjectDataExt.of(objectData).isInvalid();
        return getLayoutWithComponents(user, recordType, describe, objectData, relatedObjectList, detailObjectList,
                unauthorizedFields, pageType, fromRecycleBin, false);
    }

    @Override
    public ILayout getLayoutWithComponents(LayoutContext context, String recordType, IObjectDescribe describe, IObjectData objectData,
                                           List<RelatedObjectDescribeStructure> relatedObjectList, List<RelatedObjectDescribeStructure> detailObjectList,
                                           Collection<String> unauthorizedFields, PageType pageType) {
        boolean fromRecycleBin = objectData != null && ObjectDataExt.of(objectData).isInvalid();
        return getLayoutWithComponents(context, recordType, describe, objectData, relatedObjectList, detailObjectList,
                unauthorizedFields, pageType, fromRecycleBin, false);
    }

    @Override
    public ILayout getLayoutWithComponents(User user, String recordType, IObjectDescribe describe,
                                           IObjectData objectData, List<RelatedObjectDescribeStructure> relatedObjectList,
                                           List<RelatedObjectDescribeStructure> detailObjectList,
                                           Collection<String> unauthorizedFields, PageType pageType,
                                           boolean fromRecycleBin, boolean excludeButton) {
        return getLayoutWithComponents(buildLayoutContext(user), recordType, describe, objectData, relatedObjectList, detailObjectList,
                unauthorizedFields, pageType, fromRecycleBin, excludeButton);
    }

    @Override
    public ILayout getLayoutWithComponents(LayoutContext context, String recordType, IObjectDescribe describe,
                                           IObjectData objectData, List<RelatedObjectDescribeStructure> relatedObjectList,
                                           List<RelatedObjectDescribeStructure> detailObjectList, Collection<String> unauthorizedFields,
                                           PageType pageType, boolean fromRecycleBin, boolean excludeButton) {
        User user = context.getUser();
        log.debug("getLayoutWithComponents user:{} recordType:{} describe:{} objectData:{} pageType:{}",
                user, recordType, describe, objectData, pageType);
        ILayout layout = findObjectLayoutWithType(context, recordType, describe, ILayout.DETAIL_LAYOUT_TYPE, objectData, unauthorizedFields, true);
        log.debug("findObjectLayoutWithType layout:{}", layout);

        Map<String, ILayout> listLayoutMap = null;
        Map<String, List<IRecordTypeOption>> recordTypeOptionMap = null;
        if (CollectionUtils.notEmpty(detailObjectList) && pageType == PageType.Detail) {
            List<String> detailApiNames = detailObjectList.stream().map(x -> x.getRelatedObjectDescribe().getApiName())
                    .distinct().collect(Collectors.toList());
            listLayoutMap = findListLayoutByDescribeApiNames(user.getTenantId(), detailApiNames);
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_673)) {
                recordTypeOptionMap = recordTypeLogicService.findValidRecordTypeListMap(detailApiNames, user);
            } else {
                recordTypeOptionMap = recordTypeLogicService.findRecordTypes(user.getTenantId(), detailApiNames);
            }
        }

        String version = getVersion(user, pageType);
        List<IButton> customButtons = findCustomButtons(user, pageType, describe, objectData, excludeButton);

        List<IComponent> componentConfig = getComponentConfig(user, describe, pageType);

        List<ICustomComponent> customComponents = getCustomComponents(user, pageType, layout);

        LayoutRender.builder()
                .buttonLogicService(buttonLogicService)
                .functionPrivilegeService(functionPrivilegeService)
                .licenseService(licenseService)
                .recordTypeLogicService(recordTypeLogicService)
                .gdprService(gdprService)
                .fieldAlignService(fieldAlignService)
                .layoutLogicService(this)
                .user(user)
                .changeOrderLogicService(changeOrderLogicService)
                .pageType(pageType)
                .layoutExt(LayoutExt.of(layout))
                .describeExt(ObjectDescribeExt.of(describe))
                .objectData(objectData)
                .relatedObjectList(relatedObjectList)
                .detailObjectList(detailObjectList)
                .listLayoutMap(listLayoutMap)
                .recordTypeOptionMap(recordTypeOptionMap)
                .customButtons(customButtons)
                .componentConfig(componentConfig)
                .version(version)
                .newCustomComponents(customComponents)
                .fromRecycleBin(fromRecycleBin)
                .excludeButton(excludeButton)
                .build()
                .render();
        return layout;
    }

    private String getVersion(User user, PageType pageType) {
        if (pageType != PageType.Related) {
            return licenseService.getVersion(user.getTenantId());
        }
        return null;
    }

    private List<IButton> findCustomButtons(User user, PageType pageType, IObjectDescribe describe, IObjectData objectData, boolean excludeButton) {
        if (pageType != PageType.Related && !excludeButton) {
            return customButtonService.findButtonsByUsePageType(user, describe, objectData, ButtonUsePageType.Detail);
        }
        return null;
    }

    private List<IComponent> getComponentConfig(User user, IObjectDescribe describe, PageType pageType) {
        List<IComponent> componentConfig = Lists.newArrayList();
        if (pageType == PageType.NewDetail || com.facishare.paas.appframework.metadata.layout.LayoutContext.isMobileLayout()) {
            componentConfig = getComponentConfig(user, describe.getApiName());
        }
        return componentConfig;
    }

    private List<ICustomComponent> getCustomComponents(User user, PageType pageType, ILayout layout) {
        if (pageType == PageType.WebDetail) {
            return findCustomComponents(user.getTenantId(), layout);
        }
        return null;
    }

    @Override
    public ILayout getLayoutByApiNameWithComponents(User user, String layoutApiName, IObjectDescribe describe, IObjectData objectData) {
        return getLayoutByApiNameWithComponents(user, layoutApiName, describe, objectData, null, null, true);
    }

    @Override
    public ILayout getLayoutByApiNameWithComponents(User user, String layoutApiName, IObjectDescribe describe, IObjectData objectData,
                                                    List<RelatedObjectDescribeStructure> relatedObjectList,
                                                    List<RelatedObjectDescribeStructure> detailObjectList, boolean excludeButton) {
        ILayout layout = findObjectLayoutByApiNameAndType(user, layoutApiName, LayoutTypes.DETAIL, describe, objectData);
        // 处理 fieldAlign
        fieldAlignService.handleLayoutWithGlobalFieldAlign(user.getTenantId(), ObjectDescribeExt.of(describe), LayoutExt.of(layout));

        if (LayoutExt.of(layout).isFlowLayout()) {
            return layout;
        }
        String version = licenseService.getVersion(user.getTenantId());
        List<IButton> customButtons = customButtonService.findButtonsByUsePageType(user, describe, objectData, ButtonUsePageType.Detail);

        List<IComponent> componentConfig = getComponentConfig(user, describe, PageType.WebDetail);
        List<ICustomComponent> customComponents = getCustomComponents(user, PageType.WebDetail, layout);

        LayoutRender.builder()
                .buttonLogicService(buttonLogicService)
                .functionPrivilegeService(functionPrivilegeService)
                .licenseService(licenseService)
                .recordTypeLogicService(recordTypeLogicService)
                .gdprService(gdprService)
                .fieldAlignService(fieldAlignService)
                .layoutLogicService(this)
                .user(user)
                .pageType(PageType.WebDetail)
                .changeOrderLogicService(changeOrderLogicService)
                .layoutExt(LayoutExt.of(layout))
                .describeExt(ObjectDescribeExt.of(describe))
                .objectData(objectData)
                .relatedObjectList(relatedObjectList)
                .detailObjectList(detailObjectList)
                .customButtons(customButtons)
                .componentConfig(componentConfig)
                .version(version)
                .newCustomComponents(customComponents)
                .fromRecycleBin(false)
                .excludeButton(excludeButton)
                .build()
                .render();
        return layout;
    }

    private List<IComponent> getComponentConfig(User user, String objectApiName) {
        String config = configService.findUserConfig(user, ComponentExt.getConfigKey(objectApiName));
        if (Strings.isNullOrEmpty(config)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> mapList = JSON.parseArray(config, Map.class).stream().map(x -> (Map<String, Object>) x).collect(toList());
        //去掉没有api_name或api_name重复的组件
        ComponentExt.removeEmptyOrDuplicateComponent(mapList);
        return mapList.stream().map(x -> new GroupComponent(x)).collect(Collectors.toList());
    }

    @Override
    public ILayout getListLayoutWitchComponents(User user, IObjectDescribe describe, PageType pageType, String renderPageType) {
        return getListLayoutWitchComponents(user, describe, pageType, renderPageType, ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE);
    }

    @Override
    public ILayout getListLayoutWitchComponents(LayoutContext context, IObjectDescribe describe, PageType pageType, String renderPageType) {
        return getListLayoutWitchComponents(context, describe, pageType, renderPageType, ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE);
    }

    @Override
    public ILayout getListLayoutWitchComponents(User user, IObjectDescribe describe, PageType pageType, String renderPageType,
                                                String recordType) {

        return getListLayoutWitchComponents(buildLayoutContext(user), describe, pageType, renderPageType, recordType);
    }

    @Override
    public ILayout getListLayoutWitchComponents(LayoutContext context, IObjectDescribe describe, PageType pageType,
                                                String renderPageType, String recordType) {
        ILayout listLayout = findListLayoutByRecordType(context, describe, recordType);

        return renderListLayout(context.getUser(), describe, pageType, renderPageType, listLayout);
    }

    @Override
    public ILayout getFlowTaskListLayoutWitchComponents(User user, IObjectDescribe describe, IObjectDescribe whatObjectDescribe,
                                                        PageType pageType, LayoutAgentType layoutAgentType, String recordType) {
        return getFlowTaskListLayoutWitchComponents(buildLayoutContext(user), describe, whatObjectDescribe, pageType, layoutAgentType, recordType);
    }

    @Override
    public ILayout getFlowTaskListLayoutWitchComponents(LayoutContext context, IObjectDescribe describe,
                                                        IObjectDescribe whatObjectDescribe, PageType pageType,
                                                        LayoutAgentType layoutAgentType, String recordType) {
        User user = context.getUser();
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FLOW_TASK_LAYOUT_GRAY, user.getTenantId()) || user.isOutUser()) {
            return null;
        }
        StopWatch stopWatch = StopWatch.create("getFlowTaskListLayoutWitchComponents");
        String validRecordType;
        if (Strings.isNullOrEmpty(recordType)) {
            validRecordType = ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE;
        } else {
            validRecordType = recordType;
        }
        String layoutType = LayoutTypes.FLOW_TASK_LIST;
        String viewType = FlowTaskLayoutExt.getRoleViewType(describe.getApiName(), layoutType);
        Optional<String> layoutApiName = userRoleInfoService.getMainRoleLayoutAPIName(user, whatObjectDescribe.getApiName(),
                validRecordType, viewType);
        stopWatch.lap("getMainRoleLayoutAPIName");

        ILayout layout = layoutApiName.map(apiName -> findFlowTaskListLayoutsByLayoutNameAndType(user, describe, whatObjectDescribe, apiName, layoutType))
                .orElseGet(() -> {
                    log.warn("layoutAPIName not exist, tenantId:{}, userId:{}, objectApiName:{}, recordType:{}",
                            user.getTenantId(), user.getUserId(), describe.getApiName(), validRecordType);
                    return findDefaultFlowTaskListLayout(user, describe, whatObjectDescribe, layoutType);
                });
        stopWatch.lap("findLayout");
        FLowTaskLayoutRender.builder()
                .flowTaskLayout(layout)
                .objectDescribe(describe)
                .whatObjectDescribe(whatObjectDescribe)
                .user(user)
                .pageType(pageType)
                .layoutAgentType(layoutAgentType)
                .layoutLogicService(this)
                .componentFactoryManager(componentFactoryManager)
                .build()
                .render();
        stopWatch.lap("render");
        stopWatch.logSlow(100);
        return layout;
    }

    @Override
    public ILayout findObjectLayoutWithWhatDescribe(User user, IObjectDescribe describe, IObjectDescribe whatDescribe) {
        ILayout objectLayout = findObjectLayoutWithType(user, "", describe, ILayout.LIST_LAYOUT_TYPE, null);
        ILayout whatObjectLayout = findObjectLayoutWithType(user, "", whatDescribe, ILayout.LIST_LAYOUT_TYPE, null);
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(whatDescribe).getFieldByApiNames(LayoutExt.of(whatObjectLayout).getFieldList());

        LayoutExt layoutExt = LayoutExt.of(objectLayout);
        layoutExt.getFormComponent()
                .flatMap(component -> component.getFieldSections().stream().findFirst())
                .ifPresent(x -> x.addFields(fieldDescribes.stream()
                        .map(this::buildFormField)
                        .collect(Collectors.toList())));
        return layoutExt.getLayout();
    }

    private FormField buildFormField(IFieldDescribe field) {
        FormField formField = new FormField();
        formField.setFieldName(WhatComponentExt.getWhatFieldName(field.getDescribeApiName(), field.getApiName()));
        formField.setRenderType(field.getType());
        return formField;
    }

    private ILayout findDefaultFlowTaskListLayout(User user, IObjectDescribe describe, IObjectDescribe whatObjectDescribe, String layoutType) {
        if (LayoutTypes.FLOW_TASK_LIST.equals(layoutType)) {
            String defaultLayoutName = FlowTaskLayoutExt.generateDefaultLayoutName(describe, whatObjectDescribe);
            return findFlowTaskListLayoutsByLayoutNameAndType(user, describe, whatObjectDescribe, defaultLayoutName, layoutType);
        }
        return null;
    }

    private ILayout renderListLayout(User user, IObjectDescribe describe, PageType pageType, String renderPageType, ILayout listLayout) {
        ListLayoutRender.builder()
                .describeExt(ObjectDescribeExt.of(describe))
                .listLayoutExt(ListLayoutExt.of(listLayout))
                .customButtonService(customButtonService)
                .sceneLogicService(sceneLogicService)
                .webPageService(webPageService)
                .buttonLogicService(buttonLogicService)
                .licenseService(licenseService)
                .layoutResourceService(layoutResourceService)
                .viewComponentFactory(viewComponentFactory)
                .listComponentFactory(listComponentFactory)
                .optionalFeaturesService(optionalFeaturesService)
                .functionPrivilegeService(functionPrivilegeService)
                .layoutLogicService(this)
                .pageType(pageType)
                .user(user)
                .renderPageType(renderPageType)
                .build()
                .render();
        return listLayout;
    }

    @Override
    public ILayout getListLayoutByApiNameWitchComponents(User user, String layoutApiName, IObjectDescribe describe, PageType pageType, String renderPageType) {
        ILayout listLayout = findListLayoutByApiName(user, layoutApiName, describe.getApiName());
        return renderListLayout(user, describe, pageType, renderPageType, listLayout);
    }

    @Override
    public ILayout findListLayoutByRecordType(User user, IObjectDescribe describe, String recordType) {
        return findListLayoutByRecordType(buildLayoutContext(user), describe, recordType);
    }

    @Override
    public ILayout findListLayoutByRecordType(LayoutContext context, IObjectDescribe describe, String recordType) {
        User user = context.getUser();
        StopWatch stopWatch = StopWatch.create("findListLayoutByRecordType");
        String validRecordType;
        if (Strings.isNullOrEmpty(recordType)) {
            validRecordType = ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE;
        } else {
            validRecordType = recordType;
        }
        Optional<String> layoutAPIName = userRoleInfoService.getMainRoleLayoutAPIName(user, describe.getApiName(),
                validRecordType, LayoutTypes.LIST_LAYOUT);
        stopWatch.lap("getMainRoleLayoutAPIName");
        ILayout listLayout = layoutAPIName.map(apiName -> findLayoutByApiName(user, apiName, describe.getApiName()))
                .orElseGet(() -> {
                    log.warn("layoutAPIName not exist, tenantId:{}, userId:{}, objectApiName:{}, recordType:{}",
                            user.getTenantId(), user.getUserId(), describe.getApiName(), validRecordType);
                    ILayout finalLayout = findOtherRoleBindLayout(context.getUser(), describe.getApiName(), validRecordType, LayoutTypes.LIST_LAYOUT);
                    if (Objects.isNull(finalLayout)) {
                        finalLayout = findDefaultListLayout(context, describe);
                    }
                    return finalLayout;
                });
        stopWatch.lap("findLayout");
        stopWatch.logSlow(100);
        return listLayout;
    }

    @Override
    public ILayout findLayoutById(String layoutId, String tenantId) {
        try {
            ILayout layout = layoutService.findById(layoutId, tenantId, buildActionContext(User.systemUser(tenantId)));
            setLayoutI18n(layout);
            return layout;
        } catch (MetadataServiceException e) {
            log.warn("findLayoutById error,tenantId:{},layoutId:{}", tenantId, layoutId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    // 根据前端传的map构造UI事件
    private List<IUIEvent> getUIEvents(ILayout layout, String tenantId) {
        List<IUIEvent> eventList = UIEventExt.ofList(layout.getEvents()).stream()
                .map(UIEventExt::toUIEvent).collect(Collectors.toList());
        // 补充企业信息
        for (IUIEvent e : eventList) {
            e.setTenantId(tenantId);
            if (StringUtils.isEmpty(e.getId())) {
                UIEventExt.of(e).remove(UIEventExt.ID);
            }
        }
        return eventList;
    }

    //layout设置eventID
    private void addEventIds(ILayout layout, List<IUIEvent> eventList) {
        List<String> eventIdList = eventList.stream().map(IUIEvent::getId).collect(Collectors.toList());
        layout.setUiEventIds(eventIdList);
    }

    // 更新布局时，处理UI事件
    private List<IUIEvent> dealUIEventWhenUpdateLayout(User user, ILayout layout, ILayout layoutDB) throws MetadataServiceException {
        log.info("update layout with UI event, layout:{}, tenantID:{}, userID:{}", layout, user.getTenantId(), user.getUserId());
        List<IUIEvent> result;
        // 1. 获取UI事件, 设计传来的ui事件map包含于layout中
        List<IUIEvent> events = getUIEvents(layout, user.getTenantId());
        List<String> eventIds = layoutDB.getUiEventIds();

        // 2. 过滤出需要删除的UI事件并删除
        List<String> retainIds = events.stream().map(IUIEvent::getId).filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        eventIds.removeAll(retainIds);
        List<String> repeatUIEventIds = dealLayoutRepeatUiEventId(eventIds, user, layout);
        //不重复的走原逻辑
        if (CollectionUtils.notEmpty(repeatUIEventIds)) {
            events.removeIf(x -> repeatUIEventIds.contains(x.getId()));
            //删除该布局中共用的ui事件
            List<String> uiEventIds = layout.getUiEventIds();
            uiEventIds.removeIf(repeatUIEventIds::contains);
            layout.setUiEventIds(uiEventIds);

            List<Map<String, Object>> layoutEvents = layout.getEvents();
            layoutEvents.removeIf(x -> repeatUIEventIds.contains(x.get("_id")));
            layout.setEvents(layoutEvents);
        }
        log.info("UI events to be deleted, eventIDs:{},tenantID:{}, userID:{}", eventIds, user.getTenantId(), user.getUserId());
        List<IUIEvent> uiEventDelete = eventLogicService.findEventListByIds(eventIds, user.getTenantId());
        eventLogicService.batchDelete(eventIds, user);
        functionLogicService.batchDeleteRelation(user, buildDeleteReferenceDataList(uiEventDelete));
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), layout.getRefObjectApiName());
        // 3. 新增或更新UI事件
        result = updateOrCreateEvents(events, user, objectDescribe);
        // 4. 更新UI事件
        addEventIds(layout, result);
        return CollectionUtils.nullToEmpty(result);
    }

    private List<IUIEvent> updateOrCreateEvents(List<IUIEvent> events, User user, IObjectDescribe objectDescribe) {
        List<IUIEvent> result = Lists.newArrayList();
        // 如果存在id且id为""，去掉ID，表示将要新建的事件
        // false: 更新事件
        // true: 新增事件
        Map<Boolean, List<IUIEvent>> classifiedEventMap = events.stream()
                .collect(partitioningBy(e -> Strings.isNullOrEmpty(e.getId())));
        classifiedEventMap.forEach((k, v) -> {
            if (k) {
                result.addAll(eventLogicService.createEvents(v, user));
            } else {
                deleteModifyFunctionRelation(user, v);
                List<IUIEvent> batchUIEvents = eventLogicService.batchUpdateUIEvent(v, user);
                List<String> orderList = v.stream().map(IUIEvent::getId).collect(toList());
                CollectionUtils.sortByGivenOrder(batchUIEvents, orderList, IUIEvent::getId);
                result.addAll(batchUIEvents);
            }
        });
        functionLogicService.saveRelation(user, buildReferenceDataList(result, objectDescribe));
        return result;
    }

    private void deleteModifyFunctionRelation(User user, List<IUIEvent> eventList) {
        List<String> eventIds = eventList.stream().map(IUIEvent::getId).collect(toList());
        List<IUIEvent> uiEventsInDB = eventLogicService.findEventListByIds(eventIds, user.getTenantId());
        // 变更UI事件执行动作函数需要删除引用关系
        Map<String, String> eventIdAndFunctionApiNameMap = eventList.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getId()))
                .collect(Collectors.toMap(IUIEvent::getId, IUIEvent::getFuncApiName));
        List<IUIEvent> needDelRelationUIEventList = uiEventsInDB.stream()
                .filter(x -> eventIdAndFunctionApiNameMap.containsKey(x.getId()) && !StringUtils.equals(x.getFuncApiName(), eventIdAndFunctionApiNameMap.get(x.getId())))
                .collect(toList());
        functionLogicService.batchDeleteRelation(user, buildDeleteReferenceDataList(needDelRelationUIEventList));
    }

    // 查询布局时，补充UI事件
    private void completeLayoutWithEvents(ILayout layout, User user) {
        List<String> eventIds = layout.getUiEventIds();
        List<IUIEvent> events = eventLogicService.findEventListByIds(eventIds, user.getTenantId());
        List<Map<String, Object>> eventMaps = Lists.newArrayList();
        events.forEach(e -> {
            // triggerField字段如果是null，为空list
            UIEventExt.of(e).setTriggerFieldApiNamesNullToEmptyList();
            @SuppressWarnings("unchecked") Map<String, Object> map = ((DocumentBasedBean) e).getContainerDocument();
            eventMaps.add(map);
        });
        layout.setEvents(eventMaps);
    }

    // 布局增加events
    private void addEventWithLayout(ILayout layout, List<IUIEvent> events) {
        List<Map<String, Object>> list = Lists.newArrayList();
        for (IUIEvent event : events) {
            @SuppressWarnings("unchecked") Map<String, Object> map = ((DocumentBasedBean) event).getContainerDocument();
            list.add(map);
        }
        layout.setEvents(list);
    }

    @Override
    public List<ICustomComponent> findCustomComponents(String tenantId, String objectApiName) {
        if (Strings.isNullOrEmpty(objectApiName)) {
            return Lists.newArrayList();
        }

        try {
            QueryComponentList.Arg arg = QueryComponentList.Arg.builder()
                    .targetScope(QueryComponentList.TargetScope.ObjectDetailPage.name())
                    .objectApiName(objectApiName)
                    .build();
            Map<String, String> headers = RestUtils.buildHeaders(User.systemUser(tenantId));
            QueryComponentList.Result result = customComponentProxy.queryComponentList(arg, headers);
            if (!result.isSuccess()) {
                log.error("queryComponentList failed,arg:{},result:{}", arg, result);
                return Lists.newArrayList();
            } else {
                return result.toCustomComponents();
            }
        } catch (Exception e) {
            log.error("findCustomComponents error,tenantId:{},objectApiName:{}", tenantId, objectApiName, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<ICustomComponent> findCustomComponentsByApiNames(String tenantId, List<String> customApiNames) {
        if (CollectionUtils.empty(customApiNames)) {
            return Lists.newArrayList();
        }
        try {
            BatchQueryComponentData.Arg arg = BatchQueryComponentData.Arg.builder()
                    .apiNames(customApiNames)
                    .build();
            Map<String, String> headers = RestUtils.buildHeaders(User.systemUser(tenantId));
            BatchQueryComponentData.Result result = customComponentProxy.batchQueryComponentData(arg, headers);
            if (!result.isSuccess()) {
                log.error("batchQueryComponentData failed,arg:{},result:{}", arg, result);
                return Lists.newArrayList();
            } else {
                return result.toCustomComponents();
            }
        } catch (Exception e) {
            log.error("findCustomComponentsByApiNames error,tenantId:{},customApiNames:{}", tenantId, customApiNames, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<ICustomComponent> findCustomComponents(String tenantId, ILayout layout) {
        List<String> customApiNames = Lists.newArrayList();
        if (com.facishare.paas.appframework.metadata.layout.LayoutContext.isMobileLayout()) {
            if (LayoutExt.of(layout).isEnableMobileLayout()) {
                customApiNames = LayoutExt.of(layout.getMobileLayout()).getCustomComponents().stream()
                        .map(x -> x.getCustomCompApiName()).collect(toList());
            }
        } else {
            customApiNames = LayoutExt.of(layout).getCustomComponents().stream()
                    .map(x -> x.getCustomCompApiName()).collect(toList());
        }
        return findCustomComponentsByApiNames(tenantId, customApiNames);
    }

    @Override
    public List<IComponent> getBusinessComponents(String tenantId, String objectApiName, String layoutType) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, objectApiName);
        return getBusinessComponents(tenantId, describe, layoutType);
    }

    @Override
    public List<IComponent> getBusinessComponents(String tenantId, IObjectDescribe describe, String layoutType) {
        //流程布局的业务组件
        if (Strings.isNullOrEmpty(layoutType)) {
            return getFlowBusinessComponents(tenantId, describe);
        }
        switch (layoutType) {
            case LayoutTypes.DETAIL:
                return getDetailPageBusinessComponents(tenantId, describe);
            case LayoutTypes.EDIT:
                return getEditPageBusinessComponents(tenantId, describe);
            default:
                return Lists.newArrayList();
        }
    }

    @Override
    public List<String> filterComponentsByFunctionCode(User user, IObjectDescribe objectDescribe, PageType pageType,
                                                       boolean isMobile, List<IComponent> components) {
        if (CollectionUtils.empty(components)) {
            return Collections.emptyList();
        }
        Map<String, List<IComponent>> funcCode2ComponentMap = getFuncCode2ComponentMap(user, objectDescribe, pageType, isMobile, components);

        List<String> functionCodes = Lists.newArrayList(funcCode2ComponentMap.keySet());
        if (CollectionUtils.empty(functionCodes)) {
            return Collections.emptyList();
        }
        // 查询功能权限
        Map<String, Boolean> funcPrivileges = functionPrivilegeService.funPrivilegeCheck(user, objectDescribe.getApiName(), functionCodes);

        Set<String> toRemoveNames = Sets.newHashSet();
        funcCode2ComponentMap.forEach((funcCode, componentList) -> {
            if (BooleanUtils.isNotTrue(funcPrivileges.get(funcCode))) {
                List<String> componentNames = componentList.stream()
                        .map(IComponent::getName)
                        .collect(toList());
                toRemoveNames.addAll(componentNames);
            }
        });
        return Lists.newArrayList(toRemoveNames);
    }

    @Override
    public Map<String, String> findComponentPreKeys(List<IComponent> components) {
        Map<String, String> result = Maps.newHashMap();
        List<FindObjectPageComponentList.ObjectPageComponent> objectPageComponents = webPageProxyService.findCustomerWidget();
        getComponentInfoByConsumer(objectPageComponents, components, (objectPageComponent, component) -> {
            if (Objects.nonNull(objectPageComponent) && !Strings.isNullOrEmpty(objectPageComponent.getNameI18nKey())) {
                result.put(component.getName(), objectPageComponent.getNameI18nKey());
            }
        }, true);
        return result;
    }

    private Map<String, List<IComponent>> getFuncCode2ComponentMap(User user, IObjectDescribe objectDescribe, PageType pageType,
                                                                   boolean isMobile, List<IComponent> components) {
        String bizId = getBizId(objectDescribe, pageType);
        String templeType = isMobile ? LayoutAgentType.MOBILE.getCode() : LayoutAgentType.WEB.getCode();

        // 查询组件定义
        List<FindObjectPageComponentList.ObjectPageComponent> objectPageComponents = webPageProxyService.findCustomerWidget(user, bizId, templeType);
        Map<String, List<IComponent>> result = Maps.newHashMap();

        getComponentInfoByConsumer(objectPageComponents, components, (objectPageComponent, component) -> {
            if (Objects.nonNull(objectPageComponent) && !Strings.isNullOrEmpty(objectPageComponent.getFunctionCode())) {
                result.computeIfAbsent(objectPageComponent.getFunctionCode(), key -> Lists.newArrayList()).add(component);
            }
        }, false);

        // ai组件是否展示的校验,目前是通过功能权限的接口查询是否还有算粒
        List<IComponent> aiChatComponents = getAIComponents(components);
        if (CollectionUtils.notEmpty(aiChatComponents)) {
            result.put(PrivilegeConstants.AI_OBJECT_USER_LICENSE, aiChatComponents);
        }
        return result;
    }

    private List<IComponent> getAIComponents(List<IComponent> components) {
        if (CollectionUtils.empty(components)) {
            return Collections.emptyList();
        }
        return components.stream()
                .filter(it -> ComponentExt.of(it).isAIChatComponent()
                        || ComponentExt.of(it).isAiInsightComponent())
                .collect(toList());
    }

    private void getComponentInfoByConsumer(List<FindObjectPageComponentList.ObjectPageComponent> objectPageComponents, List<IComponent> components,
                                            BiConsumer<FindObjectPageComponentList.ObjectPageComponent, IComponent> function, boolean isGetFirstWhenHasMulti) {

        Map<String, FindObjectPageComponentList.ObjectPageComponent> apiName2Map = Maps.newHashMap();
        Map<String, List<FindObjectPageComponentList.ObjectPageComponent>> type2Map = Maps.newHashMap();
        objectPageComponents.forEach(objectPageComponent -> {
            String apiName = objectPageComponent.getApiName();
            if (!Strings.isNullOrEmpty(apiName)) {
                apiName2Map.put(apiName, objectPageComponent);
            }
            String type = objectPageComponent.getType();
            if (!Strings.isNullOrEmpty(type)) {
                type2Map.computeIfAbsent(type, key -> Lists.newArrayList()).add(objectPageComponent);
            }
        });
        components.forEach(component -> {
            FindObjectPageComponentList.ObjectPageComponent objectPageComponent = Optional.ofNullable(apiName2Map.get(component.getName()))
                    .orElse(Optional.ofNullable(apiName2Map.get(component.getType())).orElseGet(() -> {
                        List<FindObjectPageComponentList.ObjectPageComponent> list = type2Map.get(component.getType());
                        if (CollectionUtils.empty(list)) {
                            return null;
                        }
                        if (list.size() > 1 && !isGetFirstWhenHasMulti) {
                            return null;
                        }
                        return list.get(0);
                    }));
            function.accept(objectPageComponent, component);
        });
    }

    private String getBizId(IObjectDescribe objectDescribe, PageType pageType) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (PageType.Detail == pageType || PageType.NewDetail == pageType || PageType.WebDetail == pageType) {
            return describeExt.isBigObject() ? QueryComponentList.TargetScope.BigObjectDetailPage.name()
                    : QueryComponentList.TargetScope.ObjectDetailPage.name();
        }
        if (PageType.Add == pageType || PageType.Edit == pageType) {
            return describeExt.isBigObject() ? QueryComponentList.TargetScope.BigObjectEditPage.name()
                    : QueryComponentList.TargetScope.ObjectEditPage.name();
        }
        if (PageType.ListLayout == pageType) {
            return describeExt.isBigObject() ? QueryComponentList.TargetScope.BigObjectListPage.name()
                    : QueryComponentList.TargetScope.ObjectListPage.name();
        }
        return null;
    }

    private List<IComponent> getDetailPageBusinessComponents(String tenantId, IObjectDescribe describe) {
        String objectApiName = describe.getApiName();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);

        List<IComponent> businessComponents = Lists.newArrayList();
        //摘要卡片
        if (LayoutComponents.SUPPORT_SUMMERY_CARD_OBJECTS.contains(objectApiName)) {
            businessComponents.add(LayoutComponents.buildSummaryCardComponent());
        }
        //附件组件
        if (LayoutComponents.SUPPORT_ATTACH_OBJECTS.contains(objectApiName)) {
            businessComponents.add(LayoutComponents.buildAttachComponent());
        }

        if (ObjectAPINameMapping.Account.getApiName().equals(objectApiName)) {
            //客户层级关系
            //需要购买资源包
            Set<String> keys = Sets.newHashSet(ModuleCode.ACCOUNT_HIERARCHY_APP, ModuleCode.CONTACT_RELATION_APP,
                    ModuleCode.CUSTOMER_BATTLE_MAP_APP);
            Map<String, Boolean> existModule = licenseService.existModule(tenantId, keys);
            if (existModule.getOrDefault(ModuleCode.ACCOUNT_HIERARCHY_APP, false)) {
                businessComponents.add(LayoutComponents.buildAccountHierarchyComponent());
            }
            if (existModule.getOrDefault(ModuleCode.CONTACT_RELATION_APP, false)) {
                businessComponents.add(LayoutComponents.buildContactRelationComponent());
            }
            //作战地图，先判断灰度名单（老模式），再判断license（新模式）
            if (AppFrameworkConfig.isSupportAccountOperationMap(tenantId)
                    || existModule.getOrDefault(ModuleCode.CUSTOMER_BATTLE_MAP_APP, false)) {
                businessComponents.add(LayoutComponents.buildAccountOperationMapComponent());
            }
        }
        //产品属性
        if (Utils.PRODUCT_API_NAME.equals(objectApiName)) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            String moduleConfig = configService.findTenantConfig(user, "is_open_attribute");
            if (moduleConfig != null && Objects.equals(moduleConfig, "1")) {
                businessComponents.add(LayoutComponents.buildProductAttributeComponent());
            }
        }

        //市场活动层级
        if (Utils.MARKETING_EVENT_API_NAME.equals(objectApiName)) {
            businessComponents.add(LayoutComponents.buildMarketingEventPathComponent());
        }

        List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribesWithoutCopyIfGray(tenantId, objectApiName);
        List<RelatedObjectDescribeStructure> detailObjects = describeExt.getDetailObjectDescribeStructures(relatedDescribes);
        List<RelatedObjectDescribeStructure> lookupObjects = describeExt.getRelatedObjectDescribeStructures(relatedDescribes);
        //根据配置中心的配置去掉不展示的关联对象
        lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(objectApiName, x.getRelatedObjectDescribe().getApiName()));

        //从对象
        businessComponents.addAll(MasterDetailGroupComponentBuilder.builder().detailObjectsDescribeList(detailObjects).build().getComponentListForNewDesigner());
        //关联对象
        businessComponents.addAll(RelatedObjectGroupComponentBuilder.builder().objectDescribe(describeExt)
                .user(RequestContextManager.getContext().getUser())
                .relatedObjectDescribeList(lookupObjects).build()
                .getComponentListForNewDesigner());
        businessComponents.forEach(component -> component.setDefineType(ComponentDefineType.BUSINESS.getType()));

        //根据配置过滤组件
        businessComponents.removeIf(x -> DefObjConstants.isComponentInvisible(objectApiName, x.getName()));

        return businessComponents;
    }

    private List<IComponent> getEditPageBusinessComponents(String tenantId, IObjectDescribe describe) {
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesCreateWithMasterWithoutCopyIfGray(tenantId, describe.getApiName());
        List<IComponent> businessComponents = detailDescribes.stream().map(x -> {
            IComponent detailObjComponent = LayoutComponents.buildDetailObjectComponent(x);
            EditLayout.processDetailRenderType(detailObjComponent, tenantId, describe.getApiName());
            //主从一起新建是必填的从对象组件不能删除
            MasterDetail masterDetail = ObjectDescribeExt.of(x).getMasterDetailField().get();
            ComponentExt.of(detailObjComponent).setUnDeletable(Boolean.TRUE.equals(masterDetail.getIsRequiredWhenMasterCreate()));
            return detailObjComponent;
        }).collect(toList());
        return businessComponents;
    }

    private List<IComponent> getFlowBusinessComponents(String tenantId, IObjectDescribe describe) {
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesCreateWithMasterWithoutCopyIfGray(tenantId, describe.getApiName());
        List<IComponent> businessComponents = detailDescribes.stream().map(x -> {
            IComponent detailObjComponent = LayoutComponents.buildDetailObjectComponent(x);
            return detailObjComponent;
        }).collect(toList());
        return businessComponents;
    }

    @Override
    public List<LayoutRoleInfo> findAssignedListLayout(String describeApiName, Set<String> listLayouts, User user) {
        return findAssignedListLayout(describeApiName, listLayouts, user, null);
    }

    @Override
    public List<LayoutRoleInfo> findAssignedListLayout(String describeApiName, Set<String> listLayouts, User user, String sourceInfo) {
        return findAssignedListLayout(user, describeApiName, null, listLayouts, ListLayoutExt.DEFAULT_LIST_LAYOUT, sourceInfo, LayoutTypes.LIST_LAYOUT);
    }

    @Override
    public List<LayoutRoleInfo> findAssignedListLayout(User user, String describeApiName, String whatDescribeApiName, Set<String> listLayouts,
                                                       String defaultLayoutName, String layoutType, String sourceInfo) {
        LayoutContext layoutContext = buildLayoutContext(user);
        return findAssignedListLayout(layoutContext, describeApiName, whatDescribeApiName, listLayouts, defaultLayoutName, layoutType, sourceInfo);
    }

    @Override
    public List<LayoutRoleInfo> findAssignedListLayout(LayoutContext context, String describeApiName, String whatDescribeApiName,
                                                       Set<String> listLayouts, String defaultLayoutName, String layoutType, String sourceInfo) {
        User user = context.getUser();
        String appId = context.getAppId();
        String roleViewEntityId = FlowTaskLayoutExt.getRoleViewEntityId(describeApiName, whatDescribeApiName, layoutType);
        String viewType = FlowTaskLayoutExt.getRoleViewType(describeApiName, layoutType);
        List<RoleViewPojo> roleViewPojoList = recordTypeLogicService.findRoleViewList(context.getUser(), roleViewEntityId,
                viewType, false, appId);
        Map<String, String> roleCodeViewIdMap = roleViewPojoList.stream()
                .filter(it -> ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE.equals(it.getRecordTypeId()))
                .collect(Collectors.toMap(RoleViewPojo::getRoleCode, RoleViewPojo::getViewId));

        List<RoleInfoPojo> roleInfoPojoList = recordTypeLogicService.findRoleInfoByAppIdWithMangeGroup(user, sourceInfo, describeApiName, appId);
        if (CollectionUtils.empty(roleInfoPojoList)) {
            return Collections.emptyList();
        }

        List<LayoutRoleInfo> result = Lists.newArrayList();
        for (RoleInfoPojo roleInfo : roleInfoPojoList) {
            LayoutRoleInfo layoutRoleInfo = new LayoutRoleInfo();
            layoutRoleInfo.setLabel(roleInfo.getRoleName());
            layoutRoleInfo.setRoleCode(roleInfo.getRoleCode());

            LayoutRoleInfo.RecordLayoutInfo recordLayoutInfo = new LayoutRoleInfo.RecordLayoutInfo();
            recordLayoutInfo.setRecordType(ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE);
            String layoutApiName = roleCodeViewIdMap.getOrDefault(roleInfo.getRoleCode(), defaultLayoutName);
            recordLayoutInfo.setLayoutApiName(listLayouts.contains(layoutApiName) ? layoutApiName : defaultLayoutName);
            layoutRoleInfo.setRecordLayout(Collections.singletonList(recordLayoutInfo));

            result.add(layoutRoleInfo);
        }
        return result;
    }

    @Override
    public void saveListLayoutAssign(String describeApiName, List<LayoutRoleInfo> roleList, User user) {
        saveListLayoutAssign(buildLayoutContext(user), describeApiName, null, roleList, null);
    }

    @Override
    public void saveListLayoutAssign(User user, String describeApiName, String whatDescribeApiName, List<LayoutRoleInfo> roleList, String layoutType) {
        saveListLayoutAssign(buildLayoutContext(user), describeApiName, whatDescribeApiName, roleList, layoutType);
    }

    @Override
    public void saveListLayoutAssign(LayoutContext context, String describeApiName, String whatDescribeApiName,
                                     List<LayoutRoleInfo> roleList, String layoutType) {
        String appId = context.getAppId();
        User user = context.getUser();
        List<RoleViewPojo> list = Lists.newArrayList();
        String viewType = FlowTaskLayoutExt.getRoleViewType(describeApiName, layoutType);
        String roleViewEntityId = FlowTaskLayoutExt.getRoleViewEntityId(describeApiName, whatDescribeApiName, layoutType);
        for (LayoutRoleInfo layoutRole : roleList) {
            String roleCode = layoutRole.getRoleCode();
            for (LayoutRoleInfo.RecordLayoutInfo recordLayout : layoutRole.getRecordLayout()) {
                RoleViewPojo roleViewPojo = new RoleViewPojo();
                roleViewPojo.setEntityId(roleViewEntityId);
                roleViewPojo.setRecordTypeId(ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE);
                roleViewPojo.setRoleCode(roleCode);
                roleViewPojo.setTenantId(user.getTenantId());
                roleViewPojo.setViewId(recordLayout.getLayoutApiName());
                roleViewPojo.setViewType(viewType);
                list.add(roleViewPojo);
            }
        }
        recordTypeLogicService.upsertRoleViewList(user, list, appId);
    }

    @Override
    public byte getEditLayoutStatus(String tenantId, String describeApiName) {
        return getEditLayoutStatus(buildLayoutContext(User.systemUser(tenantId)), describeApiName);
    }

    @Override
    public byte getEditLayoutStatus(LayoutContext context, String describeApiName) {
        // 默认开启新建编辑页布局的对象
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_ENABLE_EDIT_PAGE_LAYOUT_OBJECT_GRAY, describeApiName)) {
            return (byte) EditLayoutStatus.ENABLE.getCode();
        }
        String appId = getAppId(context, describeApiName, LayoutTypes.EDIT);
        String key = EditLayout.buildStatusKey(describeApiName, appId);
        String value = configService.findTenantConfig(context.getUser(), key);
        return Strings.isNullOrEmpty(value) ? (byte) EditLayoutStatus.DISABLE.getCode() : Byte.parseByte(value);
    }

    @Override
    public boolean isEditLayoutEnable(String tenantId, String describeApiName, boolean useCache) {
        return isEditLayoutEnable(buildLayoutContext(User.systemUser(tenantId)), describeApiName, useCache);
    }

    @Override
    public boolean isEditLayoutEnable(LayoutContext context, String describeApiName, boolean useCache) {
        String tenantId = context.getTenantId();
        String appId = getAppId(context, describeApiName, LayoutTypes.EDIT);
        String localKey = EditLayout.buildLocalStatusKey(tenantId, describeApiName, appId);
        int status = EditLayoutStatus.DISABLE.getCode();
        if (useCache) {
            try {
                status = editLayoutStatusCache.get(localKey, () -> getEditLayoutStatus(context, describeApiName));
            } catch (ExecutionException e) {
                log.error("get editLayoutStatus from cache error,tenantId:{},describeApiName:{}", tenantId, describeApiName, e);
            }
        } else {
            status = getEditLayoutStatus(context, describeApiName);
        }

        return EditLayoutStatus.isEnable(status);
    }

    @Override
    public void deleteEditLayoutConfig(User user, String describeApiName) {
        deleteEditLayoutConfig(buildLayoutContext(user), describeApiName);
    }

    @Override
    public void deleteEditLayoutConfig(LayoutContext context, String describeApiName) {
        String appId = getAppId(context, describeApiName, LayoutTypes.EDIT);
        String key = EditLayout.buildStatusKey(describeApiName, appId);
        User user = context.getUser();
        configService.deleteTenantConfig(user, key);
        log.warn("delete editLayout config,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName);
    }

    @Transactional
    @Override
    public void enableEditLayout(User user, String describeApiName) {
        enableEditLayout(user, describeApiName, false);
    }

    @Transactional
    @Override
    public void enableEditLayout(User user, String describeApiName, boolean detailPageReferenceFieldConfig) {
        enableEditLayout(buildLayoutContext(user), describeApiName, detailPageReferenceFieldConfig);
    }

    @Override
    @Transactional
    public void enableEditLayout(LayoutContext context, String describeApiName, boolean detailPageReferenceFieldConfig) {
        User user = context.getUser();
        log.warn("enable editLayout,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName);
        int status = getEditLayoutStatus(context, describeApiName);
        if (EditLayoutStatus.isEnable(status)) {
            log.warn("enable editLayout ignored,status:{}", status);
            return;
        }
        List<ILayout> layoutList = findByTypes(context, describeApiName, Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT));
        List<ILayout> editLayoutList = layoutList.stream().filter(x -> LayoutExt.of(x).isEditLayout()).collect(toList());
        List<LayoutRuleInfo> layoutRuleInfoList = layoutRuleLogicService.findLayoutRuleByDescribe(user, describeApiName);
        List<RoleViewPojo> roleViewPojoList = recordTypeLogicService.findRoleViewList(user, describeApiName,
                LayoutTypes.DETAIL, true, context.getAppId());
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesCreateWithMasterWithoutCopyIfGray(user.getTenantId(), describeApiName);
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        List<ILayout> detailLayouts = layoutList.stream().filter(x -> LayoutExt.of(x).isDetailLayout()).collect(toList());
        List<RoleViewPojo> newRoleViewPojoList = Lists.newArrayList();
        ManageGroup manageGroup = queryLayoutManageGroup(user, describeApiName, ObjectListConfig.OBJECT_MANAGEMENT);
        detailLayouts.forEach(detailLayout -> {
            //复制布局和ui事件
            ILayout editLayout = copyLayout(context, detailLayout, editLayoutList, objectDescribe, detailDescribes, manageGroup);
            //复制布局规则
            copyLayoutRule(user, layoutRuleInfoList, detailLayout, editLayout);
            //复制视图权限
            List<RoleViewPojo> tmpRoleViewList = copyRoleViewList(roleViewPojoList, detailLayout, editLayout);
            newRoleViewPojoList.addAll(tmpRoleViewList);

            log.warn("copy layout success,tenantId:{},describeApiName:{},detailLayoutApiName:{}", user.getTenantId(),
                    describeApiName, detailLayout.getName());
        });
        saveRoleViewList(user, newRoleViewPojoList, context.getAppId());

        detailLayoutRefEditLayoutFieldConfig(user, detailLayouts, detailPageReferenceFieldConfig);
        updateEditLayoutConf(context, describeApiName);
    }

    private void updateEditLayoutConf(LayoutContext context, String describeApiName) {
        User user = context.getUser();
        String appId = getAppId(context, describeApiName, LayoutTypes.EDIT);
        String key = EditLayout.buildStatusKey(describeApiName, appId);
        configService.upsertTenantConfig(user, key, String.valueOf(EditLayoutStatus.ENABLE.getCode()), ConfigValueType.STRING);
        log.warn("enable editLayout success,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName);
    }

    @Transactional
    @Override
    public void disableEditLayout(User user, String describeApiName) {
        disableEditLayout(buildLayoutContext(user), describeApiName);
    }

    @Override
    @Transactional
    public void disableEditLayout(LayoutContext context, String describeApiName) {
        User user = context.getUser();
        log.warn("disable editLayout,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName);
        byte status = getEditLayoutStatus(context, describeApiName);
        if (EditLayout.EditLayoutStatus.isDisable(status)) {
            log.warn("disable editLayout ignored,status:{}", status);
            return;
        }
        try {
            List<ILayout> layouts = findByTypes(context, describeApiName, Lists.newArrayList(LayoutTypes.EDIT, LayoutTypes.DETAIL));
            List<ILayout> editLayouts = layouts.stream().filter(x -> LayoutExt.of(x).isEditLayout()).collect(toList());
            for (ILayout layout : editLayouts) {
                //默认布局需要先把is_default更新成true才能删除
                if (Boolean.TRUE.equals(layout.isDefault())) {
                    layout.setIsDefault(false);
                    layoutService.update(layout);
                }
                deleteLayout(user, layout);
            }
            List<ILayout> detailLayouts = layouts.stream().filter(x -> LayoutExt.of(x).isDetailLayout()).collect(toList());
            for (ILayout layout : detailLayouts) {
                LayoutExt layoutExt = LayoutExt.of(layout);
                Optional<FormComponentExt> formComponent = layoutExt.getFormComponent();
                if (formComponent.isPresent() && formComponent.get().isReferenceLayoutFieldConfig()) {
                    clearReferenceFieldConfig(layout);
                    layoutService.update(layout);
                }
            }
            deleteEditLayoutConfig(context, describeApiName);
            log.warn("disable editLayout success,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("disable editLayout failed,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void detailLayoutRefEditLayoutFieldConfig(User user, List<ILayout> detailLayouts, boolean detailPageReferenceFieldConfig) {
        if (detailPageReferenceFieldConfig) {
            detailLayouts.forEach(layout -> {
                LayoutExt layoutExt = LayoutExt.of(layout);
                Optional<FormComponentExt> formComponent = layoutExt.getFormComponent();
                formComponent.ifPresent(component -> {
                    if (!component.isReferenceLayoutFieldConfig()) {
                        component.setReferenceFieldConfig(layoutExt.getDefaultEditLayoutName());
                    }
                });
            });
            // todo 批量更新接口
            detailLayouts.forEach(detailLayout -> updateLayout(user, detailLayout, false));
        }
    }

    @Override
    public ILayout convertDetailToEditLayout(User user, String describeApiName, String layoutApiName) {
        ILayout detailLayout = findLayoutByApiName(user, layoutApiName, describeApiName);
        if (Objects.isNull(detailLayout)) {
            throw new ValidateException(I18NExt.text(PARAM_ERROR));
        }
        //UI事件置空
        detailLayout.setUiEventIds(Lists.newArrayList());
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesCreateWithMasterWithoutCopyIfGray(user.getTenantId(), describeApiName);
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        ILayout layout = InitialEditLayoutBuilder.builder()
                .detailLayout(detailLayout)
                .detailDescribes(detailDescribes)
                .describe(describe)
                .createLayoutFromDetail(true)
                .build()
                .getLayout();
        clearReferenceFieldConfig(layout);
        processButtonInfo(user, describe, layout);
        return layout;
    }

    private void clearReferenceFieldConfig(ILayout layout) {
        // 清空详情页引用相关配置的特殊属性
        LayoutExt.of(layout).getComponentsSilently().forEach(x -> {
            if (ComponentExt.FORM_COMPONENT.equals(x.getName())
                    && ComponentExt.of(x).toMap().containsKey(FormComponentExt.REFERENCE_FIELD_CONFIG)) {
                FormComponentExt.of((FormComponent) x).setReferenceFieldConfig(null);
            }
            if (ComponentExt.of(x).toMap().containsKey(ComponentExt.IS_DISABLED)) {
                ComponentExt.of(x).set(ComponentExt.IS_DISABLED, null);
            }
        });
    }

    @Override
    public ILayout convertEditToDetailLayout(User user, String describeApiName, String layoutApiName, boolean isRefFieldConfig) {
        ILayout editLayout = findLayoutByApiName(user, layoutApiName, describeApiName);
        if (Objects.isNull(editLayout)) {
            throw new ValidateException(I18NExt.text(PARAM_ERROR));
        }
        //UI事件置空
        editLayout.setUiEventIds(Lists.newArrayList());
        editLayout.setEvents(Lists.newArrayList());
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        //        processButtonInfo(user, describe, layout);
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describe);
        ILayout detailLayout = InitDetailLayoutBuilder.builder()
                .editLayout(editLayout)
                .describe(describe)
                .optionalFeaturesSwitch(optionalFeaturesSwitch)
                .describeLogicService(describeLogicService)
                .user(user)
                .getOriginalApiNameFunction((userParam, describeApiNameParam) -> changeOrderLogicService.findOriginalApiNameByChangeOrder(userParam, describeApiNameParam))
                .build()
                .getDetailLayout();
        if (isRefFieldConfig) {
            List<String> disabledComponentNames = EditLayout.of(editLayout).getComponentsWithField().stream()
                    .map(IComponent::getName)
                    .collect(toList());
            LayoutExt.of(detailLayout).getComponentsSilently().forEach(component -> {
                if (disabledComponentNames.contains(component.getName())) {
                    component.set(ComponentExt.IS_DISABLED, Boolean.TRUE);
                }
                if (ComponentExt.FORM_COMPONENT.equals(component.getName())) {
                    FormComponentExt.of((IFormComponent) component).setReferenceFieldConfig(editLayout.getName());
                }
            });
        }
        return detailLayout;
    }

    private void processButtonInfo(User user, IObjectDescribe describe, ILayout layout) {
        if (LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        Map<ButtonUsePageType, List<IButton>> customButtonMap = buttonLogicService.findButtonsByUsePages(user,
                describe, Lists.newArrayList(ButtonUsePageType.Create, ButtonUsePageType.Edit));
        EditLayout.of(layout).mergeDesignerLayoutButton(customButtonMap);
    }

    private ILayout copyLayout(LayoutContext context, ILayout detailLayout, List<ILayout> editLayoutList, IObjectDescribe objectDescribe,
                               List<IObjectDescribe> detailDescribes, ManageGroup manageGroup) {
        User user = context.getUser();
        try {
            //如果已经存在编辑页布局，则直接返回
            Optional<ILayout> dbEditLayout = editLayoutList.stream()
                    .filter(x -> x.getName().equals(LayoutExt.of(detailLayout).getDefaultEditLayoutName()))
                    .findFirst();
            if (dbEditLayout.isPresent()) {
                return dbEditLayout.get();
            }
            //把ui事件查出来
            completeLayoutWithEvents(detailLayout, user);
            //删除原默认布局上的UI事件与函数的引用关系
            List<IUIEvent> uiEvents = UIEventExt.ofList(detailLayout.getEvents()).stream()
                    .map(UIEventExt::toUIEvent).collect(Collectors.toList());
            functionLogicService.batchDeleteRelation(user, buildDeleteReferenceDataList(uiEvents));
            ILayout editLayout = InitialEditLayoutBuilder.builder()
                    .detailLayout(detailLayout)
                    .describe(objectDescribe)
                    .detailDescribes(detailDescribes)
                    .build()
                    .getLayout();
            // 开启新建编辑页布局，需要判断详情页布局是否开启了分管小组
            createLayout(context, editLayout, false, ManageGroup.support(manageGroup, detailLayout.getName()));
            syncMultiLanguageFromDetail(user, editLayout, detailLayout.getName(), objectDescribe);
            return editLayout;
        } catch (RuntimeException e) {
            log.error("copyLayout failed,tenantId:{},describeApiName:{},detailLayoutApiName:{}",
                    user.getTenantId(), detailLayout.getRefObjectApiName(), detailLayout.getName(), e);
            throw e;
        }
    }

    /**
     * 同步详情页布局的表格组件和文本框组件多语给新建编辑页
     *
     * @param user
     * @param editLayout
     * @param objectDescribe
     */
    private void syncMultiLanguageFromDetail(User user, ILayout editLayout, String originalLayoutApiName, IObjectDescribe objectDescribe) {
        LayoutExt layoutExt = LayoutExt.of(editLayout);
        List<IComponent> components = Lists.newArrayList();
        components.addAll(layoutExt.getFormTables());
        components.addAll(layoutExt.getTextComponents());
        components.addAll(layoutExt.getFormComponents());
        Set<String> keys = Sets.newHashSet();
        for (IComponent component : components) {
            keys.add(ComponentExt.getDetailComponentNameKey(objectDescribe.getApiName(), originalLayoutApiName, component.getName()));
            if (ComponentExt.of(component).isTextComponent()) {
                keys.add(ComponentExt.getTextComponentContentKey(objectDescribe.getApiName(), originalLayoutApiName, component.getName(), LayoutTypes.DETAIL));
            }
            if (ComponentExt.of(component).isFormType()) {
                FormComponentExt formComponent = (FormComponentExt) component;
                List<IFieldSection> fieldSections = formComponent.getFieldSections();
                for (IFieldSection fieldSection : fieldSections) {
                    keys.add(ComponentExt.getDetailLayoutGroupName(editLayout.getRefObjectApiName(), originalLayoutApiName, fieldSection.getName()));
                }

            }
        }
        Map<String, Localization> localizationMap = i18nSettingService.getLocalization(Lists.newArrayList(keys), user.getTenantId(), false, true);
        List<Localization> syncLocalization = Lists.newArrayList();
        localizationMap.forEach((k, localization) -> {
            if (Objects.isNull(localization) || StringUtils.isBlank(k)) {
                return;
            }
            String editKey = k.replace(originalLayoutApiName, editLayout.getName())
                    .replace("." + LayoutTypes.DETAIL + ".", "." + LayoutTypes.EDIT + ".");
            localization.setKey(editKey);
            syncLocalization.add(localization);
        });
        I18nClient.getInstance().save4Translate(user.getTenantIdInt(), syncLocalization, false);
    }

    private void copyLayoutRule(User user, List<LayoutRuleInfo> layoutRuleInfoList, ILayout detailLayout, ILayout editLayout) {
        if (CollectionUtils.empty(layoutRuleInfoList)) {
            return;
        }
        try {
            Set<String> dbEditLayoutRules = layoutRuleInfoList.stream()
                    .filter(x -> x.getLayoutApiName().equals(editLayout.getName()))
                    .map(LayoutRuleInfo::getApiName)
                    .collect(Collectors.toSet());
            List<LayoutRuleInfo> ruleInfosWithLayout = layoutRuleInfoList.stream()
                    .filter(x -> detailLayout.getName().equals(x.getLayoutApiName()))
                    .peek(x -> {
                        x.getContainerDocument().remove(LayoutRuleInfo.ID);
                        x.setApiName(LayoutRuleExt.of(x).getDefaultEditPageApiName());
                        x.setLabel(LayoutRuleExt.of(x).getDefaultEditPageLabel());
                        x.setLayoutApiName(editLayout.getName());
                    })
                    .filter(x -> !dbEditLayoutRules.contains(x.getApiName()))
                    .collect(toList());
            layoutRuleLogicService.batchCreateRule(user, ruleInfosWithLayout);
        } catch (RuntimeException e) {
            log.error("copyLayoutRule failed,tenantId:{},describeApiName:{},detailLayoutApiName:{}",
                    user.getTenantId(), detailLayout.getRefObjectApiName(), detailLayout.getName(), e);
            throw e;
        }
    }

    private List<RoleViewPojo> copyRoleViewList(List<RoleViewPojo> roleViewPojoList, ILayout detailLayout, ILayout editLayout) {
        if (CollectionUtils.empty(roleViewPojoList)) {
            return Lists.newArrayList();
        }
        return roleViewPojoList.stream()
                .filter(x -> detailLayout.getName().equals(x.getViewId()))
                .map(x -> {
                    RoleViewPojo roleViewPojo = new RoleViewPojo();
                    roleViewPojo.setTenantId(x.getTenantId());
                    roleViewPojo.setAppId(x.getAppId());
                    roleViewPojo.setRoleCode(x.getRoleCode());
                    roleViewPojo.setRecordTypeId(x.getRecordTypeId());
                    roleViewPojo.setEntityId(x.getEntityId());
                    roleViewPojo.setViewId(editLayout.getName());
                    roleViewPojo.setViewType(editLayout.getLayoutType());
                    return roleViewPojo;
                }).collect(toList());
    }

    private void saveRoleViewList(User user, List<RoleViewPojo> roleViewPojoList, String appId) {
        if (CollectionUtils.empty(roleViewPojoList)) {
            return;
        }
        recordTypeLogicService.upsertRoleViewList(user, roleViewPojoList, true, appId);
    }

    @Override
    public ILayout convertLayoutByPageType(User user, IObjectDescribe describe, ILayout layout, String pageType) {
        ListLayoutRender.builder()
                .describeExt(ObjectDescribeExt.of(describe))
                .listLayoutExt(ListLayoutExt.of(layout))
                .user(user)
                .customButtonService(customButtonService)
                .sceneLogicService(sceneLogicService)
                .buttonLogicService(buttonLogicService)
                .licenseService(licenseService)
                .layoutResourceService(layoutResourceService)
                .viewComponentFactory(viewComponentFactory)
                .optionalFeaturesService(optionalFeaturesService)
                .functionPrivilegeService(functionPrivilegeService)
                .layoutLogicService(this)
                .pageType(PageType.Designer)
                .renderPageType(pageType)
                .build()
                .render();
        // 转换接口，处理 EnableSelectedLayout 属性
        ListLayoutExt.of(layout).getFirstListComponent()
                .ifPresent(it -> it.setEnableSelectedLayout(IComponentInfo.PAGE_TYPE_SELECTED.equals(pageType)));
        return layout;
    }

    @Override
    public void handleSummaryKeyComponents(String tenantId, IObjectDescribe describe, ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        Map<String, Set<String>> referenceFieldMapping = Maps.newHashMap();
        layoutExt.getSummaryKeyComponentInfos().forEach(summaryKeyComponentInfo -> {
            List<IFormField> formFieldList = summaryKeyComponentInfo.getFieldSections().stream()
                    .filter(formField -> StringUtils.isNotEmpty(formField.getFieldName())
                            && formField.getFieldName().contains("__r."))
                    .collect(Collectors.toList());
            formFieldList.forEach(formField -> {
                String referenceFieldApiName = StringUtils.substringBefore(formField.getFieldName(), "__r.");
                String fieldApiName = StringUtils.substringAfter(formField.getFieldName(), "__r.");
                referenceFieldMapping.computeIfAbsent(referenceFieldApiName, x -> Sets.newHashSet()).add(fieldApiName);
            });
        });
        if (CollectionUtils.empty(referenceFieldMapping)) {
            return;
        }
        Set<String> lookupFieldApiNames = referenceFieldMapping.keySet();
        Map<String, String> fieldAndObjectApiNameMapping = describeExt.getLookupFieldDescribes().stream()
                .filter(fieldDescribe -> lookupFieldApiNames.contains(fieldDescribe.getApiName()))
                .collect(Collectors.toMap(ObjectReferenceWrapper::getApiName, ObjectReferenceWrapper::getTargetApiName));
        Map<String, IObjectDescribe> lookupObjectDescribes = describeLogicService.findObjectsWithoutCopyIfGray(tenantId, fieldAndObjectApiNameMapping.values());
        List<String> disableDeleteFields = Lists.newArrayList();
        lookupFieldApiNames.forEach(fieldApiName -> {
            String lookupObjectApiName = fieldAndObjectApiNameMapping.get(fieldApiName);
            IObjectDescribe objectDescribe = lookupObjectDescribes.get(lookupObjectApiName);
            Set<String> fieldApiNames = referenceFieldMapping.get(fieldApiName);
            if (Objects.isNull(objectDescribe) || !describeExt.getActiveFieldDescribeSilently(fieldApiName).isPresent()) {
                fieldApiNames.forEach(apiName -> disableDeleteFields.add(fieldApiName + "__r." + apiName));
            } else {
                fieldApiNames.forEach(apiName -> {
                    if (!ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribeSilently(apiName).isPresent()) {
                        disableDeleteFields.add(fieldApiName + "__r." + apiName);
                    }
                });
            }
        });
        if (CollectionUtils.empty(disableDeleteFields)) {
            return;
        }
        layoutExt.getSummaryKeyComponentInfos().forEach(summaryKeyComponentInfo -> {
            Set<String> needRemoveFieldApiNames = disableDeleteFields.stream()
                    .filter(fieldApiName -> summaryKeyComponentInfo.containsField(fieldApiName))
                    .collect(Collectors.toSet());
            summaryKeyComponentInfo.removeFields(needRemoveFieldApiNames);
        });
    }

    @Override
    public void processLayoutByDomainPlugin(String tenantId,
                                            IObjectDescribe describe,
                                            ILayout layout,
                                            List<String> recordTypeList,
                                            String pageType) {
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            MasterDetail masterDetail = ObjectDescribeExt.of(describe).getMasterDetailField().get();
            RecordTypeLayoutStructure layoutStructure = RecordTypeLayoutStructure.builder()
                    .detail_layout(LayoutExt.of(layout).toMap())
                    .build();
            DetailObjectListResult detailObject = DetailObjectListResult.builder()
                    .objectDescribe(ObjectDescribeExt.of(describe).toMap())
                    .objectApiName(describe.getApiName())
                    .fieldApiName(masterDetail.getApiName())
                    .layoutList(Lists.newArrayList(layoutStructure))
                    .build();
            DescribeDetailResult describeDetailResult = DescribeDetailResult.builder()
                    .detailObjectList(Lists.newArrayList(detailObject))
                    .build();
            processLayoutByDomainPlugin(tenantId, masterDetail.getTargetApiName(), describeDetailResult, recordTypeList, pageType);
        } else {
            DescribeDetailResult describeDetailResult = DescribeDetailResult.builder()
                    .objectDescribe(ObjectDescribeExt.of(describe).toMap())
                    .layout(LayoutExt.of(layout).toMap())
                    .build();
            processLayoutByDomainPlugin(tenantId, describe.getApiName(), describeDetailResult, recordTypeList, pageType);
        }
    }

    @Override
    public void processLayoutByDomainPlugin(String tenantId,
                                            String objectApiName,
                                            DescribeDetailResult describeDetailResult,
                                            List<String> recordTypeList,
                                            String pageType) {
        List<String> detailObjectApiNames = Lists.newArrayList();
        if (CollectionUtils.notEmpty(describeDetailResult.getDetailObjectList())) {
            describeDetailResult.getDetailObjectList().forEach(x -> detailObjectApiNames.add(x.getObjectApiName()));
        }
        DomainPluginLogicService.FieldConfig fieldConfig = domainPluginLogicService.findFieldConfigByDomainPlugin(tenantId, objectApiName,
                detailObjectApiNames, recordTypeList, pageType);
        processLayoutFields(describeDetailResult.getLayout(), fieldConfig.getReadOnlyFields(objectApiName),
                fieldConfig.getHiddenFields(objectApiName), pageType);
        if (CollectionUtils.notEmpty(describeDetailResult.getDetailObjectList())) {
            describeDetailResult.getDetailObjectList().forEach(x -> x.getLayoutList().forEach(layoutStructure -> {
                processLayoutFields(layoutStructure.getDetail_layout(), fieldConfig.getReadOnlyFields(x.getObjectApiName()),
                        fieldConfig.getHiddenFields(x.getObjectApiName()), pageType);
                processLayoutFields(layoutStructure.getList_layout(), fieldConfig.getReadOnlyFields(x.getObjectApiName()),
                        fieldConfig.getHiddenFields(x.getObjectApiName()), pageType);
            }));
        }
    }

    private void processLayoutFields(Map layout, Set<String> readOnlyFields, Set<String> hiddenFields, String pageType) {
        if (Objects.isNull(layout)) {
            return;
        }
        if (CollectionUtils.notEmpty(readOnlyFields) && (PageType.Add.name().equals(pageType) || PageType.Edit.name().equals(pageType))) {
            log.warn("processLayoutFields,objectApiName:{},layoutApiName:{},readOnlyFields:{}", LayoutExt.of(layout).getRefObjectApiName(),
                    LayoutExt.of(layout).getName(), readOnlyFields);
            LayoutExt.of(layout).setReadOnly(readOnlyFields, true);
        }
        if (CollectionUtils.notEmpty(hiddenFields)) {
            log.warn("processLayoutFields,objectApiName:{},layoutApiName:{},hiddenFields:{}", LayoutExt.of(layout).getRefObjectApiName(),
                    LayoutExt.of(layout).getName(), hiddenFields);
            LayoutExt.of(layout).removeFields(hiddenFields);
        }
    }

    @Override
    public List<Map<String, Object>> findLayoutTemplates(User user, String objectApiName, String business, Integer cardStyle) {
        return LayoutTemplatesConf.getLayoutTemplates(user, objectApiName, business, cardStyle);
    }

    @Override
    public void filterRelatedObjectStructure(User user, ILayout layout, List<RelatedObjectDescribeStructure> relatedObjectList) {
        if (CollectionUtils.empty(relatedObjectList)) {
            return;
        }
        List<String> relatedListComponentNames = LayoutExt.of(layout).getRelatedListComponentNames();
        List<String> relatedListConfigKeys = Lists.newArrayList();
        relatedObjectList.stream()
                .filter(lookupObject -> !relatedListComponentNames.contains(LayoutExt.getRelatedObjectComponentApiName(lookupObject)))
                .forEach(lookupObject -> relatedListConfigKeys.add(LayoutExt.getRelatedListIssueKey(lookupObject)));
        if (CollectionUtils.empty(relatedListConfigKeys)) {
            return;
        }
        if (!WebDetailLayout.of(layout).needDisplayNewlyRelatedListComponent()) {
            relatedObjectList.removeIf(x -> relatedListConfigKeys.contains(LayoutExt.getRelatedListIssueKey(x)));
            return;
        }
        Map<String, String> lookupObjectViewFlags = configService.queryTenantConfigs(user, relatedListConfigKeys);
        relatedObjectList.removeIf(x -> lookupObjectViewFlags.containsKey(LayoutExt.getRelatedListIssueKey(x)));

    }

    @Override
    public void bulkCreate(List<ILayout> layouts) {
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        try {
            layoutService.bulkCreate(layouts);
        } catch (MetadataServiceException e) {
            log.warn("bulkCreate error,layouts size:{}", layouts.size(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    @Transactional
    public void createOrUpdateLayoutForDevTools(String tenantId, String describeApiName, List<ILayout> layouts) {
        List<ILayout> layoutsInDB = findLayoutByObjectApiName(tenantId, describeApiName);
        layouts.forEach(layout -> {
            try {
                layout.setTenantId(tenantId);
                layout.setRefObjectApiName(describeApiName);
                if (CollectionUtils.notEmpty(layoutsInDB)) {
                    Optional<ILayout> oldLayoutOpt = layoutsInDB.stream().filter(x -> (layout.getLayoutType().equals(x.getLayoutType()) && x.getName().equals(layout.getName()))).findFirst();
                    if (oldLayoutOpt.isPresent()) {
                        ILayout oldLayout = oldLayoutOpt.get();
                        layout.setId(oldLayout.getId());
                        layout.setVersion(oldLayout.getVersion());
                        layoutService.replace(layout);
                    } else {
                        createLayoutAddManageGroup(User.systemUser(tenantId), layout, true);
                    }
                } else {
                    createLayoutAddManageGroup(User.systemUser(tenantId), layout, true);
                }
            } catch (MetadataServiceException e) {
                log.warn("createOrUpdateLayoutForDevTools error,layouts size:{}", layouts.size(), e);
                throw new MetaDataBusinessException(e);
            }
        });
    }

    @Override
    public ManageGroup queryLayoutManageGroup(User user, String describeApiName, String sourceInfo) {
        if (!ObjectListConfig.OBJECT_MANAGEMENT.equals(sourceInfo)) {
            return null;
        }
        return manageGroupService.queryManageGroup(user, describeApiName, ManageGroupType.LAYOUT);
    }

    @Override
    public void executeReferenceByLayout(RefMessage.ActionType actionType, IObjectDescribe sourceObjDesc, List<ILayout> sourceLayoutList) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_WHERE_A_FIELD_IS_USED_GRAY_KEY, sourceObjDesc.getTenantId()) || CollectionUtils.empty(sourceLayoutList)) {
            return;
        }
        Set<String> targetObjApiSet = ObjectDescribeExt.of(sourceObjDesc).findTargetObj();
        List<IObjectDescribe> targetObjList = describeLogicService.findObjectList(sourceObjDesc.getTenantId(), targetObjApiSet);
        List<IObjectDescribe> relatedObjList = describeLogicService.findLookupDescribes(sourceObjDesc.getTenantId(), sourceObjDesc.getApiName(), true);
        List<IObjectDescribe> detailObjList = describeLogicService.findDetailDescribes(sourceObjDesc.getTenantId(), sourceObjDesc.getApiName());
        List<IObjectDescribe> targetAndRelatedObjList = Lists.newArrayList();
        targetAndRelatedObjList.addAll(targetObjList);
        targetAndRelatedObjList.addAll(relatedObjList);
        targetAndRelatedObjList.addAll(detailObjList);
        Map<String, IObjectDescribe> allObjDesc = targetAndRelatedObjList.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity(), (a, b) -> a));
        List<Ref> allRefs = Lists.newArrayList();

        for (ILayout sourceLayout : sourceLayoutList) {
            Ref layoutRef = LayoutRef.buildRefByLayout(actionType, sourceLayout, sourceObjDesc, allObjDesc);
            allRefs.add(layoutRef);
        }

        Refs refs = Refs.builder().refs(allRefs).build();
        refFieldService.sendRefs(refs);
    }

    @Override
    @Transactional
    public void enableApplicationLayerLayoutByAppId(User user, String appId, IObjectDescribe objectDescribe) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, null);
        String describeApiName = objectDescribe.getApiName();
        List<ILayout> layoutList = findByTypes(layoutContext, describeApiName,
                Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT));

        Map<String, String> layoutNameMapping = Collections.emptyMap();
        if (isEditLayoutEnable(layoutContext, describeApiName, false)) {
            List<ILayout> editLayoutList = layoutList.stream().filter(x -> LayoutExt.of(x).isEditLayout()).collect(toList());
            layoutNameMapping = dealLayoutsAndRoleView(user, appId, objectDescribe, editLayoutList, LayoutTypes.EDIT, Collections.emptyMap());
        }
        List<ILayout> detailLayouts = layoutList.stream().filter(x -> LayoutExt.of(x).isDetailLayout()).collect(toList());
        dealLayoutsAndRoleView(user, appId, objectDescribe, detailLayouts, LayoutTypes.DETAIL, layoutNameMapping);
    }

    @Override
    @Transactional
    public void disableApplicationLayerLayoutByAppId(User user, String appId, IObjectDescribe objectDescribe) {
        LayoutContext layoutContext = LayoutContext.of(user, appId);
        String describeApiName = objectDescribe.getApiName();
        try {
            List<ILayout> layouts = findByTypes(layoutContext, describeApiName, Lists.newArrayList(LayoutTypes.EDIT, LayoutTypes.DETAIL));
            for (ILayout layout : layouts) {
                //默认布局需要先把is_default更新成true才能删除
                if (Boolean.TRUE.equals(layout.isDefault())) {
                    layout.setIsDefault(false);
                    layoutService.update(layout);
                }
                deleteLayout(user, layout);
            }
            deleteEditLayoutConfig(layoutContext, describeApiName);
            log.warn("disable application layer layout success,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("disable application layer layout failed,tenantId:{},describeApiName:{}", user.getTenantId(), describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void updateLayoutVersion(User user, String describeApiName, String layoutApiName) {
        try {
            layoutService.updateVersion(user.getTenantId(), describeApiName, layoutApiName);
        } catch (Exception e) {
            log.warn("updateLayoutVersion error,tenantId:{},describeApiName:{},layoutApiName:{}", user.getTenantId(), describeApiName, layoutApiName, e);
        }
    }

    /**
     * @param user
     * @param appId
     * @param objectDescribe
     * @param layouts
     * @param layoutType
     * @return 布局的apiName ，key为old，value为new
     */
    private Map<String, String> dealLayoutsAndRoleView(User user, String appId, IObjectDescribe objectDescribe, List<ILayout> layouts,
                                                       String layoutType, Map<String, String> layoutNameMapping) {
        LayoutContext layoutContext = LayoutContext.of(user, appId);
        String describeApiName = objectDescribe.getApiName();
        // 同步编辑页布局，需要开启下游的编辑页开关
        if (LayoutTypes.EDIT.equals(layoutType) && !Strings.isNullOrEmpty(appId)
                && !isEditLayoutEnable(layoutContext, describeApiName, false)) {
            updateEditLayoutConf(layoutContext, describeApiName);
        }
        List<RoleViewPojo> roleViewPojoList = recordTypeLogicService.findRoleViewList(user, describeApiName,
                layoutType, true, appId);

        Set<String> roleCodes = recordTypeLogicService.findRoleInfoByAppIdWithMangeGroup(user, null, describeApiName, appId).stream()
                .map(RoleInfoPojo::getRoleCode)
                .collect(toSet());
        List<RoleViewPojo> newRoleViewPojoList = Lists.newArrayList();
        Map<String, String> resultMap = Maps.newHashMap();
        layouts.forEach(layout -> {
            completeLayoutWithEvents(layout, user);
            // 创建布局
            LayoutExt newLayout = buildNewLayout(layout);
            Optional.of(newLayout)
                    .filter(LayoutExt::isDetailLayout)
                    .flatMap(LayoutExt::getFormComponent)
                    .ifPresent(it -> {
                        String oldLayoutName = it.getReferenceFieldConfig();
                        String newLayoutName = layoutNameMapping.get(oldLayoutName);
                        it.setReferenceFieldConfig(newLayoutName);
                    });
            createLayout(layoutContext, newLayout.getLayout());
            // 更新分配的业务类型视图
            List<RoleViewPojo> tmpRoleViewList = copyRoleViewList(roleViewPojoList, layout, newLayout);
            tmpRoleViewList.stream()
                    .filter(it -> roleCodes.contains(it.getRoleCode()))
                    .peek(it -> it.setAppId(appId))
                    .forEach(newRoleViewPojoList::add);
            resultMap.put(layout.getName(), newLayout.getName());
        });
        saveRoleViewList(user, newRoleViewPojoList, appId);
        return resultMap;
    }

    private LayoutExt buildNewLayout(ILayout layout) {
        ILayout newLayout = LayoutExt.of(layout).copy();
        newLayout.setName("layout_" + RandomStringUtils.randomAlphanumeric(7) + "__c");
        newLayout.setId(null);
        newLayout.setEvents(layout.getEvents());

        LayoutExt.of(newLayout).getFormComponent().ifPresent(it -> it.getReferenceFieldConfig());
        return LayoutExt.of(newLayout);
    }

    private String getAppId(LayoutContext context, String describeApiName, String layoutType) {
        String appId = context.getAppIdByLayoutType(layoutType);
        if (applicationLayeredGrayService.supportAppLayered(context.getUser(), appId, describeApiName)) {
            return appId;
        }
        return null;
    }

    public Map<String, List<String>> getAppIdByLayoutTypes(LayoutContext context, String describeApiName, Collection<String> layoutTypes) {
        Map<String, List<String>> resultMap = Maps.newHashMap();
        String defaultAppId = context.getAppId();
        boolean supportAppLayered = applicationLayeredGrayService.supportAppLayered(context.getUser(), defaultAppId, describeApiName);
        for (String layoutType : layoutTypes) {
            String appId = null;
            if (supportAppLayered) {
                appId = context.getAppIdByLayoutType(layoutType);
            }
            resultMap.computeIfAbsent(appId, key -> Lists.newArrayList()).add(layoutType);
        }
        return resultMap;
    }

    //基础业务对top_info脏数据兼容
    private ILayout dealDirtyTopInfo(ILayout iLayout) {
        LayoutExt layoutExt = LayoutExt.of(iLayout);
        if (layoutExt.isDetailLayout()) {
            layoutExt.getTopInfoComponents().forEach(x -> {
                TopInfoComponent topInfoComponent = x;
                List<IFormField> fieldSections = topInfoComponent.getFieldSections();
                if (CollectionUtils.notEmpty(fieldSections)) {
                    List<IFormField> newFieldSections = fieldSections.stream().filter(m -> {
                        FormFieldExt formFieldExt = FormFieldExt.of(m);
                        return formFieldExt.isNotEmpty();
                    }).collect(Collectors.toList());
                    topInfoComponent.setFieldSections(newFieldSections);
                }
            });
        }
        return layoutExt.getLayout();
    }

    private <T extends ILayout> void setLayoutsI18n(Collection<T> layouts) {
        layouts.forEach(this::setLayoutI18n);
    }

    /*
     * @Description: 设置布局的名称的多语
     */
    private void setLayoutI18n(ILayout iLayout) {
        if (Objects.isNull(iLayout)) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(iLayout);
        com.facishare.paas.appframework.metadata.layout.LayoutContext layoutContextUtil =
                com.facishare.paas.appframework.metadata.layout.LayoutContext.get();
        if (layoutContextUtil.needRemoveI18n()) {
            layoutExt.removeI18n();
            return;
        }
        String languageTag = RequestUtil.getLanguageTag();  // 检测license
        String tenantId = layoutExt.getTenantId();
        List<String> supportLanguageTags = i18nSettingService.getTenantSupportLanguageTags(tenantId);
        if (!supportLanguageTags.contains(languageTag)) {
            return;
        }

        String displayName = layoutExt.getDisplayName();
        List<I18nInfo> i18nInfos = CollectionUtils.nullToEmpty(layoutExt.getLayoutInfos());
        // 补充 i18n
        I18nInfo nameI18n = i18nInfos.stream()
                .filter(x -> Objects.equals(x.getApiName(), Layout.DISPLAY_NAME))
                .findFirst().orElse(null);
        // 如果没有, 读取配置补充
        if (Objects.isNull(nameI18n) || CollectionUtils.empty(nameI18n.getLanguageInfo())) {
            if (Objects.nonNull(nameI18n)) {
                i18nInfos.removeIf(x -> Objects.equals(x.getApiName(), Layout.DISPLAY_NAME)); // 移除原来空 LanguageInfo 的 I18nInfo, 防止出现两个
            }
            List<String> i18nKeys = LayoutPreNameConfig.getPreLayoutNameKeys(layoutExt);
            Optional<Localization> localizationOpt = i18nSettingService.getPreLocalization(displayName, tenantId, i18nKeys);
            nameI18n = I18nInfo.builder()
                    .apiName(Layout.DISPLAY_NAME)
                    .build();
            // 没有改过名
            if (localizationOpt.isPresent()) {
                nameI18n.convertLanguageInfo(localizationOpt.get());
            } else {
                nameI18n.setILanguageInfo(Maps.newHashMap());
            }
            i18nInfos.add(nameI18n);
            layoutExt.setI18nInfos(i18nInfos);
        }
        if (layoutContextUtil.noReplaceLayoutNameI18n()) {
            return;
        }
        String i18nDisPlayName = CollectionUtils.nullToEmpty(nameI18n.getLanguageInfo())
                .getOrDefault(languageTag, displayName);
        layoutExt.setDisplayName(i18nDisPlayName);
    }
}
