package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.FiltersComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.appframework.metadata.layout.factory.ViewComponentFactory;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.metadata.layout.strcuture.LayoutStructureExt;
import com.facishare.paas.appframework.metadata.layout.strcuture.StructureLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.webpage.customer.api.model.arg.GetHomePageFiltersArg;
import com.facishare.webpage.customer.api.model.result.GetHomePageFiltersResult;
import com.facishare.webpage.customer.api.service.WebPageService;
import com.fxiaoke.functions.utils.Maps;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * create by zhaoju on 2020/11/18
 */
@Slf4j
@Builder
public class ListLayoutRender {
    private ListLayoutExt listLayoutExt;
    private ObjectDescribeExt describeExt;
    private User user;

    private SceneLogicService sceneLogicService;
    private CustomButtonService customButtonService;
    private WebPageService webPageService;

    private ButtonLogicService buttonLogicService;
    private LicenseService licenseService;
    private LayoutLogicService layoutLogicService;

    private LayoutResourceService layoutResourceService;
    private ViewComponentFactory viewComponentFactory;
    private ListComponentFactory listComponentFactory;
    private OptionalFeaturesService optionalFeaturesService;
    private FunctionPrivilegeService functionPrivilegeService;

    private Map<String, Localization> localizationMap;
    /**
     * 进入页面 设计器、ListHeader、List等
     */
    private PageType pageType;
    private LayoutAgentType layoutAgentType;
    /**
     * list
     * selected
     * related
     * <p>
     * 默认按 大列表 list 处理
     */
    private String renderPageType;

    private boolean ignoreButton;

    public void render() {
        switch (pageType) {
            case Designer:
                renderDesigner();
                break;
            case ListHeader:
                renderListHeader();
                break;
            case ListLayout:
                renderListLayout();
                break;
            case List:
                renderList();
                break;
            default:
                break;
        }
    }

    private void renderBaseInfo() {
        // 暂时不考虑有多个 listComponent 的情况
        listLayoutExt.getFirstListComponent()
                .ifPresent(listComponentExt -> ListComponentRender.builder()
                        .listComponentExt(listComponentExt)
                        .user(user)
                        .describeExt(describeExt)
                        .sceneLogicService(sceneLogicService)
                        .customButtonService(customButtonService)
                        .buttonLogicService(buttonLogicService)
                        .licenseService(licenseService)
                        .optionalFeaturesService(optionalFeaturesService)
                        .layoutResourceService(layoutResourceService)
                        .viewComponentFactory(viewComponentFactory)
                        .ignoreButton(ignoreButton)
                        .isMobileLayout(isMobileLayout())
                        .isDesigner(pageType == PageType.Designer)
                        .renderPageType(renderPageType)
                        .functionPrivilegeService(functionPrivilegeService)
                        .build()
                        .render()
                );

        // 处理列表页布局多语
        resetHeaderByMultiLanguage();
    }


    private void dealTopNavigationMultiLanguage(StructureLayout topNavigation) {
        String transKey = ComponentExt.getTopNavigationKey(listLayoutExt.getRefObjectApiName(), listLayoutExt.getName(), topNavigation.getLayoutIndex());
        String transValue = "";
        if (Objects.nonNull(localizationMap) && Objects.nonNull(localizationMap.get(transKey))) {
            Localization localization = localizationMap.get(transKey);
            transValue = localization.get(I18N.getContext().getLanguage(), null);
        } else {
            transValue = I18NExt.getOnlyText(transKey);
        }
        if (StringUtils.isNotEmpty(transValue)) {
            topNavigation.setLabelPageName(transValue);
        }
    }

    private void resetHeaderByMultiLanguage() {
        Map<String, Boolean> stringBooleanMap = licenseService.existModule(user.getTenantId(), Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));
        Boolean existMultiLanguage = stringBooleanMap.get(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP);
        if (listLayoutExt.isEnableMultiPage()) {
            LayoutStructureExt layoutStructureExt = listLayoutExt.getLayoutStructureExt();
            for (StructureLayout structureLayout : layoutStructureExt.getPageLayout().getLayoutList()) {
                setComponentsHeader(structureLayout.getComponents(), existMultiLanguage);
                dealTopNavigationMultiLanguage(structureLayout);
            }
            return;
        }
        setComponentsHeader(listLayoutExt.toLayoutExt().getComponentsSilently(), existMultiLanguage);
    }

    private void setComponentsHeader(List<IComponent> components, boolean existMultiLanguage) {
        ComponentHeaderSetter.builder()
                .tenantId(user.getTenantId())
                .layoutType(LayoutTypes.LIST_LAYOUT)
                .layoutVersion(LayoutContext.get().getLayoutVersion())
                .components(components)
                .localizationMap(localizationMap)
                .objectApiName(listLayoutExt.getRefObjectApiName())
                .layoutApiName(listLayoutExt.getName())
                .existMultiLanguage(existMultiLanguage)
                .componentPreKeyMap(Optional.ofNullable(layoutLogicService).map(x -> x.findComponentPreKeys(components)).orElse(Maps.newHashMap()))
                .build()
                .reset();
    }

    private void renderDesigner() {
        renderBaseInfo();
        processMobileLayout();
    }

    private void renderListHeader() {
        if (isMobileLayout()) {
            replaceWithMobileLayout();
        }
        renderBaseInfo();
    }

    private void renderListLayout() {
        if (isMobileLayout()) {
            replaceWithMobileLayout();
        }
        removeChartComponentIfOuter();
        renderHomePageFilters();
        filterComponentsByFunctionCode();
        renderBaseInfo();
    }

    private void filterComponentsByFunctionCode() {
        List<IComponent> components = listLayoutExt.getAllComponents();
        List<String> componentNames = layoutLogicService.filterComponentsByFunctionCode(user, describeExt, pageType,
                isMobileLayout(), components);
        listLayoutExt.removeComponents(componentNames);
    }

    /**
     * 下游人员的请求,移除图表组件
     */
    private void removeChartComponentIfOuter() {
        if (!user.isOutUser() || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_USER_CHART_ACCESS_GRAY, user.getTenantId())) {
            return;
        }
        listLayoutExt.removeChartComponent();
    }


    private void processMobileLayout() {
        if (!listLayoutExt.isEnableMobileLayout() || isMobileLayout()) {
            return;
        }
        listLayoutExt.getMobileLayout().ifPresent(mobileLayout -> {
            ListLayoutRender.builder()
                    .describeExt(describeExt)
                    .listLayoutExt(mobileLayout)
                    .user(user)
                    .customButtonService(customButtonService)
                    .sceneLogicService(sceneLogicService)
                    .buttonLogicService(buttonLogicService)
                    .licenseService(licenseService)
                    .layoutAgentType(LayoutAgentType.MOBILE)
                    .layoutResourceService(layoutResourceService)
                    .viewComponentFactory(viewComponentFactory)
                    .listComponentFactory(listComponentFactory)
                    .optionalFeaturesService(optionalFeaturesService)
                    .build()
                    .renderButton();
            listLayoutExt.resetMobileLayout(mobileLayout);
        });
    }

    /**
     * 有图标组件或筛选器组件，就需要下发 filters 信息
     */
    private void renderHomePageFilters() {
        // 开启灰度后这里不需要查询 bi筛选器的个人配置
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BI_FILTER_FILTER_TRANSFER_COMPONENT_GRAY, user.getTenantId())) {
            return;
        }
        if (!listLayoutExt.needHomePageFilters()) {
            return;

        }
        HomePageFilters homePageFilters = getHomePageFilters();
        Map<String, Object> result = homePageFilters.getFiltersResult();
        Object filter = result.get(FiltersComponentExt.COMPONENT_TYPE_FILTERS);
        if (Objects.isNull(filter)) {
            return;
        }
        LayoutStructureExt layoutStructureExt = listLayoutExt.getLayoutStructureExt();
        if (!layoutStructureExt.isEnableMultiPage()) {
            listLayoutExt.setHomePageFilters(user, filter, homePageFilters.isDefaultFiltersFlag());
            return;
        }
        for (StructureLayout structureLayout : layoutStructureExt.getLayoutList()) {
            if (structureLayout.needHomePageFilters()) {
                structureLayout.setHomePageFilters(user, filter, homePageFilters.isDefaultFiltersFlag());
            }
        }
    }

    private HomePageFilters getHomePageFilters() {
        GetHomePageFiltersArg arg = new GetHomePageFiltersArg();
        arg.setEmployeeId(user.getUserIdInt());
        arg.setTenantId(user.getTenantIdInt());
        arg.setOuterTenantId(user.getOutTenantIdLong());
        arg.setOuterUserId(user.getOutUserIdLong());
        arg.setLayoutId(listLayoutExt.getId());
        try {
            GetHomePageFiltersResult filtersResult = webPageService.getHomePageFilters(user.getTenantId(), arg);
            return HomePageFilters.of(filtersResult.getFilters(), filtersResult.isDefaultFiltersFlag());
        } catch (Exception e) {
            log.error("getHomePageFilters error, ei:{}, user:{}", user.getTenantId(), user.getUserId(), e);
        }
        return HomePageFilters.of(Collections.emptyMap(), true);
    }

    private void renderList() {
        if (isMobileLayout()) {
            replaceWithMobileLayout();
        }
        renderButton();
    }

    private ListLayoutRender renderButton() {
        // 暂时不考虑有多个 listComponent 的情况
        ListComponentExt listComponentExt = listLayoutExt.getFirstListComponent().orElse(null);
        if (Objects.isNull(listComponentExt)) {
            return this;
        }
        ListComponentRender.builder()
                .listComponentExt(listComponentExt)
                .user(user)
                .describeExt(describeExt)
                .sceneLogicService(sceneLogicService)
                .customButtonService(customButtonService)
                .buttonLogicService(buttonLogicService)
                .licenseService(licenseService)
                .optionalFeaturesService(optionalFeaturesService)
                .layoutResourceService(layoutResourceService)
                .viewComponentFactory(viewComponentFactory)
                .functionPrivilegeService(functionPrivilegeService)
                .ignoreButton(ignoreButton)
                .isMobileLayout(isMobileLayout())
                .isDesigner(pageType == PageType.Designer)
                .renderPageType(renderPageType)
                .build()
                .render();
        return this;
    }

    private void replaceWithMobileLayout() {
        LayoutExt mobileLayout = getMobileLayout();
        listLayoutExt.syncComponentAndLayoutStructure(mobileLayout);
    }

    private LayoutExt getMobileLayout() {
        return MobileListLayoutBuilder.builder()
                .listLayout(LayoutExt.of(listLayoutExt.getLayout()))
                .pageType(pageType)
                .listComponentFactory(listComponentFactory)
                .build()
                .getMobileListLayout();
    }

    /**
     * 请求终端：移动端
     * arg -> "layoutAgentType": "mobile"
     * header "client-info": "iOSxxx" or "Androidxxx"
     *
     * @return
     */
    private boolean isMobileLayout() {
        return LayoutContext.isMobileLayout() || LayoutAgentType.MOBILE == layoutAgentType;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    private static class HomePageFilters {
        private Map<String, Object> filtersResult;
        private boolean defaultFiltersFlag;
    }
}
