package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;

import java.util.List;

public interface ReferenceLogicService {

    List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue);

    List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue, boolean isPrefixMatch);

    List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue, boolean isPrefixMatch, int limit);

    void deleteAndCreateReference(String tenantId, List<ReferenceData> items);

    void deleteReference(String tenantId, String sourceType, String sourceValue);

    List<ReferenceData> queryByTargetList(String tenantId, List<String> targetValues);
}
