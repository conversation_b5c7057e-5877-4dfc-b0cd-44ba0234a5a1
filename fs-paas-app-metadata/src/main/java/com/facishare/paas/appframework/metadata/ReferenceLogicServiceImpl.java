package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.ReferenceServiceProxy;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * Created by liwei on 2019/2/25
 */
@Slf4j
@Service("referenceLogicService")
public class ReferenceLogicServiceImpl implements ReferenceLogicService {
    @Autowired
    private ReferenceServiceProxy referenceServiceProxy;

    @Override
    public List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue) {
        return findReferenceByTarget(tenantId, targetType, targetValue, false);
    }

    @Override
    public List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue, boolean isPrefixMatch) {
        return findReferenceByTarget(tenantId, targetType, targetValue, isPrefixMatch, 1000);
    }

    @Override
    public List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue, boolean isPrefixMatch, int limit) {
        HashMap<Object, Object> pathMap = Maps.newHashMap();
        pathMap.put("tenantId", tenantId);
        FindReferenceByTarget.Result result = referenceServiceProxy.findByTarget(pathMap, targetType, targetValue,
                String.valueOf(isPrefixMatch), String.valueOf(limit));
        return result.getValues();
    }

    @Override
    public void deleteAndCreateReference(String tenantId, List<ReferenceData> items) {
        if (CollectionUtils.empty(items)) {
            return;
        }
        HashMap<Object, Object> pathMap = Maps.newHashMap();
        pathMap.put("tenantId", tenantId);
        DeleteAndCreateReference.Arg arg = DeleteAndCreateReference.Arg.builder().items(items).build();
        DeleteAndCreateReference.Result result = referenceServiceProxy.deleteAndCreate(pathMap, arg);
        log.debug("deleteAndCreateReference tenantId:{},arg:{},result:{}", tenantId, items, result);
    }

    @Override
    public void deleteReference(String tenantId, String sourceType, String sourceValue) {
        HashMap<Object, Object> pathMap = Maps.newHashMap();
        pathMap.put("tenantId", tenantId);
        DeleteReference.Result result = referenceServiceProxy.deleteReference(tenantId, sourceType, sourceValue);
        log.debug("deleteReference tenantId:{},sourceType:{},sourceValue:{},result:{}", tenantId, sourceType, sourceValue, result);
    }

    @Override
    public List<ReferenceData> queryByTargetList(String tenantId, List<String> targetValues) {
        if (CollectionUtils.empty(targetValues)) {
            return Lists.newArrayList();
        }
        
        QueryByTargetList.Arg arg = QueryByTargetList.Arg.builder()
                .targetType(TargetTypes.FUNCTION)
                .targetValues(targetValues)
                .limit(200)
                .build();
                
        QueryByTargetList.Result result = referenceServiceProxy.queryByTargetList(tenantId, arg);
        return CollectionUtils.nullToEmpty(result.getValues());
    }
}
