package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.dto.scene.SceneExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldListConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.api.service.IFieldListConfigService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldListConfig;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.invisibleFieldNameListForListLayout;
import static com.facishare.paas.appframework.common.util.CollectionUtils.sortByGivenOrder;

/**
 * Created by zhouwr on 2017/10/20
 */
@Slf4j
@Service("dataListHeaderConfigService")
public class DataListHeaderConfigServiceImpl implements DataListHeaderConfigService {

    @Autowired
    private IFieldListConfigService fieldListConfigService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private RedissonService redissonService;

    @Override
    public List<Map<String, Object>> findFieldListConfig(User user, String objectApiName, String extendAttribute) {
        IFieldListConfig fieldListConfig;
        try {
            fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(user.getTenantId(), user.getUserId(),
                    objectApiName, extendAttribute, SceneExt.getOutTenantInfo(user).getMetadataContext());
        } catch (MetadataServiceException e) {
            log.error("findFieldListConfig error,user:{},objectApiName:{}", user, objectApiName, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (fieldListConfig == null || fieldListConfig.getFieldList() == null) {
            return Lists.newArrayList();
        }
        return fieldListConfig.getFieldList();
    }

    @Override
    public List<Tuple<String, Number>> findFieldWidthConfig(User user, String objectApiName, String extendAttribute) {
        IFieldListConfig fieldListConfig;
        try {
            fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(user.getTenantId(), user.getUserId(),
                    objectApiName, extendAttribute, SceneExt.getOutTenantInfo(user).getMetadataContext());
        } catch (MetadataServiceException e) {
            log.warn("findFieldListConfig error,user:{},objectApiName:{}", user, objectApiName, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        return getFieldWidthInConfig(fieldListConfig);
    }

    private List<Tuple<String, Number>> getFieldWidthInConfig(IFieldListConfig fieldListConfig) {
        if (Objects.isNull(fieldListConfig) || (Objects.isNull(fieldListConfig.getFieldList()) && Objects.isNull(fieldListConfig.getFieldWidth()))) {
            return Lists.newArrayList();
        }
        if (Objects.nonNull(fieldListConfig.getFieldWidth())) {
            // 将库中的值转换一下
            return fieldListConfig.getFieldWidth().stream()
                    .map(fieldList -> fieldList.entrySet().stream().findFirst().map(x -> Tuple.of(x.getKey(), (Number) x.getValue())))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
        }
        // 将存在FieldList中的历史值转换一下
        return fieldListConfig.getFieldList().stream()
                .map(LayoutRuleExt.FieldConfig::fromMap)
                .map(fieldConfig -> Tuple.of(fieldConfig.getTuple().getKey(), fieldConfig.getWidth()))
                .collect(Collectors.toList());
    }

    @Override
    public IFieldListConfig createFieldListConfig(User user, String objectDescribeApiName,
                                                  List<Map<String, Object>> fieldList,
                                                  String whatApiName,
                                                  String extendAttribute,
                                                  CommonFilterField filterFields,
                                                  Boolean convertTopListFilter) {
        // 使用数据库的分布式锁，避免重复创建
        RLock rLock = getLock(user, objectDescribeApiName);
        try {
            MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
            IFieldListConfig find = findFieldListConfig(user, objectDescribeApiName, whatApiName, extendAttribute, context);
            if (find == null) {
                IFieldListConfig config = new FieldListConfig();
                config.setTenantId(user.getTenantId());
                config.setUserId(user.getUserId());
                config.setDescribeApiName(objectDescribeApiName);
                config.setFieldList(fieldList);
                config.setCreateTime(System.currentTimeMillis());
                config.setLastModifiedTime(System.currentTimeMillis());
                if (user.isOutUser()) {
                    config.setOutTenantId(user.getOutTenantId());
                    config.setOutUserId(user.getOutUserId());
                }
                if (!Strings.isNullOrEmpty(whatApiName)) {
                    config.setWhatDescribeApiName(whatApiName);
                }
                if (!Strings.isNullOrEmpty(extendAttribute)) {
                    config.setExtendAttribute(extendAttribute);
                }
                if (Objects.nonNull(filterFields) && !filterFields.isFieldListNull()) {
                    config.setFilterFields(filterFields.toMap());
                }
                config.setConvertTopListFilter(convertTopListFilter);
                return fieldListConfigService.create(config, context);
            } else {
                if (CollectionUtils.notEmpty(fieldList)) {
                    find.setFieldList(fieldList);
                }
                if (Objects.nonNull(filterFields) && !filterFields.isFieldListNull()) {
                    find.setFilterFields(JSON.parseObject(JSON.toJSONString(filterFields), Map.class));
                }
                if (!Strings.isNullOrEmpty(whatApiName)) {
                    find.setWhatDescribeApiName(whatApiName);
                }
                if (Objects.nonNull(convertTopListFilter)) {
                    find.setConvertTopListFilter(convertTopListFilter);
                }
                return fieldListConfigService.update(find, context);
            }
        } catch (MetadataServiceException e) {
            log.error("createFieldListConfig error,user:{},objectDescribeApiName:{},fieldList:{}", user, objectDescribeApiName, fieldList);
            throw new MetaDataBusinessException(e.getMessage());
        } finally {
            unLock(rLock);
        }
    }

    public void unLock(RLock rLock) {
        redissonService.unlock(rLock);
    }

    public RLock getLock(User user, String objectDescribeApiName) {
        String templateKey = String.format("%s_%s_%s", SearchTemplateLogicServiceImpl.TEMPLATE_PRE, objectDescribeApiName, user.getUserIdOrOutUserIdIfOutUser());
        return redissonService.tryLock(user, objectDescribeApiName, templateKey);
    }

    private IFieldListConfig findFieldListConfig(User user, String objectDescribeApiName, String whatApiName, String extendAttribute, MetadataContext context) {
        try {
            if (Strings.isNullOrEmpty(whatApiName)) {
                return fieldListConfigService.findByDescribeApiNameAndExtendAttribute(user.getTenantId(),
                        user.getUserId(), objectDescribeApiName, extendAttribute, context);
            }
            return fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(user.getTenantId(),
                    user.getUserId(), objectDescribeApiName, whatApiName, context);
        } catch (MetadataServiceException e) {
            log.error("findFieldListConfig error,user:{}, objectDescribeApiName:{},whatApiName:{}, extendAttribute:{}",
                    user, objectDescribeApiName, whatApiName, extendAttribute, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public IFieldListConfig createOrUpdateFieldWidthConfig(User user, String objectDescribeApiName,
                                                           List<Map<String, Object>> fieldWidth,
                                                           String extendAttribute, CommonFilterField filterFields) {
        // 使用数据库的分布式锁，避免重复创建
        RLock rLock = getLock(user, objectDescribeApiName);
        try {
            MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
            IFieldListConfig fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(user.getTenantId(), user.getUserId(),
                    objectDescribeApiName, extendAttribute, context);
            if (Objects.isNull(fieldListConfig)) {
                IFieldListConfig config = new FieldListConfig();
                config.setTenantId(user.getTenantId());
                config.setUserId(user.getUserId());
                config.setDescribeApiName(objectDescribeApiName);
                config.setCreateTime(System.currentTimeMillis());
                config.setLastModifiedTime(System.currentTimeMillis());
                config.setExtendAttribute(extendAttribute);
                config.setFieldWidth(fieldWidth);
                if (Objects.nonNull(filterFields) && !filterFields.isFieldListNull()) {
                    config.setFilterFields(filterFields.toMap());
                }
                if (user.isOutUser()) {
                    config.setOutTenantId(user.getOutTenantId());
                    config.setOutUserId(user.getOutUserId());
                }
                return fieldListConfigService.create(config, context);
            }

            Set<String> keys = Sets.newHashSet();
            if (CollectionUtils.notEmpty(fieldWidth)) {
                keys.add(IFieldListConfig.FIELD_WIDTH);
                fieldListConfig.setFieldWidth(fieldWidth);
            }
            if (Objects.nonNull(filterFields) && !filterFields.isFieldListNull()) {
                keys.add(IFieldListConfig.FILTER_FIELDS);
                fieldListConfig.setFilterFields(JSON.parseObject(JSON.toJSONString(filterFields), Map.class));
            }
            // 增量更新
            return fieldListConfigService.partialUpdate(fieldListConfig, keys, context);
        } catch (MetadataServiceException e) {
            log.error("createFieldListConfig error,user:{},objectDescribeApiName:{},fieldWidth:{}", user, objectDescribeApiName, fieldWidth);
            throw new MetaDataBusinessException(e.getMessage());
        } finally {
            unLock(rLock);
        }
    }

    @Override
    public List<Map<String, Object>> findFieldListConfigWithWhatField(User user, String objectApiName, String whatApiName) {
        IFieldListConfig fieldListConfig;
        try {
            fieldListConfig = fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
                    user.getTenantId(), user.getUserId(), objectApiName, whatApiName);
        } catch (MetadataServiceException e) {
            log.error("findFieldListConfigWithWhatField error,user:{},objectApiName:{}, what:{}", user, objectApiName, whatApiName, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (fieldListConfig == null || fieldListConfig.getFieldList() == null) {
            return Lists.newArrayList();
        }
        return fieldListConfig.getFieldList();
    }

    @Override
    public List<Map<String, Object>> findFieldListConfigWithWhatField(User user, String objectApiName, String whatApiName, String sessionKey) {
        Objects.requireNonNull(user, "user");
        Objects.requireNonNull(objectApiName, "objectApiName");
        Objects.requireNonNull(whatApiName, "whatApiName");
        IFieldListConfig fieldListConfig;
        try {
            MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
            if (Strings.isNullOrEmpty(sessionKey)) {
                fieldListConfig = fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(user.getTenantId(),
                        user.getUserId(), objectApiName, whatApiName, context);
            } else {
                fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(user.getTenantId(),
                        user.getUserId(), objectApiName, String.format("%s_%s", whatApiName, sessionKey), context);
            }
        } catch (MetadataServiceException e) {
            log.error("findFieldListConfigWithWhatField error,user:{},objectApiName:{}, whatApiName:{}, extendAttribute:{}",
                    user, objectApiName, whatApiName, sessionKey, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (fieldListConfig == null || fieldListConfig.getFieldList() == null) {
            return Lists.newArrayList();
        }
        return fieldListConfig.getFieldList();
    }

    @Override
    public List<String> findFilterFields(User user, String objectApiName, String extendAttribute) {
        IFieldListConfig fieldListConfig;
        try {
            MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
            fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
                    user.getTenantId(), user.getUserId(), objectApiName, Strings.isNullOrEmpty(extendAttribute) ? null : extendAttribute, context);
        } catch (MetadataServiceException e) {
            log.error("findFilterFields error,user:{},objectApiName:{}, extendAttribute:{}",
                    user, objectApiName, extendAttribute, e);
            throw new MetaDataBusinessException(e.getMessage());
        }

        if (Objects.nonNull(fieldListConfig)) {
            CommonFilterField field = CommonFilterField.fromMap(fieldListConfig.getFilterFields());
            return field.getFieldList();
        }
        return Lists.newArrayList();
    }

    @Override
    public Boolean convertTopListFilter(User user, String objectApiName, String extendAttribute) {

        try {
            MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
            IFieldListConfig fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
                    user.getTenantId(), user.getUserId(), objectApiName, Strings.isNullOrEmpty(extendAttribute) ? null : extendAttribute, context);

            return Optional.ofNullable(fieldListConfig).map(IFieldListConfig::getConvertTopListFilter).orElse(null);
        } catch (MetadataServiceException e) {
            log.error("convertTopListFilter error,user:{},objectApiName:{}, extendAttribute:{}",
                    user, objectApiName, extendAttribute, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<CommonFilterField.FilterField> findFilterFields(User user, IObjectDescribe describe, String extendAttribute, String whatObjectApiName, ILayout layout) {
        if (Objects.isNull(describe)) {
            return Lists.newArrayList();
        }
        MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
        IFieldListConfig fieldListConfig = findFieldListConfig(user, describe.getApiName(), whatObjectApiName, extendAttribute, context);
        List<String> config;
        if (Objects.isNull(fieldListConfig)) {
            config = Lists.newArrayList();
        } else {
            CommonFilterField field = CommonFilterField.fromMap(fieldListConfig.getFilterFields());
            config = field.getFieldList();
        }


        List<CommonFilterField.FilterField> fieldList = parseFilterableFields(describe, config, false);
        if (!Strings.isNullOrEmpty(whatObjectApiName)) {
            IObjectDescribe whatObject = describeLogicService.findObjectUseThreadLocalCache(user.getTenantId(), whatObjectApiName);
            List<CommonFilterField.FilterField> whatFields = parseFilterableFields(whatObject, config, true);
            fieldList.addAll(CollectionUtils.nullToEmpty(whatFields));
        }

        fillDefaultShowField(layout, fieldList);

        return sortByGivenOrder(fieldList, config, CommonFilterField.FilterField::getFieldName);
    }

    private String calExtendAttribute(String extendAttribute, String whatObjectApiName) {
        String findAttribute = null;
        if (!Strings.isNullOrEmpty(whatObjectApiName) && !Strings.isNullOrEmpty(extendAttribute)) {
            //目前业务不会进入这里
            findAttribute = whatObjectApiName + extendAttribute;
            log.warn("should not in here, ");
        } else if (!Strings.isNullOrEmpty(extendAttribute)) {
            findAttribute = extendAttribute;
        } else if (!Strings.isNullOrEmpty(whatObjectApiName)) {
            findAttribute = whatObjectApiName;
        }
        return findAttribute;
    }


    private List<CommonFilterField.FilterField> parseFilterableFields(IObjectDescribe describe, List<String> config, boolean isWhatObject) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        Optional<MasterDetail> masterDetail = objectDescribeExt.getMasterDetailField();
        Set<String> toRemoveFields = invisibleFieldNameListForListLayout.getOrDefault(objectDescribeExt.getApiName(), Sets.newHashSet());

        return objectDescribeExt.getFilterableFields()
                .stream()
                .filter(field -> !FieldDescribeExt.CANNOT_FILTER_FIELDS.contains(field.getApiName()))
                .filter(field -> (!masterDetail.isPresent() || !ObjectDataExt.RELEVANT_TEAM.equals(field.getApiName())))
                .filter(field -> !toRemoveFields.contains(field.getApiName()))
                .map(field -> getObjectField(describe.getApiName(), field.getApiName(), isWhatObject))
                .map(fieldName -> CommonFilterField.FilterField.builder().fieldName(fieldName)
                        .isShow(config.contains(fieldName)).build())
                .collect(Collectors.toList());
    }

    private String getObjectField(String objectApiName, String fieldApiName, boolean isWhatObject) {
        return isWhatObject ? String.format("%s.%s", objectApiName, fieldApiName) : fieldApiName;
    }

    private void fillDefaultShowField(ILayout layout, List<CommonFilterField.FilterField> fieldList) {
        if (fieldList.stream().noneMatch(a -> BooleanUtils.isTrue(a.getIsShow())) && Objects.nonNull(layout)
                && LayoutExt.of(layout).getTableComponent().isPresent()) {
            //按照列表布局配置的字段，补充默认的筛选字段
            List<CommonFilterField.FilterField> showFields = Lists.newArrayList();
            LayoutExt.of(layout).getTableComponent().ifPresent(tableComponent -> tableComponent.getIncludeFields()
                    .forEach(f -> {
                        Optional<CommonFilterField.FilterField> field = fieldList.stream()
                                .filter(a -> Objects.equals(a.getFieldName(), f.getName()))
                                .findFirst();
                        if (field.isPresent()) {
                            field.get().setIsShow(true);
                            showFields.add(field.get());
                        }
                    })
            );
            fieldList.removeAll(showFields);
            fieldList.addAll(0, showFields);
        }
    }

    @Override
    public Map<String, List<Map>> findFieldWidthConfigs(User user, List<String> objectApiNames) {
        if (CollectionUtils.empty(objectApiNames)) {
            return Collections.emptyMap();
        }
        Map<String, IFieldListConfig> configMap;
        try {
            configMap = fieldListConfigService.findByDescribeApiNames(user.getTenantId(), user.getUserId(), objectApiNames);
        } catch (MetadataServiceException e) {
            log.warn("findFieldWidthConfigs error, tenantId:{}, user:{}, objectApiNames:{}", user.getTenantId(), user, objectApiNames, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (CollectionUtils.empty(configMap)) {
            return Collections.emptyMap();
        }
        Map<String, List<Map>> resultMap = Maps.newHashMap();
        configMap.forEach((objectApiName, fieldListConfig) -> {
            List<Tuple<String, Number>> fieldWidthConfig = getFieldWidthInConfig(fieldListConfig);
            List<Map> list = CollectionUtils.nullToEmpty(fieldWidthConfig).stream()
                    .map(x -> HeadField.fromFieldNameAndWidth(x.getKey(), x.getValue()).toMap())
                    .collect(Collectors.toList());
            resultMap.put(objectApiName, list);
        });
        return resultMap;
    }

    @Override
    public void deleteByUserObjApiName(User user, String objApiName) {
        try {
            ObjectUtils.requireNotEmpty(objApiName, I18NKey.PARAM_ERROR);
            if (user.isOutUser()) {
                fieldListConfigService.deleteByTenantIdAndApiName40ut(user.getTenantId(), user.getOutTenantId(), user.getOutUserId(), objApiName);
            } else {
                fieldListConfigService.deleteByTenantIdAndApiName(user.getTenantId(), user.getUserId(), objApiName);
            }
        } catch (MetadataServiceException e) {
            log.error("deleteFieldListConfig error, user:{}, objApiName:{}", user, objApiName, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public void wipeSettingsByObjApiNameAndUser(Set<String> settingNames, User user, String objAPiName, String extendAttribute) {
        MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
        IFieldListConfig fieldListConfig = null;
        try {
            fieldListConfig = fieldListConfigService.findByDescribeApiNameAndExtendAttribute(user.getTenantId(), user.getUserId(),
                    objAPiName, extendAttribute, context);
            if (Objects.nonNull(fieldListConfig) && CollectionUtils.notEmpty(settingNames)) {
                for (String settingName : settingNames) {
                    fieldListConfig.set(settingName, null);
                }
                fieldListConfigService.partialUpdate(fieldListConfig, settingNames, context);
            }
        } catch (MetadataServiceException e) {
            log.warn("wipeSettingsByObjApiNameAndUser error user:{}, objApi:{}, extendAttribute:{}", user, objAPiName, extendAttribute, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void wipePersonalSettings(User user, String objectDescribeApiName, String whatApiName, String extendAttribute) {
        MetadataContext context = SceneExt.getOutTenantInfo(user).getMetadataContext();
        IFieldListConfig fieldListConfig = findFieldListConfig(user, objectDescribeApiName, whatApiName, extendAttribute, context);
        if (Objects.isNull(fieldListConfig)) {
            return;
        }
        fieldListConfig.setFieldList(null);
        fieldListConfigService.partialUpdate(fieldListConfig, Lists.newArrayList(IFieldListConfig.FIELD_LIST), context);
    }
}
