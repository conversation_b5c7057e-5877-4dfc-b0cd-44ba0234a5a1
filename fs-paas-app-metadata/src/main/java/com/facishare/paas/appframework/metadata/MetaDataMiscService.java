package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptStatusEnum;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.appframework.metadata.publicobject.dto.ConnectedEnterpriseDTO;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 元数据服务Action的接口
 * 包括:
 * <p>
 * Created by yusb on 2017/12/14.
 */
public interface MetaDataMiscService {

    void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    void fillObjectDataObjectManyWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                     Map<String, List<IObjectData>> refObjectDataMap);

    void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                     Map<String, List<IObjectData>> refObjectDataMap, boolean handleSFAObject);

    void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                     Map<String, List<IObjectData>> refObjectDataMap, boolean handleSFAObject, List<IFieldDescribe> refFieldList);

    void fillUserInfoExcludeSingleValue(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    void fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    Map<String, UserInfo> fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user, boolean extractExtendInfo);

    Map<String, UserInfo> fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                       boolean extractExtendInfo, Collection<String> employeeFields);

    void fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    void fillDepartmentInfoExcludeSingleValue(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    Map<String, QueryDeptInfoByDeptIds.DeptInfo> fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                                                    DeptStatusEnum deptStatus, boolean extractExtendInfo);

    Map<String, QueryDeptInfoByDeptIds.DeptInfo> fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList,
                                                                    User user, DeptStatusEnum deptStatus,
                                                                    boolean extractExtendInfo,
                                                                    Collection<String> departmentFields);

    void fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user, DeptStatusEnum deptStatus);

    void fillButtonInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user);

    Map<String, ApprovalFlowStartResult> batchStartApproval(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                                            Map<String, Map<String, Object>> dataMap);

    Map<String, ApprovalFlowStartResult> batchStartApproval(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                                            Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap,
                                                            Map<String, Object> freeApprovalDef);

    Map<String, ApprovalFlowStartResult> batchStartApproval(String type, User user, List<IObjectData> objectDataList,
                                                            Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap);

    /**
     * 批量异步触发审批流
     *
     * @param type           触发类型
     * @param user           用户信息
     * @param objectDataList 需要触发审批流的数据列表
     * @param dataMap        详情页展示的字段变更详情
     */
    void batchStartApprovalAsynchronous(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                        Map<String, Map<String, Object>> dataMap);

    /**
     * 批量异步触发审批流
     *
     * @param type            触发类型
     * @param user            用户信息
     * @param objectDataList  需要触发审批流的数据列表
     * @param dataMap         详情页展示的字段变更详情
     * @param callbackDataMap 审批流回调携带的数据信息
     */
    void batchStartApprovalAsynchronous(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                        Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap);

    void doDataPrivilegeCheck(User user, List<IObjectData> dataList, IObjectDescribe describe, String actionCode);

    /**
     * 校验业务权限（包含功能权限、数据权限、业务自身权限）
     */
    Map<String, Permissions> checkPrivilege(User user, List<IObjectData> dataList, IObjectDescribe describe, String actionCode);

    //void checkLookupRolesConfig(User user, IObjectDescribe describe, IObjectData objectData);

    //boolean isAnyMatchLookupRoles(User user, IObjectDescribe objectDescribe, IObjectData objectData, Map<String, List<String>> apiNameAndIdsMap);

    Map<String, Permissions> checkDataPrivilege(User user, List<IObjectData> dataList, IObjectDescribe describe, String actionCode);

    Map<String, Map<String, Permissions>> checkDataPrivilege(User user, List<IObjectData> dataList, IObjectDescribe describe, List<String> actionCodes);

    Map<String, Permissions> checkDataPrivilege(User user, List<String> idList, IObjectDescribe describe);

    boolean canUpdateAccountName(String tenantId);

    void validateLookupData(User user, IObjectData objectData, ObjectReferenceWrapper referenceField);

    void validateLookupData(User user, IObjectData objectData, ObjectReferenceFieldDescribe referenceField);

    void validateLookupDataIgnorePolygonal(User user, IObjectData objectData, ObjectReferenceWrapper referenceField);

    void validateLookupDataIgnorePolygonal(User user, IObjectData objectData, IObjectReferenceField referenceField);

    void validateLookupData(User user, List<IObjectData> objectData, IObjectDescribe objectDescribe);

    String generateId();

    String generateTemplate(User user, String describeLabel, List<Pair<String, IFieldDescribe>> rowList, List<List<String>> sampleList);

    /**
     * 联合导入模板生成
     */
    Workbook generateUnionTemplate(User user, String describeLabel, List<Pair<String, IFieldDescribe>> rowList,
                                   List<List<String>> sampleList, Workbook workbook);

    /**
     * 生成导入模板路径
     */
    String generatePath(Workbook workbookResult, String ea, String userId) throws IOException;

    /**
     * 联合导入，对sheet进行排序
     */
    void orderSheets(Workbook workbook);

//    Map<String, Object> getUpdatedFieldValueMapFromUpdateObjectData(User user, IObjectData objectData, IObjectDescribe objectDescribe);

    Map<String, Map<String, Object>> getUpdatedFieldValueMapFromUpdateObjectDataList(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe);

    /**
     * 查询RecordName
     */
    List<INameCache> findRecordName(IActionContext context, String describeApiName, List<String> ids);

    Map<String, String> findNameByIds(User user, String describeApiName, List<String> ids);

    String getCountryCascadeJsonString();

    void fillPhoneNumberInformation(IObjectDescribe describe, IObjectData objectData);

    void fillPhoneNumberInformation(IObjectDescribe describe, List<IObjectData> objectDataList);

    /**
     * 特殊处理回款对象的orderName字段
     */
    void parsePaymentObjOrderNames(IObjectDescribe describe, List<IObjectData> dataList, User user, boolean isInList);

    /**
     * 给数据填充国家省市区的label
     */
    void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList);

    void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList, User user);

    void fillImageInformation(IObjectDescribe describe, List<IObjectData> dataList, User user);

    void fillImageInformation(IObjectDescribe describe, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData,
                              Map<String, IObjectDescribe> detailDescribeMap, User user);

    void fillMaskFieldValue(User user, List<IObjectData> dataList, IObjectDescribe describe);

    void fillMaskFieldValue(User user, List<IObjectData> dataList, IObjectDescribe describe, boolean removeOrigValue);

    void fillMaskFieldValue(User user, List<IObjectData> dataList, IObjectDescribe describe,
                            boolean removeOrigValue, boolean overrideOrigValue);

    void fillMaskFieldValue(User user, List<IObjectData> dataList, List<IFieldDescribe> maskFields, boolean removeOrigValue);

    void fillMaskFieldValue(User user, List<IObjectData> dataList, List<IFieldDescribe> fields, boolean removeOrigValue, boolean overrideOrigValue);

    void fillMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                            Map<String, IObjectDescribe> describeMap, boolean removeOrigValue);

    void removeMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                              Map<String, IObjectDescribe> describeMap);

    void fillDimensionFieldValue(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList);

    void fillDimensionFieldValue(User user, List<IFieldDescribe> dimensionFields, List<IObjectData> dataList);

    void fillRichTextImageInfo(IObjectDescribe describe, List<IObjectData> dataList, User user);

    void fillCurrencyFieldInfo(IObjectDescribe describe, List<IObjectData> dataList, User user);

    void fillCurrencyFieldInfo(User user, List<IFieldDescribe> fieldDescribes, Map<String, String> idCurrencyCodes, List<IObjectData> dataList);

    void fillExtendFieldInfo(IObjectDescribe describe, List<IObjectData> dataList, User user);

    void fillExtendFieldInfo(IObjectDescribe describe, List<IObjectData> dataList, User user, boolean notFillQuote, boolean notFillMask);

    void fillSelectLabelInfo(IObjectDescribe describe, List<IObjectData> dataList);

    void validateDataType(IObjectDescribe describe, List<IObjectData> dataList, User user);

    boolean fillDataOwnDeptAndOrgByOutUser(User user, IObjectDescribe describe, List<IObjectData> dataList);

    void fillOutDataOwnDeptAndOrgByOutOwner(User user, IObjectDescribe objectDescribe, IObjectData objectData);

    void fillOutDataOwnDeptAndOrgByOutOwner(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList);

    /**
     * 补充公共对象字段的数据可见范围
     *
     * @param user
     * @param objectDescribe
     * @param dataList
     */
    void fillDataVisibilityRange(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList);

    Map<String, ConnectedEnterpriseDTO.Helper> fillDataVisibilityRange(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList, boolean extractExtendInfo);

    Map<String, List<TeamMemberInfo.Member>> getTeamMemberInfos(User user, List<IObjectData> oldDataList, List<IObjectData> newDataList);

    void fillCountWithMultiRegion(User user, IObjectDescribe objectDescribe, List<IObjectData> iObjectData);

    /**
     * 为对象数据添加团队成员互联互通
     *
     * @param user           用户信息
     * @param objectDataList 对象数据
     */
    void setupTeamInterconnectedDepartments(User user, IObjectDescribe objectDescribe, List<IObjectData> objectDataList);

    List<IObjectData> setupTeamInterconnectedDepartmentsByIds(User user, IObjectDescribe objectDescribe, List<String> objectDataIds);

}
