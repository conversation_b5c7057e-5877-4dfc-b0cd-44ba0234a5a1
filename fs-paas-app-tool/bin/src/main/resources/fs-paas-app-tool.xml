<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <context:component-scan base-package="com.facishare.paas.appframework.tool"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:fs-paas-app-tool-rest.xml"/>
    <import resource="classpath:fs-uc-cache.xml"/>
</beans>